%PDF-1.7

1 0 obj
  << /Length 2 0 R
     /Range [ 0.000000 1.000000 0.000000 1.000000 0.000000 1.000000 ]
     /Domain [ 0.000000 1.000000 ]
     /FunctionType 4
  >>
stream
{  1.000000 exch 1.000000 exch 1.000000 exch dup 0.000000 gt { exch pop exch pop exch pop dup 0.000000 sub 0.000000 mul 1.000000 add exch dup 0.000000 sub 0.000000 mul 1.000000 add exch dup 0.000000 sub 0.000000 mul 1.000000 add exch } if dup 0.500000 gt { exch pop exch pop exch pop dup 0.500000 sub 0.000000 mul 1.000000 add exch dup 0.500000 sub 0.000000 mul 1.000000 add exch dup 0.500000 sub 0.000000 mul 1.000000 add exch } if dup 1.000000 gt { exch pop exch pop exch pop 1.000000 exch 1.000000 exch 1.000000 exch } if pop }
endstream
endobj

2 0 obj
  530
endobj

3 0 obj
  << /Length 4 0 R
     /Range [ 0.000000 1.000000 ]
     /Domain [ 0.000000 1.000000 ]
     /FunctionType 4
  >>
stream
{  0.000000 exch dup 0.000000 gt { exch pop dup 0.000000 sub 0.560000 mul 0.000000 add exch } if dup 0.500000 gt { exch pop dup 0.500000 sub -0.560000 mul 0.280000 add exch } if dup 1.000000 gt { exch pop 0.000000 exch } if pop }
endstream
endobj

4 0 obj
  229
endobj

5 0 obj
  << /BBox [ 0.000000 0.000000 327.000000 44.000000 ]
     /Resources << /Pattern << /P1 << /Matrix [ 327.000000 -0.000032 0.000032 327.000000 -0.000032 -283.000000 ]
                                      /Shading << /Coords [ 0.000000 0.000000 1.000000 0.000000 ]
                                                  /ColorSpace /DeviceGray
                                                  /Function 3 0 R
                                                  /Domain [ 0.000000 1.000000 ]
                                                  /ShadingType 2
                                                  /Extend [ true true ]
                                               >>
                                      /PatternType 2
                                      /Type /Pattern
                                   >> >> >>
     /Subtype /Form
     /Length 6 0 R
     /Group << /Type /Group
               /S /Transparency
               /CS /DeviceGray
            >>
     /Type /XObject
  >>
stream
/DeviceGray CS
/DeviceGray cs
1.000000 0.000000 -0.000000 1.000000 0.000000 0.000000 cm
22.000000 43.000000 m
305.000000 43.000000 l
305.000000 45.000000 l
22.000000 45.000000 l
22.000000 43.000000 l
h
305.000000 1.000000 m
21.999992 1.000000 l
21.999992 -1.000000 l
305.000000 -1.000000 l
305.000000 1.000000 l
h
21.999992 1.000000 m
10.402014 1.000000 1.000000 10.402020 1.000000 22.000000 c
-1.000000 22.000000 l
-1.000000 9.297451 9.297443 -1.000000 21.999992 -1.000000 c
21.999992 1.000000 l
h
326.000000 22.000000 m
326.000000 10.402020 316.597992 1.000000 305.000000 1.000000 c
305.000000 -1.000000 l
317.702545 -1.000000 328.000000 9.297451 328.000000 22.000000 c
326.000000 22.000000 l
h
305.000000 43.000000 m
316.597992 43.000000 326.000000 33.597980 326.000000 22.000000 c
328.000000 22.000000 l
328.000000 34.702549 317.702545 45.000000 305.000000 45.000000 c
305.000000 43.000000 l
h
22.000000 45.000000 m
9.297451 45.000000 -1.000000 34.702549 -1.000000 22.000000 c
1.000000 22.000000 l
1.000000 33.597980 10.402020 43.000000 22.000000 43.000000 c
22.000000 45.000000 l
h
/Pattern cs
/P1 scn
f
n

endstream
endobj

6 0 obj
  1111
endobj

7 0 obj
  << /Pattern << /P1 << /Matrix [ 327.000000 -0.000032 0.000032 327.000000 -0.000032 -283.000000 ]
                        /Shading << /Coords [ 0.000000 0.000000 1.000000 0.000000 ]
                                    /ColorSpace /DeviceRGB
                                    /Function 1 0 R
                                    /Domain [ 0.000000 1.000000 ]
                                    /ShadingType 2
                                    /Extend [ true true ]
                                 >>
                        /PatternType 2
                        /Type /Pattern
                     >> >>
     /ExtGState << /E1 << /SMask << /Type /Mask
                                    /G 5 0 R
                                    /S /Luminosity
                                 >>
                          /Type /ExtGState
                       >> >>
  >>
endobj

8 0 obj
  << /Length 9 0 R >>
stream
/DeviceRGB CS
/DeviceRGB cs
q
1.000000 0.000000 -0.000000 1.000000 0.000000 0.000000 cm
0.160000 0.160000 0.160000 scn
0.000000 22.000000 m
0.000000 34.150265 9.849736 44.000000 22.000000 44.000000 c
305.000000 44.000000 l
317.150269 44.000000 327.000000 34.150265 327.000000 22.000000 c
327.000000 22.000000 l
327.000000 9.849735 317.150269 0.000000 305.000000 0.000000 c
21.999992 0.000000 l
9.849729 0.000000 0.000000 9.849735 0.000000 22.000000 c
0.000000 22.000000 l
h
f
n
Q
q
0.000000 22.000000 m
0.000000 34.150265 9.849736 44.000000 22.000000 44.000000 c
305.000000 44.000000 l
317.150269 44.000000 327.000000 34.150265 327.000000 22.000000 c
327.000000 22.000000 l
327.000000 9.849735 317.150269 0.000000 305.000000 0.000000 c
21.999992 0.000000 l
9.849729 0.000000 0.000000 9.849735 0.000000 22.000000 c
0.000000 22.000000 l
h
W*
n
q
/E1 gs
1.000000 0.000000 -0.000000 1.000000 0.000000 0.000000 cm
/Pattern cs
/P1 scn
22.000000 43.000000 m
305.000000 43.000000 l
305.000000 45.000000 l
22.000000 45.000000 l
22.000000 43.000000 l
h
305.000000 1.000000 m
21.999992 1.000000 l
21.999992 -1.000000 l
305.000000 -1.000000 l
305.000000 1.000000 l
h
21.999992 1.000000 m
10.402014 1.000000 1.000000 10.402020 1.000000 22.000000 c
-1.000000 22.000000 l
-1.000000 9.297451 9.297443 -1.000000 21.999992 -1.000000 c
21.999992 1.000000 l
h
326.000000 22.000000 m
326.000000 10.402020 316.597992 1.000000 305.000000 1.000000 c
305.000000 -1.000000 l
317.702545 -1.000000 328.000000 9.297451 328.000000 22.000000 c
326.000000 22.000000 l
h
305.000000 43.000000 m
316.597992 43.000000 326.000000 33.597980 326.000000 22.000000 c
328.000000 22.000000 l
328.000000 34.702549 317.702545 45.000000 305.000000 45.000000 c
305.000000 43.000000 l
h
22.000000 45.000000 m
9.297451 45.000000 -1.000000 34.702549 -1.000000 22.000000 c
1.000000 22.000000 l
1.000000 33.597980 10.402020 43.000000 22.000000 43.000000 c
22.000000 45.000000 l
h
f
n
Q
Q

endstream
endobj

9 0 obj
  1936
endobj

10 0 obj
  << /Annots []
     /Type /Page
     /MediaBox [ 0.000000 0.000000 327.000000 44.000000 ]
     /Resources 7 0 R
     /Contents 8 0 R
     /Parent 11 0 R
  >>
endobj

11 0 obj
  << /Kids [ 10 0 R ]
     /Count 1
     /Type /Pages
  >>
endobj

12 0 obj
  << /Pages 11 0 R
     /Type /Catalog
  >>
endobj

xref
0 13
0000000000 65535 f
0000000010 00000 n
0000000724 00000 n
0000000746 00000 n
0000001123 00000 n
0000001145 00000 n
0000003282 00000 n
0000003305 00000 n
0000004188 00000 n
0000006180 00000 n
0000006203 00000 n
0000006379 00000 n
0000006455 00000 n
trailer
<< /ID [ (some) (id) ]
   /Root 12 0 R
   /Size 13
>>
startxref
6516
%%EOF