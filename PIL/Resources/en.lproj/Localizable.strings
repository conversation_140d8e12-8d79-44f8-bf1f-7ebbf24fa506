/*
 Localizable.strings
 PIL
 
 Created by <PERSON> on 9/5/21.
 
 */

//alerts
"LE" = "LE";
"Lives" = "Lives";
"Alert" = "Alert";
"Downloading..." = "Downloading..";
"Left Time:" = "Left Time:";
"installWhats" = "you don't have whatsapp please Install it";
"copied" = "Copied";
"loginAlert" = "Your session expired please login again";
"logoutAlert" = "Are you sure you want to logout";
"Choose photo" = "Choose photo";
"ChooseLang" = "Choose Language";
"Camera" = "Camera";
"Gallery" = "Gallery";
"Cancel" = "Cancel";
"delete" = "Delete";
"Buy With" = "Buy With";
"OK" = "Ok";
"openLocation" = "You must open your location from setting";
"imgInvalid" = "You must choose at least one image";
"policyAgree" = "You must aggree to our terms first";
"deleteConfirmation" = "Do you want to delete this item";
"loading" = "Loading";
"errorSendEmail" = "Can not send email to this email";
"Empty" = "must fill empty fields";
"Please fill in all fields" = "Please fill in all fields";
"uninstallWarning" = "Are you sure you want to uninstall this game";
"Your version up to date" = "your version up to date";
"adsErrors" = "Ads are Available when you have less than 5 live";
"Please Enter a valid phone number" = "Please Enter a valid phone number";
"Please Enter a non Empty Email" = "Please Enter a non Empty Email";
"Please Enter a valid Email" =  "Please Enter a valid Email";
"VERIFY AND LOG IN" = "VERIFY AND LOG IN";
"Email Sent to" = "Email Sent to";
"notEnoughLives" = "You don't have enough lives Add lives from shop or watch ads";
"forbiddenTeamEdit" = "It is not allowed to change the team after the start of the match";

"You don't have Enough lives" = "You don't have Enough lives";
//Error Strings
"no Internet" = "no Internet Connection";
"Bad request" = "Bad request";
"Server error" = "Server error";
"This email does not exist" = "This email does not exist";
"phoneNotAllowed" = "Not allowed to edit mobile number";
"email" = "email";
"phone number" = "phone number";
"your profile updated Successfully" = "your profile updated Successfully";
"SMS Sent to" = "SMS Sent to";
"code Sent to" = "code Sent to email";
"sublist0" = "Profile";
"sublist1" = "Notifications";
"sublist2" = "Refer & Earn";
"sublist3" = "Settings";
"sublist4" = "Help";
"sublist5" = "Share";


"Are you sure to Delete your account" = "Are you sure to Delete your account";
"settingTitle0" = "Help";
"settingTitle1" = "Share App";
"settingTitle2" = "Game Management";
"settingTitle3" = "Language";
"settingTitle4" = "Sound";
"settingTitle5" = "Refer & Earn";
"settingTitle6" = "Privacy and policy";
"settingTitle7" = "Terms & Conditions";
"settingTitle8" = "RATE US";
"settingTitle9" = "Subscription";
"settingTitle10" = "Block List";
//"settingTitle11" = "LOGOUT";
"settingTitle11" = "Delete Account";
"settingTitle12" = "Restore Purchases";
"settingTitle13" = "Dark Mode";
"settingTitle14" = "Recover Account";

"menuTitle0" = "Setting & Privacy";
"menuTitle1" = "Refer & Earn";
"menuTitle2" = "Help";



"Current Country" = "Current Country";
"Dark" = "Dark";
"Light" = "Light";
"Change Mode" = "Change Mode To";

"prize" = "Prize";
"score" = "Score";
"Visa" = "Debit/ Credit Card";
"VodafoneCash" = "Vodafone Cash";
"Fawry" = "Fawry";
"Masary" = "Masary";


"win" = "YOU WON";
"lose" = "YOU LOST";

"teamSaved" = "team saved successfully";

"Home" = "Games";
"Store" = "Store";
"Events" = "Events";
"Branded" = "Branded";

"Start Now" = "Start Now";
"GK_Min_Message_Error" = "You Must Select a Goalkeeper First";
"Choose Your Team To Achieve The Best Prediction" = "Choose Your Team To Achieve The Best Prediction";
//MARK: - wallet
"Plase write balance" = "Please write balance";
"Added Successfully" = "Added Successfully";
"Try Tomorrow" = "Try Tomorrow";
"My Payments" = "My Payments";
"Redeem Successfully " = "Redeem Successfully ";
"Incorrect coupon" = "Incorrect coupon";

"My Games" = "My Games";
"Recently played" = "Recently played";

"Add Cash" = "Add Cash";
"Deposit" = "Deposit";
"Tickets" =  "Tickets";
"Ticket expire" = "Expired";
"Coupons" = "Coupons";
"Bouns" = "Bonus";
"Coins" = "Token";
"Winning" = "Winning";

"Scratch Cards" = "Scratch Cards";
"Choose Option" = "Choose Option";
"Edit" = "Edit";
"PIL Coins" = "Pi Token";
"Deposit Coins" = "Winning Pi Token";
"PIL Tokens" = "Pi Tokens";
"Withdraw" = "Withdraw";
"Add Cash" = "Add Cash";
"Earn More" = "Earn More";

"Please write coupon" = "Please write coupon";
"passed" = "Passed";
"onHold" = "OnHold";
"failed" = "Failed";
"You Won Cash Back" = "You Won Cash Back";
"User" = "User";
"Now" = "Now";
"APPLY" = "APPLY";
"Day" = "Day";
//MARK: - fortune
"Try Tomorrow" = "Try Tomorrow";

// help
"FAQ Details" = "FAQ Details";
"Help PIL" = "Help PIL";
"Help" = "Help";
"Privacy Policy" = "Privacy and policy";
"Terms & Conditions" = "Terms & Conditions";
"Issues History" = "Issues History";
"User Guide" = "User Guide";
"Contact Us" = "Contact Us";

// messions
"My missions" = "My Missions";
"Refer & Earn" =  "Refer Friends";
"Upload Photo" = "Upload Photo";
"Make Deposit" = "Make Deposit";
"Add Deposit" = "Add Deposit";
"Play Game" = "Play Game";
"Refer Frirnd and Win 10 Points Use Code" = "Refer Friend and Win 10 Lives Use Code";
"Get" = "Get";
"copied" = "copied";


"Details of step 1 of refer a friend and win goes here" = "Copy the code and share with your friends.";
"Details of step 2 of refer a friend and win goes here" = "Ask your friends to enter the code when registering their accounts";
"Details of step 3 of refer a friend and win goes here" = "Check the store to get a new lives";

"Step 1 of refer goes here" = "Step 1";
"Step 2 of refer goes here" = "Step 2";
"Step 3 of refer goes here" = "Step 3";
//profile
"Followers" = "Followers";
"Following" = "Following";
"This user blocked you" = "This user blocked you";
"Info" = "Info";
"Game History" = "Game History";
"Referrals" = "Referrals";
"Username" = "Username";
"Email" = "Email";
"Password" = "Password";
"Phone" = "Phone";
"There're no data" = "There're no data";
"You won" = "You won";
"You lost" = "You lost";

"bio" = "bio";

//add cash
"cardAddedSuccessfully" = "Card Added Successfully";
"cardUpdatedSuccessfully" = "Card updated Successfully";
"cardDeletedSuccess" = "Card Deleted Successfully";
"Payment" = "Payment";
"EnterCVV" = "Enter CVV To Confirm Payment";
"cvv" = "cvv";
"EnterVerificationCode" = "We sent Verification code to ypur number,\nplease write it here to cobnfirm";
"code" = "code";

"Unblock" = "Unblock";
"Block" = "Block";
"Blocked On" = "Blocked On";
"Report" = "Report";

"Goal Keeper" = "Goal Keeper";
"Goal Keepers" = "Goal Keepers";
"Defenders" = "Defenders";
"Defender" = "Defender";
"Middle Fields" = "Middle Fields";
"Middle Field" = "Middle Field";
"Forwards" = "Forwards";
"Forward" = "Forward";
"players" = "Players";
"fantasyPayMessage" = "You are about to register this team for";
"ticketPayMessage" = "You are about to buy this ticket for";
"redeemPayMessage" = "You are sure to redeem tickets";
"CONTINUE REGIETSER" = "CONTINUE REGIETSER";
"EDIT TEAM" = "EDIT TEAM";
"CONFIRM" = "CONFIRM";
"(c)and(VC)notfound" = "You must select Captain and Vice Captain in Your Team";
"Free" = "Free";
"Report" = "Report";

"CREATED" = "CREATED";
"CLAIMED" = "ACCLAIMED";

"firstLoginDisclemer" = "Welcome dear user to Pi Gaming platform.

Please read the next rules carefully to proceed to application.

Using this app confirms that you are eligible and welling to follow such rules by full understanding and acceptance from your side, it means you will be committed to  the following:

-Pay entry fees.

- Play game rounds.

- Be committed to fair play rules.

- Make sure to be connected to internet the whole sessions time.

- Prizes will be distributed after Tournament ends.



Kindly notice that Pi application is NOT in any way responsible for any unacceptable behavior from your side or any other users sides.

Pi is not a gambling platform, it's a gaming community and Esports platform.

Contests in the application are NOT sponsored by Apple.

Apple is not responsible in any way about application details or content.



By choosing to proceed is an acknowledgment that you read and understand what was presented in this disclaimer and you accept it.



Thank you, have a nice time with Pi.

";

"gamesRules" = "<html><body>
<p style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:'Cairo',sans-serif;'><strong><span style='font-size:16px;line-height:107%;font-family:Cairo;'>Free Play (<u>Unlimited</u>)</span></strong></p>
<ol style='list-style-type: undefined;'>
    <li><span style=';'>Play without Lives</span>
        <ul style='list-style-type: disc;'>
            <li><span style=';'>Players can play for unlimited times for free without any lives in their account.</span></li>
        </ul>
    </li>
</ol>
<p style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:'Cairo',sans-serif;'><strong><span style='font-size:16px;line-height:107%;font-family:Cairo;'>Battles</span></strong><strong><span style='font-family:Cairo;'>&nbsp;</span></strong></p>
<ol style='list-style-type: undefined;'>
    <li><span style=';'>Play 1vs 1 &nbsp;&nbsp;</span>
        <ul style='list-style-type: disc;'>
            <li><span style=';'>You should have at least one live to play against another player.</span></li>
            <li><span style='color:black;'>Discover a battle room, you should have at least a live to play once. There is no restriction on the number of battles you can play. &nbsp;</span></li>
            <li><span style='color:black;'>Check the battle room leaderboard after playing one battle to check your score.</span></li>
            <li><span style='color:black;'>After requesting to join a battle, you will be straightforwardly coordinated with another user playing in a similar battle room.</span></li>
            <li><span style='color:black;'>We take a live as soon as the player joins the battle and if the player disconnects while he is in the match making, the live will be returned back.</span></li>
            <li><span style=';'>If certain cases happen <span style='color:black;'>that possibly you or your opponent leaves before the game starts, your live will be returned back.</span></span></li>
            <li><span style='color:black;'>You cannot stop the game in the center, when you start the game, it should proceed till the end.&nbsp;</span><span style='font-family:Cairo;'>&nbsp;</span></li>
            <li><span style='color:black;'>The battle closes; when both you and your opponent have finished the game, the highest score wins the battle.&nbsp;</span></li>
            <li><span style='color:black;'>If two players won at the same time and they had the same score, each player would get his live back.</span></li>
        </ul>
    </li>
</ol>
<p style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:'Cairo',sans-serif;'><strong><span style='font-size:16px;line-height:107%;font-family:Cairo;color:black;'>Shop</span></strong></p>
<div style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:'Cairo',sans-serif;'>
    <ol style='margin-bottom:0in;list-style-type: undefined;'>
        <li style='margin-top:0in;margin-right:0in;margin-bottom:8.0pt;margin-left:0in;line-height:107%;font-size:15px;font-family:'Cairo',sans-serif;'><span style='font-family:Cairo;color:#4472C4;'>PlayIT Lives</span><span style='font-family:Cairo;color:black;color:black;'>: The&nbsp;</span><span style='font-family:Cairo;'>Lives<span style='color:black;'>&nbsp;transferred from Rewarded Ads and Refer and Earn: Refer your friends and get a free Lives.</span></span></li>
    </ol>
</div></div></body></html>";



//New Design
"Enter New Mobile Number" = "To Reset Account Phone Number \n Provide Account Email";
"We Sent A 6-Digital Code To Your Phone" = "We Sent A 6-Digital Code To Your Phone";
"We Sent A 6-Digital Code To Mail" = "We Sent A 6-Digital Code To Mail";
"You've exceeded the maximum number of chances, good luck next time" = "You've exceeded the maximum number of chances, good luck next time";
"Done" = "Done";



"Write your opinion about the game" = "Write your opinion about the game" ;
"See how to play Tutorials" = "See how to play Tutorials" ;
"Share application and gain more lives" = "Share application and gain more lives" ;
"Play free and practice your skills" = "Play free and practice your skills" ;
"View more challenges" = "View more challenges" ;

"Click To See Your Lives" = "Click To See Your Lives";
"For More Options Click Menu" = "For More Options Click Menu";
"You Can Get More Lives By Store" = "You Can Get More Lives By Store";
"Play Games" = "Play Games";
"Click To Join To Our Event" = "Click To Join To Our Event";
"Play Branded Games And Win" = "Play Branded Games And Win";

"Please Enter non Empty number" = "Please Enter non Empty number";
"Please Enter non Empty Email" = "Please Enter non Empty Email";

//Share App
"shareApp" = "we are live now, share the app with your friends and enjoy the ultimate gaming experience!";
"shareGame" = " Join & play this game and get ready for the ultimate fun. ";

//alert
"alertFeedBack" = "YOU ALREADY SUBMITTED A FEEDBACK , THANK YOU.";
"Please write at least 10 characters" = "Please write at least 10 characters";
"Please write a comment" = "Please write a comment";
"Internet speed is not good" = "Internet speed is not good";
"continuation" = "continuation";
"Download :" = "Download :";
"Back" = "Back";
"Missions" = "Missions";

"Arabic" = "Arabic";
"English" = "English";


//Tournament tab
"Tournaments" = "Tournaments";
"You don't have rewards yet !!" = "You don't have rewards yet !!";

"Game" = "Game";
"Tournament name" = "Tournament name";
"Number of players" = "Number of players";
"Time" = "Time";

"PLAY" = "PLAY";
"SHARE" = "SHARE";
"LEAVE" = "LEAVE";
"joining" = "joining";

"Are you sure you want to leave the tournament?" = "Are you sure you want to leave the tournament?";
"Are you sure you want to delete the tournament?" = "Are you sure you want to delete the tournament?";

"Winners" = "Winners";

"Please choose a game" = "Please choose a game";
"Please enter tournament name" = "Please enter tournament name";
"Please choose a time" = "Please choose a time";
"Please enter no. of players" = "Please enter no. of players";
"No. of player is out of range, players must be between" = "No. of player is out of range, players must be between";
"and" = "and";

"Leave" = "Leave";
"Delete" = "Delete";
"New version" = "New version";
"Update" = "Update";
"Please wait" = "Please wait...";

"Check your progress after each mission" = "Check your progress after each mission";
"Click on target mission card to see the sub mission" = "Click on target mission card to see the sub mission";
"Completed missions will be checked" = "Completed missions will be checked";
"Click on mission to complete" = "Click on mission to complete";

"This mission is done" = "This mission is done";
"The tournament is ended" = "The tournament is ended";

"h" = "h";
"m" = "m";
"s" = "s";
"d" =  "d";

"You must agree to our terms and conditions first" = "You must agree to our terms and conditions first";
"on" = "on";
"off" = "off";

"No games have been downloaded" = "No games have been downloaded";

"Memberships" = "Memberships";
"Items" = "Items";

"Five daily rewards" = "Five daily rewards";
"Five for each watch ads" = "Five for each watch ads";
"Free ads" = "Free ads";
"Successfully deleted" = "Successfully deleted";
"Group Created Successfully" = "Group Created Successfully";
"Are you sure to delete chat ?" = "Are you sure to delete chat ?";
"Are you sure to delete Group ?" = "Are you sure to delete Group ?";
"Are you sure to leave Group ?" = "Are you sure to leave Group ?";
"Auto-Renewed Every 30 Days From Purchase Date" = "Auto-Renewed Every 14 Days From Purchase Date";
"sent successfully" = "sent successfully";
"Left successfully"  = "You are no longer a member of the group";
"Chat reported successfully" = "Chat reported successfully";
"Golden Member" = "Golden Member";
"Silver Member" = "Silver Member";
"Unfollow" = "Unfollow";
"Follow" = "Follow";
"User Info" =  "User Info";
"Rewards" = "Rewards";
"Posts" = "Posts";
"Win" = "Win";
"Draw" = "Draw";
"Lose" = "Lose";
"Username Or Id" = "Username Or Id";
"Followers" =  "Followers";
"Following" = "Following";
"Five daily rewards" = "Five daily rewards";
"Five for each watch ads" = "Five for each watch ads";
"Free ads" = "Free ads";
"Room name" = "Room name";
"Topic" = "Topic";
"Game (Optional)" = "Game (Optional)";
"In order to upload photos we need access to your gallery" = "In order to upload photos we need access to your gallery";
"Settings" = "Settings";
"Cancel" = "Cancel";
"Feeds" = "Feeds";
"Explore" = "Explore";
"Rooms" = "Rooms";
"In order to upload photos we need access to your camera" = "In order to upload photos we need access to your camera";
"What's on your mind" = "What's on your mind";
"Tags" = "Tags";
"People" = "People";
"Find products" = "Find products";
"Find tournaments" = "Find tournaments";
"Find game" = "Find game";
"Tags, People and Rooms" = "Tags, People and Rooms";
"Comment can't be empty" = "Comment can't be empty";
"Add a comment" = "Add a comment";
"Games" = "Games";
"Social" = "Social";
"Movies" = "Movies";
"Series" = "Series";
"Image" = "Image";
"Video" = "Video";
"Voice" = "Voice";
"Created By "  = "Created By ";
"Delete" = "Delete";
"View Profile" = "View Profile";
"Report" = "Report";
"Block" = "Block";
"Edit" = "Edit";
"Online" = "Online";
"Offline" = "Offline";
"Share" = "Share";
"Block" = "Block";
"Report" = "Report";
"Play" = "Play";
"Join" = "Join";
"Recent" = "Recent";
"Unread" = "Unread";
"Groups" = "Groups";
"Why do you want to report this chat?" = "Why do you want to report this chat?";
"Why do you want to report this user?" = "Why do you want to report this user?";
"Reason" = "Reason";
"User has been reported successfully" = "User has been reported successfully";
"User has been bolcked successfully" = "User has been bolcked successfully";
"Min " = "Min ";
"Any Player Pay " = "Any Player Pay ";
"Please enter entry fee" = "Please enter entry fee";
"Entry fee is out of range, fee must be between" = "Entry fee is out of range, fee must be between";
"Liked by " = "Liked by ";
"Others" = "Others";
"View All " = "View All ";
"Comments" = "Comments";
"No Likes Yet" = "No Likes Yet";
"Number Of Winners" = "Number Of Winners";
"Please enter no. of winners" = "Please enter no. of winners";
"Lives & Tokens" = "Lives & Tokens";
"Voucher Purchased Successfully" = "Voucher Purchased Successfully";
"Vouchers" = "Vouchers";
"You have no subscription" = "You have no subscription";
"Find vouchers" = "Find vouchers";
"App" = "App";
"Post Saved Successfully" = "Post Saved Successfully";
"Post Added Successfully" = "Post Added Successfully";
"People" = "People";
"Please first enter a search query" = "Please first enter a search query";
"Free" = "Free";
"User Blocked successfully" = "User Blocked successfully";
"You Have" = "You Have";
"New Followers" = "New Followers";
"Likes" = "Likes";
"Post Shares" = "Post Shares";
"Comments" = "Comments";
"Shares" = "Shares";
"Find user" = "Find user";
"Link copied to clipboard" = "Link copied to clipboard";
"Code copied to clipboard" = "Code copied to clipboard";
"Reconnecting..." = "Reconnecting...";
"Please wait" = "Please wait";
"see members" = "See members";
"Unlike" = "Unlike";
"Like" = "Like";
"Views" = "Views";
"Version" = "Version";
"Recommended" = "Recommended";
"Find member" = "Find member";
"Added" = "Added";
"Add" = "Add";
"Add members" = "Add members";
"Prize: " = "Prize: ";
"Coins" = "Coins";
"Your account has been created" = "Your account has been created";
"You have no followers" = "You have no followers";
"You have no likes" = "You have no likes";
"You have no shares" = "You have no shares";
"You have no comments" = "You have no comments";
"Complete the previous missions to unlock this mission" = "Complete the previous missions to unlock this mission";
"less" = "less";
"more" = "more";
"You want more tokens" = "You want more tokens?";
"Points" = "Points";
"Tokens" = "Tokens";
"Voucher" =  "Voucher";
"Buy Vouchers" = "Buy Vouchers";
"Send" = "Send";
"You already have infinite lives" = "You already have infinite lives";
"Would you like to rate use on the store?" =  "Would you like to rate use on the store?";
"Go To Store" = "Go To Store";
"User id copied to clipboard" = "User id copied to clipboard";
"User shared successfully" =  "User shared successfully";
"Game shared successfully" = "Game shared successfully";
"Post shared successfully" = "Post shared successfully";

"is typing ..." = "is typing ...";

"Total Referrals: " = "Total Referrals: ";

// MARK: - Posts
"Save" = "Save" ;

//MARK: - Rooms
"members" = "Members" ;
"no.rooms.message" = "No Member in This Room." ;
"enter" = "Enter" ;
"create.room" = "Create Room" ;

"Room has been created successfully" = "Room has been created successfully";
"Play Now" = "Play Now";

"Mic permission Request sent" = "Mic permission Request sent";
"Admin accepted mic Request" = "Admin accepted mic Request";
"Admin rejected mic Request" = "Admin rejected mic Request";
"Request Mic Permission" = "Request Mic Permission";
"Select the number of tokens" = "Select the number of tokens";
"Reply" = "Reply";
"Forward" = "Forward";
"Edit" = "Edit";
"Delete" = "Delete";
"Messagee copied to clipboard" = "Messagee copied to clipboard";
"Copy" = "Copy";
"Report" = "Report";
"Pay" = "Pay";
"Min" = "Min";
"Tournament type" = "Tournament type";
"Search" = "Search";
"Messages" = "Messages";
"Create Group" = "Create Group";
"Create Group" = "Create Group";
"Profile" = "Profile";
"Edit Profile" = "Edit Profile";
"Challange did finished" = "Challange did finished";
"Add a Reply to" = "Add a Reply to";
"Type here" = "Type here";
"Gifts" = "Gifts";
"Dark Mode" = "Dark Mode";
"Light Mode" = "Light Mode";
"The application will be closed, please open it again" = "The application will be closed, please open it again";
"Add Members" = "Add Members";
"Feedback" = "Feedback";
"How To Play" = "How To Play";
"Recovery" = "Recovery";
"An account has been recovered to this device within the past 30 days. For more information, <NAME_EMAIL> or raise a ticket from app help section." = "An account has been recovered to this device within the past 30 days. For more information, <NAME_EMAIL> or raise a ticket from app help section.";
"Entry fee " = "Entry fee ";
"Mute" = "Mute";
"UnMute" = "UnMute";
"Admin turned off mic" = "Admin turned off the mic";
"Member" = "Member";
"Add" = "Add";
"Membership_Msg" = "Membership_Msg";
"Membership_Msg" = "This is a 30 days subscription plan that is auto renewed. Check subscription policies in PlayIT terms and conditions";
"Silver Card" = "Silver Card";
"Golden Card" = "Golden Card";
"Gifts" = "Gifts";
"New Message" = "New Message";
"Clear" = "Clear";
"Message Info" = "Message Info";
"Tournament created successfully" = "Tournament created successfully";
"Post has been deleted" = "Post has been deleted";
"Create Tournament" = "Create Tournament";
"Sign In Again" = "Sign In Again";
"Clear Chat" = "Clear Chat";
