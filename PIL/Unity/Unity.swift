//
//  Unity.swift
//  PIL
//
//  Created by Macbook on 15/10/21.
//

import Foundation
import UnityFramework

protocol UnityFrameworkDelegate: AnyObject {
    func unityDidUnload()
}

class Unity: UIResponder, UIApplicationDelegate, UIWindowSceneDelegate, UnityFrameworkListener {

    struct UnityMessage: Codable {
        let objectName: String
        let methodName: String
        let messageBody: String
    }

    private var cachedMessages = [UnityMessage]()
    static let shared = Unity()

    private let dataBundleId: String = "com.unity3d.framework"
    private let frameworkPath: String = "/Frameworks/UnityFramework.framework"

    private var ufw: UnityFramework?
    private var hostMainWindow: UIWindow?

    private var isInitialized: Bool {
        ufw?.appController() != nil
    }

    var delegate: UnityFrameworkDelegate?

    func show() {
        if let data = UserDefaults.standard.data(forKey: "note") {
            do {
                let decoder = JSONDecoder()
                let note = try decoder.decode(UnityMessage.self, from: data)
                cachedMessages.append(note)
            } catch {
                print("Unable to Decode Note (\(error))")
            }
        }

        if isInitialized {
            showWindow()
        } else {
            initWindow()
        }
    }

    func setHostMainWindow(_ hostMainWindow: UIWindow?) {
        self.hostMainWindow = hostMainWindow
    }

    private func initWindow() {
        if isInitialized {
            showWindow()
            return
        }

        guard let ufw = loadUnityFramework() else {
            print("ERROR: Was not able to load Unity")
            return unloadWindow()
        }

        self.ufw = ufw
        ufw.setDataBundleId(dataBundleId)
        ufw.register(self)
        ufw.runEmbedded(
            withArgc: CommandLine.argc,
            argv: CommandLine.unsafeArgv,
            appLaunchOpts: nil
        )

        sendCachedMessages()
    }

    private func showWindow() {
        if isInitialized {
            ufw?.showUnityWindow()
            sendCachedMessages()
        }
    }

    private func unloadWindow() {
        print("Unload app window")
        if isInitialized {
            cachedMessages.removeAll()
            ufw?.unloadApplication()
        }
    }

    private func loadUnityFramework() -> UnityFramework? {
        let bundlePath = Bundle.main.bundlePath + frameworkPath
        guard let bundle = Bundle(path: bundlePath) else { return nil }

        if !bundle.isLoaded {
            bundle.load()
        }

        return bundle.principalClass?.getInstance()
    }

    public func applicationDidEnterBackground(isBool: Bool) {
        print("applicationDidEnterBackground")
        print("games Pause is \(isBool)")
        ufw?.pause(isBool)
    }

    func sendMessage(_ objectName: String, _ methodName: String, _ message: String) {
        let msg = UnityMessage(objectName: objectName, methodName: methodName, messageBody: message)

        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(msg)
            UserDefaults.standard.set(data, forKey: "note")
        } catch {
            print("Unable to Encode Note (\(error))")
        }

        if isInitialized {
            ufw?.sendMessageToGO(withName: msg.objectName, functionName: msg.methodName, message: msg.messageBody)
        } else {
            cachedMessages.append(msg)
        }
    }

    private func sendCachedMessages() {
        for msg in cachedMessages {
            ufw?.sendMessageToGO(withName: msg.objectName, functionName: msg.methodName, message: msg.messageBody)
        }
        cachedMessages.removeAll()
    }

    func unityDidUnload(_ notification: Notification!) {
        print("unityDidUnload")
        NotificationCenter.default.post(name: Notification.Name("ReloadLives"), object: nil)
        delegate?.unityDidUnload()

        cachedMessages.removeAll()
        Media()
        ufw?.unregisterFrameworkListener(self)
        ufw = nil
        ufw?.unloadApplication()
        hostMainWindow?.makeKeyAndVisible()

        var shouldAutorotate: Bool {
            return false
        }

        var supportedInterfaceOrientations: UIInterfaceOrientationMask {
            return .portrait
        }
    }

    func unityDidQuit(_ notification: Notification!) {
        print("unityDidQuit called")
        ufw?.unregisterFrameworkListener(self)
        ufw = nil
        setHostMainWindow(nil)

        let obj = SharedGameData.shared
        obj.dealocate()
    }

    func Media() {
        let obj = SharedGameData.shared
        GoogleAnalyticsHelper.shared.end_Game(
            game_name: obj.game_name ?? "",
            game_total_time: obj.getTotalTime(),
            event_id: obj.event_id ?? "",
            game_event_type: obj.game_event_type ?? "",
            game_event_name: obj.game_event_name ?? "",
            game_prize: obj.game_prize ?? ""
        )
    }
}

class SharedGameData {
    static var shared: SharedGameData {
        return SharedGameData()
    }

    private init() {
        game_name = ""
        event_id = ""
        game_event_type = ""
        game_event_name = ""
        game_prize = ""
    }

    var game_name: String?
    var event_id: String?
    var game_event_type: String?
    var game_event_name: String?
    var game_prize: String?
    private var startGameTime: Date?

    func setData(game_name: String, event_id: String, game_event_type: String, game_event_name: String, game_prize: String) {
        self.game_name = game_name
        self.event_id = event_id
        self.game_event_type = game_event_type
        self.game_event_name = game_event_name
        self.game_prize = game_prize
        self.startGameTime = Date()
    }

    func getTotalTime() -> String {
        let components = Calendar.current.dateComponents([.minute], from: startGameTime ?? Date(), to: Date())
        return "\(components.minute ?? 0)"
    }

    func dealocate() {
        game_name = ""
        event_id = ""
        game_event_type = ""
        game_event_name = ""
        game_prize = ""
    }
}
