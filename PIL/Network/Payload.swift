//
//  Payload.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/06/2023.
//

import Foundation

protocol Paginatable{
    var createdBy: Int? {get}
    var pageSize: Int? {set  get}
    var pageIndex: Int? {set get}
}

protocol ActionSendable{
    var objectID: Int? { get }
    var targetObjectID: Int? { get }
    var actionID: Int? { get }
}

enum Payloads{
    // MARK: - UserRecordPayload
    struct UserRecordPayload: Encodable {
        let createdBy: String
        let userRecord: UserRecord
        let isDesc = true
        let orderByColumn = "CreationDate"
    }
    
    // MARK: - UserRecord
    struct UserRecord: Encodable {
        var id: String?
        var name: String? = nil
        var isUsers: String? = nil
        var isFollowers: Bool? = nil
        var isFollowing: Bool? = nil
        var isPopularity: Bool? = nil
    }
    
    // MARK: - UserActionRecordPayload
    struct UserActionRecordPayload: Encodable {
        var createdBy: String? = nil
        var languageId: Int?
        let userActionRecord: UserActionRecord
    }
    
    // MARK: - UserChatActionRecordPayload
    struct UserChatActionRecordPayload: Codable {
        var createdBy: String? = nil
        let userChatRecord: UserChatRecord
    }
    
    // MARK: - UserActionRecord
    struct UserActionRecord: Codable, ActionSendable {
        var id: Int? = nil
        var objectID: Int? = nil
        var targetObjectID: Int? = nil
        var actionID: Int? = nil
        var reactionid: Int? = nil
        var reactionImg:String? = nil
        var oldReactionImg: String?
        var isLike: Bool? = nil
    }
    
    // MARK: - UserActionsPayload
    struct PostsListPayload: Codable, Paginatable {
        var pageSize: Int?
        var pageIndex: Int?
        var createdBy: Int?
        var postRecord: PostRecord?
    }
    
    // MARK: - PostRecord
    struct PostRecord: Codable {
        var mediaType : Int? = nil
        var execludedId : Int? = nil
        var id: Int? = nil
        var createdBy: Int? = nil
        var isFeeds: Bool? = nil
        var title: String? = nil
        var createdImageUrl : String? = nil
    }
    
    struct NewFeed : Codable {
        var id: Int? = nil
        var content:   String? = nil
        var title:  String? = nil
        var imageUrl: String? = nil
        var objectI : String? = nil
        var targetObjectID : String? = nil
        var createdBy: Int? = nil
        var creationDate: String? = nil
        var creationDateStr: String? = nil
        var modificationDateStr: String? = nil
        var imageFile: String? = nil
        var commentsCount : String? = nil
        var likesCount : String? = nil
        var createdName:  String? = nil
        var createdImageUrl: String? = nil
        var mediaType : String? = nil
        var isOwner: String? = nil
        var isLike: String? = nil
        var isClearImage: String? = nil
        var isFeeds : String? = nil
        var lat : String? = nil
        var lng : String? = nil
        var width : String? = nil
        var height : String? = nil
        var shareCoun : String? = nil
        var avatarId : String? = nil
        var bio : String? = nil
        var lastUserNameLike : String? = nil
        var viewsCount : Int? = nil
        var isFollowing : Bool? = nil
        var execludedID : Int? = nil
        var reactionRecords : String? = nil
     }
    
    // MARK: - CommentActionRecordPayload
    struct CommentActionRecordPayload: Encodable {
        let createdBy: String
        let userActionRecord: CommentActionRecord
        var languageId: Int?
    }
    
    // MARK: - CommentActionRecord
    struct CommentActionRecord: Encodable, ActionSendable{
        var objectID: Int?
        var targetObjectID: Int?
        var id : Int?
        var actionID: Int?
        var actionContent: String
        var message: String
        var userName : String?
    }
    
    struct InvitePlayersPayload: Encodable{
        let createdBy: String
        let userGroupRecord: UserGroupRecord
    }

    struct UserGroupRecord: Encodable{
        let groupID: Int
        let userIDList: [Int]
    }
    
    struct ListRoomPayload: Encodable, Paginatable{
        var roomRecord: RoomRecord?
        var isDesc: Bool?
        var pageSize: Int?
        var pageIndex: Int?
        var createdBy: Int?
        var languageId: Int?
//        var isMyRoom: Bool?
    }
    
    struct RoomRecord: Encodable {
        var roomType: Int
        var topicId: Int?
        var isMyRoom: Bool?
    }
    
    struct DeleteRoomPayLoad: Encodable {
        var createdBy: Int?
        var roomRecord: DeleteRoomRecord?
    }
    
    struct DeleteRoomRecord: Encodable {
        var id: Int
    }
    
    struct Player: Encodable {
        var playerId: Int
    }
    
    struct PlayerID: Encodable {
        var player_id: Int
    }
    
}


enum SearchPayloads{
    struct Users: Encodable, Paginatable{
        var createdBy: Int?
        var userRecord: Payloads.UserRecord
        var pageSize: Int? = nil
        var pageIndex: Int? = nil
    }
    
    struct Tags: Encodable, Paginatable{
        var createdBy: Int?
        var postRecord: Payloads.PostRecord
        var pageSize: Int? = nil
        var pageIndex: Int? = nil
    }
}
