//
//  ServerResponse.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 13/03/2023.
//

import Foundation

protocol Receivable{
    var success: Bool { get }
    var statusCode: Int { get }
    var message: String? { get }
    var totalCount: Int? { get }
}

struct ServerResponse<T: Codable>: Decodable{
    let status: Int
    let message: String
    let data: T?
}

struct ServerResponse2<T: Codable>: Decodable{
    let status: Bool
    let message: String
    let data: T?
}

struct ServerResponse3<T: Codable>: Decodable{
    let status: Bool
    let message: String
    let data: NastedData<T>
    let total: Int?
}

struct NastedData<T: Codable>: Decodable{
    let data: T?
}

struct Response: Decodable{
    let status: Int?
    let success: Bool?
    let message: String?
}

struct Response2: Codable{
    let status: Bool?
    let success: Bool?
    let message: String?
}

enum UserResponses{
    struct UserRecordsResponse<T: Codable>: Decodable, Receivable{
        let userRecords: T?
        let success: Bool
        let statusCode: Int
        let message: String?
        let totalCount: Int?
    }
    
    struct UserChatRecordsResponse: Decodable, Receivable{
        let userChatRecords: [UserChatRecord]?
        let success: Bool
        let statusCode: Int
        let message: String?
        let totalCount: Int?
    }

    struct UserActionsResponse<T: Codable>: Codable, Receivable{
        let userActionRecord: T?
        let userActionRecords: T?
        let message: String?
        let success: Bool
        let statusCode: Int
        let totalCount: Int?
        let postRecord: PostRecord?
        var reactionRecord : [ReactionRecord]?
    }
}

enum PostResponses{
    struct PostRecordsResponse<T: Codable>: Decodable, Receivable{
        let postRecords: T?
        let success: Bool
        let statusCode: Int
        let message: String?
        let totalCount: Int?
    }
}

// MARK: - UserActionRecord
struct ActionRecord: Codable {
    var id, userID, objectID, targetObjectID: Int?
    var createdBy: Int?
    var creationDate, creationDateStr: String?
    var isDeleted: Bool?
    var modificationDate , modificationDateStr: String?
    var modifiedBy: String?
    var actionID: Int?
    var actionContent, message, targetUserName, targetUserImage , userName : String?
    var createdByName, createdByImage: String?
    var isOwner: Bool?
    var targetFrameURL, targetFrameType, createdFrameType, createdFrameURL: String?
    var isLike: Bool = false
    var isOpened : Bool = false
    var isFollow : Bool = false
    var hasReply : Bool?
    var reactionid : Int?
    var reactionImg : String?
    var ReplyActionRecord : [ActionRecord]?
    var reactionRecords : [ReactionRecord]?
    
    var commentStatus: CommentStatus = .success
    
    enum CodingKeys: String, CodingKey {
        case id, userID, objectID, targetObjectID, createdBy, creationDate, creationDateStr, isDeleted, modificationDateStr, modifiedBy, actionID, actionContent, message, targetUserName, userName , targetUserImage, createdByName, createdByImage, isOwner , reactionid , reactionImg , ReplyActionRecord , hasReply , reactionRecords
        case targetFrameURL
        case targetFrameType, createdFrameType
        case createdFrameURL
        case modificationDate
    }
    
    enum CommentStatus{
        case sending
        case success
    }
}

//struct ReplyActionRecord: Codable {
//    var id, userID, objectID, targetObjectID: Int?
//    var createdBy: Int?
//    var creationDate, creationDateStr: String?
//    var isDeleted: Bool?
//    var modificationDateStr: String?
//    var modifiedBy: String?
//    var actionID: Int?
//    var actionContent, message, targetUserName, targetUserImage: String?
//    var createdByName, createdByImage: String?
//    var isOwner: Bool?
//    var targetFrameURL, targetFrameType, createdFrameType, createdFrameURL: String?
//    var isLike: Bool = false
//    var isOpened : Bool = false
//    var reactionid : Int?
//    var reactionImg : String?
//
//    var commentStatus: CommentStatus = .success
//
//    enum CodingKeys: String, CodingKey {
//        case id, userID, objectID, targetObjectID, createdBy, creationDate, creationDateStr, isDeleted, modificationDateStr, modifiedBy, actionID, actionContent, message, targetUserName, targetUserImage, createdByName, createdByImage, isOwner , reactionid , reactionImg
//        case targetFrameURL
//        case targetFrameType, createdFrameType
//        case createdFrameURL
//    }
//
//    enum CommentStatus{
//        case sending
//        case success
//    }
//}


struct EmojisList : Codable {
    let emojiRecords : [EmojiRecords]?
    let message : String?
    let success : Bool?
    let statusCode : Int?
    let totalCount : Int?
    enum CodingKeys: String, CodingKey {
        case emojiRecords = "emojiRecords"
        case message = "message"
        case success = "success"
        case statusCode = "statusCode"
        case totalCount = "totalCount"
    }
}
struct EmojiRecords : Codable {
    let id : Int?
    let createdBy : String?
    let creationDate : String?
    let isDeleted : Int?
    let modificationDate : String?
    let modifiedBy : String?
    let nameAr : String?
    let nameEn : String?
    let imageUrl : String?
    let mediaType : Int?
    enum CodingKeys: String, CodingKey {
        case id = "id"
        case createdBy = "createdBy"
        case creationDate = "creationDate"
        case isDeleted = "isDeleted"
        case modificationDate = "modificationDate"
        case modifiedBy = "modifiedBy"
        case nameAr = "nameAr"
        case nameEn = "nameEn"
        case imageUrl = "imageUrl"
        case mediaType = "mediaType"
    }
}
