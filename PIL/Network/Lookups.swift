//
//  Lookups.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/06/2023.
//

import Foundation

enum Lookups{
    enum Objects: Int{
        case user = 1
        case device = 2
        case room = 3
        case group = 4
        case post = 5
        case activity = 6
        case comment = 7
        case chat = 8
        case gift = 9
        case game = 10
        case voucher = 11
        case token = 12
        case api = 13
        case voiceCall = 14
        case replyComment = 15
    }
    
    enum Actions: Int{
        case like = 1
        case follow = 2
        case comment = 3
        case report = 5
        case favourite = 6
        case block = 7
        case share = 8
        case view = 9
        case message = 10
        case newPost = 11
        case social = 12
        case gift = 13
        case level = 14
        case winTournment = 15
        case voiceCall = 16
        case VoiceCallGroup = 17

        
        var actionTitle: String{
            switch self{
            case .like: return "Likes".localized
            case .comment: return "Comments".localized
            case .share: return "Shares".localized
            default: return ""
            }
        }
    }
    
    enum MessageTypes: Int{
        case text = 1
        case image = 2
        case video = 3
        case attachment = 4
        case audio = 5
        case emoji = 6
    }
}
