//
//  ChallengesModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/6/21.
//

import Foundation
// MARK: - ChallengesModel
struct AllChallengesModel: Codable {
    var status: Bool?
    var message: String?
    var data: [ChallengesData]?
}

// MARK: - Datum
struct ChallengesData: Codable {
    var id: Int?
    var title, datumDescription, image, fees: String?
    var prize: String?
    var providerID: Int?
    var serviceID, serviceName, objectID, rules: String?
    var createdAt, updatedAt: String?
    var provider: Provider?
    var prizes: [Prize]?
    var objectImage:String?
    var objectName:String?
    
    enum CodingKeys: String, CodingKey {
        case id, title ,objectName , objectImage
        case datumDescription = "description"
        case image, fees, prize
        case providerID = "provider_id"
        case serviceID = "serviceId"
        case serviceName
        case objectID = "objectId"
        case rules
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case provider, prizes
    }
}

// MARK: - Prize
struct Prize: Codable {
    var id: Int?
    var from, to, prize, type: String?
    var image: String?
    var challengeID: Int?
    var createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, from, to, prize, type, image
        case challengeID = "challenge_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Provider
struct Provider: Codable {
    var id: Int?
    var name, image, providerDescription, createdAt: String?
    var updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, name, image
        case providerDescription = "description"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}



// MARK: - ChallengeDetailModel
struct ChallengeDetailsModel: Codable {
    var status: Bool?
    var message: String?
    var data: ChallengeDetailsData?
}

// MARK: - DataClass
struct ChallengeDetailsData : Codable {
    var id: Int?
    var title, dataDescription, image, fees: String?
    var prize: String?
    var providerID: Int?
    var serviceID, serviceName, objectID, rules: String?
    var createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, title
        case dataDescription = "description"
        case image, fees, prize
        case providerID = "provider_id"
        case serviceID = "serviceId"
        case serviceName
        case objectID = "objectId"
        case rules
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}
