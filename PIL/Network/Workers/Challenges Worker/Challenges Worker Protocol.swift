//
//  Challenges Worker Protocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/6/21.
//

import Foundation
import Alamofire

protocol ChallengesWorkerProtocols{
    
    
    func getPrizes(compilition : @escaping (Result<AllChallengesModel,AFError>,_ statusCode:Int?)-> Void)

    func getChallenges(compilition : @escaping (Result<AllChallengesModel,AFError>,_ statusCode:Int?)-> Void)

    func getChallengeDetail(challengeID:Int,compilition : @escaping (Result<ChallengeDetailsModel,AFError>,_ statusCode:Int?)-> Void)
}
