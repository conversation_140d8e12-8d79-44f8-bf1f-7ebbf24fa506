//
//  Challenges Worker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/6/21.
//

import Foundation
import Alamofire

class ChallengesWorker:ChallengesWorkerProtocols{

    func getPrizes(compilition: @escaping (Result<AllChallengesModel, AFError>, Int?) -> Void) {
        let url = Requests.url.prizesURL
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    
    func getChallenges(compilition: @escaping (Result<AllChallengesModel, AFError>, Int?) -> Void) {
        let url = Requests.url.challengesURL
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    func getChallengeDetail(challengeID: Int, compilition: @escaping (Result<ChallengeDetailsModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.challengesURL)\(challengeID)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    
    
    
}
