//
//  Filter Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 11/14/21.
//

import Foundation
import Alamofire


protocol FilterWorkerProtocol{
    
    /// parse json file of filters lists
    /// - Parameter compilition: parser compilition
    func get(compilition: (Result<FilterModel,Error>) -> Void)
    
    
    
    /// get all filters for specific type
    /// - Parameters:
    ///   - type: type of filter
    ///   - compilition: parser compilition
    func getAll(type: FilterTypes, compilition: @escaping  (Result<FiltersModel,AFError>,_ statusCde: Int?) -> Void)
}
