//
//  Filter Worker Models.swift
//  PIL
//
//  Created by <PERSON> on 11/14/21.
//

import Foundation

class FilterModel: Codable{
    var filters: [FilterDataModel]?
}

class FilterDataModel: Codable{
    var title: String?
    var type: Int?
    var filterOptions: [FilterDataModelOption]?
}

class FilterDataModelOption: Codable{
    var value: String?
    var selected: Bool?
}



import Foundation

// MARK: - FilterModel
class FiltersModel: Codable {
    let statusCode: Int?
    let status: Bool?
    let message: String?
    let data: [FilterTypeDataModel]?
}

// MARK: - Datum
class FilterTypeDataModel: Codable {
    let id: String?
    let filters: [Filter]?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case filters
    }
}

// MARK: - Filter
class Filter: Codable {
    let id, filterDescription: String?
    let extraInfo: String?
    let name: String?
    let type: String?
    let v: Int?
    var selected: Bool?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case filterDescription = "description"
        case extraInfo, name, type, selected
        case v = "__v"
    }
}
