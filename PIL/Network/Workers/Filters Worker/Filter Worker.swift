//
//  Filter Worker.swift
//  PIL
//
//  Created by <PERSON> on 11/14/21.
//

import Foundation
import Alamofire

class FilterWorker: FilterWorkerProtocol{
    
    func get(compilition: (Result<FilterModel, Error>) -> Void) {
        if let path = Bundle.main.path(forResource: "FIlterJsonResponse", ofType: "json") {
            do {
                  let fileData = try Data(contentsOf: URL(fileURLWithPath: path), options: .mappedIfSafe)
                  let jsonResult = try JSONDecoder().decode(FilterModel.self, from: fileData)
                compilition(.success(jsonResult))
                
            } catch (let error) {
                   // handle error
                debugPrint(error)
                compilition(.failure(error))
            }
        }
    }
    
    func getAll(type: FilterTypes, compilition: @escaping (Result<FiltersModel, AFError>,_ statusCde: Int?) -> Void) {
        let apiURL = "\(Requests.url.filter)?type=\(type.APItype)"
        Requests.API().performRequest(url: apiURL, method: .get, headersType: .token, completion: compilition)
    }
}
