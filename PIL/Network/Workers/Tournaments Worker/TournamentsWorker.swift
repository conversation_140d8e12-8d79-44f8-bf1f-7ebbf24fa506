//
//  TournamentsWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 20/03/2023.
//

import Foundation
import Alamofire

typealias TournamentsInputResponseCompletion = (Result<ServerResponse<TournamentsInputs>, AFError>, _ statusCode: Int?) -> Void
typealias TournamentsListCompletion = (Result<ServerResponse<Tournaments>, AFError>, _ statusCode: Int?) -> Void
typealias ResponseCompletion = (Result<Response, AFError>,_ statusCode: Int?) -> Void
typealias JoinedTournamentCompletion = (Result<ServerResponse<Tournament>, AFError>, _ statusCode: Int?) -> Void
typealias TournamentCompletion = (Result<ServerResponse<Tournament>, AFError>, _ statusCode: Int?) -> Void
typealias JoinCompletion = (Result<ServerResponse<ValidateContestDataModel>, AFError>, _ statusCode: Int?) -> Void

protocol TournamentsWorkable: AnyObject{
    /// get new tournaments inputs
    /// - Parameters:
    ///   - completion: tournaments input response completion
    func getInputs(_ completion: @escaping TournamentsInputResponseCompletion)
    /// create new private tournament
    /// - Parameters:
    ///   - playerId: string value
    ///   - body: tournament creation model
    ///   - completion: profile competion
    func createPrivateTournament(playerID: String, body: TournamentCreationModel,_ completion: @escaping TournamentCompletion)
    /// delete
    /// - Parameters:
    ///   - contestId: string value
    ///   - completion: response completion
    func delete(_ contestID: String,_ completion: @escaping ResponseCompletion)
    /// leave
    /// - Parameters:
    ///   - contestId: string value
    ///   - completion: response completion
    func leave(_ contestID: String,_ completion: @escaping ResponseCompletion)
    /// join
    /// - Parameters:
    ///   - playerId: string value
    ///   - code: string value
    ///   - completion: join tournament completion
    func join(_ playerID: String,_ code: String,_ completion: @escaping JoinedTournamentCompletion)
    /// fetch public tournaments
    /// - Parameters:
    ///   - completion: tournaments list completion
    func getPublicTournaments(_ completion: @escaping TournamentsListCompletion)
    /// fetch private tournaments
    /// - Parameters:
    ///    - playerId: string value
    ///    - completion: tournaments list completion
    func getPrivateTournaments(playerID: String,_ completion: @escaping TournamentsListCompletion)
    /// fetch tournament details
    /// - Parameters:
    ///   - contestId: int value
    ///   - gameId: int value
    ///   - completion: tournament completion
    func getTournamentDetails(_ contestID: Int,_ gameID: Int, _ completion: @escaping TournamentCompletion)
    /// fetch tournament details
    /// - Parameters:
    ///   - contestId: int value
    ///   - completion: tournament completion
    func getTournamentDetails(_ contestID: Int,_ completion: @escaping TournamentCompletion)
    /// check join contest
    /// - Parameters:
    ///   - Id: string  value
    ///   - completion: join completion
    func canJoinContest(_ id: String,_ compilition: @escaping JoinCompletion)
    /// check join contest
    /// - Parameters:
    ///   - code: string  value
    ///   - completion: join completion
    func canJoinContest(code: String,_ compilition: @escaping JoinCompletion)
    /// fetch communtiy tournaments
    /// - Parameters:
    ///   - plauerId: string  value
    ///   - completion: tournaments list completion
    func getCommunityTournaments(playerID: String,_ completion: @escaping TournamentsListCompletion)
    /// check join contest
    /// - Parameters:
    ///   - contestId: string  value
    ///   - completion: validate contest model  completion
    func canJoinContest(contestID:String , compilition: @escaping ( Result<ValidateContestModel,AFError>, _ StatusCode: Int?) -> Void)
}

final class TournamentsWorker: TournamentsWorkable{
    static let shared = TournamentsWorker()
    
    private init() {}
    
    func canJoinContest(contestID:String , compilition: @escaping ( Result<ValidateContestModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.ValidateContestURL)\(contestID)/canjoin/\(UserModel.shared.get_id())/\(UserModel.shared.get_loginAsGuestType())"
        
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
//        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())
        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , countryCode])

        Requests.API().performRequest(url: endPoint, method: .post, headersType: .token, completion: compilition)
    }
    
    func canJoinContest(_ id: String,_ compilition: @escaping JoinCompletion) {
        let url = URls().JoinContest + id + "/join"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())

        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform ])
        Requests.API().performRequest(url: endPoint, method: .post, RequestModel: PlayerIDModelRequest(playerId: UserModel.shared.get_id()) , headersType: .token, completion: compilition)
    }
    
    func canJoinContest(code: String,_ compilition: @escaping JoinCompletion) {
        let url = URls().canJoinContest + UserModel.shared.get_id() + "/can-join"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())

        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        Requests.API(indicator: .none).performRequest(url: endPoint, method: .post, RequestModel: Code.init(code: code) , headersType: .token, completion: compilition)
    }
    
    func getTournamentDetails(_ contestID: Int,_ gameID: Int, _ completion: @escaping TournamentCompletion) {
        let url = URls.init().contestDetails + "/\(gameID)"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())
        let contestIdPrm = URLQueryItem(name: "contest_id", value: "\(contestID)")
        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode , contestIdPrm])
        Requests.API().performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
    }
    
    func getTournamentDetails(_ contestID: Int,_ completion: @escaping TournamentCompletion){
        let url = URls.init().contest + "\(contestID)"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let version = URLQueryItem(name: "version", value: appVersion)
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let playerQuery = URLQueryItem(name: "playerId", value: (UserModel.shared.get_id()))
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let endPoint =  url.addQueryParameters(queries: [langQuery, playerQuery,platform, version])
        Requests.API().performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
    }
    
    func getPublicTournaments(_ completion: @escaping TournamentsListCompletion) {
        let url = URls.init().allContests + UserModel.shared.get_id()
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())

        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        var indicator: IndicatorType?
        TournamentsCacheService.shared.fetchPublic { tournaments in
            indicator = tournaments.isEmpty ? .none : .none
            Requests.API(indicator: indicator).performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
        }
    }
    
    func getPrivateTournaments(playerID: String,_ completion: @escaping TournamentsListCompletion) {
        let url = URls.init().contestsPrivate + "/\(playerID)/all"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())

        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        var indicator: IndicatorType?
        TournamentsCacheService.shared.fetchPrivate { tournaments in
            indicator = tournaments.isEmpty ? .none : .none
            Requests.API(indicator: indicator).performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
        }
        
    }
    
    func getCommunityTournaments(playerID: String,_ completion: @escaping TournamentsListCompletion) {
        let url = URls.init().contestsCommunity + "/\(playerID)/all"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())

        let endPoint =  url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        var indicator: IndicatorType?
        TournamentsCacheService.shared.fetchCommunity { tournaments in
            indicator = tournaments.isEmpty ? .none : .none
            Requests.API(indicator: indicator).performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
        }
    }
    
    func getInputs(_ completion: @escaping TournamentsInputResponseCompletion) {
        let url = URls.init().contestsPrivate
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    
    func createPrivateTournament(playerID: String, body: TournamentCreationModel,_ completion: @escaping TournamentCompletion) {
        let url = URls.init().contestsPrivate + "/\(playerID)"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let endPoint = url.addQueryParameters(queries: [langQuery, version, platform])
        Requests.API(indicator: .none).performRequest(url: endPoint, method: .post, RequestModel: body, headersType: .token, completion: completion)
    }
    
    func delete(_ contestID: String, _ completion: @escaping ResponseCompletion) {
        let url = URls.init().contestsPrivate + "/\(contestID)" + "/delete"
        Requests.API().performRequest(url: url, method: .delete, RequestModel: PlayerID(player_id: UserModel.shared.get_id()), headersType: .token, completion: completion)
    }
    
    func leave(_ contestID: String, _ completion: @escaping ResponseCompletion) {
        let url = URls.init().contestsPrivate + "/\(contestID)" + "/leave"
        Requests.API().performRequest(url: url, method: .post, RequestModel: PlayerID(player_id: UserModel.shared.get_id()), headersType: .token, completion: completion)
    }
    
    func join(_ playerID: String, _ code: String, _ completion: @escaping JoinedTournamentCompletion) {
        let url = URls.init().contestsPrivate + "/\(playerID)" + "/join-private"
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let endPoint = url.addQueryParameters(queries: [langQuery ,version ,platform ])
        Requests.API().performRequest(url: endPoint, method: .post, RequestModel: Code(code: code), headersType: .token, completion: completion)
    }
}

fileprivate struct Code: Encodable{
    let code: String
}

fileprivate struct PlayerID: Encodable{
    let player_id: String
}
