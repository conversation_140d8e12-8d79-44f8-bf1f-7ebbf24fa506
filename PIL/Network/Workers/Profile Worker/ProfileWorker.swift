//
//  ProfileWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 05/06/2023.
//

import Foundation
import Alamofire

typealias GamesRecords = [GameRecord]
typealias GamesResults = [GameResult]

typealias AllScoresCompletion = (Result<ServerResponse<AllScores>, AFError>,_ statusCode: Int?) -> Void
typealias GameResultCompletion = (Result<ServerResponse<GamesResults>, AFError>,_ statusCode: Int?) -> Void
typealias EachGameRecordCompletion = (Result<ServerResponse<GamesRecords>, AFError>,_ statusCode: Int?) -> Void
typealias ProfileCompletion = (Result<UserResponses.UserRecordsResponse<[UserDataModel]>, AFError>,_ statusCode: Int?) -> Void
typealias LevelsCompletion = (Result<ServerResponse<Levels>, AFError>,_ statusCode: Int?) -> Void
typealias TrophyCompletion = (Result<ServerResponse<[Trophy]>, AFError>,_ statusCode: Int?) -> Void
typealias PopularityCompletion = (Result<UserPopularityResponse, AFError>,_ statusCode: Int?) -> Void
typealias VouchersCompletion = (Result<ServerResponse2<[MyVouchers]>, AFError>,_ statusCode: Int?) -> Void
typealias DailyPointsCompletion = (Result<ServerResponse<DailyPoints>, AFError>,_ statusCode: Int?) -> Void
typealias ReferralsCompletion = (Result<ServerResponse2<PlayersReferrals>, AFError>,_ statusCode: Int?) -> Void

/// A type that can fetch user information
protocol ProfileWorkable: AnyObject{
    /// get user data
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: profile competion
    func getProfile(userId: String?,_ completion: @escaping ProfileCompletion)
    /// get user scores
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: all scores competion
    func getAllScores(userId: String?,_ completion: @escaping AllScoresCompletion)
    /// get user total wins
    /// - Parameters:
    ///   - userId: optional string value
    ///   - page: page number integer
    ///   - completion: game result competion
    func getWins(userId: String?,_ page: Int,_ completion: @escaping GameResultCompletion)
    /// get user total draws
    /// - Parameters:
    ///   - userId: optional string value
    ///   - page: page number integer
    ///   - completion: game result competion
    func getDraws(userId: String?,_ page: Int,_ completion: @escaping GameResultCompletion)
    /// get user total losses
    /// - Parameters:
    ///   - userId: optional string value
    ///   - page: page number integer
    ///   - completion: game result competion
    func getLosses(userId: String?,_ page: Int,_ completion: @escaping GameResultCompletion)
    /// get data for each game
    /// - Parameters:
    ///   - userId: optional string value
    ///   - page: page number integer
    ///   - completion:Each Game Record Completion
    func getEachGameRecord(userId: String?,_ page: Int,_ completion: @escaping EachGameRecordCompletion)
    /// get user levels
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: levels competion
    func getLevels(userId: String?,_ completion: @escaping LevelsCompletion)
    /// get user trophies
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: trophies  competion
    func getTrophy(userId: String?,_ completion: @escaping TrophyCompletion)
    /// get user Popularity
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: Popularity Completion
    func getPopularity(userId: String?,_ completion: @escaping PopularityCompletion)
    /// get user vouchers list
    /// - Parameters:
    ///   - userId: optional string value
    ///   - completion: Vouchers Completion
    func getVouchers(userId: String?,_ completion: @escaping VouchersCompletion)
    /// get  daily points  list
    /// - Parameters:
    ///   - completion: daily points Completion
    func getDailyPoints(_ completion: @escaping DailyPointsCompletion)
    /// get user referrals  list
    /// - Parameters:
    ///   - completion: Referrals Completion
    func getReferrals(_ completion: @escaping ReferralsCompletion)
    /// get SubReferrals
    /// - Parameters:
    ///   - Id: int value
    ///   - completion:Referrals Completion
    func getSubReferrals(id: Int,_ completion: @escaping ReferralsCompletion)
}

class ProfileWorker: ProfileWorkable{
    static let shared = ProfileWorker()
    
    private init(){}
    
    func getSubReferrals(id: Int,_ completion: @escaping ReferralsCompletion) {
        let url = URls.init().subreferral + "\(id)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getReferrals(_ completion: @escaping ReferralsCompletion) {
        let userId = UserModel.shared.get_id()
        let url = URls.init().referral + "\(userId)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getDailyPoints(_ completion: @escaping DailyPointsCompletion) {
        let userId = UserModel.shared.get_id()
        let url = URls.init().dailyPoints + "/\(userId)/get-daily"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getTrophy(userId: String?, _ completion: @escaping TrophyCompletion) {
        var userIdPrm = ""
        if userId.isNilOrEmpty{
            userIdPrm = UserModel.shared.get_id()
        }else{
            userIdPrm = userId!
        }
        let url = URls().trophy + "/\(userIdPrm)"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getVouchers(userId: String?, _ completion: @escaping VouchersCompletion) {
        let url = URls.init().myVouchers
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [.init(name: "playerId", value: userPrm)]), method: .post, headersType: .token, completion: completion)
    }
    
    func getPopularity(userId: String?, _ completion: @escaping PopularityCompletion) {
        let url = URls.init().popularity
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let requestModel = Payloads.UserRecordPayload.init(createdBy: userPrm!, userRecord: .init(id: userPrm!, isPopularity: true))
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .token, completion: completion)
    }
    
    func getLevels(userId: String?, _ completion: @escaping LevelsCompletion) {
        var userIdPrm = ""
        if userId.isNilOrEmpty{
            userIdPrm = UserModel.shared.get_id()
        }else{
            userIdPrm = userId!
        }
        let url = URls().levels + "/\(userIdPrm)"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getProfile(userId: String? = nil,_ completion: @escaping ProfileCompletion) {
        var indicator: IndicatorType? = .regular
        let url = URls.init().profile
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
            ProfilesCacheService.shared.fetch(userPrm ?? "") { profile, Levels, scores, vouchers in
                indicator = .none
            }
        }else{
            userPrm = userId
        }
        let requestModel = Payloads.UserRecordPayload.init(createdBy: UserModel.shared.get_id(), userRecord: .init(id: userPrm!))
        Requests.API(indicator: indicator).performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .token, completion: completion)
    }
    
    func getAllScores(userId: String? = nil,_ completion: @escaping AllScoresCompletion) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let url = URls().gameResultDetails + "/\(userPrm  ?? "")/scores"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getWins(userId: String? = nil,_ page: Int,_ completion: @escaping GameResultCompletion) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let url = URls().gameResultDetails + "/\(userPrm ?? "")/wins" + "?page=\(page)"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getDraws(userId: String? = nil,_ page: Int,_ completion: @escaping GameResultCompletion) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let url = URls().gameResultDetails + "/\(userPrm ?? "")/draws" + "?page=\(page)"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getLosses(userId: String? = nil,_ page: Int,_ completion: @escaping GameResultCompletion) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let url = URls().gameResultDetails + "/\(userPrm ?? "")/losses" + "?page=\(page)"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
    func getEachGameRecord(userId: String? = nil,_ page: Int,_ completion: @escaping EachGameRecordCompletion) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let url = URls().gameResultDetails + "/\(userPrm ?? "")/each-game" + "?page=\(page)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: completion)
    }
    
}
