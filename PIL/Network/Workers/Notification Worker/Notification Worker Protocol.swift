//
//  Notification Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//

import Foundation
import Alamofire

protocol NotificationWorkerProtocol{
    
    /// get Notification not seen count for userid .
    /// - Parameters:
    ///   - userID: id of the user
    ///   - compilition: response complition
    func getUnreadCount(userID: String, compilition: @escaping (Result<NotificationModel,AFError>,_ statusCode: Int?) -> Void)
    
    /// get list of all notifocations
    /// - Parameters:
    ///   - userID: the id of the user
    ///   - compilition: resonse compilition
    func getAll(compilition: @escaping (Result<NotificationModel,AFError>,_ statusCode: Int?) -> Void)
    
    /// get all filtered notifications
    /// - Parameters:
    ///   - model: filter model
    ///   - compilition: response compilition
    func getAllFiltered(model: NotificationRequestModel, compilition: @escaping (Result<NotificationModel,AFError>,_ statusCode: Int?) -> Void)
    
    func getSocialNotifications(_ completion: @escaping (Result<SocialNotifications,AFError>,_ statusCode: Int?) -> Void)
    
    func getNotificationsActionList(_ parameters: [String: String], completion: @escaping (Result<NotificationModel,AFError>,_ statusCode: Int?) -> Void)
}
