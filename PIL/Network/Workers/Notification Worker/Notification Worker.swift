//
//  Notification Worker.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//

import Foundation
import Alamofire

class NotificationWorker: NotificationWorkerProtocol{
    
    
    func getNotificationsActionList(_ parameters: [String : String], completion: @escaping (Result<NotificationModel, Alamofire.AFError>, Int?) -> Void) {
        let apiURL = Requests.url.notificationsURL
        var queries: [URLQueryItem] = []
        for (k,v) in parameters{
            queries.append(.init(name: k, value: v))
        }
        queries.append(.init(name: "key", value: "2"))
        Requests.API().performRequest(url: apiURL.addQueryParameters(queries: queries), method: .get, headersType: .token, completion: completion)
    }
    
    
    func getSocialNotifications(_ completion: @escaping (Result<SocialNotifications,AFError>,_ statusCode: Int?) -> Void) {
        let apiURL = Requests.url.socialNotifications
        let userPrm = URLQueryItem.init(name: "userid", value: UserModel.shared.get_id())
        let langQuery = URLQueryItem(name: "language", value: app_lang == "ar" ? "1":"0")
        Requests.API().performRequest(url: apiURL.addQueryParameters(queries: [userPrm, langQuery]), method: .get, headersType: .token, completion: completion)
    }
    
    func getUnreadCount(userID: String, compilition: @escaping (Result<NotificationModel, AFError>,_ statusCode: Int?) -> Void) {
        let apiURL = "\(Requests.url.notificationsCountURL)/\(userID)"
        Requests.API(indicator: .custom).performRequest(url: apiURL, method: .get, headersType: .none, completion: compilition)
    }
    
    func getAll(compilition: @escaping (Result<NotificationModel,AFError>,_ statusCode: Int?) -> Void) {
        let apiURL = "\(Requests.url.notificationsURL)"
        let userPrm = URLQueryItem.init(name: "userid", value: UserModel.shared.get_id())
        let langQuery = URLQueryItem(name: "lang", value: app_lang == "ar" ? "1":"0")
        let keyPrm = URLQueryItem.init(name: "key", value: "1")
        Requests.API().performRequest(url: apiURL.addQueryParameters(queries: [userPrm, keyPrm, langQuery]), method: .get, headersType: .token, completion: compilition)
    }
    
    func getAllFiltered(model: NotificationRequestModel, compilition: @escaping (Result<NotificationModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.filteredNotificationsURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
}
