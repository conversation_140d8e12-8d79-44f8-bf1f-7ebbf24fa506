//
//  Notification Worker Models.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//

import Foundation


class NotificationModel: Codable, Receivable{
    var success: Bool
    var statusCode: Int
    var message: String?
    var totalCount: Int?
    var notificationRecords: [NotificationDataModel]?
}

class NotificationDataModel: Codable{
    var notificationId: Int64?
    var senderId: String?
    var recipientId: String?
    var title: String?
    var body: String?
    var icon: String?
    var isSeen: Bool?
    var seenDate: String?
    var creationDate: String?
    var isDeleted: Bool?
    var deletionDate: String?
    var targetPath: String?
    var recipientRoleID: String?
    var seenBy: String?
    var deletedBy: String?
    var showDate: String?
    var totokens: [DeviceTokenRecordModel]?
    var notificationIDS: [Int64]?
    var targetObjectID: String?
//    var parentTargetObjectID : String?
    var actionId: Int?
    var objectId: String?
    var objectIdobjectId: Int?
    var mesaageEn: String?
    var mesaageAr: String?
    var titleEn: String?
    var titleAr: String?
}

class DeviceTokenRecordModel: Codable{
    var deviceToken: String?
    var recipientId: String?
}


struct SocialNotifications: Codable {
    let totalFollow, totalLike, totalComment, totalShare: Int?
    let followMsg, commentMsg, shareMsg, likeMsg: String?
    let followIcon , commentIcon , shareIcon , likeIcon : String?
}
