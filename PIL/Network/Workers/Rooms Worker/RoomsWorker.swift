//
//  RoomsWorker.swift
//  PIL
//
//  Created by Diaa on 28/08/2023.
//

import Foundation
import Alamofire

class RoomsWorker{
    static let shared = RoomsWorker()
    
    func CreateRoom(model: CreateRoomRequstModel, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.CreateRoomURL)"
        var urlKey = [String]()
        if !(fileName.isEmpty){
            urlKey = ["RoomRecord.IconFile"]
        }
        var parameters:Parameters = [
            "RoomRecord.RoomType": model.RoomRecord?.RoomType ?? "",
            "RoomRecord.CreatedName":UserModel.shared.get_name() ,
            "RoomRecord.CreatedImageUrl":UserModel.shared.get_image(),
            "CreatedBy": UserModel.shared.get_id()
        ] as [String : Any]
        
        if model.RoomRecord?.TopicId ?? "" != ""{
            parameters["RoomRecord.RoomName"] = model.RoomRecord?.RoomName ?? ""
            parameters["RoomRecord.WelcomeMessage"] = model.RoomRecord?.WelcomeMessage ?? ""
            parameters["RoomRecord.TopicId"] = model.RoomRecord?.TopicId ?? ""
            parameters["RoomRecord.IsMyRoom"] =  "true"
            parameters["RoomRecord.IdList"] =  model.RoomRecord?.IdList ?? []
        }
        
        if model.RoomRecord?.UserID ?? 0 != 0 && model.RoomRecord?.TopicId ?? "" == ""{
            parameters["RoomRecord.UserID"] = "\(model.RoomRecord?.UserID ?? 0)"

           
        }else if  model.RoomRecord?.GroupId ?? 0 != 0  && model.RoomRecord?.TopicId ?? "" == ""{
            parameters["RoomRecord.GroupId"] = "\(model.RoomRecord?.GroupId ?? 0)"
            parameters["RoomRecord.IdList"] =  model.RoomRecord?.IdList ?? []
            
        }
        print("Model add room",parameters)

        
        do{
            Requests.API().performRequest(url: url, method: .post, parameters: parameters, headersType: .token, fileUrlKey: urlKey, files: fileData, filesNames: fileName, mimeTypes: mimeType, completion: compilition)
        }catch{
            // erorr
            compilition(.failure(AFError.multipartEncodingFailed(reason: .inputStreamReadFailed(error: Error.self as! Error))) , 400)
        }
    }
    
    
    
    func CreateMeetAndPlayRoom(model: CreateMeetAndPlayRoomRequstModel, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        var parameters:Parameters = [
            "roomRecord.RoomType": model.roomRecord?.roomType ?? 0,
            "roomRecord.id" : model.roomRecord?.id ?? 0,
            "isDesc" : model.isDesc ?? true,
            "isAll" : model.isAll ?? true,
            "CreatedBy": UserModel.shared.get_id()
        ] as [String : Any]
        

        print("Model add room",parameters)

        
        do{
            Requests.API(indicator: .none).performRequest(url: Requests.url.CreateMeetAndPlayRoomURL , method: .post, RequestModel: model, headersType: .token, completion: compilition)
        }catch{
            // erorr
            compilition(.failure(AFError.multipartEncodingFailed(reason: .inputStreamReadFailed(error: Error.self as! Error))) , 400)
        }
    }
    
    
    
    func listRooms(model: CreateRoomRequstModel, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.ListRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func DeleteRoom(model: DeleteRoomRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.DeleteRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }
    
    func listMembersRoom(model: RoomJoinLeaveRM, compilition: @escaping (Result<ListUserRoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.ListUsersRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }
    
    
    func joinRoom(model: RoomJoinLeaveRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.JoinRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }
    
    func joinMeetAndPlayRoom(model: RoomJoinLeaveRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API(indicator: .none).performRequest(url: Requests.url.JoinRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }
    
    func LeaveRoom(model: RoomJoinLeaveRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API(indicator: .none).performRequest(url: Requests.url.LeaveRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }

    func ListRoomTopic(compilition: @escaping (Result<ListRoomTopicModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.ListRoomsTopicURL, method: .post, headersType: .token, completion: compilition)

    }
    
    func createRoomTournament(player: Int,
                              room: Int,
                              game: Int,
                              completion: @escaping (Result<ServerResponse<Tournament?>, AFError>, Int?) -> Void
    ){
        let url = URls.init().privateRoomTournaments + "/\(player)"
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let endPoint = url.addQueryParameters(queries: [langQuery])
        let body: CreateRoomContest = .init(game_id: game, room_id: room)
        Requests.API().performRequest(url: endPoint, method: .post, RequestModel: body, headersType: .token, completion: completion)
    }
    
    func getPrivateRoomTournamet(_ player: Int,
                                 _ room: Int,
                                 _ completion: @escaping (Result<ServerResponse<Tournament>, AFError>, Int?) -> Void) {
        let url = URls.init().privateRoomTournaments + "/\(room)/get"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let endPoint =  url.addQueryParameters(queries: [langQuery,platform,version])
        let player: Payloads.PlayerID = .init(player_id: player)
        Requests.API().performRequest(url: endPoint, method: .post, RequestModel: player, headersType: .token, completion: completion)
    }
    
    func canJoinContest(_ player: Int,
                        _ room: Int,
                        _ completion: @escaping JoinCompletion) {
        let url = URls().privateRoomTournaments + "/\(player)" + "/join"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")

        let endPoint =  url.addQueryParameters(queries: [langQuery,version , platform])
        Requests.API().performRequest(url: endPoint, method: .post, RequestModel: RoomID(room_id: room) , headersType: .token, completion: completion)
    }

    
    
    
    func groupOnCall(model: DeleteRoomRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.GroupOnCall, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }
    
}
