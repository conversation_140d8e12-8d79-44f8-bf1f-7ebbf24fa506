//
//  RoomModel.swift
//  PIL
//
//  Created by <PERSON>aa on 28/08/2023.
//

import Foundation


// MARK: - Welcome
struct RoomModel: Codable {
    var roomRecords: [RoomModelRecord]?
    var roomRecord: RoomModelRecord?
    var message: String?
    var success: Bool?
    var statusCode, totalCount: Int?
}

// MARK: - RoomRecord
struct RoomModelRecord: Codable {
    var id: Int?
    var nameAr: String?
    var nameEn, descAr, descEn: String?
    var imageURL: String?
    var createdBy: Int?
    var creationDateStr: String?
    var modificationDateStr: String?
    var modifiedBy: Int?
    var roomType , capacity: Int?
    var token, appID, channelName: String?
    var imageFile, roomName: String?
    var viewerCount: Int?
    var isOwner: Bool?
    var createdName, createdImageURL: String?
    var isFollowers, myFollowing, newRooms, commonRooms: Bool?
    var iconURL: String?
    var password, passwordExpiration: String?
    var welcomeMessage: String?
    var micPermission: Bool?
    var passwordExpired: Bool?
    var passwordItemID: Int?
//    var iconFile: String?
    var isBlocked: Bool?
    var removePassword: String?
    var isFavourite: Bool?
    var roomCupTotal: Int?
    var idList: [Int]?
    var memberCount, topicID: Int?
    var topicName: String?
    var isMyRoom:Bool?
    var groupID:Int?
    var groupId:Int?
    
    enum CodingKeys: String, CodingKey {
        case id, nameAr, nameEn, descAr, descEn , isMyRoom , groupId //, iconFile
        case imageURL = "imageUrl"
        case capacity, createdBy, creationDateStr, modificationDateStr, modifiedBy, roomType, token, appID, channelName, imageFile, roomName, viewerCount, isOwner, createdName
        case createdImageURL = "createdImageUrl"
        case isFollowers, myFollowing, newRooms, commonRooms
        case iconURL = "iconUrl"
        case password, passwordExpiration, welcomeMessage, micPermission, passwordExpired, passwordItemID, isBlocked, removePassword, isFavourite, roomCupTotal, idList, memberCount
        case topicID = "topicId"
        case topicName
    }
}



struct ListRoomTopicModel: Codable {
    var emojiRecords: [TopicModel]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount: Int?
}

// MARK: - EmojiRecord
struct TopicModel: Codable {
    var id: Int?
    var creationDate: String?
    var isDeleted: Int?
    var modificationDate, modifiedBy: String?
    var nameAr, nameEn: String?
    var imageURL, mediaType: String?

    enum CodingKeys: String, CodingKey {
        case id, creationDate, isDeleted, modificationDate, modifiedBy, nameAr, nameEn
        case imageURL = "imageUrl"
        case mediaType
    }
}



struct ListUserRoomModel: Codable {
    var roomParicipiantRecords: [RoomParicipiantRecordModel]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount: Int?
}

// MARK: - RoomParicipiantRecord
struct RoomParicipiantRecordModel: Codable {
    var id, peerID: Int?
    var isPeerFilter: Bool?
    var createdBy: Int?
    var creationDateStr: String?
    var isDeleted: Bool?
    var modificationDateStr: String?
    var modifiedBy: String?
    var roomID: Int?
    var createdName: String?
    var createdImageURL: String?
    var blocked: Bool?
    var administrator: String?
    var isFollowers: Bool?
    var isOwner: Bool?
    var name:String?
    var image:String?
    var isMute:Bool?
    var isAdmin:Bool?
    enum CodingKeys: String, CodingKey {
        case id, peerID, isPeerFilter, createdBy, creationDateStr, isDeleted, modificationDateStr, modifiedBy, roomID, createdName , isMute , isAdmin
        case createdImageURL = "createdImageUrl"
        case blocked, administrator, isFollowers, isOwner , name ,image
    }
}


struct CreateRoomContest: Encodable{
    let game_id: Int
    let room_id: Int
}

struct RoomID: Encodable{
    let room_id: Int
}
