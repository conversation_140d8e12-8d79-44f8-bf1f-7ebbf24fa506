//
//  RoomsRequestModel.swift
//  PIL
//
//  Created by <PERSON>aa on 28/08/2023.
//

import Foundation


//MARK: - Meet&PlayRoom
struct CreateMeetAndPlayRoomRequstModel:Codable{
    var createdBy: Int?
    var isAll : Bool?
    var isDesc : Bool?
    var roomRecord : roomRecordModel?
}

struct roomRecordModel : Codable {
    var id : Int?
    var roomType : Int?
}



//MARK: - CREATE ROOM
struct CreateRoomRequstModel:Codable{
    var createdBy: Int?
    var LanguageId:Int?
    var RoomRecord:CreateRoomRoomRecordRM? 
}

enum EnvironmentsTypes : String{
    case production = "production"
    case sandbox = "sandbox"
}

struct CreateRoomRoomRecordRM:Codable{
    var RoomType:String?
    var RoomName:String?
    var TopicId:String?
    var WelcomeMessage:String?
    var IdList:[String]?
    var UserID:Int?
    var id:Int?
    var GroupId:Int?
}



//MARK: - LEAVE ROOM
struct DeleteRoomRM: Encodable {
    var createdBy: Int?
    var roomRecord: RoomRecordRM?
}

// MARK: - RoomRecord
struct RoomRecordRM: Encodable {
    var id: Int?
    var roomRecord:Int?
    var GroupID:Int?
}


struct RoomJoinLeaveRM:Encodable{
    var roomParicipiantRecord: RoomParicipiantRecordRM?
    var createdBy: Int?
    var roomID:Int?
  }

  // MARK: - RoomParicipiantRecord
struct RoomParicipiantRecordRM: Encodable {
    var id: Int?
    var roomID:Int?

}

struct ListRoomParicipiantParamter: Encodable {
    var createdBy: Int?
    var roomParicipiantRecord: RoomParicipiantRecord?
    
}

struct RoomParicipiantRecord: Encodable {
    var roomID: Int?
}


