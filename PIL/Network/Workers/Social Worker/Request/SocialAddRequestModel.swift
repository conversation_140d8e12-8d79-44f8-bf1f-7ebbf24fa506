//
//  SocialAddRequestModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/10/21.
//

import Foundation
// MARK: - ReferralsModel
struct SocialAddRequestModel: Encodable {
    var userActivityRecord: SocialAddUserActivityRecord?
}

// MARK: - UserActivityRecord
struct  SocialAddUserActivityRecord: Encodable {
    var moduleID, activityTypeID, userID , targetObjectId , RemoveActivityTypeId : String?

    enum CodingKeys: String, CodingKey {
        case moduleID = "moduleId"
        case activityTypeID = "activityTypeId"
        case userID = "userId"
        case targetObjectId  , RemoveActivityTypeId
    }
}
 
