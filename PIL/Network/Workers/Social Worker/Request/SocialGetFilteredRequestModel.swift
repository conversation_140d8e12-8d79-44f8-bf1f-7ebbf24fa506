//
//  SocialGetFilteredRequestModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/10/21.
//

import Foundation
// MARK: - ReferralsModel
struct SocialGetFilteredRequestModel: Codable {
    var withDetails: Bool?
    var userActivityRecord: SocialGetFilteredUserActivityRecord?
}

// MARK: - UserActivityRecord
struct  SocialGetFilteredUserActivityRecord: Codable {
    var moduleID, activityTypeID, userID, targetObjectID: String?

    enum CodingKeys: String, CodingKey {
        case moduleID = "moduleId"
        case activityTypeID = "activityTypeId"
        case userID = "userId"
        case targetObjectID = "targetObjectId"
    }
}
