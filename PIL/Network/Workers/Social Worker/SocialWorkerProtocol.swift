//
//  SocialWorkerProtocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/10/21.
//

import Foundation
import Alamofire

typealias UsersListCompletion = (Result<UserResponses.UserRecordsResponse<[Profile]>, AFError>,_ statusCode: Int?) -> Void
typealias ListEmojies = (Result<EmojisList, AFError>,_ statusCode: Int?) -> Void
typealias UserActionCompletion = (Result<UserResponses.UserActionsResponse<ActionRecord>, AFError>,_ statusCode: Int?) -> Void
typealias SuccessCompletion = (Result<Response, AFError>,_ statusCode: Int?) -> Void
typealias PostRecordsResponseCompletion = (Result<PostResponses.PostRecordsResponse<[PostRecord]>, AFError>,_ statusCode: Int?) -> Void
typealias PostsListCompletion = (Result<PostsResponse, AFError>,_ statusCode: Int?) -> Void
typealias ListUserActionCompletion = (Result<UserResponses.UserActionsResponse<[ActionRecord]>, AFError>,_ statusCode: Int?) -> Void
typealias ListReactsCompletion = (Result<ReactsModel, AFError>,_ statusCode: Int?) -> Void
// Room
typealias ListRoomTopicCompletion = (Result<SocialGetListRoomTopicResponse, AFError>, Int?) -> Void
typealias ListRoomCompletion = (Result<SocialListRoomResponse, AFError>, Int?) -> Void
typealias DeleteRoomCompletion = (Result<DeleteRoomResponse, AFError>, Int?) -> Void
typealias ListRoomParicipiantCompletion = (Result<ListRoomParicipiantResponse, AFError>, Int?) -> Void

protocol SocialWorkerProtocol {
    
    /// search users with respect to their types
    /// - Parameters:
    ///   - payload: generic encodable
    ///   - searchType: users list type type
    ///   - compilition: users list completion
    func searchUsers<T: Encodable>(_ payload: T,_ type: SearchType,_ completion: @escaping UsersListCompletion)
    
    /// fetch posts
    /// - Parameters:
    ///   - payload: post payload
    ///   - compilition: posts list completion
    func getPostsList(_ payload: Payloads.PostsListPayload,_ completion: @escaping PostsListCompletion)
    
    /// delete post
    /// - Parameters:
    ///   - payload: post payload
    ///   - compilition: posts records response completion
    func deletePost(_ payload: Payloads.PostsListPayload,_ completion: @escaping PostRecordsResponseCompletion)
    
    /// add new post
    /// - Parameters:
    ///   - parameters: alamofire parameters
    ///   - image: uiimage
    ///   - video: url
    ///   - compilition: posts records response completion
    func addPost(_ parameters: Parameters?,_ image: UIImage?,_ video: URL?,_ completion: @escaping PostRecordsResponseCompletion)
    
    /// edit post
    /// - Parameters:
    ///   - parameters: alamofire parameters
    ///   - image: uiimage
    ///   - video: url
    ///   - compilition: posts records response completion
    func editPost(_ parameters: Parameters?,_ image: UIImage?,_ video: URL?,_ completion: @escaping PostRecordsResponseCompletion)
    
    /// add any user action such as like, comment, block, ...
    /// - Parameters:
    ///   - payload: generic encodable object descripes its action
    ///   - compilition: user action completion
    func addUserAction<T: Encodable>(_ payload: T,_ completion: @escaping UserActionCompletion)
    
    /// delete any performed user action
    /// - Parameters:
    ///   - payload: generic encodable object descripes its action
    ///   - compilition: user action completion
    func deleteUserAction<T: Encodable>(_ payload: T,_ completion: @escaping UserActionCompletion)
    
    /// fetch user list of action
    /// - Parameters:
    ///   - payload: generic encodable object descripes its action
    ///   - compilition: list user action completion
    func listUserAction<T: Encodable>(_ payload: T,_ completion: @escaping ListUserActionCompletion)
    
    /// fetch list of emojis
    /// - Parameters:
    ///   - payload: generic encodable object descripes its action
    ///   - compilition: list emojis completion
    func ListEmojis<T: Encodable>(_ payload: T,_ completion: @escaping ListEmojies)
    
    /// fetch list of followers
    /// - Parameters:
    ///   - userId: string descriping user id
    ///   - compilition: user list completion
    func getFollowers(_ userId: String,_ completion: @escaping UsersListCompletion)
    
    /// fetch list of followings
    /// - Parameters:
    ///   - userId: string descriping user id
    ///   - compilition: user list completion
    func getFollowings(_ userId: String,_ completion: @escaping UsersListCompletion)
    
    /// fetch list of recommendations
    /// - Parameters:
    ///   - userId: string descriping user id
    ///   - compilition: user list completion
    func getRecommendations(_ userId: String,_ completion: @escaping UsersListCompletion)
    
    /// add social
    /// - Parameters:
    ///   - model: moduleID (for player ), activityTypeID(for follow or block), userID , targetObjectId(id another user or game)
    ///   - compilition:
    func addSocail(model:SocialAddRequestModel, compilition: @escaping (Result<SocialAddModel,AFError>,_ statusCde: Int?) -> Void)
    
    
    
    
    /// get block , followers
    /// - Parameters:
    ///   - model: moduleID (for player ), activityTypeID(for follow or block), userID , targetObjectId(id another user or game)
    ///   - compilition: 
    func getFiltered(model:SocialGetFilteredRequestModel , compilition: @escaping (Result<SocialGetFilteredModel,AFError>,_ statusCode:Int?)->Void)

    
    
    
    /// get user filtered
    /// - Parameters:
    ///   - parameters: users ids
    ///   - compilition:
    func GetUsersFiltered(parameters:Parameters, compilition: @escaping (Result<GetUsersFilteredModel,AFError>,_ statusCode:Int?)->Void)
    
    func getListRoomTopic(_ compilition: @escaping ListRoomTopicCompletion)
    func getRoomList(_ payLoad: Payloads.ListRoomPayload, _ compilition: @escaping ListRoomCompletion)
}
