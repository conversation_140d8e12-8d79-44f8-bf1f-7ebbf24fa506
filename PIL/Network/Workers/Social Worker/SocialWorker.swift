//
//  SocialWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/10/21.
//

import Foundation
import Alamofire
import FirebaseDynamicLinks

class SocialWorker:SocialWorkerProtocol{
    
    static let shared = SocialWorker()
    
    func searchUsers<T>(_ payload: T,_ type: SearchType,_ completion: @escaping UsersListCompletion) where T : Encodable {
        Requests.API(indicator: .none).performRequest(url: type.endpoint, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func deletePost(_ payload: Payloads.PostsListPayload,_ completion: @escaping PostRecordsResponseCompletion) {
        let url = URls().deletePost
        Requests.API().performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func getPostsList(_ payload: Payloads.PostsListPayload,_ completion: @escaping PostsListCompletion) {
        let url = URls().listPosts
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func addPost(_ parameters: Parameters?,_ image: UIImage?,_ video: URL?,_ completion: @escaping PostRecordsResponseCompletion) {
        var mediaFileDic: [String: Data] = [:]
        var ext: String = ""
        if let imageData = image?.jpegData(compressionQuality: 0.5) {
            mediaFileDic["PostRecord.ImageFile"] = imageData
            ext = ".jpg"
        }else if let videoURL = video{
            do {
                let videoData = try Data(contentsOf: videoURL, options: .mappedIfSafe)
                mediaFileDic["PostRecord.ImageFile"] = videoData
                ext = ".mp4"
            } catch {}
        }
        print("[[[[[\(Array(mediaFileDic.keys)) , \(Array(mediaFileDic.values)) , \([UUID().uuidString + ext])")
        let url = URls().addPost
        Requests.API().performRequest(url: url, method: .post, parameters: parameters , headersType: .token, fileUrlKey: Array(mediaFileDic.keys), files: Array(mediaFileDic.values), filesNames: [UUID().uuidString + ext], mimeTypes: [], completion: completion)
    }
    
    func editPost(_ parameters: Parameters?,_ image: UIImage?,_ video: URL?,_ completion: @escaping PostRecordsResponseCompletion) {
        var mediaFileDic: [String: Data] = [:]
        var ext: String = ""
        if let imageData = image?.jpegData(compressionQuality: 0.5) {
            mediaFileDic["PostRecord.ImageFile"] = imageData
            ext = ".jpg"
        }else if let videoURL = video{
            do {
                let videoData = try Data(contentsOf: videoURL, options: .mappedIfSafe)
                mediaFileDic["PostRecord.ImageFile"] = videoData
                ext = ".mp4"
            } catch {}
        }
        let url = URls().editPost
        Requests.API().performRequest(url: url, method: .post, parameters: parameters, headersType: .token, fileUrlKey: Array(mediaFileDic.keys), files: Array(mediaFileDic.values), filesNames: [UUID().uuidString + ext], mimeTypes: [], completion: completion)
    }
    
    func addUserAction<T: Encodable>(_ payload: T, _ completion: @escaping UserActionCompletion) {
        let url = URls.init().userAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func editUserAction<T: Encodable>(_ payload: T, _ completion: @escaping UserActionCompletion) {
        let url = URls.init().editAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func deleteUserAction<T>(_ payload: T, _ completion: @escaping UserActionCompletion) where T : Encodable {
        let url = URls.init().deleteUserAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func listUserAction<T: Encodable>(_ payload: T, _ completion: @escaping ListUserActionCompletion) {
        let url = URls.init().userListAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func listReacts<T: Encodable>(_ payload: T, _ completion: @escaping ListReactsCompletion) {
        let url = URls.init().listReacts
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func ListEmojis<T>(_ payload: T, _ completion: @escaping ListEmojies) where T : Encodable {
        let url = URls.init().ListEmojies
        Requests.API(indicator: .none).performRequest(url: url, method: .post , RequestModel: payload , headersType: .token , completion: completion)
    }
    
    func getFollowers(_ userId: String,_ completion: @escaping UsersListCompletion){
        let url = URls.init().followers
        let requestModel = Payloads.UserRecordPayload.init(createdBy: userId, userRecord: .init(id: userId))
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .token, completion: completion)
    }
    
    func getFollowings(_ userId: String,_ completion: @escaping UsersListCompletion){
        let url = URls.init().following
        let requestModel = Payloads.UserRecordPayload.init(createdBy: userId, userRecord: .init(id: userId))
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .token, completion: completion)
    }
    
    func getRecommendations(_ userId: String, _ completion: @escaping UsersListCompletion) {
        let url = URls.init().listRecommendedUsers
        let requestModel = Payloads.UserRecordPayload.init(createdBy: userId, userRecord: .init(id: userId))
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .token, completion: completion)
    }

    func addSocail(model: SocialAddRequestModel, compilition: @escaping (Result<SocialAddModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url:  Requests.url.AddSocialURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getFiltered(model: SocialGetFilteredRequestModel, compilition: @escaping (Result<SocialGetFilteredModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.GetFilteredSocialURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
        
    
    func GetUsersFiltered(parameters: Parameters, compilition: @escaping (Result<GetUsersFilteredModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.getManyPlayersURL, method: .post, parameters: parameters, headersType: .token, fileUrlKey: [], files: [], filesNames: [], mimeTypes: [], completion: compilition)
    }
    
    
    func sharePost(with id: Int){
        
        var components = URLComponents()
        components.scheme = "https"
        components.host = "playitleague.page.link"
        components.path = "/K513"
        let postIDQueryItem = URLQueryItem(name: "postId", value: "\(id)")
        components.queryItems = [postIDQueryItem]
        guard let linkParameters = components.url else {return}
        guard let shareLink = DynamicLinkComponents.init(link: linkParameters, domainURIPrefix: "https://playitleague.page.link") else {
            return
        }
        
        if let myBundleID = Bundle.main.bundleIdentifier{
            shareLink.iOSParameters = DynamicLinkIOSParameters(bundleID: myBundleID)
        }
        
        shareLink.iOSParameters?.appStoreID = "1555945981"
        shareLink.iOSParameters = DynamicLinkIOSParameters(bundleID: "com.gt.playitleague")
        shareLink.androidParameters = DynamicLinkAndroidParameters(packageName: "com.gt.pi")
        
        shareLink.socialMetaTagParameters = DynamicLinkSocialMetaTagParameters()
        shareLink.socialMetaTagParameters?.descriptionText = "Share Post".localized

        guard shareLink.url != nil else { return }
        
        shareLink.shorten { (url, warnings, error) in
            if let error = error{
                print("Error now",error.localizedDescription)
                return
            }
            
            if let warnings = warnings{
                for warning in warnings{
                    print("warnings","\(warning)")
                }
            }
            
            guard let url = url else { return }
            
            let firstActivityItem = "Share Post".localized
            let activityViewController = UIActivityViewController(activityItems: [firstActivityItem , url ], applicationActivities: nil)
            activityViewController.popoverPresentationController?.sourceView = UIApplication.topViewController?.view
            activityViewController.modalPresentationStyle = UIModalPresentationStyle.popover
            activityViewController.excludedActivityTypes = [
                UIActivity.ActivityType.postToWeibo,
                UIActivity.ActivityType.print,
                UIActivity.ActivityType.assignToContact,
                UIActivity.ActivityType.saveToCameraRoll,
                UIActivity.ActivityType.addToReadingList,
                UIActivity.ActivityType.postToFlickr,
                UIActivity.ActivityType.postToVimeo,
                UIActivity.ActivityType.postToTencentWeibo,
                UIActivity.ActivityType.postToFacebook
            ]
            activityViewController.isModalInPresentation = true
            UIApplication.topViewController?.present(activityViewController, animated: true, completion: nil)
        }
        
    }
    
    func getListRoomTopic(_ compilition: @escaping ListRoomTopicCompletion) {
                Requests.API(indicator: .none).performRequest(url: Requests.url.listRoomTopic, method: .post, headersType: .token, completion: compilition)
    }

    
    func getRoomList(_ payLoad: Payloads.ListRoomPayload, _ compilition: @escaping ListRoomCompletion) {
            Requests.API(indicator: .none).performRequest(url: Requests.url.listRoom, method: .post, RequestModel: payLoad, headersType: .token, completion: compilition)

    }
   
    func deleteRoom(_ payload: Payloads.DeleteRoomPayLoad,_ completion: @escaping DeleteRoomCompletion) {
        let url = URls().deleteRoom
        Requests.API().performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func loadListRoomParicipiant(_ payload: ListRoomParicipiantParamter, completion: @escaping ListRoomParicipiantCompletion) {
        let url = URls().listRoomParicipiant
        Requests.API().performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func LeaveRoom(model: RoomJoinLeaveRM, compilition: @escaping (Result<RoomModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.LeaveRoomURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }

}
