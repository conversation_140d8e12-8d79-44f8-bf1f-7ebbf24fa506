//
//  SocialListRoomModel.swift
//  PIL
//
//  Created by admin on 24/09/2023.
//

import Foundation

struct SocialListRoomResponse: Codable {
    
    let roomRecords: [SocialRoomRecord]?
//    let roomRecord: JSONNull?
    let message: String?
    let success: Bool?
    let statusCode, totalCount: Int?
}

struct SocialRoomRecord: Codable {
    
    let id: Int?
    let nameAr, nameEn, descAr, descEn: String?
    let imageURL: String?
    let capacity, createdBy: Int?
    let userID: Int?
    let creationDateStr: String?
    let isDeleted: Bool?
    let modificationDateStr: String?
    let modifiedBy: Int?
    let roomType: Int?
    let token, appID, channelName: String?
    let imageFile: String?
    let roomName: String?
    let viewerCount: Int?
    let isOwner: Bool?
    let createdName: String?
    let createdImageURL: String?
    let isFollowers, myFollowing, newRooms, commonRooms: Bool?
    let iconURL: String?
    let password, passwordExpiration, welcomeMessage: String?
    let micPermission, passwordExpired: Bool?
    let passwordItemID: Int?
    let iconFile: String?
    let isBlocked: Bool?
    let removePassword: String?
    let isFavourite: Bool?
    let roomCupTotal: Int?
    let idList: String?
    let memberCount, topicID: Int?
    let topicName: String?

    enum CodingKeys: String, CodingKey {
        case id, nameAr, nameEn, descAr, descEn
        case imageURL = "imageUrl"
        case capacity, createdBy, userID, creationDateStr, isDeleted, modificationDateStr, modifiedBy, roomType, token, appID, channelName, imageFile, roomName, viewerCount, isOwner, createdName
        case createdImageURL = "createdImageUrl"
        case isFollowers, myFollowing, newRooms, commonRooms
        case iconURL = "iconUrl"
        case password, passwordExpiration, welcomeMessage, micPermission, passwordExpired, passwordItemID, iconFile, isBlocked, removePassword, isFavourite, roomCupTotal, idList, memberCount
        case topicID = "topicId"
        case topicName
    }
}
