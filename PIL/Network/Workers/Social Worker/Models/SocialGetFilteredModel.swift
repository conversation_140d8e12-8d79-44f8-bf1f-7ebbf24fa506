//
//  SocialGetFilteredModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/10/21.
//

import Foundation

// MARK: - ReferralsModel
class SocialGetFilteredModel: Codable {
    var userActivityRecords: [GetFilteredUser]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount, pageCount: Int?
}

// MARK: - UserActivityRecord
class GetFilteredUser : Codable {
    var id: Int?
    var moduleID, activityTypeID, userID: String?
    var responseObj, requestObj, serviceURL, message: String?
    var mediaURL: String?
    var isDevared: Bool?
    var creationDate, createdBy, modifiedBy, modificationDate: String?
    var targetObjectID: String?

    enum CodingKeys: String, CodingKey {
        case id
        case moduleID = "moduleId"
        case activityTypeID = "activityTypeId"
        case userID = "userId"
        case responseObj, requestObj
        case serviceURL = "serviceUrl"
        case message
        case mediaURL = "mediaUrl"
        case isDevared, creationDate, createdBy, modifiedBy, modificationDate
        case targetObjectID = "targetObjectId"
    }
 }
