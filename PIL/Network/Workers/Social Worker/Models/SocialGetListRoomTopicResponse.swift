//
//  SocialGetListRoomTopicModel.swift
//  PIL
//
//  Created by admin on 24/09/2023.
//

import Foundation

struct SocialGetListRoomTopicResponse: Codable {
    
    let emojiRecords: [RoomTopicEmojiRecords]?
    let message: String?
    let success: Bool?
    let statusCode, totalCount: Int?

}

struct RoomTopicEmojiRecords: Codable {
    
    let id: Int?
    let createdBy: String?
    let creationDate: String?
    let isDeleted: Int?
    let modificationDate, modifiedBy: String?
    let nameAr, nameEn: String?
    let imageURL, mediaType: String?

    enum CodingKeys: String, CodingKey {
        case id, createdBy, creationDate, isDeleted, modificationDate, modifiedBy, nameAr, nameEn
        case imageURL = "imageUrl"
        case mediaType
    }

}
