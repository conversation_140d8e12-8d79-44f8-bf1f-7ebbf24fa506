//
//  SearchType.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 19/06/2023.
//

import Foundation

enum SearchType{
    case users
    case followers
    case following
    
    var endpoint: String{
        switch self {
        case .users:
            return URls().listUsers
        case .followers:
            return URls().followers
        case .following:
            return URls().following
        }
    }
}
