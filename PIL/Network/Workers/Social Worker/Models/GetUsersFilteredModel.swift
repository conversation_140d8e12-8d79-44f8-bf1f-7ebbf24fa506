//
//  GetUsersFiltered.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/11/21.
//

import Foundation


// MARK: - ReferralsModel
class GetUsersFilteredModel: Codable {
    var status: Bool?
    var message: String?
    var data: [GetUsersDats]?
 
}

// MARK: - Datum
class GetUsersDats: Codable {
    var datumID:Int?
    var createdAt, creationDate: String?
    var bio: String?
    var email, name, gender: String?
    var updatedAt: String?
    var password, birthdate, pilID, username: String?
    var id, phone: String?
    var image:String?
    var modificationDate:String?
    
    enum CodingKeys: String, CodingKey {
        case createdAt = "created_at"
        case creationDate
        case bio, email, name, gender
        case updatedAt = "updated_at"
        case password, birthdate
        case pilID = "pil_id"
        case username
        case datumID = "id"
        case id = "ID"
        case phone , image
    }

}
