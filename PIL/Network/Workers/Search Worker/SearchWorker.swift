//
//  SearchWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 06/07/2023.
//

import Foundation
import Alamofire

typealias GamesSearchCompletion = (Result<ServerResponse<[Game]>, AFError>,_ statusCode: Int?) -> Void
typealias TournamentsSearchCompletion = (Result<ServerResponse<Tournaments>, AFError>, _ statusCode: Int?) -> Void
typealias StoreSearchCompletion = (Result<ServerResponse2<StoreSearchResult>, AFError>, _ statusCode: Int?) -> Void

protocol SearchWorkable{
    func searchGames(_ query: String,_ completion: @escaping GamesSearchCompletion)
    func searchTournaments(_ query: String,_ completion: @escaping TournamentsSearchCompletion)
    func searchStore(_ query: String,_ completion: @escaping StoreSearchCompletion)
    func searchVouchers(_ query: String, page: String, compilition: @escaping (Result<ServerResponse3<[Voucher]>, A<PERSON><PERSON>rror>,_ statusCode: Int?) -> Void)
}

class SearchWorker: SearchWorkable{
    static let shared = SearchWorker()
    
    private init(){ }
    
    func searchVouchers(_ query: String, page: String, compilition: @escaping (Result<ServerResponse3<[Voucher]>, AFError>, Int?) -> Void) {
        let url = URls.init().vouchers
        let searchPrm = URLQueryItem.init(name: "search", value: query)
        let pagePrm = URLQueryItem.init(name: "page", value: page)
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [pagePrm, searchPrm]), method: .get, headersType: .token, completion: compilition)
    }
    
    func searchGames(_ query: String, _ completion: @escaping GamesSearchCompletion) {
        let url = URls.init().searchGames
        let queryPrm = URLQueryItem.init(name: "search", value: query)
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [queryPrm]), method: .get, headersType: .token, completion: completion)
    }
    
    func searchTournaments(_ query: String, _ completion: @escaping TournamentsSearchCompletion) {
        let url = URls.init().allContests + UserModel.shared.get_id()
        let queryPrm = URLQueryItem.init(name: "search", value: query)
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [queryPrm]), method: .get, headersType: .token, completion: completion)
    }
    
    func searchStore(_ query: String, _ completion: @escaping StoreSearchCompletion) {
        let url = URls.init().searchStore
        let queryPrm = URLQueryItem.init(name: "search", value: query)
        let langQuery = URLQueryItem(name: "lang", value: app_lang)
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [queryPrm, langQuery]), method: .get, headersType: .token, completion: completion)
    }
}
