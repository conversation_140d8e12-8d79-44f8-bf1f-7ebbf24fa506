//
//  Challenges Game Worker.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 22/12/2022.
//

import Foundation
import Alamofire

class ChallengesGameWorker{
    
    
    func getGameDetails(gameID:String , compilition: @escaping (Result<GameIfnoModel, AFError>, Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())
        let url = "\(Requests.url.gameDetailsURL)\(gameID)"
        let apiUrl =   url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        Requests.API().performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
    
    
    func getChallengesGame(gameID:String , compilition: @escaping (Result<ChallengesGamesModel, AFError>, Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let dateTime = URLQueryItem(name: "date_time", value: "\( Date.currentTimeStamp)")
        let url = "\(Requests.url.gameDetailsURL)\(gameID)/contest"
        let apiUrl =   url.addQueryParameters(queries: [langQuery  , version , platform , operatorx , dateTime])
        Requests.API(indicator: .none).performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
    
    func getSingleChallengesGame(contestID:String ,compilition: @escaping ( Result<SingleChallengeGamesModel,AFError>, _ StatusCode: Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let url = "\(Requests.url.ValidateContestURL)\(contestID)"
        let apiUrl =   url.addQueryParameters(queries: [langQuery  , version , platform , operatorx])
        Requests.API().performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
    
    func canJoinContest(contestID:String , compilition: @escaping ( Result<ValidateContestModel,AFError>, _ StatusCode: Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let url = "\(Requests.url.ValidateContestURL)\(contestID)/canjoin/\(UserModel.shared.get_id())/\(UserModel.shared.get_loginAsGuestType())"
        let apiUrl =   url.addQueryParameters(queries: [langQuery  , version , platform , operatorx])
        Requests.API().performRequest(url: apiUrl, method: .post, headersType: .token, completion: compilition)
    }
    
    
    func getLeaderBoard(contestID:String  , compilition: @escaping ( Result<LeaderBoardContestModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.ValidateContestURL)\(contestID)/leaderboard"
        let request = LeaderBoardContestRM(token: UserModel.shared.get_token())
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: request, headersType: .token, completion: compilition)
    }
    
    func getLeaderBoardKnockout(contestID:String  , compilition: @escaping ( Result<LeaderBoardKnockoutModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.ValidateContestURL)\(contestID)/leaderboard"
        let request = LeaderBoardContestRM( playerId: UserModel.shared.get_id())
        Requests.API().performRequest(url: url, method: .post, RequestModel: request, headersType: .token, completion: compilition)
    }
    
    
    func feedBack(model:FeedBackGameRequstModel  , compilition: @escaping ( Result<FeedBackGameModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.FeedBackURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)


    }
    
    
    func cansSendFeedBack(model:FeedBackGameRequstModel   , compilition: @escaping ( Result<CandSendFeedback,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.CanSendFeedbackURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)

    }

    func checkPlayerAds(compilition: @escaping ( Result<CheckPlayerAdsModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.CheckPlayerAdsURL)/\(UserModel.shared.get_id())/\(UserModel.shared.get_loginAsGuestType())"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)

    }
    
    
    func getEventsGame(compilition: @escaping (Result<ChallengesGamesModel, AFError>, Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let url = "\(Requests.url.EventsURL)"
        let apiUrl =   url.addQueryParameters(queries: [langQuery  , version , platform , operatorx])
        Requests.API().performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
}
