//
//  ChallengesGamesModel.swift
//  PIL
//
//  Created by sameh mohammed on 22/12/2022.
//

import Foundation

struct SingleChallengeGamesModel: Codable {
    var status: Int?
    var message: String?
    var data: ChallengesGamesData?
}

// MARK: - challenges
struct ChallengesGamesModel: Codable {
    var status: Int?
    var message: String?
    var data: [ChallengesGamesData]?
}

// MARK: - Datum
class ChallengesGamesData: Codable {
    var id, joinedPlayers, maximumPlayers, maximumAttempt: Int?
    var tournamentRules: String?
    var isContest, isSpecial, isGrand, isTicket: Bool?
    var isActive: Bool?
    var contestType, type, descriptionR: String?
    var skillRange: Int?
    var contestRulesAr, startDateTime, endDateTime: String?
    var name: String?
    var prize, noOfWinners, entryfeeDeposit: Int?
    var logo: String?
    var countryOperators: String?
    var entryfeeBouns, pilMargin: Int?
    var prizesDistribution: [PrizesDistribution]?
    var gameID: Int?
    var createdAt, updatedAt: String?
    var icon:String?
    var is_knockout:Bool?
    var knockout_tries:Int?
    var game:GameDataModel?
    var timeInterval: Int?
    var entryFeeType: EntryFeeType?
    var game_type , player_number:String?
    
    func setCountDownInSeconds(){
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let startDate = Date()
        let endDate = dateFormatter.date(from: startDateTime!)
        timeInterval = Int(endDate! - startDate)
    }
    
    enum CodingKeys: String, CodingKey {
        case id , icon , is_knockout , knockout_tries , game
        case joinedPlayers = "joined_players"
        case maximumPlayers = "maximum_players"
        case maximumAttempt = "maximum_attempt"
        case tournamentRules = "tournament_rules"
        case isContest = "is_contest"
        case isSpecial = "is_special"
        case isGrand = "is_grand"
        case isTicket = "is_ticket"
        case isActive = "is_active"
        case contestType = "contest_type"
        case type
        case descriptionR = "description"
        case skillRange = "skill_range"
        case contestRulesAr = "contest_rules_ar"
        case startDateTime = "start_date_time"
        case endDateTime = "end_date_time"
        case name = "name"
         case prize
        case noOfWinners = "no_of_winners"
        case entryfeeDeposit = "entryfee_deposit"
        case logo = "logo"
        case countryOperators
        case entryfeeBouns = "entryfee_bouns"
        case pilMargin = "pil_margin"
        case prizesDistribution = "prizes_distribution"
        case gameID = "gameId"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case entryFeeType = "entryfee_type"
        case game_type , player_number
    }
}

enum EntryFeeType: String, Codable{
    case tokens = "pi_tokens"
    case lives = "lives"
    case none = ""
    
    var icon: String{
        switch self{
        case .tokens: return "pi-coins"
        default: return "Lives"
        }
    }
}

// MARK: - PrizesDistribution
struct PrizesDistribution: Codable {
    var rank, prize: Int?
    var currency: String?
}

// MARK: - Validate Contest Model
struct ValidateContestModel: Codable {
    var status: Int?
    var message: String?
    var data: ValidateContestDataModel?
}

// MARK: - DataClass
struct ValidateContestDataModel: Codable {
    var message: String?
    var canJoin:Bool?
    var reason:String?
    var entry_fee: Int?
}


struct ValidateContestRequestModel:Encodable{
    var playerId:String?
}



//MARK: - leaderboard Model
struct LeaderBoardContestModel: Codable {
    var status: Int?
    var message: String?
    var data: [LeaderBoardContestDataModel]?
}


struct LeaderBoardContestDataModel: Codable {
    var id: Int?
    var image: String?
    var name:String?
    var score,lose,rank:Int?
    var datumDefault, createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, image , name , score , lose
        case datumDefault = "default"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}




//MARK: -  LeaderBoard Knockout Model
struct LeaderBoardKnockoutModel:Codable{
    var status: Int?
    var message: String?
    var data:LeaderBoardKnockoutDataClass?
}


struct LeaderBoardKnockoutDataClass: Codable {
    var player: PlayerLeaderBoardContestModel?
    var ledearboard: [LeaderBoardContestDataModel]?
}


struct PlayerLeaderBoardContestModel: Codable {
    var userID, lose, score: Int?
    var image: String?
    var rank: Int?

    enum CodingKeys: String, CodingKey {
        case userID = "userId"
        case lose, score, image, rank
    }
}


//MARK: - leaderbaord RequestModel
struct LeaderBoardContestRM:Encodable{
    var token:String?
    var playerId:String?
}

//MARK: - leaderbaord Knockout RequestModel



//MARK: - feed back

struct FeedBackGameRequstModel: Codable {
    var gameID: Int?
    var playerID: String?
    var stars: Double?
    var message: String?

    enum CodingKeys: String, CodingKey {
        case gameID = "game_id"
        case playerID = "playerId"
        case stars, message
    }
}


struct FeedBackGameModel: Codable {
    var status: Int?
    var message: String?
//    var data: FeedBackData?
}

// MARK: - DataClass
struct FeedBackData: Codable {
    var gameID: Int?
    var playerID: String?
    var stars: Double?
    var message: String?
    var id: Int?
    var createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case gameID = "game_id"
        case playerID = "playerId"
        case stars, message, id
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct CandSendFeedback:Codable{
    var status: Int?
    var message: String?
    var data:Bool?
}


//MARK: - work
struct CheckPlayerAdsModel: Codable {
    var status: Int?
    var message: String?
    var data: PlayerAdDataClass?
}

// MARK: - DataClass
struct PlayerAdDataClass: Codable {
    var watch: Bool?
}
