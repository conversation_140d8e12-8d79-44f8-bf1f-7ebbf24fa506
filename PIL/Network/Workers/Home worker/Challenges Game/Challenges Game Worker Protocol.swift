//
//  Challenges Game Worker Protocol.swift
//  PIL
//
//  Created by sameh mohammed on 22/12/2022.
//

import Foundation
import Alamofire


protocol ChallengesGameWorkerProtoco{
    
    func getGameDetails(gameID:String ,compilition: @escaping ( Result<GameIfnoModel,AFError>, _ StatusCode: Int?) -> Void)

    func getChallengesGame(gameID:String ,compilition: @escaping ( Result<ChallengesGamesModel,AFError>, _ StatusCode: Int?) -> Void)
    
    func getSingleChallengesGame(contestID:String ,compilition: @escaping ( Result<SingleChallengeGamesModel,AFError>, _ StatusCode: Int?) -> Void)

    func canJoinContest(contestID:String  , compilition: @escaping ( Result<ValidateContestModel,AFError>, _ StatusCode: Int?) -> Void)
    
    func getLeaderBoard(contestID:String  , compilition: @escaping ( Result<ValidateContestModel,AFError>, _ StatusCode: Int?) -> Void)

    func feedBack(model:FeedBackGameRequstModel  , compilition: @escaping ( Result<FeedBackGameModel,AFError>, _ StatusCode: Int?) -> Void)

    
    func cansSendFeedBack(model:FeedBackGameRequstModel  , compilition: @escaping ( Result<CandSendFeedback,AFError>, _ StatusCode: Int?) -> Void)
    
    func getLeaderBoardKnockout(contestID:String  , compilition: @escaping ( Result<LeaderBoardKnockoutModel,AFError>, _ StatusCode: Int?) -> Void)

    
    func getEventsGame(compilition: @escaping (Result<ChallengesGamesModel, AFError>, Int?) -> Void)
}
