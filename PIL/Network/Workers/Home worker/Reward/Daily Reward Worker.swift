//
//  Daily Reward Worker.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation
import Alamofire

class DailyRewardWorker:DailyRewardWorkerProtocol{
    
    func checkReward(compilition: @escaping (Result<DailyRewardModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.DeservesDailyLivesURL)\(UserModel.shared.get_id())"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    func collectReward(compilition: @escaping ( Result<collectRewardModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.CollectDailyLivesURL)\(UserModel.shared.get_id())"
        Requests.API(indicator: .none).performRequest(url: url, method: .post, headersType: .token, completion: compilition)
    }
}
