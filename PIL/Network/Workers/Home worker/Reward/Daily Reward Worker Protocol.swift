//
//  Daily Reward Worker Protocol.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 05/01/2023.
//

import Foundation
import Alamofire

protocol  DailyRewardWorkerProtocol{
    
    func checkReward(compilition: @escaping ( Result<DailyRewardModel,AFError>, _ StatusCode: Int?) -> Void)
    
    func collectReward(compilition: @escaping ( Result<collectRewardModel,AFError>, _ StatusCode: Int?) -> Void)

}
