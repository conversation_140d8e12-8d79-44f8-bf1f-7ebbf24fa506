//
//  UpdateVersionWorker.swift
//  PIL
//
//  Created by sameh mohammed on 15/03/2023.
//

import Foundation
import Alamofire


class UpdateVersionWorker: UpdateVersionProtocol{
    func updateVersion(compilition: @escaping (Result<UpdateVersionModel, AFError>, Int?) -> Void) {
            let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
            let langQuery = URLQueryItem(name: "language", value: app_lang)
            let version = URLQueryItem(name: "version", value: appVersion)
            let platform =  URLQueryItem(name: "platform", value: "ios")
            let url = "\(Requests.url.UpdateVersionURL)"
            let apiUrl =   url.addQueryParameters(queries: [langQuery , version , platform])
            Requests.API(indicator: .none).performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
        }
}





