//
//  Game Details Worker.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 21/12/2022.
//

import Foundation
import Alamofire

class GameDetailsWorker: GameDetailsWorkerProtocol{
    
    func getGameDetails(gameID:String , compilition: @escaping (Result<GameIfnoModel, AFError>, Int?) -> Void){
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let operatorx = URLQueryItem(name: "operator", value: UserModel.shared.getOperator())
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())
        let url = "\(Requests.url.gameDetailsURL)\(gameID)"
        let apiUrl =   url.addQueryParameters(queries: [langQuery , version , platform , operatorx , countryCode])
        Requests.API(indicator: .none).performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
}
