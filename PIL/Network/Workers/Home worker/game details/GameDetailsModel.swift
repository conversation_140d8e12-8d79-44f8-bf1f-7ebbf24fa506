//
//  GameDetailsModel.swift
//  PIL
//
//  Created by sameh mohammed on 21/12/2022.
//

import Foundation

// MARK: - Welcome
struct GameIfnoModel: Codable {
    var status: Int?
    var message: String?
    var data: GameDetailsDataModel?
}

struct GameDetailsDataModel:Codable{
    var game:GameDataModel?
    var contest:ChallengesGamesData?
    var leaderboard:[LeaderBoardContestDataModel]?
    var battle: ChallengesGamesData?
    var battles: [ChallengesGamesData]?
    
}
// MARK: - DataClass
struct GameDataModel: Codable {
    var id: Int?
    var gameOrientation: String?
    var assetsbundle: String?
    var gamePlatform, pilMargin: String?
    var sceneName, createdBy, userID, gameLogoType: String?
    var howToPlay: String?
    var gdDlink: String?
    var tag: String?
    var tutorialcards: [String]?
    var countryTag, botConfig: String?
    var version: String?
//    var andriodassetsbundle: String?
    var mode: String?
    var income, gameBackground: String?
    var dataDescription, gameConfig, nameAr: String?
    var icons: String?
    var faq: String?
    var lobbyConfig: String?
    var isActive: Bool?
    var engine, format, descriptionAr, createdAt: String?
    var updatedAt: String?
    var categoryID: Int?
    var userExperience, tutorialVideos: String?
    var genre, gameSize, gameName: String?
    var rule:String?
    var is_knockout:Bool?
    
    enum CodingKeys: String, CodingKey {
        case id, gameOrientation, assetsbundle, gamePlatform , rule , is_knockout
        case pilMargin = "pil_margin"
        case sceneName, createdBy
        case userID = "userId"
        case gameLogoType = "GameLogoType"
        case howToPlay = "how_to_play"
        case gdDlink = "GDDlink"
        case tag, tutorialcards, countryTag, botConfig, version, mode, income
        case gameBackground = "GameBackground"
        case dataDescription = "description"
        case gameConfig
        case nameAr = "name_ar"
        case icons, faq, lobbyConfig
        case isActive = "is_active"
        case engine, format
        case descriptionAr = "description_ar"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case categoryID = "categoryId"
        case userExperience, tutorialVideos, genre
        case gameSize = "game_size"
        case gameName
    }
}
