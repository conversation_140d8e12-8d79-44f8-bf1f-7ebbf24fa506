//
//  More Worker.swift
//  PIL
//
//  Created by <PERSON> on 9/28/21.
//

import Foundation


class MoreWorker: MoreWorkerProtocol{
    func getMoreOptions(compilition: (Result<MoreModel, Error>) -> Void) {
        if let path = Bundle.main.path(forResource: "MoreDataBase", ofType: "json") {
            do {
                  let fileData = try Data(contentsOf: URL(fileURLWithPath: path), options: .mappedIfSafe)
                  let jsonResult = try JSONDecoder().decode(MoreModel.self, from: fileData)
                compilition(.success(jsonResult))
                
            } catch (let error) {
                   // handle error
                compilition(.failure(error))
            }
        }
    }
}
/*
 {
     "title_AR": "مزيد من المعلومات حول الأحداث",
     "title_EN": "More info about events",
     "icon": "info-1",
     "linkType": "moreInfoAboutEvent"
 },
 */
