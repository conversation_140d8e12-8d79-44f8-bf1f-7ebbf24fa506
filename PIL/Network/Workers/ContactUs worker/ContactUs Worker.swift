//
//  ContactUs Worker.swift
//  PIL
//
//  Created by mac on 09/01/2022.
//

import Foundation
import Alamofire

class ContactUsWorker: ContactUsWorkerProtocol{
    
    func get(compilition: @escaping ( Result<ContactusModel,AFError>, _ StatusCode: Int?) -> Void){
        let url = "\(Requests.url.contactUsURL)?language=\(app_lang)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    func create(model: ContactUsRequestModel, compilition: @escaping (Result<DefaultModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.createContactUsURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
}
