//
//  ContactUs  Worker Protocol.swift
//  PIL
//
//  Created by mac on 09/01/2022.
//

import Foundation
import Alamofire

protocol ContactUsWorkerProtocol{
    
    /// get all options in contact Us
    func get(compilition: @escaping ( Result<ContactusModel,AFError>, _ StatusCode: Int?) -> Void)
    
    /// create Contact Us Request
    /// - Parameter model: request model to send
    func create(model: ContactUsRequestModel, compilition: @escaping ( Result<DefaultModel,AFError>, _ StatusCode: Int?) -> Void)
    
    
}
