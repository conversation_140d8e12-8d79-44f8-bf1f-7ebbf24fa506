//
//  Categories Worker.swift
//  PIL
//
//  Created by <PERSON> on 9/30/21.
//

import Foundation

class CategoriesWorker: CategoriesWorkerProtocol{
    func getCategories(compilition: (Result<CategoriesModel, Error>) -> Void) {
        if let path = Bundle.main.path(forResource: "CategoriesDataBase", ofType: "json") {
            do {
                  let fileData = try Data(contentsOf: URL(fileURLWithPath: path), options: .mappedIfSafe)
                  let jsonResult = try JSONDecoder().decode(CategoriesModel.self, from: fileData)
                compilition(.success(jsonResult))
                
            } catch (let error) {
                   // handle error
                compilition(.failure(error))
            }
        }
    }
}
