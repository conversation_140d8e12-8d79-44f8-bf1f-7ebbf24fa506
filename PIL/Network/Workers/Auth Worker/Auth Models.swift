//
//  Auth Models.swift
//  PIL
//
//  Created by <PERSON> on 8/27/21.
//

import Foundation


struct VerificationCodeModel: Codable{
    var status: Bool?
    var message: String?
    var code: Int?
    var error: String?
}

class loginModel: Codable{
    var status: Bool?
    var user: UserDataModel?
    var data: UserDataModel?
    var code: Int?
    var message: String?
}

class UserDataModel: Codable{
    var created_at: String?
    var bio: String?
    var email: String?
    var name: String?
    var gender: String?
    var updated_at: String?
    var birthdate: String?
    var pil_id: String?
    var username: String?
    var id: AnyType?
    var ID: String?
    var phone: String?
    var token: String?
    var image: String?
    var level: Int?
    var followers: Int?
    var following: Int?
    var popularity: Int?
    var winning: Int?
    var cashWin: Int?
    var rank: String?
    var winPercintage: Double?
    var tournamentsWon: String?
    var referralCode:String?
    var referrallink:String?
//    var referral:String?
    var device: UserDeviceDataModel?
    var operatorop:String?
    var helpdeskId:String?
    var group:String?
    var type,watched_tutorial:String?
    var avatar_id:Int?
    var first_login:String?
    var isFollowers, isFollowing: Bool?
    var isOnline: Bool?
    var isBlocked:Bool?
    var sum: Int?
    var status : String?
    var message : String?
    var snap_chat_id:String?
    var snapChatId:String?
    
    enum CodingKeys: String, CodingKey {
        case  created_at ,
        message,
        status,
        group,
        avatar_id,
        type ,
        watched_tutorial,
        bio ,
        email ,
        name,
        gender,
        updated_at,
        birthdate,
        pil_id,
        username,
        id,
        ID,
        phone,
        token,
        image,
        level,
        followers,
        following,
        winning,
        cashWin,
        rank,
        winPercintage,
        tournamentsWon,
        referralCode,
//        referral,
        device,
        helpdeskId,
        referrallink,
        popularity,
        isFollowers, isFollowing,
        isOnline ,
        isBlocked,
        first_login,
        sum,
        snap_chat_id , snapChatId
        
        case operatorop = "operator"
     
    }
}

class UserDeviceModel: Codable{
    var status: Bool?
    var message: String?
    var code: Int?
    var data: UserDeviceDataModel?
}

class UserDeviceDataModel: Codable{
    var model: String?
    var created_at: String?
    var release: String?
    var brand: String?
    var fingerprint: String?
    var broad: String?
    var updated_at: String?
    var manufacturer: String?
    var platform: String?
    var userId: String?
    var host: String?
    var FCMToken: String?
    var sdk: String?
    var id: AnyType?
    var ID: String?
    var type: String?
    var voip_token: String?
}


// verification code reset password
struct VerificationCodeResetModel: Codable{
    var status: Bool?
    var message: Int?
    var data: UserDataClass?
    var error: String?

}

// MARK: - DataClass
struct UserDataClass: Codable {
    var referralCode, referral, email, name: String?
    var referrallink:String?
    var gender: String?
    var birthdate, pilID, username: String?
    var id, phone: String?
    var dataID:Int?

    enum CodingKeys: String, CodingKey {
        case  referralCode, referral, email, name, gender , referrallink
        case birthdate
        case pilID = "pil_id"
        case username
        case dataID = "id"
        case id = "ID"
        case phone
    }
}

//

struct  EmailResetPasswordModel:Codable{
    var status: Bool?
    var message: String?
    var data: EmailResetDataClass?
}

// MARK: - DataClass
struct EmailResetDataClass: Codable {
    var referralCode, referral, email, name: String?
    var referrallink:String?
    var gender: String?
    var birthdate, pilID, username: String?
    var id, phone, token: String?
    var dataID:Int?
    
    enum CodingKeys: String, CodingKey {
        case  referralCode, referral, email, name, gender , referrallink
        case birthdate
        case pilID = "pil_id"
        case username
        case dataID = "id"
        case id = "ID"
        case phone, token
    }
}

//MARK: - dial numbers
struct DialNumbersModel: Codable{
    var status: Bool?
    var message: String?
    var data: [DialNumbersDataModel]?
}

struct DialNumbersDataModel: Codable{
    var _id: String?
    var name: String?
    var alph_code: String?
    var num_code: String?
    var name_ar:String?
    var name_en:String?
}


// MARK: - Avatar
struct AvatarModel: Codable {
    var status: Bool?
    var message: String?
    let data: [AvatarData]?
}

struct AvatarData: Codable {
    var id: Int?
    var image: String?
    var datumDefault, createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, image
        case datumDefault = "default"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct SocialLoginModel: Encodable{
    let token: String
    let email: String?
    var phoneNumber: String = ""
    let fcm_token: String = UserModel.shared.get_FCMToken()
    let language: String = app_lang
    let vendor: LoginProvider
    var birth: String = ""
    let name: String?
}

enum LoginProvider: String, Encodable{
    case facebook = "facebook"
    case google = "google"
    case apple = "apple"
    case phone = "phone"
}

struct PlayersReferrals: Codable {
    var players: [UserDataModel]?
    let sum: Int?
}
