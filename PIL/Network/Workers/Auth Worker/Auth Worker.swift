//
//  Auth Worker.swift
//  PIL
//
//  Created by <PERSON> on 8/27/21.
//

import Foundation
import Alamofire

class AuthWorker: AuthWorkerProtocol{
    
    static let shared = AuthWorker()
    
    func loginToServer(requestModel: SocialLoginModel, completion: @escaping (Result<loginModel, AFError>, Int?) -> Void) {
        let url = URls.init().socialLogin
        Requests.API().performRequest(url: url, method: .post, RequestModel: requestModel, headersType: .none, completion: completion)
    }
    
    func getEmailCode(email: String, compilition: @escaping (Result<VerificationCodeModel, AFError>,_ statusCode: Int?) -> Void){
        let url = "\(Requests.url.createCodeURL)?email=\(email)&language=\(app_lang)"
        Requests.API().performRequest(url: url, method: .get, headersType: .none, completion: compilition)
    }
    
    func getPhoneCode(for Phone: String, code: String, compilition: @escaping (Result<VerificationCodeModel, AFError>,_ statusCode: Int?) -> Void) {
        let url = "\(Requests.url.createCodeURL)?phone=\(Phone)&country_code=\(code)&language=\(app_lang)"
        Requests.API().performRequest(url: url, method: .get, headersType: .none, completion: compilition)
    }
    
    func Login(model: loginRequestModel, compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.loginURL, method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    func loginAsGuest(compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void){
        Requests.API().performRequest(url: Requests.url.LoginAsGuestURL, method: .post, RequestModel: DeviceID(), headersType: .none, completion: compilition)
    }
    
    func SignUp(model: signUpRequestModel, compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.signUpURL, method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    func getEmailCode(model: emialVerificationCodeRequestModel, compilition: @escaping (Result<VerificationCodeResetModel, AFError>,_ statusCode: Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.emailVerificationURL, method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    func setDevice(model: deviceRequestModel, compilition: @escaping (Result<UserDeviceModel,AFError>,_ statusCode: Int?) -> Void) {
        Requests.API(indicator: .none).performRequest(url: Requests.url.setDeviceURL, method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    func resetPassword(model: ResetPasswordRequestModel, compiliation: @escaping (Result<EmailResetPasswordModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.resetPassword, method: .post, RequestModel: model, headersType: .none, completion: compiliation)
    }
    
    
    
    func NewPhoneNumber(model: NewPhoneNumberRequestModel,userID:String, compiliation: @escaping (Result<UserProfileModel, AFError>, Int?) -> Void) {
        let url =  Requests.url.NewPhoneNumber+userID
        let HttpHeaders:HTTPHeaders = [
                                       "Content-Type": "application/json",
                                       "Authorization": UserModel.shared.get_token(),
                                       "Language":UserModel.shared.getLanguage()
                                       ]
        
        Requests.API().performRequest2(url:url, method: .post, RequestModel: model, headersType: HttpHeaders, completion: compiliation)

    }

    func dialCodes(compiliation: @escaping  (Result<DialNumbersModel,AFError>,_ statusCode: Int?) -> Void){
        let url = "\(Requests.url.DialCodes)"
        Requests.API().performRequest(url: url, method: .get, headersType: .none, completion: compiliation)
    }
    
    
    func avatarList(compiliation: @escaping  (Result<AvatarModel,AFError>,_ statusCode: Int?) -> Void){
        Requests.API().performRequest(url: Requests.url.AvatarListURL, method: .get, headersType: .token, completion: compiliation)
    }

    func assignAvatar(model:AssignAvatarRequestModel , compiliation: @escaping  (Result<loginModel,AFError>,_ statusCode: Int?) -> Void){
        Requests.API().performRequest(url: Requests.url.AssignAvatarURL, method: .post, RequestModel: model, headersType: .token, completion: compiliation)
    }
    
    func getUserSubscription(_ userId: String? = nil,_ completion: @escaping (Result<ServerResponse2<SubscriptionResponse>, AFError>, Int?) -> Void) {
        var userPrm: String?
        if userId.isNilOrEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = userId
        }
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let url = URls().subscription + "/\(userPrm ?? "")/sub"
        let urlWithPrms = url.addQueryParameters(queries: [.init(name: "platform", value: "ios"), langQuery])
        Requests.API(indicator: .none).performRequest(url: urlWithPrms, method: .get, headersType: .token, completion: completion)
    }
    
    func Logout(completion: @escaping (Result<Response2, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: URls().logoutURL, method: .post, headersType: .token, completion: completion)
    }
    /// recover user account
    /// - Parameters:
    ///   - type: string value
    ///   - device: string value
    ///   - completion: server response completion
    func recoverAccount(_ type: String, _ device: String, _ completion: @escaping (Result<Response2, AFError>, Int?) -> Void) {
        Requests.API(indicator: .none).performRequest(url: URls().recover, method: .post, parameters: ["type": type, "device": device], headersType: .token, fileUrlKey: [], files: [], filesNames: [], mimeTypes: [], completion: completion)
    }
    /// verify recover user account
    /// - Parameters:
    ///   - type: string value
    ///   - device: string value
    ///   - sercret: string value
    ///   - code: string value
    ///   - completion: login model completion
    func verifyRecoverAccount(_ type: String,_ device: String,_ secret: String,_ code: String,_ completion: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void) {
        Requests.API(indicator: .none).performRequest(url: URls().verifyRecover, method: .post, parameters: ["type": type, "device": device, "secret_id": secret, "verification": code], headersType: .token, fileUrlKey: [], files: [], filesNames: [], mimeTypes: [], completion: completion)
    }
    
    /// check if user is eligible to recover his account
    /// - Parameters:
    ///   - completion: server response completion
    func canRecoverAccount(_ completion: @escaping (Result<ServerResponse2<Response2>, AFError>,_ status: Int?) -> Void){
        Requests.API(indicator: .regular).performRequest(url: URls().canRecover, method: .post, parameters: ["secret_id": UIDevice.current.identifierForVendor!.uuidString.encryptAES()?.encryptAES() ?? ""], headersType: .token, fileUrlKey: [], files: [], filesNames: [], mimeTypes: [], completion: completion)
    }
}
