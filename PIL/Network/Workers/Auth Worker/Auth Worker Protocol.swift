//
//  Auth Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 8/27/21.
//

import Foundation
import Alamofire

protocol AuthWorkerProtocol{
    
    /// login to PlayIT servers
    /// - Parameters:
    ///  - requestModel: encodable SocialLoginModel
    func loginToServer(requestModel: SocialLoginModel, completion: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void)
    
    /// get verification Code for phone number
    /// - Parameters:
    ///   - Phone: valid phone number
    ///   - code: country code
    ///   - compilition: request compilition
    func getPhoneCode(for Phone: String, code: String , compilition: @escaping (Result<VerificationCodeModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// login
    /// - Parameters:
    ///   - model: model of phone number and verificationPhone
    ///   - compilition: request compilition
    func Login(model: loginRequestModel, compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void)
    
    func Logout(completion: @escaping (Result<Response2, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// sign up
    /// - Parameters:
    ///   - model: model of phone number
    ///   - compilition: request compilition
    func SignUp(model: signUpRequestModel, compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void)
    
    /// get verification code for email
    /// - Parameters:
    ///   - model: model of email
    ///   - compilition: request compilition
    func getEmailCode(model: emialVerificationCodeRequestModel, compilition: @escaping (Result<VerificationCodeResetModel, AFError>,_ statusCode: Int?) -> Void)
    
    /// set user device to API
    /// - Parameters:
    ///   - mode: device model
    ///   - compilition: request compilition
    func setDevice(model: deviceRequestModel, compilition: @escaping (Result<UserDeviceModel,AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// reset password
    /// - Parameters:
    ///   - model: verification code and email
    ///   - compiliation: request compilition
    func resetPassword(model:ResetPasswordRequestModel,compiliation: @escaping  (Result<EmailResetPasswordModel,AFError>,_ statusCode: Int?) -> Void)
    
    
    /// set newe phone number for user
    /// - Parameters:
    ///   - model: new phone number
    ///   - userID: user id
    ///   - compiliation: request compilition
    func NewPhoneNumber(model:NewPhoneNumberRequestModel,userID:String,compiliation: @escaping  (Result<UserProfileModel,AFError>,_ statusCode: Int?) -> Void)
    
    
    /// get the available countries and its countries
    func dialCodes(compiliation: @escaping  (Result<DialNumbersModel,AFError>,_ statusCode: Int?) -> Void)

    
    
    func avatarList(compiliation: @escaping  (Result<AvatarModel,AFError>,_ statusCode: Int?) -> Void)
    
    func assignAvatar(model:AssignAvatarRequestModel , compiliation: @escaping  (Result<loginModel,AFError>,_ statusCode: Int?) -> Void)
    
    func loginAsGuest(compilition: @escaping (Result<loginModel, AFError>,_ statusCode: Int?) -> Void)
    
    func getUserSubscription(_ userId: String?,_ completion: @escaping (Result<ServerResponse2<SubscriptionResponse>, AFError>,_ status: Int?) -> Void)
    
    func recoverAccount(_ type: String,_ device: String,_ completion: @escaping (Result<Response2, AFError>,_ status: Int?) -> Void)
    
    func verifyRecoverAccount(_ type: String,_ device: String,_ secret: String,_ code: String,_ completion: @escaping (Result<loginModel, AFError>,_ status: Int?) -> Void)
    
    func canRecoverAccount(_ completion: @escaping (Result<ServerResponse2<Response2>, AFError>,_ status: Int?) -> Void)
    
    func getEmailCode(email: String, compilition: @escaping (Result<VerificationCodeModel, AFError>,_ statusCode: Int?) -> Void)
}
