//
//  Auth Request Models.swift
//  PIL
//
//  Created by <PERSON> on 8/27/21.
//

import Foundation


struct loginRequestModel: Encodable{
    var phone: String?
    var verification: String?
    var country_code: String?
    var language:String?
    var device_id: String = UIDevice.current.identifierForVendor!.uuidString
    var fcm_token = UserModel.shared.get_FCMToken()
}


struct signUpRequestModel: Encodable{
    var email: String
    var country_code: String
    var phone: String
    var referral: String
    var language:String
    var fcm_token:String
    var device_id: String = UIDevice.current.identifierForVendor!.uuidString
}


struct emialVerificationCodeRequestModel: Encodable{
    var username: String
    var language:String
}


struct deviceRequestModel: Encodable{
    var model: String?
    var release: String?
    var brand: String?
    var fingerprint: String?
    var broad: String?
    var base: String?
    var updated_at: String?
    var manufacturer: String?
    var platform: String?
    var userId: String?
    var host: String?
    var FCMToken: String = UserModel.shared.get_FCMToken()
    var sdk: String?
    var device_id: String = UIDevice.current.identifierForVendor!.uuidString
    var type: String?
    var language = app_lang
    var voip_token : String = UserModel.shared.get_VoIPToken()
    var versionName: String? = Bundle.main.releaseVersionNumber
    var versionCode: String? = Bundle.main.buildVersionNumber
}


struct ResetPasswordRequestModel :Encodable {
    var username:String?
    var verification:Int?
    var language:String
}

struct NewPhoneNumberRequestModel :Encodable {
    var country_code: String
    var phone:String?
}

//MARK: - assign
struct AssignAvatarRequestModel:Encodable{
    var playerId:String?
    var avatar_id:Int?
}


// MARK: - Check S
struct SubscriptionModel:Codable{
    var data:SubscriptionResponse?
}
struct SubscriptionResponse: Codable {
    let subscribed: Bool
    let subscription: [Subscription]?
}

// MARK: - Subscription
struct Subscription: Codable {
    let id: Int
    let subId, productId, name, state: String?
    let subscriptionPeriod, groupLevel: String?
    let availableInAllTerritories, familySharable: Int?
    let description: String?
    let icon: String?
    let inApp, currency, createdAt: String?
    let price: AnyType?
    let updatedAt, from: String?
    let upgradable: Bool?
    
    enum CodingKeys: String, CodingKey {
        case id
        case subId = "sub_id"
        case productId = "product_id"
        case name, state, subscriptionPeriod, groupLevel, availableInAllTerritories, familySharable, description, icon
        case inApp
        case price, currency
        case createdAt
        case updatedAt
        case from
        case upgradable
    }
}

struct DeviceID: Encodable{
    let device_id: String? = UIDevice.current.identifierForVendor?.uuidString
}

