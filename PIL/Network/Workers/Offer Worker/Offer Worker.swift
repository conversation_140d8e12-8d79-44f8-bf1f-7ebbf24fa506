//
//  Offer Worker.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation
import Alamofire

class OfferWorker: OfferWorkerProtocol{

    func getOffers(model: offerRequestModel, compilition: @escaping (Result<OfferModel, AFError>, Int?) -> Void) {
        
        Requests.API(indicator: .custom).performRequest(url: Requests.url.offersURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func GetMissions(compilition: @escaping (Result<MissionGamesModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.MissionPlayer)\(UserModel.shared.get_id())"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
}
