//
//  Offer Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation
import Alamofire

protocol OfferWorkerProtocol {
    
    /// get offers depends on type
    /// - Parameters:
    ///   - model: offer request model
    ///   - compilition: response compilition
    func getOffers(model: offerRequestModel, compilition : @escaping (Result<OfferModel,AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// get mission
    /// - Parameter compilition: response compilition
    func GetMissions( compilition : @escaping (Result<MissionGamesModel,AFError>,_ statusCode: Int?) -> Void)
}
