//
//  Offer Request Models.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation

enum offerType: String{
    case fantasy = "FANTASY OFFER TYPE"
    case gamesOffers = "GAME OFFER TYPE"
    case gamesMissions = ""
}

struct offerRequestModel: Encodable{
    var offerRecord: offerRecordModel?
}

struct offerRecordModel: Encodable{
    var offerType: String?
    var objectId:String?
    let valid: Bool = true
}

