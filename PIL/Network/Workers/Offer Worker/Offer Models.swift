//
//  Offer Models.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation

// MARK: - Welcome
struct OfferModel: Codable {
    let offerRecords: [OfferDataModel]?
    let message: String?
    let success: Bool?
    let statusCode, totalCount, pageCount: Int?
}

// MARK: - OfferRecord
struct OfferDataModel: Codable {
    let id: Int?
    let creationdate, name, offerRecordDescription: String?
    let isdeleted: Bool?
    let validfrom, validto: String?
    let modificationdate: String?
    let discount: String?
    let status: Bool?
//    let purpose: JSONNull?
    let imageurl: String?
    let maxusagecount: Int?
    let modifiedBy: String?
    let createdBy: String?
//    let languageID, formImage, valid, addUse: JSONNull?
    let objectTypeID, objectID: String? //objectTypeID = match id
    let usedcount: Int?
    let objectURL: String?
//    let minValue, maxValue: JSONNull?
    let actionTypeID, constantType, actionType: String
    let offerTypeID: Int?
    let offerType: String?
//    let translates: JSONNull?

    enum CodingKeys: String, CodingKey {
        case id, creationdate, name
        case offerRecordDescription = "description"
        case isdeleted, validfrom, validto, modificationdate, discount, status, imageurl, maxusagecount, modifiedBy, createdBy
//        case languageID = "languageId"
//        case formImage, valid, addUse
        case objectTypeID = "objectTypeId"
        case objectID = "objectId"
        case usedcount
        case objectURL = "objectUrl"
//        case minValue, maxValue
        case actionTypeID = "actionTypeId"
        case constantType, actionType
        case offerTypeID = "offerTypeId"
        case offerType
//        case translates, purpose
    }
}


// MARK: - MissionGamesModel
struct MissionGamesModel: Codable {
    var statusCode: Int?
    var status: Bool?
    var message: String?
    var data: [MissionGamesData]?
}

// MARK: - Datum
struct MissionGamesData: Codable {
    let id, name, datumDescription: String?
    let image: String?
    let prize: Int?
    let createdAt, updatedAt: String?
    let v: Int?
    let action, status: String?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case name
        case datumDescription = "description"
        case image, prize, createdAt, updatedAt
        case v = "__v"
        case action, status
    }
}
