//
//  Help Worker Protocols.swift
//  PIL
//
//  Created by mac on 22/12/2021.
//

import Foundation
import Alamofire

protocol HelpWorkerProtocol{
    
    /// get list of Help Types
    /// - Parameters:
    ///     - type: types of pil help
    func getList(type: PILHelpTopics, compilition: @escaping (Result<HelpModel,AFError>,_ StatusCode: Int?) -> Void)
    
    /// get link of Help Types
    /// - Parameters:
    ///     - model: model og the type
    func getLink(model: PILHelpReuestModel, compilition: @escaping (Result<HelpModel,AFError>,_ StatusCode: Int?) -> Void)
    
    func getDeviceLogin(model: GetDeviceLoginModel , compilition: @escaping (Result<loginModel,AFError>,_ StatusCode: Int?) -> Void)
    
    
    func deleteAccount(model: DeleteAccountReuestModel, compilition: @escaping (Result<SocialAddModel,AFError>,_ StatusCode: Int?) -> Void)
    func getIssuesList(completion: @escaping (Result<IssusesHistoryResponse, AFError>,_ StatusCode: Int?) -> Void)
}
 
