//
//  Help Worker.swift
//  PIL
//
//  Created by mac on 22/12/2021.
//

import Foundation
import Alamofire

class HelpWorker: HelpWorkerProtocol{
    
    static let shared = HelpWorker()
    
    func getList(type: PILHelpTopics, compilition: @escaping (Result<HelpModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.pilHelpURL, method: .post, RequestModel: type.requestModel, headersType: .token, completion: compilition)
    }
    
    func getLink(model: PILHelpReuestModel, compilition: @escaping (Result<HelpModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.pilHelpURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getDeviceLogin(model: GetDeviceLoginModel , compilition: @escaping (Result<loginModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.device_login , method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    func getRestoreAccount(model: GetDeviceLoginModel , compilition: @escaping (Result<loginModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.restore_account , method: .post, RequestModel: model, headersType: .none, completion: compilition)
    }
    
    
    func deleteAccount(model: DeleteAccountReuestModel, compilition: @escaping (Result<SocialAddModel,AFError>,_ StatusCode: Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.DeleteAccount, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getIssuesList(completion: @escaping (Result<IssusesHistoryResponse, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: URls.init().issuesHistory, method: .post, RequestModel: UserID.init(user_id: UserModel.shared.get_id()), headersType: .token, completion: completion)
    }

}

fileprivate struct UserID: Encodable{
    let user_id: String
}
