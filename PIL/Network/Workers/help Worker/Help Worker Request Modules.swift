//
//  Help Worker Request Modules.swift
//  PIL
//
//  Created by mac on 22/12/2021.
//

import Foundation

enum PILHelpTopics: String{
    case FAQPAGESALL = "FAQPAGESALL"
    case HelpGames = "HelpGames"
    case FantasySetting = "FantasySetting"
    case HelpFantasy = "HelpFantasy"
    case terms = "termsandcondition"
    case policy = "privacyPolicy"
    case deviceLogin = "device_login"
    
    var requestModel: PILHelpReuestModel{
        return PILHelpReuestModel(topic: self.rawValue)
    }
    
}

struct PILHelpReuestModel: Encodable{
    var topic: String
    var lang: String = app_lang
}

struct GetDeviceLoginModel : Encodable{
    var secret_id: String
    var language : String
    var platform : String
    var version  : String
}


struct DeleteAccountReuestModel:Encodable{
    var user_id:String?
    var device_id: String = UIDevice.current.identifierForVendor!.uuidString
}
