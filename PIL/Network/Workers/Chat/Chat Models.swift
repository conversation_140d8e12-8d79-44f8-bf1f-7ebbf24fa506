//
//  Chat Models.swift
//  PIL
//
//  Created by <PERSON> on 11/21/21.
//

import Foundation

struct SuccessModel: Codable{
    
}

enum ChatResult<Success , Failure> where Success : Decodable{
    case success(Success)
    case fail(Failure)
}

enum ChatKeys: String{
    // ------ main Keys -----
    case fireBaseServerLink = "https://fcm.googleapis.com/fcm/send"
    case chatRooms = "Users Rooms"
    case chats = "Chats"
    case users = "Users"
    
    // ------ users -----
    case FCM = "FCM"
    case image = "image"
    case name = "name"
    case online = "online"

    // ----- chats ----
    case members = "members"
    case ChatMessages = "messages"
    case delivered = "delivered"
    case message = "message"
    case seen = "seen"
    case senderID = "senderID"
    case time = "time"
    

    // ----- chatRooms ----
    case unReadCount = "unreadCount"
    case lastMessage = "lastMessage"
    case member = "member"
    case memberID = "memberID"
    case memberName = "memberName"
}


class ChatRoomsDataModel: Codable{
    var chatID: String?
    var lastMessage: MessageModel?
    var member: LastMessageMemberModel?
    var unreadCount: Int?
    
//    enum CodingKeys: String, CodingKey {
//        case lastMessage = "lastMessage"
//        case unreadCount = "unreadCount"
//        case chatID, member
//    }
    
    init(chatID: String? = nil, member: LastMessageMemberModel? = nil, lastMessage: MessageModel? = nil, unreadCount: Int? = nil) {
        self.chatID = chatID
        self.lastMessage = lastMessage
        self.unreadCount = unreadCount
        self.member = member
    }
}

class LastMessageMemberModel: Codable{
    var memberID, memberName, memberImage: String?
    
    init(memberID: String? = nil, memberName: String? = nil, memberImage: String? = nil) {
        self.memberID = memberID
        self.memberName = memberName
    }
}



class ChatMessagesModel: Codable{
    var total: Int?
    var messages: [MessageModel]?
    
    init(total: Int? = nil, messages: [MessageModel]? = nil) {
        self.total = total
        self.messages = messages
    }
}


class ChatRoomsModel: Codable{
    var total: Int?
    var rooms: [ChatRoomsDataModel]?
    
    init(total: Int? = nil, rooms: [ChatRoomsDataModel]? = nil) {
        self.total = total
        self.rooms = rooms
    }
}

// MARK: - Welcome
struct membersGroupModel: Codable {
    var memberRecords: [MemberRecord]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount: Int?
}

// MARK: - MemberRecord
struct MemberRecord: Codable {
    var id: Int?
    var name: String?
    var image: String?
    var username: String?
    var isFollowers, isFollowing: Bool?
    var creationDate: String?
    var isAdmin: Bool?
    var followers, following, popularity, unReadMessages: Int?
    var userID: Int?
    var isUsers, isOnline: Bool?

}
