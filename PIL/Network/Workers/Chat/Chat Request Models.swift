//
//  Chat Request Models.swift
//  PIL
//
//  Created by <PERSON> on 11/21/21.
//

import Foundation

struct ChatRoomModel: Encodable{
    var members: ChatMembersModel
    var messages: MessageModel
}

struct ChatMembersModel: Encodable{
    var user1ID: String
    var user2ID: String
}

class MessageModel: Codable{
    var senderID: String
    var message: String?
    var time: Int64?
    var delivered: Bool
    var seen: Bool
    
    init(senderID: String, message: String?, time: Int64?, delivered: Bool, seen: Bool) {
        self.senderID = senderID
        self.message = message
        self.time = time
        self.delivered = delivered
        self.seen = seen
    }
}

//MARK: - Chat Notification

struct ChatNotificationModel: Codable{
    var to: String // destination user token
    var notification: ChatNotificationDataModel
    var data: ChatNotificationAdditionalModel
}

struct ChatNotificationDataModel: Codable{
    var title: String
    var body: String
    var sound: String = "default"
}

struct ChatNotificationAdditionalModel: Codable{
    var navigation_to: String
    var other_user_id: String
    var room_id: String
}

