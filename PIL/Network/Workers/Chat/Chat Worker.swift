//
//  Chat Worker.swift
//  PIL
//
//  Created by <PERSON> on 11/21/21.
//

import Foundation
import FirebaseCore
import FirebaseDatabase
import CodableFirebase
import Alamofire

class ChatWorker: ChatWorkerProtocol{
    static let shared = ChatWorker()
//    private let ref = Database.database().reference()
    
    
    // ------------- chats rooms ----------------
    
    func getChatRooms(userID: String, pagination: PaginationRequestModel, compilition: @escaping (ChatResult<ChatRoomsModel, String>) -> Void) {
//        Indicator.shared.showProgressView()
//        let userChatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID)
//        var roomsList = [ChatRoomsDataModel]()
//        var total = 0
//
//        userChatRoomsRef.observe(.value) { data in
//            total = Int(data.childrenCount)
//
//            let limitedItemsNumber = (pagination.page) * (pagination.perPage)
//            userChatRoomsRef.queryLimited(toLast: UInt(limitedItemsNumber)).observe(.value) { (data) in
//                roomsList.removeAll()
//                if let data = data.value as? [String: Any]{
//                    roomsList.removeAll()
//                    for (key,value) in data{
//
//                        do{
//                            let room = try FirebaseDecoder().decode(ChatRoomsDataModel.self, from: value)
//                            room.chatID = key
//                            if let _ = room.lastMessage, let _ = room.member, let _ = room.unreadCount{
//                                roomsList.append(room)
//                            }
//                        }catch{
//                            Indicator.shared.hideProgressView()
//                            compilition(.fail(error.localizedDescription))
//                        }
//                    }
//                }
//
//                Indicator.shared.hideProgressView()
//                compilition(.success(ChatRoomsModel(total: total, rooms: roomsList)))
//            }
//        }
    }
    
    func searchInChatRooms(userID: String, by key: String, compilition: @escaping (ChatResult<[ChatRoomsDataModel], String>) -> Void) {
//        Indicator.shared.showProgressView()
//        let userChatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID)
//        var roomsList = [ChatRoomsDataModel]()
//        let keyToObserve = "\(ChatKeys.member.rawValue)/\(ChatKeys.memberName.rawValue)"
//        userChatRoomsRef.queryOrdered(byChild: keyToObserve).queryStarting(atValue: key).queryEnding(atValue: key+"\u{f8ff}").observeSingleEvent(of: .value) { data in
//            if let data = data.value as? [String: Any]{
//                roomsList.removeAll()
//                for (key,value) in data{
//                    do{
//                        let room = try FirebaseDecoder().decode(ChatRoomsDataModel.self, from: value)
//                        room.chatID = key
//                        roomsList.append(room)
//
//                    }catch{
//                        Indicator.shared.hideProgressView()
//                        compilition(.fail(error.localizedDescription))
//                    }
//                }
//            }
//
//            Indicator.shared.hideProgressView()
//            compilition(.success(roomsList))
//        }
    }
    
    func removeChatRoomsObservers(userID: String) {
//        let userChatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID)
//        userChatRoomsRef.removeAllObservers()
    }
    
    // ------------- chat room ----------------
    
    func getChatID(meID: String, with userID: String, compilition: @escaping (ChatResult<String, String?>) -> Void) {
//        let myChatRoomKey = ref.child(ChatKeys.chatRooms.rawValue).child(meID)
//        let keyToObserve = "\(ChatKeys.member.rawValue)/\(ChatKeys.memberID.rawValue)"
//        myChatRoomKey.queryOrdered(byChild: keyToObserve).queryEqual(toValue: userID).observeSingleEvent(of: .value) { data in
//            if let data = data.value as? [String:Any]{
//                if let chatKey = data.keys.first{
//                    compilition(.success(chatKey))
//                }else{
//                    compilition(.fail(nil))
//                }
//            }else{
//                compilition(.fail(nil))
//            }
//        }
    }
    
    func getChatMessages(chatID: String,  pagination: PaginationRequestModel, compilition: @escaping (ChatResult<ChatMessagesModel, String>) -> Void) {
//        Indicator.shared.showProgressView()
//        let chatKey = ref.child(ChatKeys.chats.rawValue).child(chatID).child(ChatKeys.ChatMessages.rawValue)
//        var messages = [MessageModel]()
//        var total = 0
//
//        chatKey.observe(.value){ (data) in
//            total = Int(data.childrenCount)
//
//            let limitedItemsNumber = (pagination.page) * (pagination.perPage)
//            chatKey.queryLimited(toLast: UInt(limitedItemsNumber)).observe(.value) { (data) in
//                if let data = data.value as? [String: Any]{
//                    messages.removeAll()
//                    for (_,value) in data{
//                        do{
//                            let room = try FirebaseDecoder().decode(MessageModel.self, from: value)
//                            messages.append(room)
//                        }catch{
//                            Indicator.shared.hideProgressView()
//                            compilition(.fail(error.localizedDescription))
//                        }
//                    }
//                }
//
//                Indicator.shared.hideProgressView()
//                compilition(.success(ChatMessagesModel(total: total, messages: messages)))
//            }
//        }
    }
    
    func removeChatObservers(chatID: String) {
//        let chatKey = ref.child(ChatKeys.chats.rawValue).child(chatID).child(ChatKeys.ChatMessages.rawValue)
//        chatKey.removeAllObservers()
    }
    
    func getChatMemberID(chatID: String, myID: String, compilition: @escaping (ChatResult<String, String>) -> Void) {
//        let chatRef = ref.child(ChatKeys.chats.rawValue).child(chatID).child(ChatKeys.members.rawValue)
//        chatRef.observeSingleEvent(of: .value) { (data) in
//            if let values = data.value as? [String: Any]{
//                for (_,value) in values{
//                    if let userID = value as? String{
//                        if userID != myID{
//                            compilition(.success(userID))
//                        }
//                    }
//                }
//            }
//        }
    }
    
    func createChat(chatModel: ChatRoomModel, compilition: @escaping (ChatResult<String,String>) -> Void){
//        let usersKey = ref.child(ChatKeys.chats.rawValue).childByAutoId()
//        let chatIDLink = URL(string: "\(usersKey)")!
//        let chatID = chatIDLink.lastPathComponent
//
//
//        do {
//            let membersObj = try FirebaseEncoder().encode(chatModel.members)
//            usersKey.child(ChatKeys.members.rawValue).setValue(membersObj)
//
//            let messagesObj = try FirebaseEncoder().encode(chatModel.messages)
//            usersKey.child(ChatKeys.ChatMessages.rawValue).childByAutoId().setValue(messagesObj)
//
//            compilition(.success(chatID))
//
//        } catch let error {
//            compilition(.fail(error.localizedDescription))
//        }
    }
    
    func updateChat(chatID: String, message: MessageModel, compilition: @escaping (ChatResult<Bool,String>) -> Void){
//        let usersKey = ref.child(ChatKeys.chats.rawValue).child(chatID)
//
//        do {
//
//            let messagesObj = try FirebaseEncoder().encode(message)
//            usersKey.child(ChatKeys.ChatMessages.rawValue).childByAutoId().setValue(messagesObj)
//
//            compilition(.success(true))
//
//        } catch let error {
//            compilition(.fail(error.localizedDescription))
//        }
    }
    
    func setMessageIn(userID: String, chatID: String, memberID: String, message: MessageModel, compilition: @escaping (ChatResult<Bool,String>) -> Void){
        
//        let meChatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID).child(chatID)
//
//        let userKey = ref.child(ChatKeys.users.rawValue).child(memberID)
//
//        userKey.observeSingleEvent(of: .value) { data in
//            if let data = data.value{
//                do{
//                    let user = try FirebaseDecoder().decode(UserChatModel.self, from: data)
//                    let messagesObj = try FirebaseEncoder().encode(message)
//
//                    if message.seen{
//                        meChatRoomsRef.child(ChatKeys.unReadCount.rawValue).setValue(0)
//                    }else{
//                        meChatRoomsRef.child(ChatKeys.unReadCount.rawValue).observeSingleEvent(of: .value) { (data) in
//                            if let count = data.value as? Int{
//                                meChatRoomsRef.child(ChatKeys.unReadCount.rawValue).setValue(count+1)
//                            }else{
//                                meChatRoomsRef.child(ChatKeys.unReadCount.rawValue).setValue(1)
//                            }
//                        }
//                    }
//
//                    meChatRoomsRef.child(ChatKeys.lastMessage.rawValue).setValue(messagesObj)
//                    meChatRoomsRef.child(ChatKeys.member.rawValue).child(ChatKeys.memberID.rawValue).setValue(memberID)
//                    meChatRoomsRef.child(ChatKeys.member.rawValue).child(ChatKeys.memberName.rawValue).setValue(user.name)
//                    compilition(.success(true))
//
//                }catch{
//                    compilition(.fail(error.localizedDescription))
//                }
//            }
//        }
    }
    
    func seenMessages(chatID: String, userID: String) {
//        let chatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID).child(chatID)
//
//        chatRoomsRef.child(ChatKeys.unReadCount.rawValue).observe(.value) { (data) in
//            chatRoomsRef.child(ChatKeys.unReadCount.rawValue).setValue(0)
//        }
    }
    
    func removeUnReadCountObservers(userID: String, chatID: String) {
//        let chatRoomsRef = ref.child(ChatKeys.chatRooms.rawValue).child(userID).child(chatID)
//        let unreadKey = chatRoomsRef.child(ChatKeys.unReadCount.rawValue)
//        unreadKey.removeAllObservers()
    }
    
    func SendNotification(model: ChatNotificationModel, compilition: @escaping (Result<SuccessModel, AFError>, Int?) -> Void) {
//        Requests.API(indicator: .custom).performRequest(url: ChatKeys.fireBaseServerLink.rawValue, method: .post, RequestModel: model, headersType: .chatNotification, completion: compilition)
    }
    
    
 
    
    //MARK: - NEW Chat
    //Get user profile
    func getUserProfile(model: GetUserInfoRequestModel ,compilition: @escaping (Result<userRecordsModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.profile
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    //Get list Chat
    func getListChat(progress: Bool = true, model: UserChatRecordRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.listChat
        BuddiesCacheService.shared.fetch(nil) { items in
            var indicatorType: IndicatorType?
            if items.isEmpty{
                indicatorType = progress ? .regular : .none
            }else{
                indicatorType = .none
            }
            Requests.API(indicator: indicatorType).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
        }
    }
    
    //Get chat user
    func getChat(model: UserChatRecordRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.chatURL
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    // Delete Chat
    func deleteChatUser(model: DeleteChatUserRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.deleteChatUser
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .appInfo, completion: compilition)
    }
    
    // Delete Group
    func deleteGrouptUser(model: DeleteGtoupUserRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.deleteGroupUser
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func addUserAction<T: Encodable>(_ payload: T, _ completion: @escaping UserActionCompletion) {
        let url = URls.init().userChatAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func leaveGrouptUser(model: LeaveGtoupUserRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.leaveGroup
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func createGroup( model: CreateGroupRM, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<CreateGroupModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.CreateGroup)"
        var urlKey = [String]()
        
        if !(fileName.isEmpty){
            urlKey = ["GroupRecord.ImageFile"]
        }
        
        let parameters:Parameters = [
            "GroupRecord.Name":model.GroupRecord?.Name ?? "",
            "GroupRecord.Title": model.GroupRecord?.Title ?? "",
            "GroupRecord.CreatedName":UserModel.shared.get_name() ,
            "GroupRecord.CreatedImageUrl":UserModel.shared.get_image(),
            "CreatedBy": UserModel.shared.get_id(),
            "GroupRecord.GroupMemberIds": model.GroupRecord?.GroupMemberIds ?? []
        ]
        
        do{
//            let parameters = try JSONSerialization.jsonObject(with: JSONEncoder().encode(model), options: .allowFragments) as? [String: String]
            print("Parameters",parameters)
            Requests.API().performRequest(url: url, method: .post, parameters: parameters, headersType: .multipart, fileUrlKey: urlKey, files: fileData, filesNames: fileName, mimeTypes: mimeType, completion: compilition)
        }catch{
            // erorr
            compilition(.failure(AFError.multipartEncodingFailed(reason: .inputStreamReadFailed(error: Error.self as! Error))) , 400)
        }
    }
    
    
    func uploadImage( fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<UploadImageModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.UploadImage)"
        var urlKey = [String]()
        if !(fileName.isEmpty){
            urlKey = ["ImageFile"]
        }

        do{
            Requests.API(indicator: .none).performRequest(url: url, method: .post, parameters: nil, headersType: .multipart, fileUrlKey: urlKey, files: fileData, filesNames: fileName, mimeTypes: mimeType, completion: compilition)
        }catch{
            // erorr
            compilition(.failure(AFError.multipartEncodingFailed(reason: .inputStreamReadFailed(error: Error.self as! Error))) , 400)
        }
    }
    
    
    func getGroupInfo(model: GetGroupRM ,compilition: @escaping (Result<CreateGroupModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.GetGroupInfo
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func getMembersGroup(model: MembersGroupRM ,compilition: @escaping (Result<membersGroupModel, AFError>,_ statusCde: Int?) -> Void){
        let url = Requests.url.GroupMember
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func addMembersGroup(model: Payloads.InvitePlayersPayload, compilition: @escaping (Result<membersGroupModel, AFError>, Int?) -> Void) {
        let url = Requests.url.inviteMember
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func sendGift(model: Parameters, compilition: @escaping (Result<Response2, AFError>, Int?) -> Void) {
        let url = Requests.url.sendGift
        Requests.API(indicator: .none).performRequest(url: url, method: .post, parameters: model, headersType: .token, fileUrlKey: [], files: [], filesNames: [], mimeTypes: [], completion: compilition)
    }
    
    func deleteChat(model: Payloads.UserChatActionRecordPayload, compilition: @escaping (Result<UserResponses.UserChatRecordsResponse, AFError>, Int?) -> Void) {
        let url = Requests.url.chatDeleteAction
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func deleteMessage(model: Payloads.UserChatActionRecordPayload, compilition: @escaping (Result<UserResponses.UserChatRecordsResponse, AFError>, Int?) -> Void) {
        let url = Requests.url.messageDeleteAction
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func editChat(model: Payloads.UserChatActionRecordPayload, compilition: @escaping (Result<UserResponses.UserChatRecordsResponse, AFError>, Int?) -> Void) {
        let url = Requests.url.chatEditAction
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func listUserAction<T: Encodable>(_ payload: T, _ completion: @escaping ListUserActionCompletion) {
        let url = URls.init().listUserChatAction
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func listReacts<T: Encodable>(_ payload: T, _ completion: @escaping ListUserActionCompletion) {
        let url = URls.init().listUserActionEmojies
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: payload, headersType: .token, completion: completion)
    }
    
    func getChallange(_ id: Int, compilition: @escaping (Result<ServerResponse<Tournament>, AFError>, Int?) -> Void) {
        let url = Requests.url.contest + "\(id)"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let countryCode  = URLQueryItem(name: "country_code", value: UserModel.shared.get_countryCode())
        let player = URLQueryItem(name: "playerId", value: UserModel.shared.get_id())

        let endPoint =  url.addQueryParameters(queries: [langQuery , platform , version, countryCode, player])
        Requests.API().performRequest(url: endPoint, method: .get, headersType: .token, completion: compilition)
    }
    
    func undo(model: Payloads.UserChatActionRecordPayload ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void) {
        let url = Requests.url.undoDeletion
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
}
