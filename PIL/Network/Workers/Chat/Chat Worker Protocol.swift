//
//  Chat Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 11/21/21.
//

import Foundation
import Alamofire

protocol ChatWorkerProtocol{
    
    // ------------- chats rooms ----------------
    /// get all chat rooms of specific user
    /// - Parameters:
    ///   - userID: user id
    ///   - pagination: pagination of the list
    ///   - compilition: response handler
    func getChatRooms(userID: String, pagination: PaginationRequestModel, compilition: @escaping (ChatResult<ChatRoomsModel,String>) -> Void)
    
    /// search in list of user rooms with sub string of thier names
    /// - Parameters:
    ///    - userID: user id
    ///    - by Key: the serach subString that you search with
    ///    - compilition: response handler
    func searchInChatRooms(userID: String, by key: String, compilition: @escaping (ChatResult<[ChatRoomsDataModel],String>) -> Void)
    
    /// stop observe on chat messages
    /// - Parameters:
    ///   - userID: user id
    func removeChatRoomsObservers(userID: String)
    
    
    // ------------- chat room ----------------
    
    /// get the chat id if it is exist depending on user id
    /// - Parameters:
    ///    - meID: my id
    ///    - userID: the user ID that i chat with
    ///    - compilition: response handler
    func getChatID(meID: String, with userID: String, compilition: @escaping (ChatResult<String,String?>) -> Void)
    
    /// get chat messages
    /// - Parameters:
    ///    - chatID: user id
    ///    - pagination: pagination of the list
    ///    - compilition: response handler
    func getChatMessages(chatID: String, pagination: PaginationRequestModel, compilition: @escaping (ChatResult<ChatMessagesModel,String>) -> Void)
    
    /// stop observe on chat messages
    /// - Parameters:
    ///   - chatID: chat id
    func removeChatObservers(chatID: String)
    
    /// get the id of the user that i chat with
    /// - Parameters:
    ///   - chatID: chat id
    ///   - myID: my id
    ///   - compilition: response handler
    func getChatMemberID(chatID: String, myID: String, compilition: @escaping (ChatResult<String,String>) -> Void)
    
    /// create chat for the first time
    /// - Parameters:
    ///   - chatModel: chat components
    ///   - compilition: response handler
    func createChat(chatModel: ChatRoomModel, compilition: @escaping (ChatResult<String,String>) -> Void)
    
    /// send new message for existing chat
    /// - Parameters:
    ///   - chatID: chat id
    ///   - userIDs: users Id that
    ///   - message: message content info object
    ///   - compilition: response handler
    func updateChat(chatID: String, message: MessageModel, compilition: @escaping (ChatResult<Bool,String>) -> Void)
    
    /// put my messages seen
    /// - Parameters:
    ///   - chatID: chat id
    ///   - userID: member of the chat
    func seenMessages(chatID: String, userID: String)
    
    /// set the new message in room of two users
    /// - Parameters:
    ///   - userID: member of the chat
    ///   - chatID: chat id
    ///   - message: message content info model
    ///   - compilition: handler
    func setMessageIn(userID: String, chatID: String, memberID: String, message: MessageModel, compilition: @escaping (ChatResult<Bool,String>) -> Void)
    
    /// stop observing on unread messages count on ropm
    /// - Parameters:
    ///   - userID: member of the chat
    ///   - chatID: chat id
    func removeUnReadCountObservers(userID: String, chatID: String)
    
    /// send Notification to specific user
    ///  - Parameter model: the notification model
    ///  - Parameter compilition: handler
    func SendNotification(model: ChatNotificationModel, compilition: @escaping (Result<SuccessModel, AFError>, Int?) -> Void)
    
    
    
    
    
    
    //MARK: - Chat New Signal R
    
    /// get user profile
    /// - Parameters:
    ///   - model: user info request
    ///   - completion: user records model, alamofire error and status code
    func getUserProfile ( model: GetUserInfoRequestModel , compilition: @escaping (Result<userRecordsModel,AFError>,_ statusCde: Int?) -> Void)
    /// get user chat list buddies
    /// - Parameters:
    ///   - progress: request progress indicator
    ///   - model:  user chat record request moddel
    ///   - completion: list user chat model, alamofire error and status code
    func getListChat(progress: Bool, model: UserChatRecordRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void)
    /// get conversation
    /// - Parameters:
    ///   - model: user chat record request model
    ///   - completion:list user chat model, alamofire error and status code
    func getChat(model: UserChatRecordRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void)
    /// delete chat
    /// - Parameters:
    ///   - model: delete user chat record request model
    ///   - completion:list user chat model, alamofire error and status code
    func deleteChatUser(model: DeleteChatUserRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void)
    /// create group
    /// - Parameters:
    ///   - model: create group request model
    ///   - fileName: array of strings
    ///   - fileDate: array of data
    ///   - mimeType: array of strings
    ///   - completion:create group model, alamofire error and status code
    func createGroup( model: CreateGroupRM, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<CreateGroupModel, AFError>, Int?) -> Void)
    /// upload image
    /// - Parameters:
    ///   - fileName: array of strings
    ///   - fileDate: array of data
    ///   - mimeType: array of strings
    ///   - completion:upload image model, alamofire error and status cod
    func uploadImage( fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<UploadImageModel, AFError>, Int?) -> Void)
    /// get group info
    /// - Parameters:
    ///   - model: get group request model
    ///   - completion: create group model, alamofire error and status code
    func getGroupInfo(model: GetGroupRM ,compilition: @escaping (Result<CreateGroupModel, AFError>,_ statusCde: Int?) -> Void)
    /// leave group
    /// - Parameters:
    ///   - model: leave group request model
    ///   - completion: create group model, alamofire error and status code
    func leaveGrouptUser(model: LeaveGtoupUserRM ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void)
    /// get group members
    /// - Parameters:
    ///   - model: member group request model
    ///   - completion: members group model, alamofire error and status code
    func getMembersGroup(model: MembersGroupRM ,compilition: @escaping (Result<membersGroupModel, AFError>,_ statusCde: Int?) -> Void)
    /// add group members
    /// - Parameters:
    ///   - model: invite players model
    ///   - completion: members group model, alamofire error and status code
    func addMembersGroup(model: Payloads.InvitePlayersPayload, compilition: @escaping (Result<membersGroupModel, AFError>,_ statusCde: Int?) -> Void)
    /// send gifts
    /// - Parameters:
    ///   - model: alamofire parameters
    ///   - completion: server response 2l, alamofire error and status code
    func sendGift(model: Parameters, compilition: @escaping (Result<Response2, AFError>,_ statusCde: Int?) -> Void)
    /// add user action
    /// - Parameters:
    ///   - payload: generic encodable
    ///   - completion: user action completion
    func addUserAction<T: Encodable>(_ payload: T,_ completion: @escaping UserActionCompletion)
    /// list user action
    /// - Parameters:
    ///   - payload: generic encodable
    ///   - completion:list  user action completion
    func listUserAction<T: Encodable>(_ payload: T,_ completion: @escaping ListUserActionCompletion)
    /// get reacts
    /// - Parameters:
    ///   - payload: generic encodable
    ///   - completion: list user action completion
    func listReacts<T: Encodable>(_ payload: T,_ completion: @escaping ListUserActionCompletion)
    /// get challange
    /// - Parameters:
    ///   - id: int
    ///   - completion: tournament model, alamofire error and status code
    func getChallange(_ id: Int, compilition: @escaping (Result<ServerResponse<Tournament>, AFError>, Int?) -> Void)
    ///  unfo delete
    /// - Parameters:
    ///   - model: user chat action record payload
    ///   - completion:  list user chat model, alamofire error and status code
    func undo(model: Payloads.UserChatActionRecordPayload ,compilition: @escaping (Result<listUserChatModel, AFError>,_ statusCde: Int?) -> Void)
}

