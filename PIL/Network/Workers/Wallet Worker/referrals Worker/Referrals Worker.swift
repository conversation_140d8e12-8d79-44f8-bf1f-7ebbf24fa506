//
//  ReferralsWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/9/21.
//

import Foundation
import Alamofire

class ReferralsWorker : ReferralsWorkerProtocols{
    
    func getReferrals(userID:String ,compilition: @escaping (Result<ReferralsModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.referralsURL)/\(userID)/referrals"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    
}
