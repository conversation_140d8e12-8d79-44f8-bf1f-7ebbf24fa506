//
//  Referrals Worker Protocols.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/9/21.
//

import Foundation
import Alamofire

protocol  ReferralsWorkerProtocols {
     
    
    /// get all referrals
    /// - Parameters:
    ///   - userID: get user id
    ///   - compilition: 
    func getReferrals(userID:String ,compilition: @escaping (Result<ReferralsModel, AFError>,_ statusCode: Int?) -> Void)

}
