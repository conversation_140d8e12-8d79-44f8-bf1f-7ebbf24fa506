//
//  ReferralsModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/9/21.
//

import Foundation
// MARK: - ReferralsModel
class ReferralsModel: Codable {
    var status: Bool?
    var message: String?
    var data: [ReferralsData]?
 
}

// MARK: - Datum
class ReferralsData: Codable {
    var createdAt: String?
    var bio: String?
    var referralCode, referral, email, name: String?
    var referrallink:String?
    var gender: String?
    var updatedAt: String?
    var password: String?
    var birthdate: String?
    var pilID, username, datumID, id: String?
    var phone: String?

    enum CodingKeys: String, CodingKey {
        case createdAt = "created_at"
        case bio, referralCode, referral, email, name, gender , referrallink
        case updatedAt = "updated_at"
        case password, birthdate
        case pilID = "pil_id"
        case username
        case datumID = "id"
        case id = "ID"
        case phone
    }

 
}
