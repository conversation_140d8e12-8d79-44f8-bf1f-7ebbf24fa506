//
//  Incentives Worker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/18/21.
//

import Foundation
import Alamofire

class IncentivesWorker: IncentivesWorkerProtocol{


    func getMyIncentive(model: MyIncentiveWorkerRquestModel, compilition: @escaping (Result<MyTicketsModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.getIncentiveUserURl)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func getIncentives(model: IncentiveWorkerRquestModel, compilition: @escaping (Result<IncentiveModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.GetFilterdURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func redeemRequest(model: IncentiveRecord, compilition: @escaping (Result<IncentivesRedeemModel, AFError>, Int?) -> Void) {
        let userID = UserModel.shared.get_id()
        let url = "\(Requests.url.redeemIncentivesURL)/\(userID)/redeemCards"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    //MARK: - add incentive
    func AddIncentiveUser(model: AddIncentiveRequestModel, compilition: @escaping (Result<IncentiveModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.AddIncentiveURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
 
    //MARK: - add incentive Coupon
    func AddIncentiveUserCoupon(model: AddIncentiveCouponRequestModel, compilition: @escaping (Result<IncentiveModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.AddIncentiveURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    //MARK: - get wheel
    func getWheel(model:WheelRequestModel,compilition: @escaping (Result<IncentiveModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.GetFilterdURL)"
        Requests.API(indicator: .custom).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }

    func CheckWheel(model: WheelCheclRequestModel, compilition: @escaping (Result<WheelCheclModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.wheelCheckURL)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
 }
