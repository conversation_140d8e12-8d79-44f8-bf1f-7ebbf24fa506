//
//  Incentives Redeem Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/20/21.
//

import Foundation

// MARK: - AddIncentiveRequestModel
class IncentivesRedeemModel: Codable {
    var status: Bool?
    var message: String?
    var data: DataReddemModel?
 
}

// MARK: - DataClass
class DataReddemModel: Codable {
    var id: String?
    var details: DetailsReddemModel?
    var userID: String?
    var coins: Int?
    var updatedAt, createdAt: String?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case details
        case userID = "user_id"
        case coins
        case updatedAt = "updated_at"
        case createdAt = "created_at"
    }
 
}

// MARK: - Details
class DetailsReddemModel: Codable {
    var winning, despoite, bouns: Int?
 
}
