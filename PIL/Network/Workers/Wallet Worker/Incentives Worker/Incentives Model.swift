//
//  TicketsModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/18/21.
//

import Foundation

// MARK: - TicketsModel
class IncentiveModel: Codable {
    var incentiveRecords: [IncentiveRecord]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount, pageCount: Int?
}

// MARK: - IncentiveRecord
class IncentiveRecord: Codable, Comparable {
    
    
    var id: Int?
    var userID: String?
    var incentiveID: Int?
    var code, expireDate: String?
    var amount, statusID: Int?
    var isRedeem: Bool?
    var createdBy, creationDate: String?
    var isDevared: Bool?
    var modificationDate, modifiedBy, imageURL: String?
    var name: String?
    var priceTypeID, typeID: Int?
    var isCoins, isFixed, isPercent, isWeel: Bool?
    var maxUsageCount: Int?
    var statusName: String?
    var isExpired: Bool?
    var expiredInDays:Int?
    var descriptionIncentive:String?
    var typeName:String?
    var priceTypeName:String?
    var price:Int?
    var expireAfterApplyDate:String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case userID = "userId"
        case incentiveID = "incentiveId"
        case code, expireDate, amount
        case statusID = "statusId"
        case isRedeem, createdBy, creationDate, isDevared, modificationDate, modifiedBy
        case imageURL = "imageUrl"
        case name , priceTypeName
        case priceTypeID = "priceTypeId"
        case typeID = "typeId"
        case isCoins, isFixed, isPercent, isWeel, maxUsageCount, statusName, isExpired
        case expiredInDays
        case descriptionIncentive = "description"
        case typeName
        case price
        case expireAfterApplyDate
    }
    
    static func == (lhs: IncentiveRecord, rhs: IncentiveRecord) -> Bool {
        return (lhs.id ?? 0) == (rhs.id ?? 0)
    }
    
    static func < (lhs: IncentiveRecord, rhs: IncentiveRecord) -> Bool {
        guard let lhsDate = (lhs.creationDate ?? "").getDate(currentFormate: "yyyy-MM-ddTHH:mm:ss", from: "en"),
              let rhsDate = (rhs.creationDate ?? "").getDate(currentFormate: "yyyy-MM-ddTHH:mm:ss", from: "en") else{
                  return false
              }
        return lhsDate < rhsDate
    }
}



class MyTicketsModel:Codable{
    var incentiveUserRecords: [IncentiveRecord]?
    var message: String?
    var success: Bool?
    var statusCode, totalCount, pageCount: Int?
}


class WheelCheclModel:Codable{
    var message: String?
    var status: Bool?
    var error:String?
    var code:Int?
}
