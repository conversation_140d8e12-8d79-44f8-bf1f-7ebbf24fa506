//
//  Incentives Worker Protocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/18/21.
//

import Foundation
import Alamofire

protocol IncentivesWorkerProtocol{
    
    
    ///   get tickets
    /// - Parameters:
    ///   - typeID: (2 , tickets)  --- (3,Scratch)
    ///   - compilition: request compilition
    func getIncentives(model: IncentiveWorkerRquestModel, compilition: @escaping (Result<IncentiveModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// add incentives
    /// - Parameters:
    ///   - model: send user id and incentive id
    ///   - compilition: request compilition
    func AddIncentiveUser(model: AddIncentiveRequestModel, compilition: @escaping (Result<IncentiveModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// add incentive coupon
    /// - Parameters:
    ///   - model: send code coupon , user ID , (type id -> 1 coupons)
    ///   - compilition: request compilition
    func AddIncentiveUserCoupon(model:AddIncentiveCouponRequestModel, compilition: @escaping (Result<IncentiveModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// get my incentive
    /// - Parameters:
    ///   - model: take user id and type id -> (2, tickets)  --- (3,Scratch)
    ///   - compilition: request compilition
    func getMyIncentive(model: MyIncentiveWorkerRquestModel, compilition: @escaping (Result<MyTicketsModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// get wheel
    /// - Parameters:
    ///   - model: have is wheel ->true
    ///   - compilition: request compilition
    func getWheel(model:WheelRequestModel,compilition: @escaping (Result<IncentiveModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// send reddem
    /// - Parameters:
    ///   - model:
    ///   - compilition:  request compilition
    func redeemRequest(model:IncentiveRecord, compilition: @escaping (Result<IncentivesRedeemModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    /// check wheel if avalible today or no
    /// - Parameters:
    ///   - model: user id - is pain
    ///   - compilition:  request compilition
    func CheckWheel(model:WheelCheclRequestModel, compilition: @escaping (Result<WheelCheclModel, AFError>,_ statusCode: Int?) -> Void)
}
