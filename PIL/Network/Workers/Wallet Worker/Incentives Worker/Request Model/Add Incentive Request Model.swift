//
//  Add Incentive Request Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/19/21.
//

import Foundation


struct AddIncentiveRequestModel: Encodable{
    let incentiveUserRecord: AddncentiveUserRecord
 
    }

// MARK: - IncentiveUserRecord
struct  AddncentiveUserRecord: Encodable {
    let userID: String
    let incentiveID: Int
    let isDouble:Bool
    
    enum CodingKeys: String, CodingKey {
         case userID = "userId"
        case incentiveID = "incentiveId"
        case isDouble
    }
 
}

// MARK: - AddIncentiveCouponRequestModel
struct AddIncentiveCouponRequestModel: Codable {
    let incentiveUserRecord: AddIncentiveUserRecordCoupon
}

// MARK: - IncentiveUserRecord
struct AddIncentiveUserRecordCoupon: Codable {
    let userID, code: String
    let typeID: Int

    enum CodingKeys: String, CodingKey {
        case userID = "userId"
        case code
        case typeID = "typeId"
    }
}
