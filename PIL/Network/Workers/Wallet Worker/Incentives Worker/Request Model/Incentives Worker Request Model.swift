//
//  IncentiveWorkerRquestModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/18/21.
//

import Foundation

 
struct IncentiveWorkerRquestModel: Encodable{
     let incentiveRecord: IncentiveRecordRquestModel
 }

// MARK: - IncentiveRecord
struct IncentiveRecordRquestModel: Codable {
    let typeId: Int
}




struct MyIncentiveWorkerRquestModel: Encodable{
     let incentiveUserRecord: MyIncentiveRecordRquestModel
 }

// MARK: - IncentiveRecord
struct MyIncentiveRecordRquestModel: Codable {
    let typeId: Int
    let userId:String
}


