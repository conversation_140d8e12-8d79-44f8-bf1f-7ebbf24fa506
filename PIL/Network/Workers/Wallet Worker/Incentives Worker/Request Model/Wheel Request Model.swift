//
//  WheelRequestModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/24/21.
//

import Foundation
// MARK: - WheelRequestModel
struct WheelRequestModel: Encodable {
    let incentiveRecord: WheelIncentiveRecord

   
}

// MARK: - IncentiveRecord
struct WheelIncentiveRecord: Encodable {
    let isWheel: Bool
 
}


struct WheelCheclRequestModel:Encodable {
    var user_id:String?
    var is_spin:Bool?
}
