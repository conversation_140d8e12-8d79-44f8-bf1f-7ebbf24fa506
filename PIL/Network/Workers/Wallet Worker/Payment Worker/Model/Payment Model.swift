//
//  Payment Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/5/21.
//

import Foundation

// MARK: - PaymentModel
struct AllPaymentModel: Codable {
    var status: Bool?
    var message: String?
    var data: [PaymentData]?
 
}

// MARK: - Datum
struct  PaymentData: Codable {
    var id, name, key, datumDescription: String?
    var photo: String?
    var updatedAt, createdAt: String?
    var selected: Bool?
    var creditNumber: String?
    var month, year: String?
    var saved: Bool?
//    var cardData: cardData?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case name, key
        case datumDescription = "description"
        case photo, selected
        case updatedAt = "updated_at"
        case createdAt = "created_at"
        case creditNumber
        case month
        case year
        case saved
    }

 
}


// MARK: - PostModel
//credit
//{
//   "status":true,
//   "message":"",
//   "data":{
//      "details":{
//         "deposit":25
//      },
//      "user_id":"d44abcb0-9d3e-46dd-8903-3c2d5783ed4d",
//      "coins":25,
//      "updated_at":"2022-04-24 04:41:45",
//      "created_at":"2022-04-24 04:41:45",
//      "_id":"6264d509a15cec2814400382"
//   }
//}

// fawry
//{
//   "status":true,
//   "data":{
//      "message":"Operation done successfully",
//      "responsecode":200,
//      "body":{
//         "referenceNumber":"7100656366",
//         "orderAmount":5,
//         "paymentAmount":5,
//         "fawryFees":0,
//         "orderStatus":"UNPAID",
//         "customerName":"pil_62559fb482788",
//         "customerMobile":"01060635602",
//         "customerMail":"<EMAIL>"
//      },
//      "needVerify":false,
//      "verify_url":""
//   },
//   "transactionId":"6264d697f4a4e04ccf11ba25"
//}


//jawwal
//{
//    "status": true,
//    "msg": "Success",
//    "url": "http://jawwal.mediaworldiq.com/dcb/API/VMSOneTimePurchase/actions/sendPincode?user=grand&password=gr0nd22!&msisdn=01065875651&shortcode=37788&serviceId=11087&spId=5225",
//    "data": {
//        "status": true,
//        "msg": "Success",
//        "url": "http://jawwal.mediaworldiq.com/dcb/API/VMSOneTimePurchase/actions/sendPincode?user=grand&password=gr0nd22!&msisdn=01065875651&shortcode=37788&serviceId=11087&spId=5225",
//        "needVerify": true,
//        "verify_url": "https://api.pil.live/api/wallet/userPurchases"
//    }
//}

struct UserPurchasesModel: Codable {
    var status: Bool?
    var message, msg, error: String?
    var data: dataUserPurchasesModel?
    // jawwal
    var transactionId: String?
}

// MARK: - DataClass
struct dataUserPurchasesModel: Codable {
    // credit
    var details: detailsUserPurchasesModel?
    var user_id: String?
    var coins: Int?
    
    //fawry response
    var message: String?
    var responsecode: Int?
    var body: BodyUserPurchasesModel?
    
    //jawal
    var msg: String?
    var needVerify: Bool?
}

// MARK: - Body
struct BodyUserPurchasesModel: Codable {
    var referenceNumber: String?
    var orderAmount, paymentAmount, fawryFees: Int?
    var orderStatus, customerName, customerMobile, customerMail: String?
}

struct detailsUserPurchasesModel: Codable{
    var deposit: Int?
}


//MARK: - perchase history model
struct PaymentHistoryModel: Codable {
    var status: Bool?
    var message: String?
    var data: [PaymentHistoryDataModel]?
 
}

struct PaymentHistoryDataModel: Codable{
    var transaction_id: String?
    var refNumber: String?
    var payment_status: String?
    var payment_type: String?
    var date: String?
    var amount: Int?
}
