//
//  Payment User Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/5/21.
//

import Foundation
// MARK: - PaymentUserModel
class PaymentUserModel: Codable {
    var status: Bool?
    var message: String?
    var data: [PaymentUserDataModel]?
 
}

// MARK: - Data
class PaymentUserDataModel:Codable {
    var id, userID, paymentID, creditNumber: String?
    var firstSixDigits, lastFourDigits: String?
    var month, year: String?
    var cardType, key: String?
    var saved: Bool?
    var updatedAt, createdAt: String?
    var selected: Bool?
    var paymentMethodObj: paymentMethodModel?
    var holderName, mobile, email: String?
    var packages: [paymentPackages]?
    // for local change
//    var steps: depositPaySteps? = .step1
    var coinsToDeposit: String?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case userID = "user_id"
        case paymentID = "payment_id"
        case creditNumber, month, year, saved, selected, coinsToDeposit
        case updatedAt = "updated_at"
        case createdAt = "created_at"
        case paymentMethodObj = "payment_method_type"
        case firstSixDigits, lastFourDigits, packages, cardType, key
        case holderName, mobile, email
    }

}

class paymentMethodModel: Codable{
    var _id: String?
    var name: String?
    var key: String?
    var description: String?
    var photo: String?
}

class paymentPackages: Codable{
    var _id: String?
    var name: String?
    var type: String?
    var price: String?
    var returnedValue: String?
    var op: String?
    var countryCode: String?
    var serviceName: String?
    var priceStatement: String?
    //local changes
    var selected: Bool?
    
    enum CodingKeys: String, CodingKey {
        case _id
        case name
        case type
        case price
        case returnedValue
        case op = "operator"
        case countryCode
        case priceStatement
        case serviceName, selected
    }
}

//MARK: - add card
class AddPaymentCardModel:Codable{
    var status: Bool?
    var message: String?
    var error: String?
    var data: PaymentUserDataModel?
}



//MARK: - success Deleted

class DeletedPaymentCardModel:Codable{
    var status: Bool?
    var data:String?
    var message: String?
    var error: String?
}
