//
//  Payment Worker Request Models.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/6/21.
//

import Foundation

struct paymentCardRequestModel: Encodable{
    var payment_id: String?
    var creditNumber: String
    var month: String
    var year: String
    var cvv: String
    var holderName:String
    var saved: Int = 1
    var email:String
    var mobile:String
}


struct userPurchasesRequestModel:Encodable{
    var cardType: String?
    var email: String?
    var holderName: String?
    var mobile: String?
    var serviceName: String?
    var totalDeposit: String?
    var user_id: String?
    
    // credit card
    var credit_id: String?
    var cvv: String?
    
    // step 2 in jawal
    var action: String?
    var pin: String?
}
