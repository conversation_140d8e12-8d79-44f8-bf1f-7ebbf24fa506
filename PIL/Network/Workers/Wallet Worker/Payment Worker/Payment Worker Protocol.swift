//
//  Payment Worker Protocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/5/21.
//

import Foundation
import Alamofire

protocol PaymentWorkerProtocol {
   
    
    /// get all type of payment like (visa - master card - fawry)
    /// - Parameter compilition: to get response
    /// - Parameter countryCode: country copde of the account
    func getPayment(countryCode: String, compilition: @escaping (Result<AllPaymentModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// to get all cards user
    /// - Parameter compilition: to get response
    /// - Parameter countryCode: country copde of the account
    /// - Parameter onlyCredit: check if i want all payment or just ceredit cards
    func getUserPayment(userID:String, countryCode: String, onlyCredit: Bool,  compilition: @escaping (Result<PaymentUserModel, AFError>,_ statusCode: Int?) -> Void)
    
 
    
    /// add new card
    /// - Parameters:
    ///     model :  request model for card
    ///   - compilition: compilition description
    func addPaymentCard(userID:String ,  model: paymentCardRequestModel, compilition: @escaping (Result<AddPaymentCardModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    
    /// delete card payment
    /// - Parameters:
    ///   - ID: card id
    ///   - compilition: compilition description
    func deleteCard(ID:String, compilition: @escaping (Result<DeletedPaymentCardModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    
    
    /// update information cars
    /// - Parameters:
    ///   - model: request model for card
    ///   - compilition: compilition description
    func UpdateCard(paymentID: String, model: paymentCardRequestModel, compilition: @escaping (Result<AddPaymentCardModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// deposit user mony for payment in deposit
    ///- Parameters:
    ///   - model: perchuse model
    ///   - compilition: compilition description
    func UserPurchases(model:userPurchasesRequestModel,compilition: @escaping (Result<UserPurchasesModel, AFError>, _ statusCode:Int?)->Void)
    
    /// get the history of payments
    /// - Parameters:
    ///   - userID: id of the user
    func history(userID: String, compilition: @escaping (Result<PaymentHistoryModel, AFError>, _ statusCode:Int?)->Void)

}
