//
//  Payment Worker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/5/21.
//

import Foundation
import  Alamofire

class PaymentWorker:PaymentWorkerProtocol{
    

    /// get all type of payment like (visa - master card - fawry)
    /// - Parameter compilition: get response
    func getPayment(countryCode: String, compilition: @escaping (Result<AllPaymentModel, AFError>, Int?) -> Void) {
        let baseUrl = Requests.url.GetPayment
        let countryCode = URLQueryItem(name: "country_code", value: countryCode)
        let creaditOnly = URLQueryItem(name: "credit_card_only", value: "true")
        let url = baseUrl.addQueryParameters(queries: [countryCode, creaditOnly])
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
        

    }

    
    /// get all user cards
    /// - Parameter compilition: get response
    func getUserPayment( userID:String, countryCode: String, onlyCredit: Bool, compilition: @escaping (Result<PaymentUserModel, AFError>, Int?) -> Void) {
        let baseUrl = "\(Requests.url.GetUserPaymentURL)/\(userID)"
        let countryCode = URLQueryItem(name: "country_code", value: countryCode)
        let creaditOnly = URLQueryItem(name: "credit_card_only", value: onlyCredit ? "true":"false")
        let url = baseUrl.addQueryParameters(queries: [countryCode,creaditOnly])
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
        
    }
    
    
    
    func addPaymentCard(userID:String , model: paymentCardRequestModel, compilition: @escaping (Result<AddPaymentCardModel, AFError>, Int?) -> Void) {
         let url = "\(Requests.url.AddCardPaymentURL)/\(userID)"
       
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func deleteCard(ID: String, compilition: @escaping (Result<DeletedPaymentCardModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.DeleteCardURL)/\(ID)/delete"
        Requests.API().performRequest(url: url, method: .post, headersType: .token, completion: compilition)
    }
    
    
    
    func UpdateCard(paymentID: String, model: paymentCardRequestModel, compilition: @escaping (Result<AddPaymentCardModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.UpdateCardPaymentURL)/\(paymentID)/update"
       
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
 
    
    func UserPurchases(model: userPurchasesRequestModel, compilition: @escaping (Result<UserPurchasesModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.userPurchasesURL, method: .post , RequestModel: model, headersType: .token, completion: compilition)
    }

    
    func history(userID: String, compilition: @escaping (Result<PaymentHistoryModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.paymentHistory)/\(userID)"

        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
}
