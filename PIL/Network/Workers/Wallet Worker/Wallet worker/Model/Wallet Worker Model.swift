//
//  Walvar Worker Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/23/21.
//

import Foundation



// MARK: - AddIncentiveCouponRequestModel
class WalletWorkerModel: Codable {
    var status: Bool?
    var message: String?
    var data: WalletWorkerData?
    
}

// MARK: - DataClass
class WalletWorkerData : Codable {
    var id: String?
    var details: Details?
    var userID: String?
    var coins: Int?
    var playerToken: Int?
    var updatedAt, createdAt: String?
    var lives: Int?
    var piTkensWallet: PiTokensWallet?
    
    enum CodingKeys: String, CodingKey {
        case piTkensWallet = "pi_tokens_wallet"
        case id = "_id"
        case details 
        case playerToken = "pi_tokens"
        case userID = "user_id"
        case coins, lives
        case updatedAt = "updated_at"
        case createdAt = "created_at"
    }
    
}


class PiTokensWallet:Codable{
    var daily:Int?
    var winning:Int?
    var deposit:Int?
    var bonus:Int?
}


// MARK: - Details
class Details: Codable {
    var winning: Int?
    var bonus:Int?
    var deposit:Int?
}





// MARK: - Welcome
class AppPurchasesModel: Codable {
    var status: Bool?
    var message: String?
    var data: typeAppPurchaseModel?
}


class typeAppPurchaseModel: Codable{
    var product: [AppPurchasesDataModel]?
    var subscription: [AppPurchasesDataModel]?
}

struct StoreSearchResult: Codable{
    var products: [ProductSearchResult]?
    var subscription: [SubscriptionSearchResult]?
}


// MARK: - Product
struct ProductSearchResult: Codable {
    let pakageName, title, description, name: String?
    let price: PriceCurrency?
    let status, sku, purchaseType, type: String?
    let icon: String?
    let inApp: String?
    let amount: Int?
    let defaultLanguage, defaultPrice: String?
    let product_id : String?

    enum CodingKeys: String, CodingKey {
        case pakageName, title, description, name, price, status, sku, purchaseType, type, icon , product_id
        case inApp
        case amount, defaultLanguage, defaultPrice
    }
}

// MARK: - Subscription
struct SubscriptionSearchResult: Codable {
    let packageName, productId, basePlanID, state: String?
    let billingPeriodDuration, gracePeriodDuration, resubscribeState, prorationMode: String?
    let title: String?
    let price: [PriceElement]
    let icon: String?
    let inApp, description, createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case packageName
        case productId
        case basePlanID
        case state, billingPeriodDuration, gracePeriodDuration, resubscribeState, prorationMode, title, price, icon
        case inApp
        case description
        case createdAt
        case updatedAt
    }
}

// MARK: - PriceElement
struct PriceElement: Codable {
    let regionCode: String
    let newSubscriberAvailability: Bool
    let price: PricePrice
}

// MARK: - PricePrice
struct PricePrice: Codable {
    let currencyCode, units: String
    let nanos: Int
}

struct PriceCurrency: Codable {
    let currency: String
}

// MARK: - Datum
class AppPurchasesDataModel: Codable {
    var iapID, name, productID, state , description , product_id : String?
    var price: AnyType?
    var icon: String?
    var title, currency: String?
    var amount:Int?
    var inApp:String?
    var sub_id:String?
    
    enum CodingKeys: String, CodingKey {
        case iapID = "iap_id"
        case inApp = "in-app"
        case name , amount , description
        case productID = "product_id"
        case state, price, icon, title, currency
        case sub_id
    }
    
}
