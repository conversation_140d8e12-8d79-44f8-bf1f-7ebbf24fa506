//
//  Mission Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 11/4/21.
//

import Foundation

// MARK: - MissionsModel
class MissionsModel: Codable {
    var statusCode: Int?
    var status: Bool?
    var message: String?
    var data: [MissionsData]?
 
}

// MARK: - Datum
class MissionsData: Codable {
    var id, name, datumDescription: String?
    var image: String?
    var prize: Double?
    var createdAt, updatedAt: String?
    var v: Int?
    var action: String?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case name
        case datumDescription = "description"
        case image, prize, createdAt, updatedAt
        case v = "__v"
        case action
    }
 
}
