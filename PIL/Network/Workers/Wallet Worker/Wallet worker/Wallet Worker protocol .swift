//
//  Wallet Worker protocol .swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/23/21.
//

import Foundation
import Alamofire

protocol WalletWorkerProtocol{
    
    
    /// get wallet info in main screen 
    /// - Parameter compilition: : request compilition
    func getCoinsInfo(userID:String , compilition: @escaping (Result<WalletWorkerModel, AFError>,_ statusCode: Int?) -> Void)
    
    /// add coins
    /// - Parameter compilition: : request compilition
    func addCoins(model:AddCoinsRequestModel,compilition: @escaping (Result<WalletWorkerModel, AFError>,_ statusCode: Int?) -> Void)
    
    /// add Deposit
    /// - Parameter compilition: : request compilition
    func addDeposit(model:AddCoinsRequestModel,compilition: @escaping (Result<WalletWorkerModel, AFError>,_ statusCode: Int?) -> Void)

    
    func GetMission(compilition : @escaping (Result<MissionsModel,AFError>,_ statusCode:Int?) -> Void)
    
    /// send that the user compelete watching an ad
    func compeletWatchingAd(model: compeleteWatchingAdRequestModel, compilition: @escaping (Result<WalletWorkerModel, AFError>,_ statusCode: Int?) -> Void)
    
    func compeletPayment(model: compeletePaymentRequestModel, compilition: @escaping (Result<DefaultModel, AFError>,_ statusCode: Int?) -> Void)

    func ListAppPurchases(compilition: @escaping (Result<AppPurchasesModel, AFError>,_ statusCode: Int?) -> Void)
    
    func getVouchers(page: String, compilition: @escaping (Result<ServerResponse3<[Voucher]>, AFError>,_ statusCode: Int?) -> Void)
    
    func purhaseVoucher(playerID: String, productID: String, compilition: @escaping (Result<ServerResponse2<Voucher>, AFError>,_ statusCode: Int?) -> Void)
}



