//
//  Wallet Worker.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/23/21.
//

import Foundation
import Alamofire

class WalletWorker:WalletWorkerProtocol{
    
    func getVouchers(page: String, compilition: @escaping (Result<ServerResponse3<[Voucher]>, AFError>,_ statusCode: Int?) -> Void) {
        let url = URls.init().vouchers
        let langQuery = URLQueryItem(name: "lang", value: app_lang)
        let pagePrm = URLQueryItem(name: "page", value: page)
        Requests.API(indicator: .none).performRequest(url: url.addQueryParameters(queries: [pagePrm, langQuery]), method: .get, headersType: .token, completion: compilition)
    }
    
    func purhaseVoucher(playerID: String, productID: String, compilition: @escaping (Result<ServerResponse2<Voucher>, Alamofire.AFError>, Int?) -> Void) {
        let url = URls.init().purchaseVoucher
        let productPrm = URLQueryItem.init(name: "productId", value: productID)
        let playerPrm = URLQueryItem.init(name: "playerId", value: playerID)
        Requests.API().performRequest(url: url.addQueryParameters(queries: [productPrm,playerPrm]), method: .post, headersType: .token, completion: compilition)
    }

    func getCoinsInfo(userID:String , compilition: @escaping (Result<WalletWorkerModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.WalletIconsURL)/\(userID)?type=\(UserModel.shared.get_loginAsGuestType())"
        Requests.API(indicator: .none).performRequest(url: url, method: .get, headersType: .token, completion: compilition)
     }
    
    func addCoins(model:AddCoinsRequestModel,compilition: @escaping (Result<WalletWorkerModel, AFError>, Int?) -> Void) {
        let userID = UserModel.shared.get_id()
        let url = "\(Requests.url.WalletIconsURL)/\(userID)/winning"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
     
    func addDeposit(model: AddCoinsRequestModel, compilition: @escaping (Result<WalletWorkerModel, AFError>, Int?) -> Void) {
        let userID = UserModel.shared.get_id()
        let url = "\(Requests.url.WalletIconsURL)/\(userID)/deposit"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
 
    func GetMission(compilition: @escaping (Result<MissionsModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.MissionsURL, method: .get, headersType: .token, completion: compilition)
    }
    
    
    
    func compeletWatchingAd(model: compeleteWatchingAdRequestModel, compilition: @escaping (Result<WalletWorkerModel, AFError>,_ statusCode: Int?) -> Void){
        let url = Requests.url.compeleteWatchAdURL(userID: model.playerId)
        
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func compeletPayment(model: compeletePaymentRequestModel, compilition: @escaping (Result<DefaultModel, AFError>, Int?) -> Void) {
        let url = Requests.url.CompeletePaymentURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func ListAppPurchases(compilition: @escaping (Result<AppPurchasesModel, AFError>,_ statusCode: Int?) -> Void){
        let url = Requests.url.ListAppPurchasesURL
        let langQuery = URLQueryItem(name: "lang", value: app_lang)
//        let CurrencyQuery = URLQueryItem(name: "currency", value: UserModel.shared.getCountryCode())
        print("URL IS",url)
        var indicator: IndicatorType?
        ShopCacheService.shared.fetchDataMembership { data in
            indicator = data.isEmpty ? .regular : .none
            Requests.API(indicator: indicator).performRequest(url: url.addQueryParameters(queries: [langQuery]), method: .get, headersType: .token, completion: compilition)
        }
    }
    
}
