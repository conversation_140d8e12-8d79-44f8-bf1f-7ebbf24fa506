//
//  AddCoinsRequestModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/24/21.
//

import Foundation
struct AddCoinsRequestModel:Encodable {
    let scores:Int
}



struct compeleteWatchingAdRequestModel: Encodable{
    var lang: String
    var playerId: String
    var playerToken: String
    var value:String?
    var type:Int?

}


struct  compeletePaymentRequestModel:Encodable{
    var transactionIdentifier:String?
    var transactionState:Int?
    var applicationUsername:String?
    var transactionDate:Date?
    var productIdentifier:String?
    var quantity:Int?
    var simulatesAskToBuyInSandbox:Bool?
    var originaltransactionIdentifier:String?
    var playerId:String?
    var inApp:String?
    
    enum CodingKeys: String, CodingKey {
        case inApp = "in-app"
        case transactionIdentifier , transactionState , applicationUsername , transactionDate , productIdentifier , quantity , simulatesAskToBuyInSandbox , originaltransactionIdentifier , playerId
         
    }
}


class DefaultModel:Codable{
    var status, success: Bool?
    var message: String?
    var error: String?
}
