//
//  MissionsWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 13/03/2023.
//

import Foundation
import Alamofire
 
struct MissionsCallBacks{
    typealias missions = (Result<ServerResponse<Missions>, AFError>,_ statusCode: Int?) -> Void
    typealias subMissions = (Result<ServerResponse<[MissionContent]>, AFError>,_ statusCode: Int?) -> Void
    typealias prizes = (Result<ServerResponse<MissionRewards>, AFError>,_ statusCode: Int?) -> Void
    typealias prizeReward = (Result<ServerResponse<MissionReward>, AFError>,_ statusCode: Int?) -> Void
}

protocol MissionsWorkable: AnyObject{
    /// get mission list
    /// - Parameters:
    ///   - userId: string value
    ///   - completion: missions call back completion
    func getMissions(userID: String,_ completion: @escaping MissionsCallBacks.missions)
    /// get sub mission
    /// - Parameters:
    ///   - missionId: string value
    ///   - userId: string value
    ///   - completion: sub missions call back completion
    func getSubMission(missionID: String, userID: String,_ completion: @escaping MissionsCallBacks.subMissions)
    /// get mission prizes
    /// - Parameters:
    ///   - userId: string value
    ///   - completion: missions prizes call back competion
    func getPrizes(playerID: String,_ completion: @escaping MissionsCallBacks.prizes)
    /// get mission reward
    /// - Parameters:
    ///   - missionId: string value
    ///   - userId: string value
    ///   - completion: missions prize reward call back competion
    func getPrizeReward(missionID: String, playerID: String,_ completion: @escaping MissionsCallBacks.prizeReward)
}

class MissionsWorker: MissionsWorkable{
    static let shared = MissionsWorker()
    
    private init() {}
    
    func getMissions(userID: String,_ completion: @escaping MissionsCallBacks.missions) {
        let url = URls.init().missions + "/\(userID)"
        let endPoint = url.addQueryParameters(queries: [.init(name: "language", value: app_lang)])
        MissionsCacheService.shared.fetchList { data in
            Requests.API(indicator: data.isEmpty ? .regular : .none).performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
        }
    }
    
    func getSubMission(missionID: String, userID: String,_ completion: @escaping MissionsCallBacks.subMissions) {
        let url = URls.init().subMission + "/\(missionID)" + "/subs" + "/\(userID)"
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let version = URLQueryItem(name: "version", value: appVersion)
        let platform =  URLQueryItem(name: "platform", value: "ios")
        let endPoint = url.addQueryParameters(queries: [langQuery, version, platform])
        MissionsCacheService.shared.fetchDetails(id: missionID) { data in
            Requests.API(indicator: data.isEmpty ? .regular : .none).performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
        }
    }
    
    func getPrizes(playerID: String,_ completion: @escaping MissionsCallBacks.prizes) {
        var userPrm: String?
        if playerID.isEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = playerID
        }
        let url = URls.init().prizes + "/\(userPrm ?? "")"
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let endPoint = url.addQueryParameters(queries: [langQuery])
        Requests.API().performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
    }
    
    func getPrizeReward(missionID: String, playerID: String, _ completion: @escaping MissionsCallBacks.prizeReward) {
        var userPrm: String?
        if playerID.isEmpty{
            userPrm = UserModel.shared.get_id()
        }else{
            userPrm = playerID
        }
        let url = URls.init().reward + "/\(missionID)" + "/reward" + "/\(userPrm ?? "")"
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let endPoint = url.addQueryParameters(queries: [langQuery])
        Requests.API().performRequest(url: endPoint, method: .get, headersType: .token, completion: completion)
    }
}
