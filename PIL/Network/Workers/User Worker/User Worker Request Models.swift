//
//  User Worker Request Models.swift
//  PIL
//
//  Created by <PERSON> on 10/18/21.
//

import Foundation

struct ProfileRequestModel: Encodable{
    var bio: String?
    var email: String?
    var username: String?
    var name:String?
    var gender: String?
    var birthdate: String? //1970-01-01
    var referral : String?
    var phone: String?
    var image:String?
    var snap_chat_id:String?
}


struct ReportRequestModel: Codable{
    var message: String
    var reporter_id: String
    var reporting_id: String
    var status = "pending"
    var language = app_lang
}
