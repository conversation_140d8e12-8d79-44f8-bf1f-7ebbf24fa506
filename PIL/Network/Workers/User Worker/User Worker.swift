//
//  User Worker.swift
//  PIL
//
//  Created by <PERSON> on 10/18/21.
//

import Foundation
import Alamofire
import FirebaseCore
import FirebaseDatabase
import CodableFirebase


class UserWorker: UserWorkerProtocol{
    private let ref = Database.database().reference()
    static let shared = UserWorker()
    
    func get(ID: String, compilition: @escaping (Result<UserProfileModel, AFError>,_ statusCode: Int?) -> Void) {
        let url = "\(Requests.url.userProfileURL)/\(ID)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    func update(ID: String, model: ProfileRequestModel, compilition: @escaping (Result<UserProfileModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.updateUserProfileURL)/\(ID)?language=\(app_lang)"
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func updateWithfile(ID: String, model: ProfileRequestModel, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<UserProfileModel, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.updateUserProfileURL)/\(ID)"
        var urlKey = [String]()
        
        if !(fileName.isEmpty){
            urlKey = ["image"]
        }
        
        do{
            let parameters = try JSONSerialization.jsonObject(with: JSONEncoder().encode(model), options: .allowFragments) as? [String: String]
            Requests.API().performRequest(url: url, method: .post, parameters: parameters, headersType: .token, fileUrlKey: urlKey, files: fileData, filesNames: fileName, mimeTypes: mimeType, completion: compilition)
        }catch{
            // erorr
            compilition(.failure(AFError.multipartEncodingFailed(reason: .inputStreamReadFailed(error: Error.self as! Error))) , 400)
        }
    }
    
    func uploadChatUser(userID: String, model: UserChatModel, compilition: @escaping (Bool, String?) -> Void) {
        do {
            let userObj = try FirebaseEncoder().encode(model)
            ref.child(ChatKeys.users.rawValue).child(userID).setValue(userObj)
            compilition(true, nil)
        } catch let error {
            compilition(false, error.localizedDescription)
        }
    }
    
    func getChatMemberModel(userID: String, compilition: @escaping (ChatResult<UserChatModel, String>) -> Void) {
//        let ref = Database.database().reference()
        ref.child(ChatKeys.users.rawValue).child(userID).observe(.value) { (data) in
            if let data = data.value as? [String:Any]{
                do{
                    let user = try FirebaseDecoder().decode(UserChatModel.self, from: data)
                    compilition(.success(user))
                }catch{
                    compilition(.fail(error.localizedDescription))
                }
            }
        }
    }
    
    func report(model: ReportRequestModel, compilition: @escaping (Result<DefaultModel, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.reportPlayerURL, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getReferrals(page:String,compelition: @escaping (Result<Referrals, AFError>, Int?) -> Void) {
        let url = "\(Requests.url.referralsProfileURL)?page=\(page)"
        Requests.API().performRequest(url: url, method: .get, headersType: .token, completion: compelition)
    }
    
    func getGameHistory(compelition: @escaping (Result<GameHistory, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.gameHistoryURL, method: .get, headersType: .token, completion: compelition)
    }
}
