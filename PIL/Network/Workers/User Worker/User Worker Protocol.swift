//
//  User Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 10/18/21.
//

import Foundation
import Alamofire


protocol UserWorkerProtocol {
    
    /// get the all details of the user
    /// - Parameters:
    ///   - ID: id of the user
    ///   - compilition: response compilition
    func get(ID: String, compilition: @escaping (Result<UserProfileModel,AFError>,_ statusCde: Int?) -> Void)
    
    /// update User Data
    /// - Parameters:
    ///   - ID: user ID
    ///   - model: request model
    ///   - compilition: response compilition
    func update (ID: String, model: ProfileRequestModel , compilition: @escaping (Result<UserProfileModel,AFError>,_ statusCde: Int?) -> Void)
    
    /// update user Data with image file
    /// - Parameters:
    ///   - ID: id of user
    ///   - parameters: data that will be updated
    ///   - fileName: selected image file
    ///   - fileData: data of the selected image
    ///   - mimeType: mime type of the image
    ///   - compilition: response compilition
    func updateWithfile(ID: String, model: ProfileRequestModel, fileName: [String], fileData: [Data], mimeType: [String], compilition: @escaping (Result<UserProfileModel,AFError>,_ statusCde: Int?) -> Void)
    
    
    /// save user model in fireBase
    /// - Parameters:
    ///   - userID: id of the user
    ///   - model: user Model
    ///   - compilition: response compilition
    func uploadChatUser(userID: String, model: UserChatModel, compilition: @escaping (_ Status: Bool,_ error: String?) -> Void)
    
    /// get chat module info of specific user
    /// - Parameters:
    ///   - userID: user id
    ///   - compilition: response compilition
    func getChatMemberModel(userID: String, compilition: @escaping (ChatResult<UserChatModel,String>) -> Void)
    
    /// report user
    /// - Parameters:
    ///   - model: report model
    ///   - compilition: response compilition
    func report(model: ReportRequestModel, compilition: @escaping (Result<DefaultModel,AFError>,_ statusCde: Int?) -> Void)
    
    func getReferrals(page:String, compelition: @escaping (Result<Referrals, AFError>,_ statusCode: Int?) -> Void)
    
    func getGameHistory(compelition: @escaping (Result<GameHistory, AFError>,_ statusCode: Int?) -> Void)
}
