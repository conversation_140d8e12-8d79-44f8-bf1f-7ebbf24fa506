//
//  Guide Worker.swift
//  PIL
//
//  Created by mac on 11/01/2022.
//

import Foundation
import Alamofire

class GuideWorker: GuideWorkerProtocol{
    
    func get(compilition: @escaping (Result<GuideModel, AFError>, Int?) -> Void){
        let langQuery = URLQueryItem(name: "language", value: app_lang)
        let apiUrl =   Requests.url.guideURL.addQueryParameters(queries: [langQuery])
        Requests.API().performRequest(url: apiUrl, method: .get, headersType: .token, completion: compilition)
    }
}
