//
//  EmojisModel.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/12/2023.
//

import Foundation

struct EmojisResponse: Codable {
    let emojiRecords: [EmojiRecord]?
    let message: String?
    let success: Bool?
    let statusCode, totalCount: Int?
}

struct EmojiRecord: Codable {
    var id: Int?
    var createdBy: String?
    var creationDate: String?
    var isDeleted: Int?
    var modificationDate, modifiedBy, nameAr, nameEn: String?
    var imageURL: String?
    var mediaType: Int?

    enum CodingKeys: String, CodingKey {
        case id, createdBy, creationDate, isDeleted, modificationDate, modifiedBy, nameAr, nameEn
        case imageURL = "imageUrl"
        case mediaType
    }
}
