//
//  EmojisWorker.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/12/2023.
//

import Foundation
import Alamofire

class EmojisWorker{
    static let shared = EmojisWorker()
    
    func getList(compilition: @escaping (Result<EmojisResponse, AFError>, Int?) -> Void) {
        Requests.API().performRequest(url: Requests.url.emojis, method: .post, headersType: .token, completion: compilition)
    }
}
