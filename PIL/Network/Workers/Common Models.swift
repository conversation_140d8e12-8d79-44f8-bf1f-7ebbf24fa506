//
//  Common Models.swift
//  PIL
//
//  Created by mac on 30/06/2022.
//

import Foundation

struct AnyType{
    private(set) var string:String?
    private(set) var int:Int?
    
    init(value: String){
        self.string = value
    }
    
    init(value: Int){
        self.int = value
    }
}
 
extension AnyType: Decodable{
    init(from decoder: Decoder) throws{
        var container = try decoder.singleValueContainer()
        
        if let value = try? container.decode(String.self){
            self.string = value
        }else if let value = try? container.decode(Int.self){
            self.int = value
        }else{
            throw DecodingError.typeMismatch(AnyType.self, .init(codingPath: [],debugDescription:"can not find any initial type"))
        }
    }
}
 
extension AnyType: Encodable{
    init(to encoder: Encoder) throws{
        var container = try encoder.singleValueContainer()
        
        if let value = string{
            try container.encode(value)
        }else if let value = int{
            try container.encode(value)
        }else{
            try container.encodeNil()
        }
    }
}

extension AnyType{
    var getValue: String{
        if let value = self.int{
            return "\(value)"
        }else{
            return self.string ?? "0"
        }
    }
}
