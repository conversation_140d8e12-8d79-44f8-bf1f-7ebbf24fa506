//
//  Games Request Models.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation

//MARK: - requst Tournaments List
struct TournamentsListRequestModel:Encodable{
    var gameID:String?
}


//MARK: - requst Battle List
struct BattlesListRequestModel:Encodable{
    var gameID:String?
    var filter_field:String?
    var filter_text:String?
    var order_field:String?
    var order_by:String?
    var language:String?
}


//MARK:- create Tournamentes

struct CreateTournamentRequestModel: Encodable {
    var game_id:String?
    var tournament_name_ar:String?
    var tournament_name_en:String?
    var entryfee_deposit:String?
    var maximum_players:String?
    var start_time:String?
    var end_time:String?
    var userId:String?
}

//MARK:- special event request model
struct SpecialEventRequestModel: Encodable {
    let language:String = app_lang
}

//MARK:- game Details
struct GameDetailsRequstModel: Encodable {
    var gameId:String?
    var is_private:Int?
    var operatorOp:String?
    var country_code:String?
    var type:String?
    var version:String?
    
    enum CodingKeys: String, CodingKey {
        case gameId , is_private , country_code , type , version
        case  operatorOp = "operator"
    }
}


//MARK:- history
struct HistoryGameRequestModel:Encodable {
    var gameId:String?
    var userId:String?
    var language:String?
}

//MARK:- leaderboard game Details
struct LeaderBoardGameDetailsRequestModel: Encodable{
    var gameId:String?
    var language:String?
}

//MARK:- leaderboard tournament
struct LeaderBoardTournamentRequestModel: Encodable {
    var tournamentId:String?
    var language:String?
}

//MARK:- leader board battle
struct LeaderBoardBattleRequestModel: Encodable {
    var battleId:String?
} 


//MARK:- join private tournament
struct JoinPrivateTournamentRequestModel : Encodable{
    var referal_code: String
    var playerId: String
    var playerToken:String
}


//MARK:- search
struct SearchGameRequstModel : Encodable {
    var language:String?
    var filter_text: String?
    var filter_field:String?
}


//MARK: - get tournament
struct getPrivateTournamentRequestModel : Encodable {
    var userId:String?
    var referal_code:String?
}


struct PlayerIDModelRequest: Encodable {
    var playerId:String?
    var language:String?
}

// MARK:- validateAttempts screen join
struct validateAttemptsBattleRequestModel : Encodable {
    var playerId:String?
    var playerToken:String?
    var battleId:String?
    var offerObject:String?
    var objectType:String?
    var language:String?
}


struct validateAttemptsTournamenetRequestModel : Encodable {
    var playerId:String?
    var playerToken:String?
    var tournamentId:String?
    var offerObject:String?
    var objectType:String?
    var language:String?
}

//MARK: - recentlyplayedgames
struct recentlyplayedgamesRequestModel: Encodable{
    var language:String?
    var userId:String?
}
