//
//  HistoryModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/14/21.
//

import Foundation
// MARK: - BattleHistoryModel
struct HistoryGameModel: Codable {
    var message: String?
    var status: Bool?
    var data: [BattleHistoryModelData]?
}

// MARK: - Datum
struct BattleHistoryModelData: Codable, Comparable {
    var playerID: String?
    var loginTime: Int64?
    var isDebugMode: Bool?
    var profileData: ProfileData?
    var playerState: String?
    var gameData: GameData?
    var isWinner, isTie: Bool?
    var playerImage: String?
    var joiningFee:Int?
    var created_at:String?
    
    enum CodingKeys: String, CodingKey {
        case playerID = "playerId"
        case loginTime, isDebugMode, profileData, playerState, gameData, isWinner, isTie, playerImage
        case joiningFee , created_at
    }
    
    static func < (lhs: BattleHistoryModelData, rhs: BattleHistoryModelData) -> Bool {
        guard let lhsDate = (lhs.created_at ?? "").getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss.SSSZ", from: "en"),
              let rhsDate = (rhs.created_at ?? "").getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss.SSSZ", from: "en") else {
            print("lhs & rhs are nil")
                  return false
        }
        
        return lhsDate  < rhsDate
    }
    
    static func > (lhs: BattleHistoryModelData, rhs: BattleHistoryModelData) -> Bool {
        guard let lhsDate = (lhs.created_at ?? "").getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss.SSSZ", from: "en"),
              let rhsDate = (rhs.created_at ?? "").getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss.SSSZ", from: "en") else {
            print("lhs is nil")
                  return false
        }
        return lhsDate > rhsDate
    }
    
    static func == (lhs: BattleHistoryModelData, rhs: BattleHistoryModelData) -> Bool {
        return false
    }
}

// MARK: - GameData
struct GameData: Codable {
    var sessionID: String?
    var rank, score, elapsedTime, gameStartTime: Int?
    var gameEndTime: Int?
    var isFinished: Bool?
    var gameEndReason: String?

    enum CodingKeys: String, CodingKey {
        case sessionID = "sessionId"
        case rank, score, elapsedTime, gameStartTime, gameEndTime, isFinished, gameEndReason
    }
}

// MARK: - ProfileData
struct ProfileData: Codable {
    var loginTime: Int?
    var playerID, playerName: String?
    var profilePicURL: String?
    var mobileNo, appVersion, gameVersion: Int?
    var isBot: Bool?
    
    enum CodingKeys: String, CodingKey {
        case loginTime
        case playerID = "playerId"
        case playerName
        case profilePicURL = "profilePicUrl"
        case mobileNo, appVersion, gameVersion, isBot
    }
}
