//
//  Games Models.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation

class MainGamesModel: Codable{
    var status: Bool?
    var message: String?
    var data: [MainGamesDataModel]?
}


class MainGamesDataModel: Codable{
    var rowRecords: [RowRecordDataModel]?
    var title: String?
    var viewType: String?
    var viewPriority: Int?
    var icon: String?
}


class RowRecordDataModel: Codable {
    var id: Int
    var name, rowRecordDataModelDescription: String?
    var photo, thumb: String?
    var tags, type, status: String?
    var top: Int?
    var features: String?
    var categoryID: Int?
    var createdAt, updatedAt: String?
    var photoLandscape, photoPortrait: String?
    var categoryname: String?
    var category: CategoryModel?

    enum CodingKeys: String, CodingKey {
        case id, name
        case rowRecordDataModelDescription = "description"
        case photo, thumb, tags, type, status, top, features
        case categoryID = "category_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case photoLandscape = "photo_landscape"
        case photoPortrait = "photo_portrait"
        case categoryname, category
    }
}

// MARK: - Category
class CategoryModel: Codable {
    var id: Int?
    var name, categoryDescription, image: String?
    var createdAt, updatedAt: String?

    enum CodingKeys: String, CodingKey {
        case id, name
        case categoryDescription = "description"
        case image
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}



                //========================================================//
 // MARK: - MainAllGamesModel
 struct MainAllGamesModel: Codable {
     var message: String?
     var status: Int?
     var data: [RowsRecordDataModel]?

     enum CodingKeys: String, CodingKey {
         case message = "Message"
         case status, data
     }
 }

 // MARK: - Datum
// struct MainAllGamesDataModel: Codable {
//     var rowRecords: [RowsRecordDataModel]?
// }

 // MARK: - RowRecord
 struct RowsRecordDataModel: Codable {
     var id: Int?
     var gameOrientation: String?
//     var iosassetsbundle: String?
     var gamePlatform, pilMargin: String?
     var sceneName, createdBy, userID: String?
     var gameName: String?
     var gameLogoType, howToPlay: String?
     var gdDlink, tag: String?
     var tutorialcards: [String]?
     var countryTag, botConfig: String?
     var version, andriodassetsbundle: String?
     var mode: String?
     var income, gameBackground: String?
     var welcomeDescription, gameConfig, nameAr: String?
     var icons, faq: String?
     var lobbyConfig: String?
     var isActive: Bool?
     var engine, format, descriptionAr, createdAt: String?
     var updatedAt: String?
     var categoryID: Int?
     var userExperience, tutorialVideos: String?
     var genre: String?
     var gameSize, assetsbundle: String?
     var games:[Game]?
     var title_ar , title : String?
     
    enum CodingKeys: String, CodingKey {
        case games , title , title_ar
        case id, gameOrientation, /*iosassetsbundle,*/ gamePlatform
        case pilMargin = "pil_margin"
        case sceneName, createdBy
        case userID = "userId"
        case gameName
        case gameLogoType = "GameLogoType"
        case howToPlay = "how_to_play"
        case gdDlink = "GDDlink"
        case tag, tutorialcards, countryTag, botConfig, version, andriodassetsbundle, mode, income
        case gameBackground = "GameBackground"
        case welcomeDescription = "description"
        case gameConfig
        case nameAr = "name_ar"
        case icons, faq, lobbyConfig
        case isActive = "is_active"
        case engine, format
        case descriptionAr = "description_ar"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case categoryID = "categoryId"
        case userExperience, tutorialVideos, genre
        case gameSize = "game_size"
        case assetsbundle
    }
 }

 // MARK: - Game
struct Game: Codable {
     var gameOrientation: String?
     var gamePlatform: String?
     var createdBy: String?
     var version:String?
     var gameName: String?
     var howToPlay: String?
     var userID: String?
     var gdDlink: String?
     var tag: [String]?
     var countryTag: [String]?
     var id: Int?
     var botConfig: [BotConfig]?
     var mode, gameGameDescription: String?
     var gameConfig: String?
     var gameNameAR: String?
     var genre: String?
     var faq: String?
     var tutorialcard: [String]?
     var isActive: Bool?
     var engine: String?
     var category, format, gameDescriptionAR, name: String?
     var gameDescription: String?
     var  screenshots, photolandscape, photoportrait: [String]?
     var gameBackground:String?
     var categoryDetails: CategoryDetails?
     var gameIcons: String?
     var gameLogoType: String?
     var introvideo: String?
     var tournament:[TournamentData]?
     var battles: [BattleData]?
//     var iosassetsbundle:String?
     var sceneName:String?
    
    enum CodingKeys: String, CodingKey {
         case gameOrientation, gamePlatform, createdBy, gameName
         case howToPlay = "how_to_play"
         case userID = "userId"
         case gdDlink = "GDDlink"
         case tag = "Tag"
         case countryTag, id, botConfig, mode
         case gameGameDescription = "gameDescription"
         case gameConfig
         case gameNameAR = "gameName_AR"
         case genre, faq, tutorialcard
         case isActive = "is_active"
         case engine
         case category = "Category"
         case format
         case gameDescriptionAR = "gameDescription_AR"
         case name
         case gameDescription = "description"
         case gameBackground = "GameBackground"
         case screenshots, photolandscape, photoportrait
         case categoryDetails = "CategoryDetails"
         case gameIcons = "icons"
         case gameLogoType = "GameLogoType"
         case introvideo
         case battles ,  tournament
         case /*iosassetsbundle ,*/ sceneName , version
     }
     
     
 }

 // MARK: - BotConfig
 struct BotConfig: Codable {
    var skillRangeFrom, skillRangeto, winningProbability: Int?
    var winningCertainty:Bool?
 }

 


 // MARK: - CategoryDetails
 struct CategoryDetails: Codable {
     var categoryNameEn: String?
//     var image: String?
     var isActive: Bool?
     var id, categoryNameAr, name, categoryDetailsDescription: String?

     enum CodingKeys: String, CodingKey {
         case categoryNameEn = "category_name_en"
//         case image
         case isActive = "is_active"
         case id
         case categoryNameAr = "category_name_ar"
         case name
         case categoryDetailsDescription = "description"
     }
 }

            //========================================================//

// MARK: - BattlesListModel
struct BattlesListModel: Codable {
    var message: String?
    var status: Int?
    var data: [BattleData]?
}

// MARK: - Datum
struct BattleData: Codable , Comparable{
    var battleNameEn: String?
    var tournamentRulesEn: String?
    var  battvarype, battleNameAr: String?
    var entryfeeDeposit:Int?
    var noOfWinners:Int?
    var prizesDistribution: [PrizesDistributionModel?]?
    var tournamentRulesAr: String?
    var startDateTime, maximumPlayers: String?
    var entryfeeBouns: Int?
    var isActive: Bool?
    var endDateTime, id, maximumAttempt, gameID: String?
    var name, desc: String?
    var rules: String?
    var game: Game?
    var icon:String?
    var joined_players:Int?
    var prize:Int?
    var is_ticket:Bool?
    var is_grand:Bool?
    var skill_range:Int?

    enum CodingKeys: String, CodingKey {
        case joined_players , prize , is_ticket , is_grand , skill_range
        case battleNameEn = "battle_name_en"
        case tournamentRulesEn = "tournament_rules_en"
        case noOfWinners = "no_of_winners"
        case entryfeeDeposit = "entryfee_deposit"
        case battvarype = "battle_type"
        case battleNameAr = "battle_name_ar"
        case prizesDistribution = "prizes_distribution"
        case tournamentRulesAr = "tournament_rules_ar"
        case startDateTime = "start_date_time"
        case maximumPlayers = "maximum_players"
        case entryfeeBouns = "entryfee_bouns"
        case isActive = "is_active"
        case endDateTime = "end_date_time"
        case desc = "description"
        case id
        case maximumAttempt = "maximum_attempt"
        case gameID = "game_id"
        case name, rules, game , icon
    }
    
    static func < (lhs: BattleData, rhs: BattleData) -> Bool {
        let lhsValue = (lhs.entryfeeDeposit ?? 0) + (lhs.entryfeeBouns ?? 0)
        let rhsValue = (rhs.entryfeeDeposit ?? 0) + (rhs.entryfeeBouns ?? 0)
        return lhsValue < rhsValue
    }
    
    static func == (lhs: BattleData, rhs: BattleData) -> Bool {
        return (lhs.id ?? "0") == (rhs.id ?? "0")
    }
}
 
class PrizesDistributionModel:Codable{
    var rank, prize, currency:String?
}

class prizesDistributionScore:Codable{
    var score:String?
}
                //========================================================//

// MARK: - TournamentsListModel
struct TournamentsListModel: Codable {
    var message: String?
    var status: Int?
    var data: [TournamentData]?
}

// MARK: - Datum
struct TournamentData: Codable , Comparable{
    var tournamentType: String?
    var entryfeeDeposit:Int?
    var startTime, endTime: String?
    var prizesTable: String?
    var tournamentNameAr: String?
    var maximumPlayers: String?
    var userID: String?
    var tournamentNameEn: String?
    var isActive: Bool?
    var id, gameID, name: String?
    var rules: String?
    var game: Game?
//    var prizesDistributionScore: [prizesDistributionScore]?
    var tournamentRulesEn: String?
    var noOfWinners: Int?
    var prizesDistribution: [PrizesDistributionModel?]?
    var tournamentRulesAr: String?
    var startDateTime: String?
    var entryfeeBouns:Int?
    var isTicket: Bool?
    var endDateTime, maximumAttempt: String?
    var isSpecial: Bool?
    var prizeMoney: [String]?
    var icon, desc: String?
    var joined_players:Int?
    var prize:Int?
    var is_grand:Bool?
    var referal_code:Int?
    var skill_range:Int?
    
    
    enum CodingKeys: String, CodingKey {
        case joined_players , prize , is_grand , referal_code , skill_range
        case tournamentType = "tournament_type"
        case entryfeeDeposit = "entryfee_deposit"
        case startTime = "start_time"
        case endTime = "end_time"
        case prizesTable = "prizes_table"
        case tournamentNameAr = "tournament_name_ar"
        case maximumPlayers = "maximum_players"
        case userID = "userId"
        case tournamentNameEn = "tournament_name_en"
        case isActive = "is_active"
        case id, icon
        case desc = "description"
        case gameID = "game_id"
        case name, rules, game
//        case prizesDistributionScore = "prizes_distribution_score"
        case tournamentRulesEn = "tournament_rules_en"
        case noOfWinners = "no_of_winners"
        case prizesDistribution = "prizes_distribution"
        case tournamentRulesAr = "tournament_rules_ar"
        case startDateTime = "start_date_time"
        case entryfeeBouns = "entryfee_bouns"
        case isTicket = "is_ticket"
        case endDateTime = "end_date_time"
        case maximumAttempt = "maximum_attempt"
        case isSpecial = "is_special"
        case prizeMoney = "prize_money"
    }
    
    static func < (lhs: TournamentData, rhs: TournamentData) -> Bool {
        let lhsValue = (lhs.entryfeeDeposit ?? 0) + (lhs.entryfeeBouns ?? 0)
        let rhsValue = (rhs.entryfeeDeposit ?? 0) + (rhs.entryfeeBouns ?? 0)
        return lhsValue < rhsValue
    }
    
    static func == (lhs: TournamentData, rhs: TournamentData) -> Bool {
        return (lhs.id ?? "0") == (rhs.id ?? "0")
    }
}

//MARK:- Special Event Model
struct SpecialEventsModel: Codable {
    var message: String?
    var status: Bool?
    var data: [SpecialEventsData]?

    enum CodingKeys: String, CodingKey {
        case message = "Message"
        case status, data
    }
}

// MARK: - Datum
struct SpecialEventsData: Codable {
    var rowRecords: [SpecialEventsRowRecord]?
}

// MARK: - RowRecord
struct SpecialEventsRowRecord: Codable {
    var viewPriority: Int?
    var battleID: [String]?
    var hasMore, isActive: Bool?
    var viewType, id: String
    var tournamentID: [String]?
    var title: String?
    var tournament:[TournamentData]?
    var battle: [BattleData]?

    enum CodingKeys: String, CodingKey {
        case viewPriority
        case battleID = "battle_id"
        case hasMore
        case isActive = "is_active"
        case viewType, id
        case tournamentID = "tournament_id"
        case title
        case tournament
        case battle
    }
}


//MARK:- game Details
struct GameDetailsModel : Decodable {
    var status:Bool?
    var Message:String?
    var data:Game?
}

//MARK: - search games
class SearchGameModel:Codable{
    var status: Bool?
    var message: String?
    var data: [Game]?

}

//MARK:- players skills use in config game
class PlayerSkillsModel:Codable{
    var data:PlayerSkillsDataModel?
    var status: Int?
    var message: String?
}

class PlayerSkillsDataModel:Codable{
    var skill:Int?
}

// MARK:- validate Attempts Battle screen join

class validateAttemptsBattlModel:Codable{
    var status:Bool?
    var message:String?
    var transactionId:String?
}

// MARK:- validate Attempts tournament screen join

class validateAttemptsTournamentModel:Codable{
    
    var status:Bool?
    var message:String?
    var transactionId:String?
}
