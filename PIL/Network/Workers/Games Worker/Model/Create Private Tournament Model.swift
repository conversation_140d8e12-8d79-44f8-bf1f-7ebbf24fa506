//
//  Create Private Tournament Model.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/14/21.
//

import Foundation

class CreatePrivateTournamentModel:Codable{
    var message: String?
    var status: Bool?
    var data: CreateTournamentData?
}

  // MARK: - DataClass
class CreateTournamentData: Codable {
    var tournamentID: String?
    var referal_code: Int?
}


//MARK:- join private tournament
class JoinPrivateTournamentModel : Codable{
    var message: String?
    var status: Bool?
}


//MARK: - get private Tournament
class GetPrivateTournamentModel: Codable{
    var message: String?
    var status: Bool?
    var data: [TournamentData]?
}
