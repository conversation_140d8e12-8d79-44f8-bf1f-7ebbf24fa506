//
//  GameLeaderBoardModel.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 12/14/21.
//

import Foundation

//MARK:- get leaderboard Game
class GameLeaderBoardModel:Codable{
    var message: String?
    var status: Bool?
    var data: [GameLeaderBoardData]?

}


// MARK: - Datum
class GameLeaderBoardData: Codable {
    var noofwining, score: Int?
    var winingAmount: WiningAmount?
    var playerID, id, gameID: String?
    var rank: Int?
    var battleID: String?
    var playerName:String?
    var playerImage:String?
    var prize:Int?//ID_CodableModel?
    
    enum CodingKeys: String, CodingKey {
        case noofwining, score, winingAmount , playerName , playerImage , prize
        case playerID = "playerId"
        case id
        case gameID = "gameId"
        case rank
        case battleID = "battleId"
    }
 
}

// MARK: - WiningAmount
class WiningAmount: Codable {
    var rank, prize: String?
    var currency: String?
 
}
