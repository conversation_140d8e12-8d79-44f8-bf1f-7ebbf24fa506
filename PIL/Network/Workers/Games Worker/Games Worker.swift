//
//  Games Worker.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation
import Alamofire

class GamesWorker: GamesWorkerProtocol{

    func getGames(compilition: @escaping (Result<MainGamesModel, AFError>,_ statusCode: Int?) -> Void) {
        Requests.API(indicator: .custom).performRequest(url: Requests.url.gamesURL, method: .get, headersType: .token, completion: compilition)
    }
    
    
    // get all games in home 
    func GetMainGames(compilition: @escaping (Result<MainAllGamesModel, AFError>, Int?) -> Void) {
        var indicator: IndicatorType? = .regular
         let url =  "\(Requests.url.gamesURL)?language=\(app_lang)"
        if let data = UserDefaults.standard.data(forKey: "HomeGames") {
            indicator = .none
        }
        Requests.API(indicator: indicator).performRequest(url: url, method: .get, headersType: .token, completion: compilition)
    }
    
    func GetBattlesList(model: BattlesListRequestModel, compilition: @escaping (Result<BattlesListModel, AFError>, Int?) -> Void) {
        let url = Requests.url.battlesURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getTournamentsList(model: TournamentsListRequestModel, compilition: @escaping (Result<TournamentsListModel, AFError>, Int?) -> Void) {
        let url = Requests.url.tournamentsURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
        
    }
    

    
    func getGameDetails(model: GameDetailsRequstModel, compilition: @escaping (Result<GameDetailsModel, AFError>, Int?) -> Void) {
        let url = Requests.url.GameDetailsURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    

    
    
    func getGameLeaderBoard(model: LeaderBoardGameDetailsRequestModel, compilition: @escaping (Result<GameLeaderBoardModel, AFError>, Int?) -> Void) {
        let url = Requests.url.gameLeaderBoardGameURL
        
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func getLeaderBoardBattle(model: LeaderBoardBattleRequestModel, compilition: @escaping (Result<GameLeaderBoardModel, AFError>, Int?) -> Void) {
        let url = Requests.url.LeaderBoardBattleURL
        
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    func getLeaderBoardTournament(model: LeaderBoardTournamentRequestModel, compilition: @escaping (Result<GameLeaderBoardModel, AFError>, Int?) -> Void) {
        let url = Requests.url.LeaderBoardTournamentURL
        
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    
    func createTournament(model: CreateTournamentRequestModel, compilition: @escaping (Result<CreatePrivateTournamentModel, AFError>, Int?) -> Void) {
        let url = Requests.url.createPrivateTournamentURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    

    
    func searchGames(model: SearchGameRequstModel, compilition: @escaping (Result<SearchGameModel, AFError>, Int?) -> Void) {
        let url = Requests.url.SearchGamesURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func getPrivateTournament(model: getPrivateTournamentRequestModel, compilition: @escaping (Result<GetPrivateTournamentModel, AFError>, Int?) -> Void) {
        let url = Requests.url.getPrivateTournamentURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
 
    func JoinPrivateTournament(model: JoinPrivateTournamentRequestModel, compilition: @escaping (Result<JoinPrivateTournamentModel, AFError>, Int?) -> Void) {
        let url = Requests.url.JoinPrivateTournamentURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func getSKills(model: PlayerIDModelRequest, compilition: @escaping (Result<PlayerSkillsModel, AFError>, Int?) -> Void) {
        let url = Requests.url.GetSkillsURL
        Requests.API(indicator: .none).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func validateBattle(model: validateAttemptsBattleRequestModel, compilition: @escaping (Result<validateAttemptsBattlModel, AFError>, Int?) -> Void) {
        let url = Requests.url.validateAttemptsBattle
        Requests.API(indicator: .custom).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    

    func validateTournament(model: validateAttemptsTournamenetRequestModel, compilition: @escaping (Result<validateAttemptsTournamentModel, AFError>, Int?) -> Void) {
        let url = Requests.url.validateAttemptsTournament
        Requests.API(indicator: .custom).performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    
    
    func recentlyplayedgames(model: recentlyplayedgamesRequestModel, compilition: @escaping (Result<SearchGameModel, AFError>, Int?) -> Void) {
        let url = Requests.url.recentlyplayedgamesURL
        Requests.API().performRequest(url: url, method: .post, RequestModel: model, headersType: .token, completion: compilition)
    }
    


}
