//
//  Games Worker Protocol.swift
//  PIL
//
//  Created by <PERSON> on 9/26/21.
//

import Foundation
import Alamofire

protocol GamesWorkerProtocol{
    
    /// get main games
    /// - Parameter compilition: request compilition
    func getGames(compilition: @escaping (Result<MainGamesModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// get all games
    /// - Parameter compilition:  request compilition
    func GetMainGames(compilition: @escaping (Result<MainAllGamesModel, AFError>,_ statusCode: Int?) -> Void)
    
    
    /// get all Battle list
    /// - Parameters:
    ///   - model: GameID and filter data and language and order By
    ///   - compilition: request compilition
    func GetBattlesList(model:BattlesListRequestModel , compilition: @escaping (Result<BattlesListModel ,AFError> ,_ statusCode:Int?) -> Void)
    
    
    
    /// get all Tournaments List
    /// - Parameters:
    ///   - model: GameID
    ///   - compilition: request compilition
    func getTournamentsList(model:TournamentsListRequestModel, compilition: @escaping (Result<TournamentsListModel,AFError> ,_ statusCode:Int?)->Void)
    
    
    func getGameDetails(model:GameDetailsRequstModel , compilition:@escaping (Result<GameDetailsModel,AFError>,_ statusCode:Int?)->Void)

    
    /// get leader board
    /// - Parameters:
    ///   - model: game id
    ///   - compilition: request compilition
    func getGameLeaderBoard(model:LeaderBoardGameDetailsRequestModel , compilition:@escaping (Result<GameLeaderBoardModel,AFError>,_ statusCode:Int?)->Void)
    
    
    
    /// get leaderboard battle
    /// - Parameters:
    ///   - model: battle id
    ///   - compilition: request compilition
    func getLeaderBoardBattle(model:LeaderBoardBattleRequestModel , compilition:@escaping (Result<GameLeaderBoardModel,AFError>,_ statusCode:Int?)->Void)
    
    
    
    /// get leaderboard tournament
    /// - Parameters:
    ///   - model: tournament
    ///   - compilition: request compilition
    func getLeaderBoardTournament(model:LeaderBoardTournamentRequestModel , compilition:@escaping (Result<GameLeaderBoardModel,AFError>,_ statusCode:Int?)->Void)
    
    
    
    /// create tournament
    /// - Parameters:
    ///   - model: gameID and data create
    ///   - compilition: request compilition
    func createTournament(model:CreateTournamentRequestModel , compilition: @escaping (Result<CreatePrivateTournamentModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    /// Get data search game
    /// - Parameters:
    ///   - model: language and text search
    ///   - compilition: request compilition
    func searchGames(model:SearchGameRequstModel, compilition: @escaping (Result<SearchGameModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    
    
    /// get private tournament
    /// - Parameters:
    ///   - model: code and user id
    ///   - compilition: request compilition
    func getPrivateTournament(model:getPrivateTournamentRequestModel, compilition: @escaping (Result<GetPrivateTournamentModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    /// Join private tournament
    /// - Parameters:
    ///   - model: code
    ///   - compilition: request compilition
    func JoinPrivateTournament(model:JoinPrivateTournamentRequestModel, compilition: @escaping (Result<JoinPrivateTournamentModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    
    /// get player skills
    /// - Parameters:
    ///   - model: player id
    ///   - compilition: request compilition
    func getSKills(model:PlayerIDModelRequest, compilition: @escaping (Result<PlayerSkillsModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    
    /// validate battle
    /// - Parameters:
    ///   - model: battle data and coins
    ///   - compilition: request compilition
    func validateBattle(model:validateAttemptsBattleRequestModel, compilition: @escaping (Result<validateAttemptsBattlModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    func validateTournament(model:validateAttemptsTournamenetRequestModel, compilition: @escaping (Result<validateAttemptsTournamentModel,AFError>,_ statusCode:Int?)-> Void)
    
    
    /// ger recently play games
    /// - Parameters:
    ///   - model: user id
    ///   - compilition:  request compilition
    func recentlyplayedgames(model:recentlyplayedgamesRequestModel, compilition: @escaping (Result<SearchGameModel,AFError>,_ statusCode:Int?)-> Void)
}
