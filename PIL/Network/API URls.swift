//
//  API URls.swift
//  test app 2
//
//  Created by <PERSON> on 2/10/21.
//

import Foundation

//enum AppEnvironment: String{
//    case production = "https://api.pil.live"
//    case staging = "https://dev-api.pil.live"
//


class URls{
    
    // Developer ---
//    lazy var BASE_URL_INDIA = "https://test-dev-console.pil.live"
//    lazy var BASE_URL = "https://api.pil.live"
//    lazy var SmartFoxHost = "***********"
    
    // Live
    lazy var SOCIAL_BASE_URL = "https://socialapp.pil.live"

//   //  production ---
    lazy var BASE_URL_INDIA = "https://new-dev-console.pil.live"
    lazy var BASE_URL =  "https://api.pil.live"
    lazy var SmartFoxHost = "*************" //"*************" // SmartFoxHost Live
    

    
    ///change the environment of the app
//    lazy fileprivate var environment : AppEnvironment = .production
//
//    var getBaseDomain: String{
//        return environment.rawValue
//    }
    lazy fileprivate var Domain = "\(BASE_URL)/api"
    
    lazy fileprivate var FantasyDomain = "\(Domain)/fantasydata"
    lazy fileprivate var walletDomain = "\(Domain)/incentive"
    lazy fileprivate var GamesURL = "\(BASE_URL_INDIA)/api/"

    //Auth
    lazy var createCodeURL = "\(Domain)/auths/generate/verification"

    lazy var loginURL = "\(Domain)/auths/login"
    lazy var logoutURL = "\(Domain)/auths/logout"
    lazy var LoginAsGuestURL = "\(Domain)/auths/guest/login"
    lazy var signUpURL = "\(Domain)/auths/signup"
    lazy var emailVerificationURL = "\(Domain)/auths/username"
    lazy var setDeviceURL = "\(Domain)/auths/users/device"
    lazy var resetPassword =  "\(Domain)/auths/password/reset"
    lazy var NewPhoneNumber = "\(Domain)/players/updatePlayer/"
    lazy var DialCodes = "\(Domain)/systemsetting/countries"
    lazy var DeleteAccount = "\(Domain)/auths/userDelete"
    lazy var AvatarListURL = "\(Domain)/players/avatars"
    lazy var AssignAvatarURL = "\(Domain)/players/assignAvatar"
    lazy var socialLogin = "\(Domain)/auths/socialLogin"
    lazy var recover = "\(Domain)/auths/recover_account"
    lazy var verifyRecover = "\(Domain)/auths/verify_account"
    lazy var canRecover = "\(Domain)/auths/can-recover"

    //games
    var getGamesDomain: String{
       return BASE_URL_INDIA
    }
    lazy var gamesURL = "\(GamesURL)home"
    lazy var gameDetailsURL = "\(GamesURL)contests/game/"
    lazy var ValidateContestURL  = "\(GamesURL)contests/"
    lazy var FeedBackURL = "\(GamesURL)user-feedback"
    lazy var CanSendFeedbackURL = "\(GamesURL)user-feedback/cansendfeedback"
    lazy var  battlesURL = "\(GamesURL)battles"
    lazy var tournamentsURL = "\(GamesURL)tournaments"
    lazy var createPrivateTournamentURL = "\(GamesURL)createPrivateTournament"
    lazy var SpecialEventURL = "\(GamesURL)specialevents"
    lazy var GameDetailsURL = "\(GamesURL)game"
    lazy var SearchGamesURL = "\(GamesURL)games"
    
    lazy var JoinContest = "\(GamesURL)contests/public/android/"
    lazy var battleHistoryURL = "\(GamesURL)getBattleHistory"
    lazy var TournamentHistoryURL = "\(GamesURL)getTournamentHistory"

    lazy var gameLeaderBoardGameURL = "\(GamesURL)gameLeaderBoard"
    lazy var LeaderBoardBattleURL = "\(GamesURL)getLeaderBoard"
    lazy var LeaderBoardTournamentURL = "\(GamesURL)getLeaderBoardTournament"
    lazy var JoinPrivateTournamentURL = "\(GamesURL)joinPrivateTournament"
    lazy var getPrivateTournamentURL = "\(GamesURL)getPrivateTournament"
    lazy var GetSkillsURL = "\(GamesURL)getPlayerSkill"
    lazy var validateAttemptsBattle = "\(GamesURL)validateAttempts"
    lazy var validateAttemptsTournament = "\(GamesURL)validateAttemptsTournament"
    lazy var CheckPlayerAdsURL = "\(GamesURL)player-ads"

    lazy var recentlyplayedgamesURL = "\(GamesURL)recentlyplayedgames"
    
    lazy var DeservesDailyLivesURL = "\(Domain)/wallet/DeservesDailyLives/"
    lazy var CollectDailyLivesURL = "\(Domain)/wallet/collectDailyLives/"
    
    //Events
    lazy var EventsURL = "\(GamesURL)contests/special"
    
    //fantasy
    lazy var FantasyContestListURL = "\(FantasyDomain)/Leagues/GetFiltered"
    lazy var FantasyContestURL = "\(Domain)/contest"
    lazy var FantasyLeaderBoard = "\(Domain)/contest/leaderboard"
    lazy var MatchesURL = "\(FantasyDomain)/Matches/GetFiltered"
    lazy var matchContestsURL = "\(Domain)/contest/"
    lazy var playersFilteredURL = "\(FantasyDomain)/Players/GetFilteredByPosition"
    lazy var teamsFilterURL = "\(Domain)/FantasyTeams/FantasyTeams/GetFiltered"
    lazy var checkMatchStartURL = "\(FantasyDomain)/Matches/MatchStart"
    lazy var addFantasyTeamURL = "\(Domain)/FantasyTeams/FantasyTeams/Add"
    lazy var updateFantasyTeamURL = "\(Domain)/FantasyTeams/FantasyTeams/Update"
    lazy var canJoinContestURL = "\(Domain)/FantasyTeams/FantasyTeams/IsCanJoin"
    func JoinContestURL(value: String) -> String{
        let base = "\(Domain)/contest/"
        let endOfUrl = "/join"
        return base + value + endOfUrl
    }
    func deductPointURL(userID: String) -> String{
//        let base = "\(Domain)/wallet/coins/"
        let base = "\(Domain)/wallet/lives/"

        let endOfUrl = "/deductePoints"
        return base + userID + endOfUrl
    }
    lazy var filter = "\(Domain)/systemsetting/consts/all"

    //menu
    lazy var notificationsCountURL = "\(Domain)/notification/Notification/Count"
    lazy var notificationsURL = "\(Domain)/notification/notification/GetByRecipientId"
    lazy var filteredNotificationsURL = "\(Domain)/notification/Notification/GetFiltered"
    lazy var userProfileURL = "\(Domain)/players"
    lazy var updateUserProfileURL = "\(Domain)/players/updatePlayer"
    lazy var reportPlayerURL = "\(Domain)/players/report"
    lazy var referralsProfileURL = "\(Domain)/players/\(UserModel.shared.get_id())/referrals"
    lazy var gameHistoryURL = "\(GamesURL)game-result-details/player/\(UserModel.shared.get_id())"
    lazy var socialNotifications = "\(Domain)/notification/Notification/GetNotificationCount"
        
    //wallet
    lazy var  GetFilterdURL = "\(walletDomain)/Incentive/GetFiltered"
    lazy var getIncentiveUserURl = "\(walletDomain)/IncentiveUser/GetFiltered"
    lazy var AddIncentiveURL = "\(walletDomain)/IncentiveUser/Add"
//    lazy var redeemIncentivesURL = "\(Domain)/wallet/coins"
    lazy var redeemIncentivesURL = "\(Domain)/wallet/lives"

//    lazy var WalletIconsURL = "\(Domain)/wallet/coins"
    lazy var WalletIconsURL = "\(Domain)/wallet/lives"

    func compeleteWatchAdURL(userID: String) -> String{
//        let base = "\(Domain)/wallet/coins/"
        let base = "\(Domain)/wallet/lives/"

        let endOfUrl = "/AdsRewarding"
        return base + userID + endOfUrl
    }
    lazy var CompeletePaymentURL = "https://api.pil.live/api/in-appproduct/acknowledge-ios"
    lazy var ListAppPurchasesURL = "https://api.pil.live/api/in-appproduct/ios-products"
    lazy var wheelCheckURL = "\(Domain)/wallet/wheel"

    //payment
    lazy var GetPayment = "\(Domain)/wallet/payments"
    lazy var GetUserPaymentURL =  "\(Domain)/wallet/userPayments"
    lazy var AddCardPaymentURL = "\(Domain)/wallet/userPayments/addPayment"
    lazy var UpdateCardPaymentURL = "\(Domain)/wallet/userPayments"
    lazy var DeleteCardURL  = "\(Domain)/wallet/userPayments"
    lazy var userPurchasesURL = "\(Domain)/wallet/userPurchases"
    lazy var paymentHistory = "\(Domain)/wallet/userPurchases"
    
    //offers
    lazy var offersURL = "\(Domain)/Offer/GetFiltered"

    //Missions
    lazy var MissionsURL = "\(Domain)/missions/"
    lazy var referralsURL = "\(Domain)/players"
    lazy var MissionPlayer = "\(Domain)/missions/players?player="
    lazy var missions = "\(GamesURL)mission/player"
    lazy var subMission = "\(GamesURL)mission"
    lazy var prizes = "\(GamesURL)prizes/player"
    lazy var reward = "\(GamesURL)mission"
    
    // Follow and block
    lazy var AddSocialURL = "\(Domain)/social/Social/Add"
    lazy var GetFilteredSocialURL = "\(Domain)/social/Social/GetFiltered"
    lazy var getManyPlayersURL = "\(Domain)/players/many"

    // challenges
    lazy var challengesURL = "\(Domain)/challenges/"
    lazy var prizesURL = "\(Domain)/prizes"

    //PIL Help
    lazy var pilHelpURL = "\(Domain)/helpdesk/knowledgebase"
    lazy var contactUsURL = "\(Domain)/helpdesk/requestTypeList"
    lazy var createContactUsURL = "\(Domain)/helpdesk/createRequest"
    lazy var guideURL = "\(Domain)/players/guide"
    
    
    //update version
    lazy var UpdateVersionURL = "\(GamesURL)version"
    lazy var device_login = "\(Domain)/auths/device_login"
    lazy var restore_account = "\(Domain)/auths/restore_account"
    
    //Tournaments
    lazy var contestsPrivate = "\(GamesURL)contests/private"
    lazy var contestsCommunity = "\(GamesURL)contests/community"
    lazy var contestsPlayer = "\(GamesURL)contests/player"
    lazy var allContests = "\(GamesURL)contests/public/"
    lazy var contestDetails = "\(GamesURL)contests/contestshow"
    lazy var contest = "\(GamesURL)contests/"
    
    lazy var issuesHistory = "\(Domain)/helpdesk/getRequestByUser"
    lazy var subscription = "\(Domain)/in-appproduct/player"
    lazy var canJoinContest = "\(GamesURL)contests/private/"
    
    //Game Result Details
    lazy var gameResultDetails = "\(GamesURL)game-result-details/player"
    
    
    //Profile
    lazy var profile = "\(SOCIAL_BASE_URL)/CommonUser/UserProfile"
    lazy var popularity = "\(SOCIAL_BASE_URL)/CommonUser/UserPopularity"
    lazy var myVouchers = "\(Domain)/in-appproduct/voucher/my_vouchers"
    lazy var followers = "\(SOCIAL_BASE_URL)/CommonUser/UserFollowers"
    lazy var following = "\(SOCIAL_BASE_URL)/CommonUser/UserFollowing"
    lazy var levels = "\(GamesURL)player-level"
    lazy var trophy = "\(GamesURL)player-trophy"
    lazy var dailyPoints = "\(GamesURL)player-daily"
    
    //Social
    lazy var userAction = "\(SOCIAL_BASE_URL)/UserAction/AddUserAction"
    lazy var editAction = "\(SOCIAL_BASE_URL)/UserAction/EditUserAction"
    lazy var deleteUserAction = "\(SOCIAL_BASE_URL)/UserAction/DeleteUserAction"
    lazy var userListAction = "\(SOCIAL_BASE_URL)/UserAction/ListUserAction"
    lazy var listReacts = "\(SOCIAL_BASE_URL)/UserAction/ListUserActionEmojies"
    lazy var ListEmojies = "\(SOCIAL_BASE_URL)/Shared/ListEmoji"
    lazy var addPost = "\(SOCIAL_BASE_URL)/Post/AddPost"
    lazy var editPost = "\(SOCIAL_BASE_URL)/Post/EditPost"
    lazy var deletePost = "\(SOCIAL_BASE_URL)/Post/DeletePost"
    lazy var listPosts = "\(SOCIAL_BASE_URL)/Post/ListPost"
    lazy var listUsers = "\(SOCIAL_BASE_URL)/CommonUser/UserList"
    lazy var listRecommendedUsers = "\(SOCIAL_BASE_URL)/CommonUser/Recommended"
    // Rooms
    lazy var listRoomTopic = "\(SOCIAL_BASE_URL)/Shared/ListRoomTopic"
    lazy var listRoom = "\(SOCIAL_BASE_URL)/Room/ListRoom"
    lazy var deleteRoom = "\(SOCIAL_BASE_URL)/Room/DeleteRoom"
    lazy var listRoomParicipiant = "\(SOCIAL_BASE_URL)/Room/ListRoomParicipiant"
    lazy var privateRoomTournaments = "\(GamesURL)contests/private-room"
    
    
    //Chat
    lazy var listChat = "\(SOCIAL_BASE_URL)/UserChat/ListChatBuddies"
    lazy var chatURL = "\(SOCIAL_BASE_URL)/UserChat/ListUserChat"
    lazy var CreateGroup = "\(SOCIAL_BASE_URL)/Group/AddGroup"
    lazy var deleteChatUser = "\(SOCIAL_BASE_URL)/UserChat/DeleteUserChat"
    lazy var deleteGroupUser = "\(SOCIAL_BASE_URL)/Group/DeleteGroup"
    lazy var GetGroupInfo = "\(SOCIAL_BASE_URL)/Group/ListGroup"
    lazy var UploadImage = "\(SOCIAL_BASE_URL)/Shared/UploadImage"
    lazy var leaveGroup = "\(SOCIAL_BASE_URL)/Group/LeaveGroup"
    lazy var GroupMember = "\(SOCIAL_BASE_URL)/Group/ListGroupMembers"
    lazy var inviteMember = "\(SOCIAL_BASE_URL)/Group/InvitePlayers"
    lazy var GroupOnCall = "\(SOCIAL_BASE_URL)/Group/GroupOnCall"
    lazy var userChatAction = "\(SOCIAL_BASE_URL)/UserChatAction/AddUserAction"
    lazy var chatDeleteAction = "\(SOCIAL_BASE_URL)/UserChat/DeleteUserChat"
    lazy var messageDeleteAction = "\(SOCIAL_BASE_URL)/UserChat/DeleteUserMessage"
    lazy var chatEditAction = "\(SOCIAL_BASE_URL)/UserChat/EditUserChat"
    lazy var listUserActionEmojies = "\(SOCIAL_BASE_URL)/UserChatAction/ListUserActionEmojies"
    lazy var listUserChatAction = "\(SOCIAL_BASE_URL)/UserChatAction/ListUserAction"
    lazy var chatGameChallange = "\(GamesURL)contests/chat-challenge"
    lazy var undoDeletion = "\(SOCIAL_BASE_URL)/UserChat/UnDoUserMessage"
    
    //Seach
    lazy var searchGames = "\(GamesURL)games"
    lazy var searchContests = "\(GamesURL)contests/all"
    lazy var searchStore = "\(Domain)/in-appproduct/products"
    
    //Vouchers
    lazy var vouchers = "\(Domain)/in-appproduct/voucher/show_products"
    lazy var purchaseVoucher = "\(Domain)/in-appproduct/voucher/purchase"
    lazy var sendGift = "\(Domain)/in-appproduct/voucher/send_gift"
    
    
    //Rooms
    lazy var CreateRoomURL = "\(SOCIAL_BASE_URL)/Room/AddRoom"
    lazy var CreateMeetAndPlayRoomURL = "\(SOCIAL_BASE_URL)/Room/CreateMeetAndPlayRoom"
    lazy var DeleteRoomURL = "\(SOCIAL_BASE_URL)/Room/DeleteRoom"
    lazy var ListRoomURL = "\(SOCIAL_BASE_URL)/Room/ListRoom"
    lazy var ListUsersRoomURL = "\(SOCIAL_BASE_URL)/Room/ListRoomParicipiant"
    lazy var JoinRoomURL = "\(SOCIAL_BASE_URL)/Room/JoinRoom"
    lazy var LeaveRoomURL = "\(SOCIAL_BASE_URL)/Room/LeaveRoom"
    lazy var ListRoomsTopicURL = "\(SOCIAL_BASE_URL)/Shared/ListRoomTopic"
    lazy var rate = "\(Domain)/rate"
    
    lazy var referral = "\(Domain)/players/referral/"
    lazy var subreferral = "\(Domain)/players/referral/second/"
    
    lazy var emojis = "\(SOCIAL_BASE_URL)/Shared/ListEmoji"
}

