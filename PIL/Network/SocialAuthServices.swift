//
//  SocialAuthServices.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/05/2023.
//

import Foundation
import GoogleSignIn
import FirebaseAuth
import Firebase
import AuthenticationServices
import CryptoKit
import FBSDKCoreKit
import FBSDKLoginKit

protocol SocialAuthDelegate: AnyObject{
    func didCompleteSignInWithCredentials(_ result: AuthDataResult?,_ error: Error?)
    func didCompleteSignInToServers(_ response: loginModel?,_ error: Error?)
}

protocol SocialAuthProtocol: AnyObject{
    var delegate: SocialAuthDelegate? {set get}
    var user: SocialLoginModel? {set get}
    var authWorker: AuthWorker? {set get}
    func signInWithGoogle(_ view: UIViewController)
    func signInWithApple()
    func signInWithFacebook(_ view: UIViewController)
    func signInWithCredentials(_ credential: AuthCredential)
}

class SocialAuthServices: NSObject, SocialAuthProtocol{ 
    var authWorker: AuthWorker?
    var delegate: SocialAuthDelegate?
    var user: SocialLoginModel?
    private var savedAppleID: String{
        return KeychainWrapper.standard.string(forKey: "appleId") ?? ""
    }
    
    init(authWorker: AuthWorker){
        self.authWorker = authWorker
    }
   
    func signInWithGoogle(_ view: UIViewController){
        guard let clientID = FirebaseApp.app()?.options.clientID else { return }
        
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config
        
        GIDSignIn.sharedInstance.signIn(withPresenting: view) { [unowned self] result, error in
            guard error == nil else { return }
            guard let googleUser = result?.user,
                  let idToken = googleUser.idToken?.tokenString
            else { return }
            
//            let credentials = GoogleAuthProvider.credential(withIDToken: idToken,
//                                                           accessToken: user.accessToken.tokenString)
//            self.signInWithCredentials(credentials)
            
            user = SocialLoginModel(token: idToken,
                                    email: googleUser.profile?.email,
                                    vendor: .google,
                                    name: (googleUser.profile?.givenName ?? "") + " " + (googleUser.profile?.familyName ?? ""))
            self.signInToServers(user!)
        }
    }
    
    func signInWithApple() {
        let provider = ASAuthorizationAppleIDProvider()
        let request = provider.createRequest()
        request.requestedScopes = [.fullName, .email]
        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = self
        controller.performRequests()
    }
    
    func signInWithFacebook(_ view: UIViewController) {
        let fbLoginManager = LoginManager()
        fbLoginManager.logOut()
        fbLoginManager.logIn(permissions: ["public_profile", "email"], from: view){ (result, error) in
            guard error == nil else{
                self.delegate?.didCompleteSignInWithCredentials(nil, error)
                return
            }
            guard let accessToken = AccessToken.current?.tokenString else {
                return
            }
            GraphRequest.init(graphPath: "me", parameters: ["fields": "email,name,birthday,phone,gender"], tokenString: accessToken, version: nil, httpMethod: .get)
                .start { [self] connection, result, error in
                    
                    let json = result as! NSDictionary
                    print(result)
                    
//                    let credential = FacebookAuthProvider.credential(withAccessToken: accessToken)
//                    self.signInWithCredentials(credential)
                    
                    user = SocialLoginModel(token: accessToken,
                                            email: json["email"] as? String,
                                            vendor: .facebook,
                                            name: json["name"] as? String)
                    user?.phoneNumber = (json["phone"] as? String) ?? ""
                    user?.birth = (json["birthday"] as? String) ?? ""
                    self.signInToServers(user!)
                }
        }
    }
    
    func signInWithCredentials(_ credential: AuthCredential) {
        Auth.auth().signIn(with: credential){ resutl, error in
            self.delegate?.didCompleteSignInWithCredentials(resutl, error)
        }
    }
    
    func signInToServers(_ user: SocialLoginModel) {
        print("constructed social user: ", user)
        authWorker?.loginToServer(requestModel: user, completion: { [weak self] result, statusCode in
            switch result{
            case .success(let response):
                self?.delegate?.didCompleteSignInToServers(response, nil)
            case .failure(let error):
                self?.delegate?.didCompleteSignInToServers(nil, error)
            }
        })
    }
    
   
}

extension SocialAuthServices: ASAuthorizationControllerDelegate{
    func authorizationController(controller: ASAuthorizationController,
                                 didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else{
            return
        }
//        guard let nonce = currentNonce else {
//            fatalError("Invalid state: A login callback was received, but no login request was sent.")
//        }
        guard let appleIDToken = appleIDCredential.identityToken else {
            print("Unable to fetch identity token")
            return
        }
        guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
            print("Unable to serialize token string from data: \(appleIDToken.debugDescription)")
            return
        }
//        let credential = OAuthProvider.appleCredential(withIDToken: idTokenString,
//                                                       rawNonce: nonce,
//                                                       fullName: appleIDCredential.fullName)
//
//        self.signInWithCredentials(credential)
        if !appleIDCredential.email.isNilOrEmpty{
            KeychainWrapper.standard.set(appleIDCredential.email!, forKey: "appleId")
        }
        
        user = SocialLoginModel(token: idTokenString,
                                email: savedAppleID,
                                vendor: .apple,
                                name: (appleIDCredential.fullName?.givenName ?? "") + " " + (appleIDCredential.fullName?.familyName ?? ""))
        self.signInToServers(user!)
        UserModel.shared.loginAsGuest(status: false)
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("Sign in with Apple errored: \(error)")
        
     }
}
