//
//  SocialSignOutService.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 17/05/2023.
//

import Foundation
import AuthenticationServices
import FBSDKLoginKit
import FBSDKCoreKit
import GoogleSignIn

protocol SocialSignOutServiceProtocols{
    func signoutFromAllProviders()
}

class SocialSignOutService: SocialSignOutServiceProtocols{
    static let shared = SocialSignOutService()
    
    func signoutFromAllProviders() {
        LoginManager().logOut()
        GIDSignIn.sharedInstance.signOut()
    }
}
