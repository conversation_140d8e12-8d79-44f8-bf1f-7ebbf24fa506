<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22222" systemVersion="22G711" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Buddies" representedClassName=".Buddies" syncable="YES">
        <attribute name="chatId" optional="YES" attributeType="String"/>
        <attribute name="createdBy" optional="YES" attributeType="String"/>
        <attribute name="createdImageURL" optional="YES" attributeType="String"/>
        <attribute name="createdName" optional="YES" attributeType="String"/>
        <attribute name="creationDate" optional="YES" attributeType="String"/>
        <attribute name="creationDateStr" optional="YES" attributeType="String"/>
        <attribute name="deleteForAll" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="groupID" optional="YES" attributeType="String"/>
        <attribute name="groupName" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="isAdmin" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isEdited" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isForward" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isGroupChat" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isOnline" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isSeen" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="message" optional="YES" attributeType="String"/>
        <attribute name="parentMessageId" optional="YES" attributeType="String"/>
        <attribute name="parentMessageRecord" optional="YES" attributeType="Binary"/>
        <attribute name="reactionRecords" optional="YES" attributeType="Binary"/>
        <attribute name="toGroupId" optional="YES" attributeType="String"/>
        <attribute name="toRoomId" optional="YES" attributeType="String"/>
        <attribute name="toUserID" optional="YES" attributeType="String"/>
        <attribute name="typeID" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="unReadChatCount" optional="YES" attributeType="String"/>
        <attribute name="userID" optional="YES" attributeType="String"/>
    </entity>
    <entity name="Messages" representedClassName=".Messages" syncable="YES">
        <attribute name="chatId" optional="YES" attributeType="String"/>
        <attribute name="createdBy" optional="YES" attributeType="String"/>
        <attribute name="createdImageURL" optional="YES" attributeType="String"/>
        <attribute name="createdName" optional="YES" attributeType="String"/>
        <attribute name="creationDate" optional="YES" attributeType="String"/>
        <attribute name="creationDateStr" optional="YES" attributeType="String"/>
        <attribute name="deleteForAll" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="groupID" optional="YES" attributeType="String"/>
        <attribute name="groupName" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="isAdmin" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isEdited" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isForward" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isGroupChat" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isOnline" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isSeen" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="message" optional="YES" attributeType="String"/>
        <attribute name="parentMessageId" optional="YES" attributeType="String"/>
        <attribute name="parentMessageRecord" optional="YES" attributeType="Binary"/>
        <attribute name="privateChatLocalId" optional="YES" attributeType="String"/>
        <attribute name="reactionRecords" optional="YES" attributeType="Binary"/>
        <attribute name="toGroupId" optional="YES" attributeType="String"/>
        <attribute name="toRoomId" optional="YES" attributeType="String"/>
        <attribute name="toUserID" optional="YES" attributeType="String"/>
        <attribute name="typeID" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="unReadChatCount" optional="YES" attributeType="String"/>
        <attribute name="userID" optional="YES" attributeType="String"/>
    </entity>
    <entity name="PostsCachedList" representedClassName=".PostsCachedList" syncable="YES">
        <attribute name="bio" optional="YES" attributeType="String"/>
        <attribute name="commentsCount" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="createdBy" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="createdImageUrl" optional="YES" attributeType="String"/>
        <attribute name="createdName" optional="YES" attributeType="String"/>
        <attribute name="creationDate" optional="YES" attributeType="String"/>
        <attribute name="creationDateStr" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="imageFile" optional="YES" attributeType="String"/>
        <attribute name="imageUrl" optional="YES" attributeType="String"/>
        <attribute name="isClearImage" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isExpand" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isFeeds" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isFollowing" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isLike" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isOwner" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="lastUserNameLike" optional="YES" attributeType="String"/>
        <attribute name="lat" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="likesCount" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="lng" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="mediaData" optional="YES" attributeType="Binary"/>
        <attribute name="mediaType" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="modificationDateStr" optional="YES" attributeType="String"/>
        <attribute name="objectId" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="shareCount" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="targetObjectID" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <attribute name="viewsCount" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="Profiles" representedClassName="Profiles" syncable="YES">
        <attribute name="bio" optional="YES" attributeType="String"/>
        <attribute name="draws" optional="YES" attributeType="Binary"/>
        <attribute name="email" optional="YES" attributeType="String"/>
        <attribute name="followers" optional="YES" attributeType="String"/>
        <attribute name="following" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String"/>
        <attribute name="image" optional="YES" attributeType="String"/>
        <attribute name="isBlocked" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="levels" optional="YES" attributeType="Binary"/>
        <attribute name="loses" optional="YES" attributeType="Binary"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="popularity" optional="YES" attributeType="String"/>
        <attribute name="scores" optional="YES" attributeType="Binary"/>
        <attribute name="username" optional="YES" attributeType="String"/>
        <attribute name="vouchers" optional="YES" attributeType="Binary"/>
        <attribute name="wins" optional="YES" attributeType="Binary"/>
        <relationship name="followersList" optional="YES" toMany="YES" deletionRule="Nullify" ordered="YES" destinationEntity="Profiles"/>
        <relationship name="games" optional="YES" toMany="YES" deletionRule="Nullify" ordered="YES" destinationEntity="UserGamesRecords" inverseName="user" inverseEntity="UserGamesRecords"/>
    </entity>
    <entity name="UserGamesRecords" representedClassName="UserGamesRecords" syncable="YES">
        <attribute name="drawCount" optional="YES" attributeType="String"/>
        <attribute name="gameName" optional="YES" attributeType="String"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="lossesCount" optional="YES" attributeType="String"/>
        <attribute name="topScore" optional="YES" attributeType="String"/>
        <attribute name="winsCount" optional="YES" attributeType="String"/>
        <relationship name="user" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Profiles" inverseName="games" inverseEntity="Profiles"/>
    </entity>
</model>