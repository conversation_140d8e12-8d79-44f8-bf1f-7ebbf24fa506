<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22222" systemVersion="22G711" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Games" representedClassName=".Games" syncable="YES">
        <attribute name="available" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="categoryID" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="filePath" optional="YES" attributeType="String"/>
        <attribute name="gameID" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="image" optional="YES" attributeType="String"/>
        <attribute name="name" attributeType="String"/>
        <attribute name="productCurrency" optional="YES" attributeType="String"/>
        <attribute name="productCurrencyAr" optional="YES" attributeType="String"/>
        <attribute name="productImage" optional="YES" attributeType="String"/>
        <attribute name="productImageWhiteLabel" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
        <attribute name="productNameAr" optional="YES" attributeType="String"/>
        <attribute name="productOptionalFields" optional="YES" attributeType="String"/>
        <attribute name="productPrice" optional="YES" attributeType="String"/>
        <attribute name="productPriceWithoutVat" optional="YES" attributeType="String"/>
        <attribute name="sceneName" optional="YES" attributeType="String"/>
        <attribute name="sellPrice" optional="YES" attributeType="String"/>
        <attribute name="sellPriceWithoutVat" optional="YES" attributeType="String"/>
        <attribute name="token_price" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="vatAmount" optional="YES" attributeType="String"/>
        <attribute name="vatPercentage" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="version" optional="YES" attributeType="String"/>
    </entity>
    <entity name="MembershipStore" representedClassName="MembershipStore" syncable="YES">
        <attribute name="amount" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="currency" optional="YES" attributeType="String"/>
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="in_app" optional="YES" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="price" optional="YES" attributeType="String"/>
        <attribute name="product_id" optional="YES" attributeType="String"/>
        <attribute name="state" optional="YES" attributeType="String"/>
        <attribute name="sub_id" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
    </entity>
    <entity name="MissionDetails" representedClassName="MissionDetails" syncable="YES">
        <attribute name="contest" optional="YES" attributeType="Binary"/>
        <attribute name="count" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="done" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="parent_mission_id" optional="YES" attributeType="String"/>
        <attribute name="targetID" optional="YES" attributeType="String"/>
        <attribute name="type" optional="YES" attributeType="String"/>
    </entity>
    <entity name="MissionsLocal" representedClassName="MissionsLocal" syncable="YES">
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="String"/>
        <attribute name="isActive" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="locked" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="progress" optional="YES" attributeType="String"/>
        <attribute name="subMissionsCount" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="winners" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
    <entity name="ProductStore" representedClassName="ProductStore" syncable="YES">
        <attribute name="amount" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="currency" optional="YES" attributeType="String"/>
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="iap_id" optional="YES" attributeType="String"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="in_app" optional="YES" attributeType="String"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="price" optional="YES" attributeType="String"/>
        <attribute name="product_id" optional="YES" attributeType="String"/>
        <attribute name="state" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
    </entity>
    <entity name="TournamentsLocal" representedClassName="TournamentsLocal" syncable="YES">
        <attribute name="arName" optional="YES" attributeType="String"/>
        <attribute name="code" optional="YES" attributeType="String"/>
        <attribute name="contestType" optional="YES" attributeType="String"/>
        <attribute name="countryOperators" optional="YES" attributeType="String"/>
        <attribute name="createdAt" optional="YES" attributeType="String"/>
        <attribute name="description_" optional="YES" attributeType="String"/>
        <attribute name="endDateTime" optional="YES" attributeType="String"/>
        <attribute name="enName" optional="YES" attributeType="String"/>
        <attribute name="entryfeeBouns" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="entryfeeDeposit" optional="YES" attributeType="String"/>
        <attribute name="entryFeeType" optional="YES" attributeType="Binary"/>
        <attribute name="game" optional="YES" attributeType="Binary"/>
        <attribute name="game_type" optional="YES" attributeType="String"/>
        <attribute name="icon" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="isActive" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isCommunity" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isContest" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isFinished" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isGrand" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isJoined" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isKnockout" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isPrivate" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isPublic" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isSpecial" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isStarted" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="isTicket" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="joinedPlayers" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="knockoutTries" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="maximumAttempt" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="maximumPlayers" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="name" optional="YES" attributeType="String"/>
        <attribute name="noOfWinners" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="pilMargin" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="player_number" optional="YES" attributeType="String"/>
        <attribute name="playerID" optional="YES" attributeType="Integer 64" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="playerName" optional="YES" attributeType="String"/>
        <attribute name="prize" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="prizesDistribution" optional="YES" attributeType="Binary"/>
        <attribute name="rules" optional="YES" attributeType="String"/>
        <attribute name="skillRange" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="startDateTime" optional="YES" attributeType="String"/>
        <attribute name="timeInterval" optional="YES" attributeType="String"/>
        <attribute name="type" optional="YES" attributeType="String"/>
        <attribute name="updatedAt" optional="YES" attributeType="String"/>
    </entity>
    <entity name="VouchersStore" representedClassName="VouchersStore" syncable="YES">
        <attribute name="available" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="categoryID" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="config" optional="YES" attributeType="Binary"/>
        <attribute name="id" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="productCurrency" optional="YES" attributeType="String"/>
        <attribute name="productCurrencyAr" optional="YES" attributeType="String"/>
        <attribute name="productImage" optional="YES" attributeType="String"/>
        <attribute name="productImageWhiteLabel" optional="YES" attributeType="String"/>
        <attribute name="productName" optional="YES" attributeType="String"/>
        <attribute name="productNameAr" optional="YES" attributeType="String"/>
        <attribute name="productOptionalFields" optional="YES" attributeType="String"/>
        <attribute name="productPrice" optional="YES" attributeType="String"/>
        <attribute name="productPriceWithoutVat" optional="YES" attributeType="String"/>
        <attribute name="sellPrice" optional="YES" attributeType="String"/>
        <attribute name="sellPriceWithoutVat" optional="YES" attributeType="String"/>
        <attribute name="token_price" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="vatAmount" optional="YES" attributeType="String"/>
        <attribute name="vatPercentage" optional="YES" attributeType="Integer 32" defaultValueString="0" usesScalarValueType="YES"/>
    </entity>
</model>