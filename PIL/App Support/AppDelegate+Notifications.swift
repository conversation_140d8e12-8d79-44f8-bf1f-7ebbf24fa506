//
//  AppDelegate+Notifications.swift
//  PIL
//
//  Created by sameh mohammed on 07/03/2023.
//

import Foundation
import UIKit
import CoreData
import MOLH
import IQKeyboardManager
import FirebaseCore
import FirebaseMessaging
import FirebaseDynamicLinks
import UserNotifications
import NotificationCenter
import BranchSDK
import PushKit
import CallKit

extension AppDelegate {
    
    func registerForPushNotifications() {
        if #available(iOS 10.0, *) {
            // For iOS 10 display notification (sent via APNS)
            UNUserNotificationCenter.current().delegate = self
            let authOptions: UNAuthorizationOptions = [.alert, .badge, .sound]
            UNUserNotificationCenter.current().requestAuthorization(
                options: authOptions,
                completionHandler: {_, _ in })
            // For iOS 10 data message (sent via FCM)
            Messaging.messaging().delegate = self
        } else {
            let settings: UIUserNotificationSettings =
            UIUserNotificationSettings(types: [.alert, .badge, .sound], categories: nil)
            UIApplication.shared.registerUserNotificationSettings(settings)
        }
        UIApplication.shared.registerForRemoteNotifications()
    }
    
    func getNotificationSettings() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            print("Notification settings: \(settings)")
        }
    }
}

////MARK: - Notifications
//@available(iOS 14.0, *)
extension AppDelegate: UNUserNotificationCenterDelegate, MessagingDelegate{
    
    func notificationClicked(withScreenName screenName: String?, kvPairs: [AnyHashable : Any]?, andPushPayload userInfo: [AnyHashable : Any]) {
        
        
    }
    
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        print("fcm token",fcmToken)
        UserModel.shared.setFCM(token: fcmToken!)
        setUserData(online: true)
    }
    
        func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification,
     withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {

            let userInfo = notification.request.content.userInfo
            print(userInfo)
//            let dict = userInfo[AnyHashable("aps")] as! [String : Any]
            let body = userInfo[AnyHashable("body")] as? String ?? ""
            self.actionId = userInfo[AnyHashable("actionId")] as? String ?? ""
//            let objectId = userInfo[AnyHashable("gcm.notification.objectId")] as? String ?? ""
            let targetId = userInfo[AnyHashable("targetObjectID")] as? String ?? ""
            
            
            print("KKKKKKK-->>\(userInfo[AnyHashable("actionId")] as? String) -- \(self.actionId)")
            
            
            let name = body.byWords
//            if Int(actionId) == 1 {
//                self.incomingCall(name: "ABOOOOOD")
//            }

            if Int(actionId) == Lookups.Actions.voiceCall.rawValue {
                print("Voice Call One To One")
                if AppDelegate.fristNoti == false{
                    AppDelegate.fristNoti = true
                    self.RoomID =  Int(targetId)!
                    self.incomingCall(name: body)
                    completionHandler([.sound])
                }
            }else if Int(actionId) == Lookups.Actions.VoiceCallGroup.rawValue{
                print("Voice Call Group")
                self.RoomID =  Int(targetId)!
                incomingCall(name:  "\(name.last ?? "")")
                completionHandler([.sound])
            }
            else if Int(targetId) == Lookups.Objects.chat.rawValue{
                completionHandler([])
            }
            else{
                completionHandler([.sound, .badge, .banner])
            }

    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        
//        MoEngage.sharedInstance().setPushToken(deviceToken)
        
//        Messaging.messaging().apnsToken = deviceToken
        let deviceTokenString = deviceToken.hexString

        print("didRegisterForRemoteNotificationsWithDeviceToken",deviceTokenString)
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        
        let actionIdentifier = response.actionIdentifier
        
        switch actionIdentifier {
        case UNNotificationDismissActionIdentifier: // Notification was dismissed by user
            completionHandler()
        case UNNotificationDefaultActionIdentifier: // App was opened from notification
            
            let userInfo = response.notification.request.content.userInfo
            print("---->")
            print("userInfo",userInfo)
            
            guard
                let aps = userInfo[AnyHashable("aps")] as? NSDictionary,
                let alert = aps["alert"] as? NSDictionary,
                let _ = alert["title"] as? String,
                let _ = alert["body"] as? String,
                UserModel.shared.getLogin(),
                let topVC = UIApplication.topViewController
            else { return }
                 
            if let value = userInfo["actionId"] as? String,
               let action = Lookups.Actions.init(rawValue: Int(value)!){
                switch action{
                case .gift:
                    if !topVC.isKind(of: SplashVC.self), !topVC.isKind(of: MainProfileVC.self){
                        let vc = MainProfileRouter.createModule(profile: .myProfile)
                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }else{
                        UserModel.shared.didReceiveGiftNotification(true)
                    }
                case .level:
                    if !topVC.isKind(of: SplashVC.self), !topVC.isKind(of: MainProfileVC.self){
                        let vc = MainProfileRouter.createModule(profile: .myProfile)
                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }else{
                        UserModel.shared.didReceiveLevelUpNotification(true)
                    }
                default: break
                }
            }
            if let value = userInfo["objectId"] as? String,
               let action = userInfo["actionId"] as? String,
               let object = Lookups.Objects.init(rawValue: Int(value)!){
                
                print("TTTT\(value)--\(action)---\(object)")
                
                guard let targetId = userInfo["targetObjectID"] as? String else{ return }
                
                guard let parentTargetId = userInfo["parentTargetObjectID"] as? String else{ return }
                
                guard let senderId = userInfo["senderId"] as? String else{ return }
                
                
                print("SSSSSS")
                
                switch object{
                case .user :
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let viewController = MainProfileRouter.createModule(profile: .publicProfile(senderId))
                        viewController.hidesBottomBarWhenPushed = true
                        topVC.navigationController?.pushViewController(viewController, animated: true)
                    }else{
                        UserModel.shared.setUserIDFromDeepLink(senderId)
                    }
                case .post:
                    let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                                     postRecord: .init(id: Int(targetId)!))
                    SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
                        switch result{
                        case .success(let response):
                            if let data = response.postRecords.first{
                                if !topVC.isKind(of: SplashVC.self){
                                    if userInfo["mediaType"] as? String == "7" {
                                        let vc = ReelsRouter.createModule(data, isFeed: true, 0.0)
                                        topVC.navigationController?.pushViewController(vc, animated: true)
                                    }else{
                                        let vc = UserPostRouter.createModule(data)
                                        topVC.navigationController?.pushViewController(vc, animated: true)
                                    }
                                }else{
                                    UserModel.shared.setPostIDFromDeepLink("\(targetId)")
                                }
                            }
                        default: break
                        }
                    }
                case .chat :
                    if !topVC.isKind(of: SplashVC.self){
                        print("Notification Target ID",targetId)
                        let vc = ChatNewRotur.createModule(userId: Int(targetId)!, groupID: 0,chatId: nil, isMeet: false)
                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }else{
                        UserModel.shared.setChatIDFromDeepLink("\(targetId)")
                    }
                case .group:
                    if !topVC.isKind(of: SplashVC.self){
                        print("Notification Target ID",targetId)
                        let vc = ChatNewRotur.createModule(userId: 0, groupID: Int(targetId)! ,chatId: nil, isMeet: false)
                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }else{
                        UserModel.shared.setChatIDFromDeepLink("\(targetId)")
                    }
                    
                case .voiceCall :
                    if Int(action) == Lookups.Actions.voiceCall.rawValue {
                        print("Voice Call One To One")
//                        let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: Int(targetId)!,incomingCall: true)
//
//                        topVC.navigationController?.pushViewController(vc, animated: true)
//                        let id = UUID()
//                        self.callKitManagerDelegate.reportIncomingCall(id: id , handle: "Caller" , actionId: action , targetObjectID : targetId)
//                        let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: Int(targetId)!,incomingCall: true)
//                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }else if Int(action) == Lookups.Actions.VoiceCallGroup.rawValue{
                        print("Voice Call Group")
//                        let vc = GroupRoomChatRouter.createModule(userID: 0, roomID: Int(targetId)!, groupID: 0, incomingCall: true)
//                        topVC.navigationController?.pushViewController(vc, animated: true)
                    }
                case .comment :
                    let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                                     postRecord: .init(id: Int(parentTargetId)!))
                    SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
                        switch result{
                        case .success(let response):
                            if let data = response.postRecords.first{
                                if !topVC.isKind(of: SplashVC.self){
                                    if userInfo["mediaType"] as? String == "7" {
                                        let vc = ReelsRouter.createModule(data, isFeed: true, 0.0)
                                        topVC.navigationController?.pushViewController(vc, animated: true)
                                    }else{
                                        let vc = UserPostRouter.createModule(data)
                                        topVC.navigationController?.pushViewController(vc, animated: true)
                                    }
                                }else{
                                    UserModel.shared.setPostIDFromDeepLink("\(targetId)")
                                }
                            }
                        default: break
                        }
                    }
                default: break
                }
            }
            
            completionHandler()
        default:
            print("DATA DATA DATTA DATA DATA DATA DATA DATA")
            completionHandler()
        }
    }
    
    
    //MoEngage
    // MARK:- Remote notification received callback method for iOS versions below iOS10
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any]) {
        //Call only if MoEngageAppDelegateProxyEnabled is NO
//        MoEngage.sharedInstance().didReceieveNotificationinApplication(application, withInfo: userInfo)
        if let customData = userInfo["actionId"] as? String {
                // Access your custom data
                print("Received custom data: \(customData)")
            }
        print("------>    \(userInfo)")
    }
    
    func didReceiveRemoteNotification( _ userInfo: [AnyHashable : Any],fetchCompletionHandler completionHandler: Void) {
        print("-----")
    }
    
    func application(_ application: UIApplication,didReceiveRemoteNotification userInfo: [AnyHashable : Any],fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("------>    \(userInfo)")
    }
    
//    func application(_ application: UIApplication, performFetchWithCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
//        // Perform background fetch tasks here
//        let bgTaskID = UIApplication.shared.beginBackgroundTask(expirationHandler: nil)
//        self.incomingCall(name: "Tawfiq")
//        UIApplication.shared.endBackgroundTask(bgTaskID)
//        completionHandler(.newData)
//    }

//    func pushRegistry( _ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
//        print("PUSHER IS ---> ONE")
//        if type == PKPushType.voIP {
////            self.incomingCall(name: "TEST PUSHER ONE")
//
//            let tokenParts = pushCredentials.token.map { data -> String in
//                return String(format: "%02.2hhx", data)
//            }
//            let tokenString = tokenParts.joined()
//            print(tokenString)
//        }
//    }
//
//    func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
//        print("PUSHER IS ---> TWO")
//           print("pushRegistry:didInvalidatePushTokenForType:")
//       }
//
//    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
//        print("PUSHER IS ---> THREE")
//         print("------>",payload.dictionaryPayload)
//        self.incomingCall(name: "TEST PUSHER TWO")
//    }
}



//MARK: - dynamic link
extension AppDelegate{
    func handleIncomingDynamicLink(_ dynamicLink: DynamicLink){
        guard let url = dynamicLink.url else {
            print("That's weird. My dynamic link object has no url")
            return
        }
        print("Your incoming link parameter is \(url.absoluteString)")
        
        guard (dynamicLink.matchType == .unique  || dynamicLink.matchType == .default ) else {
            print("Not a strong enough match type to contiune")
            return
        }
        
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false) , let queryItems = components.queryItems else {return}
        if components.path == "/AvmB"{
            if let gameID = queryItems.first(where: {$0.name == "gameId"}){
                guard let gameValue = gameID.value  else {return}
                if UserModel.shared.getLogin() == true{
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let viewController = ContestGamesRouter.createModule(gameID: gameValue) as! ContestGamesVC
                        viewController.hidesBottomBarWhenPushed = true
                        topVC.navigationController?.pushViewController(viewController, animated: true)
                    }else{
                        UserModel.shared.setGameIDFromDeepLink(gameValue)
                    }
                }
            }
            
            
            if let record = queryItems.first(where: {$0.name == "record"}) ,let game = queryItems.first(where: {$0.name == "game"}) {
                guard let gameValue = game.value  else {return}
                guard let recordValue = record.value  else {return}
                if UserModel.shared.getLogin() == true{
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let viewController = ContestGamesRouter.createModule(gameID: gameValue) as! ContestGamesVC
                        viewController.hidesBottomBarWhenPushed = true
                        topVC.navigationController?.pushViewController(viewController, animated: true)
                    }else{
                        UserModel.shared.setGameIDFromDeepLink(gameValue)
                    }
                }
            }
        }else if components.path == "/CTck"{
            if let userIDKey = queryItems.first(where: {$0.name == "userId"}){
                guard let userIDValue = userIDKey.value  else {return}
                if UserModel.shared.getLogin() == true{
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let viewController = MainProfileRouter.createModule(profile: .publicProfile(userIDValue))
                        viewController.hidesBottomBarWhenPushed = true
                        topVC.navigationController?.pushViewController(viewController, animated: true)
                    }else{
                        UserModel.shared.setUserIDFromDeepLink(userIDValue)
                    }
                }
            }
        }else if components.path == "/K513"{
            if let postIDKey = queryItems.first(where: {$0.name == "postId"}){
                guard let postIDValue = postIDKey.value  else {return}
                if UserModel.shared.getLogin() == true{
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                                         postRecord: .init(id: Int(postIDValue) ?? 0))
                        SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
                            switch result{
                            case .success(let response):
                                if let data = response.postRecords.first{
                                    let vc = UserPostRouter.createModule(data)
                                    topVC.navigationController?.pushViewController(vc, animated: true)
                                }
                            default: break
                            }
                        }
                    }else{
                        UserModel.shared.setPostIDFromDeepLink(postIDValue)
                    }
                }
            }
        }else if components.path == "/Y4yC"{
            if let postIDKey = queryItems.first(where: {$0.name == "reelId"}){
                guard let postIDValue = postIDKey.value  else {return}
                if UserModel.shared.getLogin() == true{
                    if let topVC = UIApplication.topViewController, !topVC.isKind(of: SplashVC.self){
                        let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                                         postRecord: .init(id: Int(postIDValue) ?? 0))
                        SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
                            switch result{
                            case .success(let response):
                                if let data = response.postRecords.first{
                                    let reelsVC = ReelsRouter.createModule(data, isFeed: true, 0.0)
                                    topVC.navigationController?.pushViewController(reelsVC, animated: true)
                                }
                            default: break
                            }
                        }
                    }else{
                        UserModel.shared.setPostIDFromDeepLink(postIDValue)
                    }
                }
            }
        }
        
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        
        if userActivity.webpageURL?.absoluteString.contains("app.link") == true {
          return Branch.getInstance().continue(userActivity)
        }
        
        if let incomingURL = userActivity.webpageURL {
            print("Incoming URL is \(incomingURL)")
            let linkHandled = DynamicLinks.dynamicLinks().handleUniversalLink(incomingURL) { (dynamicLink, error) in
                guard error == nil else{
                    print("Found an error! \(error!.localizedDescription)")
                    return
                }
                if let dynamicLink = dynamicLink {
                    self.handleIncomingDynamicLink(dynamicLink)
                }
            }
            if linkHandled {
                return true
            } else {
                return false
            }
        }
        return false
    }
}

extension StringProtocol { // for Swift 4 you need to add the constrain `where Index == String.Index`
    var byWords: [SubSequence] {
        var byWords: [SubSequence] = []
        enumerateSubstrings(in: startIndex..., options: .byWords) { _, range, _, _ in
            byWords.append(self[range])
        }
        return byWords
    }
}

extension Data {
    var hexString: String {
        let hexString = map { String(format: "%02.2hhx", $0) }.joined()
        return hexString
    }
}


extension UIApplication {
    class var topViewController: UIViewController? { return getTopViewController() }
    
    private class func getTopViewController(base: UIViewController? = UIApplication.shared.keyWindow?.rootViewController) -> UIViewController? {
        if let nav = base as? UINavigationController { return getTopViewController(base: nav.visibleViewController) }
        if let tab = base as? UITabBarController {
            if let selected = tab.selectedViewController { return getTopViewController(base: selected) }
        }
        if let presented = base?.presentedViewController { return getTopViewController(base: presented) }
        return base
    }

    private class func _share(_ data: [Any],
                              applicationActivities: [UIActivity]?,
                              setupViewControllerCompletion: ((UIActivityViewController) -> Void)?) {
        let activityViewController = UIActivityViewController(activityItems: data, applicationActivities: nil)
        setupViewControllerCompletion?(activityViewController)
        UIApplication.topViewController?.present(activityViewController, animated: true, completion: nil)
    }

    class func share(_ data: Any...,
                     applicationActivities: [UIActivity]? = nil,
                     setupViewControllerCompletion: ((UIActivityViewController) -> Void)? = nil) {
        _share(data, applicationActivities: applicationActivities, setupViewControllerCompletion: setupViewControllerCompletion)
    }
    class func share(_ data: [Any],
                     applicationActivities: [UIActivity]? = nil,
                     setupViewControllerCompletion: ((UIActivityViewController) -> Void)? = nil) {
        _share(data, applicationActivities: applicationActivities, setupViewControllerCompletion: setupViewControllerCompletion)
    }
}
