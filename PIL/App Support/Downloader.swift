//
//  Downloader.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 21/11/2023.
//

import Foundation

class FileDownloader {
    
    static func downloadFile(from url: URL, completion: @escaping (String?, Error?) -> Void) {
        let task = URLSession.shared.downloadTask(with: url) { (localURL, response, error) in
            // Check for errors
            if let error = error {
                print("Error downloading file: \(error.localizedDescription)")
                return
            }
            
            // Ensure the file was downloaded
            guard let localURL = localURL else {
                print("Failed to download file.")
                return
            }
            
            do {
                
                let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!

                let destinationUrl = documentsUrl.appendingPathComponent(url.lastPathComponent)

                if FileManager().fileExists(atPath: destinationUrl.absoluteString)
                {
                    print("File already exists [\(destinationUrl.absoluteString)]")
                    completion(destinationUrl.absoluteString, nil)
                }
                else if let dataFromURL = NSData(contentsOf: url)
                {
                    if dataFromURL.write(to: destinationUrl, atomically: true)
                    {
                        print("file saved \(destinationUrl.absoluteString)")
                        completion(destinationUrl.absoluteString, nil)
                    }
                    else
                    {
                        print("error saving file")
                        let error = NSError(domain:"Error saving file", code: 1001, userInfo:nil)
                        completion(destinationUrl.absoluteString, error)
                    }
                }
                else
                {
                    let error = NSError(domain:"Error downloading file", code: 1002, userInfo:nil)
                    completion(destinationUrl.absoluteString, error)
                }
                
            } catch {
                print("Error saving file: \(error.localizedDescription)")
            }
        }
        
        // Start the download task
        task.resume()
    }

    static func loadFileSync(url: URL, completion: @escaping (String?, Error?) -> Void)
    {
        let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!

        let destinationUrl = documentsUrl.appendingPathComponent(url.lastPathComponent)

        if FileManager().fileExists(atPath: destinationUrl.absoluteString)
        {
            print("File already exists [\(destinationUrl.absoluteString)]")
            completion(destinationUrl.absoluteString, nil)
        }
        else if let dataFromURL = NSData(contentsOf: url)
        {
            if dataFromURL.write(to: destinationUrl, atomically: true)
            {
                print("file saved \(destinationUrl.absoluteString)")
                completion(destinationUrl.absoluteString, nil)
            }
            else
            {
                print("error saving file")
                let error = NSError(domain:"Error saving file", code: 1001, userInfo:nil)
                completion(destinationUrl.absoluteString, error)
            }
        }
        else
        {
            let error = NSError(domain:"Error downloading file", code: 1002, userInfo:nil)
            completion(destinationUrl.absoluteString, error)
        }
    }

    static func loadFileAsync(url: URL, completion: @escaping (String?, Error?) -> Void)
    {
        let documentsUrl =  FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!

        let destinationUrl = documentsUrl.appendingPathComponent(url.lastPathComponent)

        if FileManager().fileExists(atPath: destinationUrl.absoluteString)
        {
            print("File already exists [\(destinationUrl.absoluteString)]")
            completion(destinationUrl.absoluteString, nil)
        }
        else
        {
            let session = URLSession(configuration: URLSessionConfiguration.default, delegate: nil, delegateQueue: nil)
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            let task = session.dataTask(with: request, completionHandler:
            {
                data, response, error in
                if error == nil
                {
                    if let response = response as? HTTPURLResponse
                    {
                        if response.statusCode == 200
                        {
                            if let data = data
                            {
                                if let _ = try? data.write(to: destinationUrl, options: Data.WritingOptions.atomic)
                                {
                                    completion(destinationUrl.absoluteString, error)
                                }
                                else
                                {
                                    completion(destinationUrl.absoluteString, error)
                                }
                            }
                            else
                            {
                                completion(destinationUrl.absoluteString, error)
                            }
                        }
                    }
                }
                else
                {
                    completion(destinationUrl.absoluteString, error)
                }
            })
            task.resume()
        }
    }
}
