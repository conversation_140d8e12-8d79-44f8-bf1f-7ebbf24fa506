/// Copyright (c) 2019 Razeware LLC
///
/// Permission is hereby granted, free of charge, to any person obtaining a copy
/// of this software and associated documentation files (the "Software"), to deal
/// in the Software without restriction, including without limitation the rights
/// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
/// copies of the Software, and to permit persons to whom the Software is
/// furnished to do so, subject to the following conditions:
///
/// The above copyright notice and this permission notice shall be included in
/// all copies or substantial portions of the Software.
///
/// Notwithstanding the foregoing, you may not use, copy, modify, merge, publish,
/// distribute, sublicense, create a derivative work, and/or sell copies of the
/// Software in any work that is designed, intended, or marketed for pedagogical or
/// instructional purposes related to programming, coding, application development,
/// or information technology.  Permission for such use, copying, modification,
/// merger, publication, distribution, sublicensing, creation of derivative works,
/// or sale is expressly withheld.
///
/// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
/// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
/// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
/// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
/// THE SOFTWARE.

import AVFoundation
import CallKit

class ProviderDelegate: NSObject {
    private let callManager: CallManager
    private let provider: CXProvider
    var typeCall = String()
    init(callManager: CallManager) {
        self.callManager = callManager
        provider = CXProvider(configuration: ProviderDelegate.providerConfiguration)
        super.init()
        provider.setDelegate(self, queue: nil)
        
    }
    
    static var providerConfiguration: CXProviderConfiguration = {
        let providerConfiguration = CXProviderConfiguration(localizedName: "Hotline")
        
        providerConfiguration.supportsVideo = false
        providerConfiguration.maximumCallsPerCallGroup = 1
        providerConfiguration.supportedHandleTypes = [.phoneNumber]
        providerConfiguration.ringtoneSound = "call_tone.mp3";
        
        return providerConfiguration
    }()
    
    func reportIncomingCall(
        uuid: UUID,
        handle: String,
        hasVideo: Bool = false,
        completion: ((Error?) -> Void)?
    ) {
        let update = CXCallUpdate()
        update.remoteHandle = CXHandle(type: .phoneNumber, value: handle)
        update.hasVideo = false
        update.supportsHolding = false
        update.supportsUngrouping = false
        update.supportsGrouping = false
        update.supportsDTMF = false
        
        provider.reportNewIncomingCall(with: uuid, update: update) { error in
            if error == nil {
                configureAudioSession()
                AppDelegate.shared.callID = uuid
                let call = Call(uuid: uuid, handle: handle)
                self.callManager.add(call: call)
            }
            
            completion?(error)
        }
    }
}

// MARK: - CXProviderDelegate
extension ProviderDelegate: CXProviderDelegate {
    func providerDidReset(_ provider: CXProvider) {
        stopAudio()
        for call in callManager.calls {
            call.end()
        }
        callManager.removeAllCalls()
    }

    
    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
        guard let call = callManager.callWithUUID(uuid: AppDelegate.shared.callID) else {
            action.fail()
            return
        }
//        configureAudioSession()
        
        call.answer()
        let targetId  = AppDelegate.shared.RoomID
        let actionId = AppDelegate.shared.actionId
        if Int(actionId) == Lookups.Actions.voiceCall.rawValue {
            print("Voice Call One To One")
            self.typeCall = "one"
//            let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: targetId ,incomingCall: true, sameCall: false)
            let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: targetId , incomingCall: false, sameCall: false, isCallOpened: false , isMute: false, isListen: true)
            UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
            
        }else if Int(actionId) == Lookups.Actions.VoiceCallGroup.rawValue{
            print("Voice Call Group")
            self.typeCall = "group"
            let vc = GroupRoomChatRouter.createModule(userID: 0, roomID: targetId, groupID: 0, incomingCall: true)
            UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
        }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
        startAudio()
        print("XXXXXXXXX ONE")

    }
    
    func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
        print("XXXXXXXXX TWO")
    }
    
    
    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        guard let call = callManager.callWithUUID(uuid: AppDelegate.shared.callID) else {
            action.fail()
            return
        }
        AppDelegate.fristNoti = false
        stopAudio()
        call.end()
        action.fulfill()
        callManager.remove(call: call)
        
        let info = ["status":true , "type":typeCall] as [String : Any]
        NotificationCenter.default
                    .post(name:NSNotification.Name("DeclineCall"),
                          object: nil,userInfo: info)
    }
    
    func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) {
        guard let call = callManager.callWithUUID(uuid: AppDelegate.shared.callID) else {
            action.fail()
            return
        }
        
        call.state = action.isOnHold ? .held : .active
        
        if call.state == .held {
            stopAudio()
        } else {
            startAudio()
        }
        
        action.fulfill()
    }
    
    func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        let call = Call(uuid: AppDelegate.shared.callID, outgoing: true,
                        handle: action.handle.value)
//        configureAudioSession()
        
        call.connectedStateChanged = { [weak self, weak call] in
            guard
                let self = self,
                let call = call
            else {
                return
            }
            
            if call.connectedState == .pending {
                self.provider.reportOutgoingCall(with: call.uuid, startedConnectingAt: nil)
            } else if call.connectedState == .complete {
                self.provider.reportOutgoingCall(with: call.uuid, connectedAt: nil)
            }
        }
        
        call.start { [weak self, weak call] success in
            guard
                let self = self,
                let call = call
            else {
                return
            }
            
            if success {
                action.fulfill()
                self.callManager.add(call: call)
            } else {
                action.fail()
            }
        }
    }
    
    func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        let info = ["status":action.isMuted , "type":typeCall] as [String : Any]
        NotificationCenter.default
                    .post(name:NSNotification.Name("MuteCall"),
                          object: nil,userInfo: info)
        action.fulfill()
    }

    
}
