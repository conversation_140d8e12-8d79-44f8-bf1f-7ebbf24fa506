//
//  AppDelegate + AppLovin.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 13/05/2024.
//

import Foundation
import AppLovinSDK


extension AppDelegate {
    
    func configuerAppLovin(){
        //        app lovin sdk
        // Please make sure to set the mediation provider value to "max" to ensure proper functionality
        ALSdk.shared().mediationProvider = "max"
        
        ALSdk.shared().userIdentifier = "USER_ID"
        
        // Initialize AppLovin SDK
        ALSdk.shared().initializeSdk { (configuration: ALSdkConfiguration) in
            print("AppLovin SDK initialized: \(configuration)")
        }
    }
}
