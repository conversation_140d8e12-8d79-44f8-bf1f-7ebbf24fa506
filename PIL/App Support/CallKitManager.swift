////
////  CallManager.swift
////  PIL
////
////  Created by sameh mohammed on 06/11/2023.
////
//
//import Foundation
//import CallKit
//
////@available(iOS 14.0, *)
//class CallKitManager : NSObject, CXProviderDelegate  {
//    
//    let provider = CXProvider(configuration: CXProviderConfiguration())
//    private let callController: CXCallController
//    var targetObjectID = ""
//    var actionId = ""
//    
//    init(callController: CXCallController) {
//        self.callController = callController
//        
//        super.init()
//
//        provider.setDelegate(self, queue: nil)
//      }
//    
//    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
//        print("EEEEEEEEEEEE")
//        action.fail()
//        return
//    }
//    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
//        print("SSSSŠSSSSSS")
//        action.fulfill()
//        DispatchQueue.main.async {
//            let topVC = UIApplication.topViewController?
//            if Int(self.actionId) ?? 0 == Lookups.Actions.voiceCall.rawValue {
//                print("Voice Call One To One")
//                let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: Int(self.targetObjectID)! , incomingCall: true)
//                topVC?.navigationController?.pushViewController(vc, animated: true)
//            }else{
//                print("Voice Call Group")
//                let vc = GroupRoomChatRouter.createModule(userID: 0, roomID: Int(self.targetObjectID)! , groupID: 0 , incomingCall: true)
//                topVC?.navigationController?.pushViewController(vc, animated: true)
//            }
//        }
//    }
//    public func reportIncomingCall(id : UUID , handle : String , actionId : String , targetObjectID : String) {
//        let update = CXCallUpdate()
//        update.remoteHandle = CXHandle(type: .generic , value: handle )
//        let config = CallKit.CXProviderConfiguration()
//        config.ringtoneSound = "call_tone.mp3";
//        provider.configuration = config
//        provider.reportNewIncomingCall(with: id , update: update) { error in
//            if let error = error {
//                print(error)
//            }else{
//                self.actionId = actionId
//                self.targetObjectID = targetObjectID
//                print("Call Reported")
//            }
//        }
//    }
////    public func startCall(id : UUID , handle : String , actionId : String , targetObjectID : String) {
////        let handle = CXHandle(type: .generic , value: handle )
////        let action = CXStartCallAction(call: id , handle: handle)
////        let transactions = CXTransaction(action: action)
////        let topVC = UIApplication.topViewController?
////        callController.request(transactions) { error in
////            if let error = error {
////                print(error)
////            }else{
////                print("Call Started")
////                action.fulfill()
////                DispatchQueue.main.async {
////                    if Int(actionId) ?? 0 == Lookups.Actions.voiceCall.rawValue {
////                        print("Voice Call One To One")
////                        let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: Int(targetObjectID)! , incomingCall: true)
////                        topVC?.navigationController?.pushViewController(vc, animated: true)
////                    }else{
////                        print("Voice Call Group")
////                        let vc = GroupRoomChatRouter.createModule(userID: 0, roomID: Int(targetObjectID)! , groupID: 0 , incomingCall: true)
////                        topVC?.navigationController?.pushViewController(vc, animated: true)
////                    }
////                }
////            }i
////        }
////    }
//    // MARK: - Delegate
//    func providerDidReset(_ provider: CXProvider) {
//        //
//    }
//    
//}
