//
//  AppDelegate+CallObserver.swift
//  PIL
//
//  Created by sameh mohammed on 08/11/2023.
//

import Foundation
//import CallKit
//
//extension AppDelegate: CXCallObserverDelegate {
// func callObserver(_ callObserver: CXCallObserver, callChanged call: CXCall) {
//     print(call)
//   if call.hasEnded == true {
//     print("Call Disconnected")
//   }
//   if call.isOutgoing == true && call.hasConnected == false {
//     print("Dialing")
//   }
//   if call.isOutgoing == false && call.hasConnected == false && call.hasEnded == false {
//     print("Incoming")
//   }
//   if call.hasConnected == true && call.hasEnded == false {
//     print("Connected")
//   }
// }
//}
