//
//  AppDelegate+Location.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 06/12/2023.
//

import Foundation
import GoogleMaps
import GooglePlaces

extension AppDelegate : CLLocationManagerDelegate {
  func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
    let location: CLLocation = locations.last!
//    print("Location: \(location)")
      
    LocationHelper.SaveUser_lat(lat: location.coordinate.latitude)
    LocationHelper.SaveUser_Lng(lng: location.coordinate.longitude)
      
  }
    
  func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
    switch status {
    case .restricted:
      print("Location access was restricted.")
    case .denied:
      print("User denied access to location.")
    case .notDetermined:
      print("Location status not determined.")
    case .authorizedAlways: fallthrough
    case .authorizedWhenInUse:
      print("Location status is OK.")
        locationManager.startUpdatingLocation()

    @unknown default:
        print("Error")
    }
  }

  func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
    locationManager.stopUpdatingLocation()
    print("Error: \(error)")
  }
}
