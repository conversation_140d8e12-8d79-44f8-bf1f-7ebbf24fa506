//
//  GoogleAnalyticsHelper.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 01/03/2023.
//

import Foundation
import FirebaseCore
import FirebaseAnalytics


class GoogleAnalyticsHelper : NSObject {
    var userID = UserModel.shared.get_id()
    
    static var shared: GoogleAnalyticsHelper{
        let obj = GoogleAnalyticsHelper()
        return obj
    }
    
    func getCurrentData()->String{
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        return stringDate
    }
    
    
    func App_Launch(Date:String,Diff:String,Coin_Balance:String ){ //   Done But need know parametres
        Analytics.logEvent("App_Launch", parameters: ["Date":Date , "Diff":Diff ,"Coin Balance": Coin_Balance ,"user_id": "\(userID)"])
    }
    
    
    func Register_success(Registeration_datetime:String,Register_success:String){ //   Done
        Analytics.logEvent("Register_success", parameters: ["Registeration_datetime":Registeration_datetime , "Register_success":Register_success ,"user_id": "\(userID)"])
        
    }
    
    func Login_success(login_datetime:String,login_success:String){ //   Done
        Analytics.logEvent("Login_success", parameters: ["login_datetime":login_datetime , "login_success":login_success ,"user_id": "\(userID)"])
    }
    
    
    func trackLoginEvent(user:UserDataModel){
        var eventName = ""
        if UserModel.shared.get_loginAsGuest() == true{
            eventName = "Login_success_as_guest"
        }else{
            eventName = "Login_success"
        }
        Analytics.logEvent(eventName, parameters: [
                        "Login_success":"true" ,
                        "date_time": getCurrentData() ,
                        "user_id": "\(userID)",
                        "name":user.name ?? "",
                        "email":user.email ?? "",
                        "phone":user.phone ?? "",
                        "gender":user.gender ?? ""
        ])

    }
    
    
    func reset_account(reset_account:String){ //    Done
        Analytics.logEvent("reset_account", parameters: ["reset_account":reset_account  ,"user_id": "\(userID)"])
    }
    
    func start_game(start_game_time:String,game_name:String,event_id:String,game_entree_fee:String,game_event_type:String,game_event_name:String){ // Done
        
        Analytics.logEvent("start_game", parameters: ["start_game_time":start_game_time,
                                                      "game_name":game_name,
                                                      "game_entree_fee":game_entree_fee,
                                                      "game_event_type":game_event_type,
                                                      "game_event_name":game_event_name,
                                                      "event_id":event_id,
                                                      "user_id": "\(userID)"])
        
    }
    
    func end_Game(game_name:String,game_total_time:String,event_id:String,game_event_type:String,game_event_name:String,game_prize:String){
        Analytics.logEvent("end_Game", parameters: ["end_game_time":getCurrentData() ,
                                                    "game_name":game_name ,
                                                    "game_total_time":game_total_time,
                                                    "game_event_type":game_event_type,
                                                    "game_event_name":game_event_name,
                                                    "game_prize":game_prize,
                                                    "event_id":event_id,
                                                    "user_id": "\(userID)"])
        
    }
    
    func Data_Consumed(MB_consumed:String , Bitrate:String , date_time:String){ //   Done
        Analytics.logEvent("Data_Consumed", parameters: ["MB consumed":MB_consumed  ,
                                                         "Bitrate":Bitrate,
                                                         "date_time":date_time,
                                                         "user_id": "\(userID)"])
    }
    
    
    func purchaseItem(quantity:String , product:String  , price:String , currency:String , userLocation:String , payment_method:String){ //
        Analytics.logEvent("purchaseItem", parameters: ["quantity":quantity  ,
                                                        "product":product,
                                                        "purchaseDatetime":getCurrentData(),
                                                        "price":price,
                                                        "currency":currency,
                                                        "userLocation":userLocation,
                                                        "payment_method":payment_method,
                                                        "user_id": "\(userID)"])
        
        
    }
    
    
    func refer_a_friend(referal_url:String,referal_code:String){    // Done
        Analytics.logEvent("refer_a_friend", parameters: ["referal_url":referal_url  ,
                                                          "referal_code":referal_code,
                                                          "user_id": "\(userID)"])
    }
    
    func spin_the_wheel(type_name:String , amount:String , spin_date_time:String){  //12 Done
        Analytics.logEvent("spin_the_wheel", parameters: ["type_name":type_name  ,
                                                          "amount":amount,
                                                          "spin_date_time":spin_date_time,
                                                          "user_id": "\(userID)"])
        
    }
    
    
    func buy_scratch_cards( prize_given:String , amount_paid:String){ //
        Analytics.logEvent("buy_scratch_cards", parameters: ["date_time":getCurrentData()  ,
                                                             "prize_given":prize_given,
                                                             "amount_paid":amount_paid,
                                                             "user_id": "\(userID)"])
    }
    
    func redeem_scratch_cards(redemtion_successful:String){ // Done
        Analytics.logEvent("redeem_scratch_cards", parameters: ["date_time":getCurrentData()  ,
                                                                "redemtion_successful":redemtion_successful,
                                                                "user_id": "\(userID)"])
        
    }
    
    func buy_ticket(prize_given:String ,amount_paid:String){ //
        Analytics.logEvent("buy_ticket", parameters: ["date_time":getCurrentData()  ,
                                                      "prize_given":prize_given,
                                                      "amount_paid":amount_paid,
                                                      "user_id": "\(userID)"])
    }
    
    func redeem_ticket( redemtion_successful:String){ // Done
        Analytics.logEvent("redeem_ticket", parameters: ["date_time":getCurrentData()  ,
                                                         "redemtion_successful":redemtion_successful,
                                                         "user_id": "\(userID)"])
    }
    
    func apply_coupon(coupon_code:String , prize_given:String){ //Done
        Analytics.logEvent("redeem_ticket", parameters: ["coupon_code":coupon_code,
                                                         "date_time":getCurrentData(),
                                                         "prize_given":prize_given,
                                                         "user_id": "\(userID)"])
    }
    
    
    func redeem_coupon( coupon_code:String , redemtion_successful:String){//Done
        Analytics.logEvent("redeem_ticket", parameters: ["date_time":getCurrentData()  ,
                                                         "coupon_code":coupon_code,
                                                         "redemtion_successful":redemtion_successful,
                                                         "user_id": "\(userID)"])
    }
    
    
    func search_game(game_name:String){ // Done
        Analytics.logEvent("search_game", parameters: ["game_name":game_name  ,
                                                       "user_id": "\(userID)"])
    }
    
    
    func update_user_info(name:String , email:String , birthdate:String , phone:String  , gender:String){ // Done
        Analytics.logEvent("update_user_info", parameters: ["name":name,
                                                            "email":email,
                                                            "birthdate":birthdate,
                                                            "phone":phone,
                                                            "gender":gender,
                                                            "user_id": "\(userID)"])
        
    }
    
    func achieve_mission(mission_name:String , mission_prize:String , date_time:String , mission_type:String){ // Done
        Analytics.logEvent("achieve_mission", parameters: ["mission_name":mission_name,
                                                           "mission_prize":mission_prize,
                                                           "date_time":date_time,
                                                           "mission_type":mission_type,
                                                           "user_id": "\(userID)"])
    }
    
    
    
    func help_desk_ticket( ticket_type_id:String , report_message:String){ // Done
        
        Analytics.logEvent("help_desk_ticket", parameters: ["date_time":getCurrentData() ,
                                                            "ticket_type":ticket_type_id,
                                                            "report_message":report_message,
                                                            "user_id": "\(userID)"])
    }
    
    
    
    
    func trackShareAppEvent(){ // Done
        Analytics.logEvent("Share-App", parameters:  ["user_id": "\(userID)"])
    }
    
    func trackJoinPracticeEvent(gameName: String) {
        Analytics.logEvent("Join-Practice-\(gameName)", parameters:  ["user_id": "\(userID)"])
    }
    
    func trackJoinChallengesEvent(gameName: String) {
        Analytics.logEvent("Join-Challenge-\(gameName)", parameters:  ["user_id": "\(userID)"])
    }
    
}
