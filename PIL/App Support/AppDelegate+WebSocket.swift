//
//  AppDelegate+WebSocket.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/09/2023.
//

import Foundation
import SignalRClient
import NotificationBannerSwift
import SwiftPopup

extension AppDelegate{
    /// configure singlton web socket object so that we can listen to any event at any screen in app
    func configureSocket(){
        self.notificationsHubConnectionDelegate = NotificationsHubConnectionChatDelegate(controller: self)
        AppDelegate.notificationsHubConnection = HubConnectionBuilder(url: URL(string: self.serverUrl)!)
            .withLogging(minLogLevel: .debug)
            .withAutoReconnect()
            .withHubConnectionDelegate(delegate: self.notificationsHubConnectionDelegate!)
            .build()
        AppDelegate.notificationsHubConnection!.start()
       /// An event which send new gifts or level up notifications
        AppDelegate.notificationsHubConnection?.on(method: "RecieveMessage", callback: { (receiveMessage: ReceiveMessage?) in
            switch receiveMessage?.key{
            case .LEVEL_UP:
                let vc = LevelUpDialogVC.loadFromNib()
                vc.setDialogAnimation()
                vc.receivedNotification = receiveMessage
                vc.show(above: UIApplication.topViewController)
            default:
                let vc = GiftDialogVC.loadFromNib()
                vc.setDialogAnimation()
                vc.receivedNotification = receiveMessage
                vc.show(above: UIApplication.topViewController)
            }
            Indicator.shared.hideProgressView()
        })
        /// An event which send new conversation messages
        AppDelegate.notificationsHubConnection?.on(method: "NewMessage",
                                                   callback: {
            (messageOBJ:MessageOBJ?)  in
            if let messageOBJ = messageOBJ{
                /// Post new notification and any class can listen to it
                NotificationCenter.default.post(name: .init("NewMessage"), object: nil, userInfo: ["new_message": messageOBJ])
                guard Int(UserModel.shared.get_id()) ?? 0 != messageOBJ.fromUserId else { return }
                if (UIApplication.topViewController?.isKind(of: ChatNewVC.self) ?? false){
                    guard "\(messageOBJ.fromUserId ?? 0)" !=  "\(ChatNewVC.currentChatID)" else { return }
                }
                var notificationContent: String = ""
                
                if messageOBJ.typeID == 1{
                    notificationContent = messageOBJ.message ?? ""
                }else{
                    notificationContent = TypeMessage.init(rawValue: messageOBJ.typeID ?? 0)?.title ?? ""
                }
                
                
                let icon = UIImageView(frame: .init(x: 0, y: 0, width: 100, height: 100))
                icon.contentMode = .scaleAspectFill
                icon.sd_setImage(with: .init(string: messageOBJ.image ?? ""))
                
                let banner = FloatingNotificationBanner(
                    title: messageOBJ.name ?? "New Message".localized,
                    subtitle: notificationContent,
                    titleColor: .init(named: "Black-White"),
                    leftView: icon,
                    colors: CustomBannerColors()
                )
                banner.onTap = {
                    let type = messageOBJ.toGroupId != 0
                    var groupID = messageOBJ.toGroupId ?? 0
                    var userID = messageOBJ.fromUserId ?? 0
                    var chatID = messageOBJ.id ?? 0
                    if type == true{
                        userID = 0
                    }else{
                        groupID = 0
                    }
                    let chat = ChatNewRotur.createModule(userId: userID, groupID: groupID, chatId: chatID, isMeet: false) as! ChatNewVC
                    UIApplication.topViewController?.navigationController?.pushViewController(chat, animated: true)
                }
                banner.show()
            }
        })
        
        /// An event which send lootie animation file and present it
        AppDelegate.notificationsHubConnection?.on(method: "LottieFileNotificationPopup", callback: { (url: String?) in
            let vc = LottieDialogVC.loadFromNib()
            vc.url = url ?? ""
            vc.show(above: UIApplication.topViewController)
        })
    }
    fileprivate func connectionDidOpen() {
        print("----- connectionDidOpen")
    }
    
    fileprivate func connectionDidFailToOpen(error: Error) {
        print("----- connectionDidOpen")
        print(error.localizedDescription )
    }
    
    fileprivate func connectionDidClose(error: Error?) {
        print("---- connectionDidClose")
    }
    
    fileprivate func connectionWillReconnect(error: Error?) {
        print("---- connectionWillReconnect")
    }
    
    fileprivate func connectionDidReconnect() {
        print("---- connectionDidReconnect")
    }
    
    fileprivate func downloadLottieJson(from url: String?,_ completion: @escaping (URL) -> Void){
        guard let url = URL(string: url ?? "") else{ return }
        URLSession.shared.dataTask(with: url) { (data, response, error) -> Void in
            if error == nil && data != nil {
                do {
                    let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
                    let documentsDirectory = paths[0]
                    let jsonFileURL = documentsDirectory.appendingPathComponent(url.lastPathComponent)
                    if FileManager.default.fileExists(atPath: jsonFileURL.absoluteString){
                        print("fileExist ", jsonFileURL.absoluteString)
                        completion(jsonFileURL)
                    }else{
                        try data?.write(to: jsonFileURL)
                        print("DidWriteFile ", jsonFileURL.absoluteString)
                        completion(jsonFileURL)
                    }
                } catch { }
            }
        }.resume()
    }
    
}

class NotificationsHubConnectionChatDelegate: HubConnectionDelegate {


    weak var controller: AppDelegate?

    init(controller: AppDelegate) {
        self.controller = controller
    }

    func connectionDidOpen(hubConnection: HubConnection) {
        controller?.connectionDidOpen()
    }

    func connectionDidFailToOpen(error: Error) {
        controller?.connectionDidFailToOpen(error: error)
    }

    func connectionDidClose(error: Error?) {
        controller?.connectionDidClose(error: error)
    }

    func connectionWillReconnect(error: Error) {
        controller?.connectionWillReconnect(error: error)
    }

    func connectionDidReconnect() {
        controller?.connectionDidReconnect()
    }

    
}

struct ReceiveMessage: Decodable{
    var key: PushMessageTypes?
    var title: String?
    var image: String?
    var body_title: String?
    var body: String?
    var to: Int?
    var player_avatar: String?
    var animation_url: String?
}

enum PushMessageTypes: String, Codable{
    case GIFT = "GIFT"
    case LEVEL_UP = "LEVEL_UP"
    case POINT_WIN = "POINT_WIN"
    case TOKENS_WIN = "TOKENS_WIN"
    case MISSION_WIN = "MISSION_WIN"
}

class CustomBannerColors: BannerColorsProtocol {

    internal func color(for style: BannerStyle) -> UIColor {
        switch style {
        default: return UIColor(named: "Black")!
        }
    }

}
