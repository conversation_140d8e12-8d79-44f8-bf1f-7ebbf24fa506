//
//  MoEngageHelper.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/23/22.
//

// Possible Values for Data Center

//import Foundation
//import MoEngage
//import AppTrackingTransparency

//class MoEngageHelper{
//    static var shared: MoEngageHelper{
//        let obj = MoEngageHelper()
//        return obj
//    }
//    var APPID = ""//"4D3INGY2Q4WH12MRBFXY4UTY"
//    var AppGroupID = ""
//
//    private init(){}
//
//    func initialisationMoEngage( didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?){
//
//        let yourMoEAppID = APPID
//        let sdkConfig = MOSDKConfig.init(appID: yourMoEAppID)
////        sdkConfig.moeDataCenter = DATA_CENTER_03
//
//        DispatchQueue.main.async {
//            #if DEBUG
//            MoEngage.sharedInstance().initializeTest(with: sdkConfig, andLaunchOptions: launchOptions)
//            #else
//            MoEngage.sharedInstance().initializeLive(with: sdkConfig, andLaunchOptions: launchOptions)
//            #endif
//        }
//    }
//
//
//    func UpdateConfigMoengage(){
//        if let currentConfig = MoEngage.sharedInstance().getDefaultSDKConfiguration() {
//            currentConfig.analyticsDisablePeriodicFlush = true
//            MoEngage.sharedInstance().update(currentConfig)
//        }
//    }
//
//
//    func dataCenterMoengage(){
//        print("Configuer MoEngag 2")
//        let sdkConfig = MOSDKConfig.init(appID: APPID)
//        sdkConfig.moeDataCenter = DATA_CENTER_03 //eg
//    }
//
//
//    /*
//     - every build add new number of build in constant file
//     - save key in userDefault to Build App
//     - when update new version update build in constat file
//     - and check the same build numer or no with user default
//     - if same send install App
//     - if not send update App
//     */
//    func InstallAPPMoengage(){
//        MoEngage.sharedInstance().appStatus(INSTALL)
//    }
//
//    func UpdateAppMoengage(){
//        MoEngage.sharedInstance().appStatus(UPDATE)
//    }
//
//
//    func loginMoengage(UserID:String){
//        MoEngage.sharedInstance().setUserUniqueID(UserID) // UNIQUE_ID is used to uniquely identify a user.
//    }
//
//    func logoutMoengage(){
//        MoEngage.sharedInstance().resetUser()
//    }
//
//    func updateUniqieID(UPDATED_UNIQUE_ID:String){
//        MoEngage.sharedInstance().setAlias(UPDATED_UNIQUE_ID)
//    }
//
//    func SetUSerDataMoengage(userName:String , userEmailID:String , userPhoneNo:String , gender:UserGender ){
//        MoEngage.sharedInstance().setUserName(userName)
//        MoEngage.sharedInstance().setUserEmailID(userEmailID)
//        MoEngage.sharedInstance().setUserMobileNo(userPhoneNo) //User Phone No / Mobile Number must be tracked as a string to work properly in MoEngage systems.
//        MoEngage.sharedInstance().setUserGender(gender) //Use UserGender enumerator for this
//    }
//
//
//    func CustomUserAttributes(){ // if need to add new key and value
//        MoEngage.sharedInstance().setUserAttribute("Bengaluru", forKey: "Current_City")
//    }
//
//
//    func setLoacationMoengage(lat:Double , long:Double){
//        MoEngage.sharedInstance().setUserAttributeLocationLatitude(lat, longitude: long, forKey:"location_attribute_name")
//    }
//
//    func reackingUserLoaction(){
//        MoEngage.sharedInstance().trackLocale()
//    }
//
//
//    //MARK: - notifications
//    func SDKNotifications(){
//        //For SDK version 7.0.0 and above
//        let sdkConfig = MOSDKConfig.init(appID: APPID)
//        sdkConfig.appGroupID = AppGroupID
//
//        // For SDK version 6.3.1 and below
//        MoEngage.setAppGroupID(AppGroupID)
//
//    }
//
//     func requestIDFAPermission() {
//        if #available(iOS 14, *) {
//            ATTrackingManager.requestTrackingAuthorization { status in
//                switch status {
//                case .authorized:
//                    print("ATTrackingManager - Authorized")
//                case .denied:
//                    print("Denied")
//                case .notDetermined:
//                    // Tracking authorization dialog has not been shown
//                    print("Not Determined")
//                case .restricted:
//                    print("Restricted")
//                @unknown default:
//                    print("Unknown")
//                }
//            }
//        }
//    }
    
//}


extension AppDelegate{
    //MARK:- Deeplinks Processing

 

    //MARK:- Methods Deprecated from iOS9

//    func application(_ application: UIApplication, handleOpen url: URL) -> Bool {
//        MoEngage.sharedInstance().processURL(url)
//        // Pass deep link to Adjust in order to potentially reattribute user.
//        Adjust.appWillOpen(url)
//        return true
//    }

//    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
//        MoEngage.sharedInstance().processURL(url)
        //rest of the implementation
        // Pass deep link to Adjust in order to potentially reattribute user.
//        if (userActivity?.activityType == NSUserActivityTypeBrowsingWeb) {
//            // Pass deep link to Adjust in order to potentially reattribute user.
//            Adjust.appWillOpen((userActivity?.webpageURL!)!)
//        }

//        return true
//    }
//    
//    func adjustAttributionChanged(_ attribution: ADJAttribution?) {
//        NSLog("Attribution callback called!")
//        NSLog("Attribution: %@", attribution ?? "")
//    }
//
//    func adjustEventTrackingSucceeded(_ eventSuccessResponseData: ADJEventSuccess?) {
//        NSLog("Event success callback called!")
//        NSLog("Event success data: %@", eventSuccessResponseData ?? "")
//    }
//
//    func adjustEventTrackingFailed(_ eventFailureResponseData: ADJEventFailure?) {
//        NSLog("Event failure callback called!")
//        NSLog("Event failure data: %@", eventFailureResponseData ?? "")
//    }
//
//    func adjustSessionTrackingSucceeded(_ sessionSuccessResponseData: ADJSessionSuccess?) {
//        NSLog("Session success callback called!")
//        NSLog("Session success data: %@", sessionSuccessResponseData ?? "")
//    }
//
//    func adjustSessionTrackingFailed(_ sessionFailureResponseData: ADJSessionFailure?) {
//        NSLog("Session failure callback called!");
//        NSLog("Session failure data: %@", sessionFailureResponseData ?? "")
//    }
//
//    func adjustDeeplinkResponse(_ deeplink: URL?) -> Bool {
//        NSLog("Deferred deep link callback called!")
//        NSLog("Deferred deep link URL: %@", deeplink?.absoluteString ?? "")
//        return true
//    }
    
}
