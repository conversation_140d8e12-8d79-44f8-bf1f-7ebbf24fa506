////
////  AdjustHelper.swift
////  PIL
////
////  Created by <PERSON><PERSON> saeed on 1/31/22.
////
//
////import Foundation
////import Adjust
////import AppTrackingTransparency
////import AdSupport
//
//class AdjustHelper : NSObject, AdjustDelegate {
////    var AppID = ""//"h6sx3zjgnpc0"
////    var userID = UserModel.shared.get_id()
////    
////    static var shared: AdjustHelper{
////        let obj = AdjustHelper()
////        return obj
////    }
////  
////    private override init(){}
////    
////    func getCurrentData()->String{
////        let currentDate = Date()
////        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
////        return stringDate
////    }
////    
////    func initialisationAdjust(){
////        let appToken = AppID
////        let environment = ADJEnvironmentProduction
////
////        let adjustConfig = ADJConfig(appToken: appToken, environment: environment, allowSuppressLogLevel: true)
////        adjustConfig?.deactivateSKAdNetworkHandling()
////        adjustConfig?.allowAdServicesInfoReading = true
////        adjustConfig?.allowIdfaReading = true
////        adjustConfig?.eventBufferingEnabled = true
////        adjustConfig?.defaultTracker = AppID
////        adjustConfig?.logLevel = ADJLogLevelVerbose
////        adjustConfig?.delegate = self
////
////        Adjust.appDidLaunch(adjustConfig)
////
////        askPermation()
////    }
//    
// 
////    func askPermation(){
////        if #available(iOS 14, *) {
////            print("status notDetermined == \(ATTrackingManager.trackingAuthorizationStatus == .notDetermined)")
////            print("status authorized == \(ATTrackingManager.trackingAuthorizationStatus == .authorized)")
////            print("IDFA == \(ASIdentifierManager.shared().advertisingIdentifier)")
////            ATTrackingManager.requestTrackingAuthorization { (status) in
////                print("IDFA == \(ASIdentifierManager.shared().advertisingIdentifier)")
////                print("authorized == \(status == .authorized)")
////                print("denied == \(status == .denied)")
////                print("restricted == \(status == .restricted)")
////            }
////        }
////    }
////
////    func adjustDeeplinkResponse(_ deeplink: URL?) -> Bool {
////        // deeplink object contains information about deferred deep link content
////
////        // Apply your logic to determine whether the Adjust SDK should try to open the deep link
////        return true
////        // or
////        // return false
////    }
////
//    
//    func App_Launch(Date:String,Diff:String,Coin_Balance:String ){ //   Done But need know parametres
////        let event = ADJEvent(eventToken: "wh90ep")
////        event?.addPartnerParameter("Date", value:Date)
////        event?.addPartnerParameter("Diff", value:Diff)
////        event?.addPartnerParameter("Coin Balance", value:Coin_Balance)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func Register_success(Registeration_datetime:String,Register_success:String){ //   Done
////        let event = ADJEvent(eventToken: "ew0b62")
////        event?.addPartnerParameter("Registeration_datetime", value:Registeration_datetime)
////        event?.addPartnerParameter("Register_success", value:Register_success)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////        Adjust.trackEvent(event)
//    }
//    
//    func Login_success(login_datetime:String,login_success:String){ //   Done
////        let event = ADJEvent(eventToken: "anhyib")
////        event?.addPartnerParameter("login_datetime", value:login_datetime)
////        event?.addPartnerParameter("login_success", value:login_success)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func reset_account(reset_account:String){ //    Done
////        let event = ADJEvent(eventToken: "v3ps0b")
////        event?.addPartnerParameter("reset_account", value:reset_account)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////        Adjust.trackEvent(event)
//    }
//    
//    func start_game(start_game_time:String,game_name:String,event_id:String,game_entree_fee:String,game_event_type:String,game_event_name:String){ // Done
////        let event = ADJEvent(eventToken: "ijgufd")
////        event?.addPartnerParameter("start_game_time", value:start_game_time)
////        event?.addPartnerParameter("game_name", value:game_name)
////        event?.addPartnerParameter("game_entree_fee", value:game_entree_fee)
////        event?.addPartnerParameter("game_event_type", value:game_event_type)
////        event?.addPartnerParameter("game_event_name", value:game_event_name)
////        event?.addPartnerParameter("event_id", value:event_id)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func end_Game(game_name:String,game_total_time:String,event_id:String,game_event_type:String,game_event_name:String,game_prize:String){
//        //end_game_time: -> getCurrentData
////        let event = ADJEvent(eventToken: "fgcjuu")
////        event?.addPartnerParameter("end_game_time", value:getCurrentData())
////        event?.addPartnerParameter("game_name", value:game_name)
////        event?.addPartnerParameter("game_total_time", value:game_total_time)
////        event?.addPartnerParameter("game_event_type", value:game_event_type)
////        event?.addPartnerParameter("game_event_name", value:game_event_name)
////        event?.addPartnerParameter("game_prize", value:game_prize)
////        event?.addPartnerParameter("event_id", value:event_id)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func Data_Consumed(MB_consumed:String , Bitrate:String , date_time:String){ //   Done
////        let event = ADJEvent(eventToken: "9r743h")
////        event?.addPartnerParameter("MB consumed", value:MB_consumed)
////        event?.addPartnerParameter("Bitrate", value:Bitrate)
////        event?.addPartnerParameter("date_time", value:date_time)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func purchaseItem(quantity:String , product:String  , price:String , currency:String , userLocation:String , payment_method:String){ //
////
////        let event = ADJEvent(eventToken: "uaf1sj")
////        event?.addPartnerParameter("quantity", value:quantity)
////        event?.addPartnerParameter("product", value:product)
////        event?.addPartnerParameter("purchaseDatetime", value: getCurrentData())
////        event?.addPartnerParameter("price", value:price)
////        event?.addPartnerParameter("currency", value:currency)
////        event?.addPartnerParameter("userLocation", value:userLocation)
////        event?.addPartnerParameter("payment_method", value:payment_method)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//        
//    }
//    
//    
//    func social_media_share(share_url:String){ // XXXX
////        let event = ADJEvent(eventToken: "7pgglf")
////        event?.addPartnerParameter("share_url", value:share_url)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func refer_a_friend(referal_url:String,referal_code:String){    // Done
////        let event = ADJEvent(eventToken: "dbponn")
////        event?.addPartnerParameter("referal_url", value:referal_url)
////        event?.addPartnerParameter("referal_code", value:referal_code)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func spin_the_wheel(type_name:String , amount:String , spin_date_time:String){  //12 Done
////        let event = ADJEvent(eventToken: "4gy5da")
////        event?.addPartnerParameter("type_name", value:type_name)
////        event?.addPartnerParameter("amount", value:amount)
////        event?.addPartnerParameter("spin_date_time", value:spin_date_time)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func buy_scratch_cards( prize_given:String , amount_paid:String){ //
////        let event = ADJEvent(eventToken: "wljqg8")
////        event?.addPartnerParameter("date_time", value: getCurrentData() )
////        event?.addPartnerParameter("prize_given", value:prize_given)
////        event?.addPartnerParameter("amount_paid", value:amount_paid)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func redeem_scratch_cards(redemtion_successful:String){ // Done
////        let event = ADJEvent(eventToken: "ukl2dr")
////        event?.addPartnerParameter("date_time", value: getCurrentData() )
////        event?.addPartnerParameter("redemtion_successful", value:redemtion_successful)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func buy_ticket(  prize_given:String ,amount_paid:String){ //
////        let event = ADJEvent(eventToken: "ilds8y")
////        event?.addPartnerParameter("date_time", value: getCurrentData())
////        event?.addPartnerParameter("prize_given", value:prize_given)
////        event?.addPartnerParameter("amount_paid", value:amount_paid)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func redeem_ticket( redemtion_successful:String){ // Done
////        let event = ADJEvent(eventToken: "q9ajcq")
////        event?.addPartnerParameter("date_time", value: getCurrentData())
////        event?.addPartnerParameter("redemtion_successful", value:redemtion_successful)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func apply_coupon(coupon_code:String , prize_given:String){ //Done
////        let event = ADJEvent(eventToken: "x42sgy")
////        event?.addPartnerParameter("coupon_code", value:coupon_code)
////        event?.addPartnerParameter("date_time", value:getCurrentData() )
////        event?.addPartnerParameter("prize_given", value:prize_given)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func redeem_coupon( coupon_code:String , redemtion_successful:String){//Done
////        let event = ADJEvent(eventToken: "270j88")
////        event?.addPartnerParameter("coupon_code", value:coupon_code)
////        event?.addPartnerParameter("date_time", value:getCurrentData())
////        event?.addPartnerParameter("redemtion_successful", value:redemtion_successful)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func search_game(game_name:String){ // Done
////        let event = ADJEvent(eventToken: "ccotlg")
////        event?.addPartnerParameter("game_name", value:game_name)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func update_user_info(name:String , email:String , birthdate:String , phone:String  , gender:String){ // Done
////        let event = ADJEvent(eventToken: "l7pqs5")
////        event?.addPartnerParameter("name", value:name)
////        event?.addPartnerParameter("email", value:email)
////        event?.addPartnerParameter("birthdate", value:birthdate)
////        event?.addPartnerParameter("phone", value:phone)
////        event?.addPartnerParameter("gender", value:gender)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func achieve_mission(mission_name:String , mission_prize:String , date_time:String , mission_type:String){ // Done
////        let event = ADJEvent(eventToken: "9i8pwf")
////        event?.addPartnerParameter("mission_name", value:mission_name)
////        event?.addPartnerParameter("mission_prize", value:mission_prize)
////        event?.addPartnerParameter("date_time", value:date_time)
////        event?.addPartnerParameter("mission_type", value:mission_type)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//
//    }
//    
//    func social_activity_follow(target_user_id:String){  // Done
////        let event = ADJEvent(eventToken: "69jb2k")
////        event?.addPartnerParameter("target_user_id", value:target_user_id)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func social_activity_block(target_user_id:String){ // Done
////        let event = ADJEvent(eventToken: "3t87qe")
////        event?.addPartnerParameter("target_user_id", value:target_user_id)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    
//    func social_activity_report(target_user_id:String ,report_type:String , report_message:String){ //Done
////        let event = ADJEvent(eventToken: "2de738")
////        event?.addPartnerParameter("target_user_id", value:target_user_id)
////        event?.addPartnerParameter("date_time", value:getCurrentData())
////        event?.addPartnerParameter("report_type", value:report_type)
////        event?.addPartnerParameter("report_message", value:report_message)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
//    func fantasy_create_team(team1_name:String , team2_name:String , contest_entree_fee:String){ // Done
////        let event = ADJEvent(eventToken: "t0n1z8")
////        event?.addPartnerParameter("team1_name", value:team1_name)
////        event?.addPartnerParameter("team2_name", value:team2_name)
////        event?.addPartnerParameter("contest_entree_fee", value:contest_entree_fee)
////        event?.addPartnerParameter("create_time_date", value:getCurrentData())
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//    
// 
//    func help_desk_ticket( ticket_type_id:String , report_message:String){ // Done
////        let event = ADJEvent(eventToken: "a4c3ch")
////        event?.addPartnerParameter("date_time", value:getCurrentData() )
////        event?.addPartnerParameter("ticket_type", value:ticket_type_id)
////        event?.addPartnerParameter("report_message", value:report_message)
////        event?.addPartnerParameter("user_id", value:"\(userID)")
////
////        Adjust.trackEvent(event)
//    }
//}
