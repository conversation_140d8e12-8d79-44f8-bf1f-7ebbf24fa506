////
////  SceneDelegate.swift
////  PIL
////
////  Created by <PERSON> on 8/25/21.
////
//
//import UIKit
//import IQKeyboardManager
//import MOLH
//class SceneDelegate: UIResponder, UIWindowSceneDelegate {
//    
//    var window: UIWindow?
//    var userWorker: UserWorkerProtocol?
//    var User = UserModel.shared
//
//    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
//        
//        if let windoScene = scene as? UIWindowScene{
//            let window = UIWindow(windowScene: windoScene)
//            self.window = window
//            window.makeKeyAndVisible()
//            
////            Unity.shared.setHostMainWindow(window)
//            IQKeyboardManager.shared().isEnabled = true
//            MOLH.shared.activate(true)
//            
//            var rootvc : UIViewController
//            rootvc = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "SplashVC") as! SplashVC
//            let sharedSceneDelegate = UIApplication.shared.connectedScenes.first?.delegate as? SceneDelegate
//            let nav1 = UINavigationController()
//            nav1.viewControllers = [rootvc]
//            sharedSceneDelegate?.window?.rootViewController = nav1
//            sharedSceneDelegate?.window?.makeKeyAndVisible()
//            
//        }
//    }
//
//    func sceneDidDisconnect(_ scene: UIScene) {
//        // Called as the scene is being released by the system.
//        // This occurs shortly after the scene enters the background, or when its session is discarded.
//        // Release any resources associated with this scene that can be re-created the next time the scene connects.
//        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
//        print("sceneDidDisconnect")
//
//    }
//
//    func sceneDidBecomeActive(_ scene: UIScene) {
//        // Called when the scene has moved from an inactive state to an active state.
//        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
//        setUserData(online: true)
//        print("sceneDidBecomeActive")
//        Unity.shared.applicationDidEnterBackground(isBool: false)
//    }
//
//    func sceneWillResignActive(_ scene: UIScene) {
//        // Called when the scene will move from an active state to an inactive state.
//        // This may occur due to temporary interruptions (ex. an incoming phone call).
//        print("sceneWillResignActive")
//    }
//
//    func sceneWillEnterForeground(_ scene: UIScene) {
//        // Called as the scene transitions from the background to the foreground.
//        // Use this method to undo the changes made on entering the background.
//        print("sceneWillEnterForeground")
//    }
//
//    func sceneDidEnterBackground(_ scene: UIScene) {
//        // Called as the scene transitions from the foreground to the background.
//        // Use this method to save data, release shared resources, and store enough scene-specific state information
//        // to restore the scene back to its current state.
//
//        // Save changes in the application's managed object context when the application transitions to the background.
//        setUserData(online: false)
//        
//        print("sceneDidEnterBackground")
//        Unity.shared.applicationDidEnterBackground(isBool: true)
//        (UIApplication.shared.delegate as? AppDelegate)?.saveContext()
//    }
//
//    func setUserData(online: Bool){
//        if User.getLogin(){
//            userWorker = UserWorker()
//            DispatchQueue.global(qos: .background).async{ [weak self] in
//                guard let self = self else { return }
//                let userModel = UserChatModel(name: self.User.get_username(),
//                                              image: self.User.get_image(),
//                                              FCM: self.User.get_FCMToken(),
//                                              online: online)
//
//
//                self.userWorker?.uploadChatUser(userID: self.User.get_id(), model: userModel, compilition: { (Status, Error) in
//                    if let error = Error{
//                        print(error)
//                    }else{
//                        print("Success")
//                    }
//                })
//            }
//        }
//    }
//}
