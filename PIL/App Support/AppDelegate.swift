//
//  AppDelegate.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//

import UIKit
import CoreData
import MOLH
import IQKeyboardManager
import FirebaseCore
import FirebaseMessaging
import FirebaseDynamicLinks
import FirebaseAnalytics
import UserNotifications
import NotificationCenter
import GoogleSignIn
import GoogleMobileAds
import BranchSDK
import SignalRClient
import netfox
import CallKit
import PushKit
import GoogleMaps
import GooglePlaces
import FBSDKCoreKit
//import FBAudienceNetwork
import AppTrackingTransparency
import AdSupport
import SCSDKLoginKit
import AppLovinSDK

@main
class AppDelegate: UIResponder, UIApplicationDelegate, UISceneDelegate/*, MOMessagingDelegate*/ , CXProviderDelegate ,PKPushRegistryDelegate , CXCallObserverDelegate {
    
    static var fristNoti = false
    var window: UIWindow?
    var userWorker: UserWorkerProtocol?
    var User = UserModel.shared
    var orientationMask: UIInterfaceOrientationMask = .portrait
    let serverUrl = "https://socialapp.pil.live/ws?User=\(UserModel.shared.get_id())"
    static var notificationsHubConnection: HubConnection?
    var notificationsHubConnectionDelegate: HubConnectionDelegate?
    let callManager = CallManager()
    var providerDelegate: ProviderDelegate!
    var RoomID = Int()
    var SenderID = Int()
    var actionId = String()
    var callID = UUID()
    var locationManager = CLLocationManager()
    var lastReceivedNotification: [AnyHashable: Any]?

    class var shared: AppDelegate {
      return UIApplication.shared.delegate as! AppDelegate
    }
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        //        UIApplication.shared.isIdleTimerDisabled = true
        MOLH.shared.activate(true)
        //        UserModel.shared.setLang(lang: app_lang)
        GADMobileAds.sharedInstance().start(completionHandler: nil)
        if #available(iOS 13.0, *) {
            if UserModel.shared.get_DarkMode() == "light"{
                window?.overrideUserInterfaceStyle = .light
            }else{
                UserModel.shared.darMode(status: "dark")
                window?.overrideUserInterfaceStyle = .dark
            }
        }
        IQKeyboardManager.shared().isEnabled = true
//         FB FBAudienceNetwork
//        FBAdSettings.setAdvertiserTrackingEnabled(true)
        
        FirebaseApp.configure()
        Analytics.setAnalyticsCollectionEnabled(false)
        registerForPushNotifications()
        //MARK: - unity
        Unity.shared.setHostMainWindow(window)
        
        openSplash()
        SetCountryFristTime()
        
        ApplicationDelegate.shared.application(application,didFinishLaunchingWithOptions: launchOptions) // faceBook
        Settings.shared.isAdvertiserIDCollectionEnabled  = true
        Settings.shared.isAutoLogAppEventsEnabled = true
        Settings.shared.isAdvertiserTrackingEnabled = true
        
        Branch.getInstance().initSession(launchOptions: launchOptions) { (params, error) in
            print(params as? [String: AnyObject] ?? {})
            // Access and use deep link data here (nav to page, display content, etc.)
        }
        
        configureSocket()
        configureGoogleMaps()
        
        locationManager.delegate = self
        locationManager.requestWhenInUseAuthorization()
        locationManager.startUpdatingLocation()

                
        if let userInfo = launchOptions?[.remoteNotification] as? [AnyHashable: Any] {
            // Process the notification data when the app is launched due to the notification
            self.handleBackgroundNotification(userInfo)
        }        
        /// Handle Call Provider
        /// - Parameters:  callManager
        ///   - completion: Output Call
        providerDelegate = ProviderDelegate(callManager: callManager)
        /// Handle PushKit
        let registry = PKPushRegistry(queue: nil)
        registry.delegate = self
        registry.desiredPushTypes = [PKPushType.voIP]
        requestIDFA()
//        NFX.sharedInstance().start()
        
        configuerAppLovin()
        return true
    }
    
    
    func requestIDFA() {
        ATTrackingManager.requestTrackingAuthorization { status in
            DispatchQueue.main.async {
                print("--->Status---->\(status)")
                if status == .authorized {
                    let idfa = ASIdentifierManager.shared().advertisingIdentifier.uuidString
                    print(idfa)
                    UserModel.shared.setIDFA(code: idfa)
                } else {
                    print("NOT")
                }
            }
        }
    }
    
    @objc func providerDidReset(_ provider: CXProvider) {
    }
 
    //MARK: - CXAnswerCallAction
    /// - Parameters :
    /// - roomID : string
    /// - action : Go To LiveOneToOne Controller and Open Call
    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
        UserModel.shared.setCallUUID(callUUID: "\(action.callUUID)")
        print("Answeeer")
        if Int(actionId) == Lookups.Actions.voiceCall.rawValue {
//            let vc = LiveOneToOneRouter.createModule(userID: 0, roomID: self.RoomID  ,incomingCall: true, sameCall: false)
            let vc = LiveOneToOneRouter.createModule(userID: self.SenderID , roomID: self.RoomID , incomingCall: false, sameCall: false, isCallOpened: false , isMute: false, isListen: true)
            UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
        }else{
            let vc = GroupRoomChatRouter.createModule(userID: 0, roomID: self.RoomID , groupID: 0, incomingCall: true)
            UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
        }
        action.fulfill()
    }
    /// CXEndCallAction
    /// complition : Leave & End Call
    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        print("Cancel")
        let request = RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: self.RoomID),
                                     createdBy:  Int(UserModel.shared.get_id()) ?? 0)
        leaveRoom(model: request)
        action.fulfill()
    }
    
    
    
    func provider(_ provider: CXProvider, timedOutPerforming action: CXAction) {
        
    }
    
    /// callChanged when happened any changed in call
    func callObserver(_ callObserver: CXCallObserver, callChanged call: CXCall) {
        if call.hasEnded == true {
            print("Call Disconnected")
        }
        if call.isOutgoing == true && call.hasConnected == false {
            print("Dialing")
        }
        if call.isOutgoing == false && call.hasConnected == false && call.hasEnded == false {
            print("Incoming")
        }
        if call.hasConnected == true && call.hasEnded == false {
            print("Connected")
        }
    }
    
    /// PushKit didUpdate
    /// - completion: VoIP Token
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        let VoIPToken = pushCredentials.token.map { String(format: "%02.2hhx", $0) }.joined()
        UserModel.shared.setVoIP(token: VoIPToken)
        print("TTTokkkkenVVVV---> \(VoIPToken)")
        print(pushCredentials.token.map { String(format: "%02.2hhx", $0) }.joined())
    }
    /// didReceiveIncomingPushWith
    /// Receive payload from pushRegistry
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        
        let dict = payload.dictionaryPayload
        let notification = dict["notification"] as! Dictionary<String, Any>
        let body = notification["body"] as? String ?? "Tawfiq"
        let actionId = notification["actionId"] as! String
        let targetObjectId = notification["targetObjectID"] as! String
        let senderId = notification["sender"] as! String
        //        GetImg(senderId) { image in
        //            print("User's image: \(image)")
        self.SenderID = Int(senderId) ?? 0
        self.actionId = actionId
        self.RoomID =  Int(targetObjectId) ?? 0
        print(dict , body , targetObjectId , actionId)
        let config: CXProviderConfiguration
        if #available(iOS 14, *) {
            config = CXProviderConfiguration()
        } else {
            config = CXProviderConfiguration(localizedName: "PlayIt")
        }
        let iconMaskImage = #imageLiteral(resourceName: "A_NewLogo")
        if let img = iconMaskImage.pngData() {
            config.iconTemplateImageData = iconMaskImage.pngData()
        }else{
            print("FFFFFFailed")
        }
        config.ringtoneSound = "call_tone.mp3"
        config.includesCallsInRecents = false;
        config.supportsVideo = false;
        let provider = CXProvider(configuration: config)
        provider.setDelegate(self, queue: nil)
        let update = CXCallUpdate()
        update.remoteHandle = CXHandle(type: .generic, value: body)
        update.hasVideo = false
        provider.reportNewIncomingCall(with: UUID(), update: update, completion: { error in })
        //        }
    }
    
    //MARK: - incomingCall
    /// - Parameters :
    /// - name : string
    /// - completion : displayIncomingCall
    func incomingCall(name:String){
        let backgroundTaskIdentifier =
          UIApplication.shared.beginBackgroundTask(expirationHandler: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
          AppDelegate.shared.displayIncomingCall(
            uuid: UUID(),
            handle: name,
            hasVideo: false
          ) { _ in
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
          }
        }
    }
    /// - displayIncomingCall
    /// - Parameters :
    /// - uuid : UUID
    /// - handle: String
    /// - hasVideo: Bool
    /// - completion : Call
    func displayIncomingCall(
      uuid: UUID,
      handle: String,
      hasVideo: Bool = false,
      completion: ((Error?) -> Void)?
    ) {
      providerDelegate.reportIncomingCall(
        uuid: uuid,
        handle: handle,
        hasVideo: hasVideo,
        completion: completion)
    }
    
    //MARK: - Leave Room
    /// - Parameters :
    /// - model : RoomJoinLeaveRM
    /// - completion : Leaved Room Success
    func leaveRoom(model: RoomJoinLeaveRM){
        SocialWorker.shared.LeaveRoom(model: model) { result , statusCode in
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success leave Room
                    print("Leaved Success")
                }
                break
            case .failure(let error):
                break
            }
        }
    }
    
    
    func GetImg(_ userId: String, completion: @escaping (Data) -> Void) {
        ProfileWorker.shared.getProfile(userId: userId) { result, statusCode in
            switch result {
            case .success(let response):
                let img = response.userRecords?.first?.image ?? ""
                self.loadImage(from: URL(string: img)!) { img in
                    let imgData = img?.pngData()
                    completion(imgData!)
                }
            case .failure:
                completion((UIImage(named: "A_NewLogo")?.pngData())!) // Pass an empty string on failure
            }
        }
    }
    
    func loadImage(from url: URL, completion: @escaping (UIImage?) -> Void) {
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let data = data, let image = UIImage(data: data) {
                completion(image)
            } else {
                completion(nil)
            }
        }
        task.resume()
    }
    
    
    
    func configureGoogleMaps() {
        GMSServices.provideAPIKey(googleApiKey)
        GMSPlacesClient.provideAPIKey(googleApiKey)
    }
  
    
    
    func openSplash(){
        var rootvc : UIViewController
        rootvc = UIStoryboard(name: "Main", bundle: nil).instantiateViewController(withIdentifier: "SplashVC") as! SplashVC
        let nav1 = UINavigationController()
        nav1.viewControllers = [rootvc]
        self.window?.rootViewController = nav1
        self.window?.makeKeyAndVisible()
    }
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return self.orientationMask
    }
    
    // faceBook And MoEngage
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
//        MoEngage.sharedInstance().processURL(url)
//        
//        ApplicationDelegate.shared.application(
//            app,
//            open: url,
//            sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
//            annotation: options[UIApplication.OpenURLOptionsKey.annotation]
//        )
//        
//        
//        guard let _ = options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
//              let _ = options[UIApplication.OpenURLOptionsKey.annotation] else {
//            return false
//        }
//        
//        if url.absoluteString.contains("link_click_id") == true{
//            return Branch.getInstance().application(app, open: url, options: options)
//        }
        
      
        return SCSDKLoginClient.application(app, open: url, options: options)

//        return GIDSignIn.sharedInstance.handle(url)
    }
    
    
    // MARK: - Core Data stack
    
    lazy var persistentContainer: NSPersistentContainer = {
        
        let container = NSPersistentContainer(name: "PIL")
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        return container
    }()
    
    lazy var persistentSocialContainer: NSPersistentContainer = {
        
        let container = NSPersistentContainer(name: "Social")
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        return container
    }()
    
    // MARK: - Core Data Saving support
    
    func saveContext () {
        let context = persistentContainer.viewContext
        if context.hasChanges {
            do {
                try context.save()
                print("Save successfully")
            } catch {
                
                let nserror = error as NSError
                fatalError("Unresolved error \(nserror), \(nserror.userInfo)")
            }
        }
    }
    
    func saveSocialContext () {
        let context = persistentSocialContainer.viewContext
        if context.hasChanges {
            do {
                try context.save()
                print("Save successfully")
            } catch {
                
                let nserror = error as NSError
                fatalError("Unresolved error \(nserror), \(nserror.userInfo)")
            }
        }
    }
    
    
    func SetCountryFristTime(){
        let currentLocale = Locale.current
        // Get the country code (e.g., "US", "GB")
        if let countryCode = currentLocale.regionCode {
            // Get the currency code (e.g., "USD", "GBP")
            if let currencyCode = currentLocale.currencyCode {
                print("Currency Code: \(currencyCode)")
                let codeFlag = Locale(identifier: countryCode)
                print("codeFlag : \(codeFlag.identifier)")
                if UserModel.shared.getCountryCode() == "" {
                    UserModel.shared.setCountryCode(code: currencyCode)
                    let flag = emojiFlagDelegate(for: codeFlag.identifier) ?? ""
                    print("Currency flag: \(flag)")
                    UserModel.shared.setCountryFlag(code: flag)
                }
            }
        }
    }
    
    func emojiFlagDelegate(for countryCode: String) -> String! {
        func isLowercaseASCIIScalar(_ scalar: Unicode.Scalar) -> Bool {
            return scalar.value >= 0x61 && scalar.value <= 0x7A
        }

        func regionalIndicatorSymbol(for scalar: Unicode.Scalar) -> Unicode.Scalar {
            precondition(isLowercaseASCIIScalar(scalar))

            // 0x1F1E6 marks the start of the Regional Indicator Symbol range and corresponds to 'A'
            // 0x61 marks the start of the lowercase ASCII alphabet: 'a'
            return Unicode.Scalar(scalar.value + (0x1F1E6 - 0x61))!
        }

        let lowercasedCode = countryCode.lowercased()
        guard lowercasedCode.count == 2 else { return nil }
        guard lowercasedCode.unicodeScalars.reduce(true, { accum, scalar in accum && isLowercaseASCIIScalar(scalar) }) else { return nil }

        let indicatorSymbols = lowercasedCode.unicodeScalars.map({ regionalIndicatorSymbol(for: $0) })
        return String(indicatorSymbols.map({ Character($0) }))
    }
    
    //    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
    ////        print("source application:\(sourceApplication)")
    //        let value: String = url.absoluteString as String
    ////        print("value :\(value)")
    ////        print("scheme: \(url.scheme)")
    ////        print("query: \(url.query)")
    //
    //        return true
    //    }
    
    
    func handleBackgroundNotification(_ userInfo: [AnyHashable: Any]) {
        // Process the notification data here
        // You can perform specific actions based on the received data
        
        // Store the notification if the app is not yet active
        if UIApplication.shared.applicationState != .active {
            self.lastReceivedNotification = userInfo
        }
    }
    
    
    //MoEngage
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Registration failed!", error.localizedDescription)
        //        MoEngage.sharedInstance().didFailToRegisterForPush()
        
    }
    
}


extension AppDelegate{
    func applicationWillTerminate(_ application: UIApplication) {
        //        setUserData(online: false)
        print("Application Will Terminate")
        
        if KeyCenter.AppId != "" {
            guard UserModel.shared.getMeetRoomId() != "" else {return}
            let request = RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: Int(UserModel.shared.getMeetRoomId())),
                                         createdBy:  Int(UserModel.shared.get_id()) ?? 0)
            leaveRoom(model: request)
        }
    }
    
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        print("Application Did Enter Background")
        //        setUserData(online: false)
        
        print("sceneDidEnterBackground")
        Unity.shared.applicationDidEnterBackground(isBool: true)
        (UIApplication.shared.delegate as? AppDelegate)?.saveContext()
        
        
    }
    
    
    func applicationWillEnterForeground(_ application: UIApplication) {//sceneWillEnterForeground
        //        setUserData(online: true)
        print("Application Will Enter Foreground")
        
    }
    
    
    func applicationDidFinishLaunching(_ application: UIApplication) {
        //        setUserData(online: true)
        print("Application Did Finish Launching")
        
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {//sceneDidBecomeActive
        print("Application Did Become Active")
        requestIDFA()
        //        setUserData(online: true)
        Unity.shared.applicationDidEnterBackground(isBool: false)
        if let notification = self.lastReceivedNotification {
            self.handleBackgroundNotification(notification)
            self.lastReceivedNotification = nil
        }
    }
    
    
    func applicationWillResignActive(_ application: UIApplication) { //sceneWillResignActive
        print("Application Will Resign Active")
        //        setUserData(online: true)
        Unity.shared.applicationDidEnterBackground(isBool: true)
        
    }
    
    
    func setUserData(online: Bool){
        if User.getLogin(){
            userWorker = UserWorker()
            DispatchQueue.global(qos: .background).async{ [weak self] in
                guard let self = self else { return }
                let userModel = UserChatModel(name: self.User.get_username(),
                                              image: self.User.get_image(),
                                              FCM: self.User.get_FCMToken(),
                                              online: online)
                
                self.userWorker?.uploadChatUser(userID: self.User.get_id(), model: userModel, compilition: { (Status, Error) in
                    if Status{
                        print("set User Data Success")
                        
                    }else if let error = Error{
                        print("error in set value in fireBase -> ", error)
                    }
                })
            }
        }
    }
    
    
}
////MARK: - orientation
//struct AppUtility {
//    
//    static func lockOrientation(_ orientation: UIInterfaceOrientationMask) {
//        
//        if let delegate = UIApplication.shared.delegate as? AppDelegate {
//            delegate.orientationLock = orientation
//        }
//    }
//    
//    /// OPTIONAL Added method to adjust lock and rotate to the desired orientation
//    static func lockOrientation(_ orientation: UIInterfaceOrientationMask, andRotateTo rotateOrientation:UIInterfaceOrientation) {
//        
//        self.lockOrientation(orientation)
//        
//        UIDevice.current.setValue(rotateOrientation.rawValue, forKey: "orientation")
//        UINavigationController.attemptRotationToDeviceOrientation()
//    }
//    
//}

extension UITabBarController {
    override open var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return .portrait
    }
}
