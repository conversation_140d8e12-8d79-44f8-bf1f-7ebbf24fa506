//
//  ImageDownloader.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/09/2024.
//

import Foundation
import Photos

class ImageDownloader {

    func downloadImage(from url: URL, completion: @escaping (UIImage?) -> Void) {
        URLSession.shared.dataTask(with: url) { data, response, error in
            guard let data = data, error == nil else {
                print("Failed to download image: \(error?.localizedDescription ?? "Unknown error")")
                completion(nil)
                return
            }
            
            if let image = UIImage(data: data) {
                completion(image)
            } else {
                print("Invalid image data")
                completion(nil)
            }
        }.resume()
    }

    func saveImageToPhotoLibrary(image: UIImage) {
        PHPhotoLibrary.shared().performChanges {
            PHAssetChangeRequest.creationRequestForAsset(from: image)
        } completionHandler: { success, error in
            if let error = error {
                print("Failed to save image to photo library: \(error.localizedDescription)")
            } else {
                print("Image saved to photo library")
            }
        }

    }
}
