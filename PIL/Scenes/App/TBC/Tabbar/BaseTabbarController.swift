//
//  BaseTabbarController.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//

import UIKit

class BaseTabbarController: UITabBarController {
    
    //MARK: - variables
    /// tabbar icons
    var StoreItem: UITabBarItem!
    var HomeItem: UITabBarItem!
    var EventsItem: UITabBarItem!
    var SocialItem: UITabBarItem!
    var BrandedItem: UITabBarItem!
    var MissionsItem: UITabBarItem!
    var tournamentItem: UITabBarItem!
    var profileItem: UITabBarItem!
    
    //MARK: - view lifeCycle
    override func viewDidLoad() {
        super.viewDidLoad()
        TabBarViewControllersItems()
        selectedIndex = 2   /// default -> tabbar start from tab number 2 ( games )
        tabBar.backgroundColor = .clear

        /// tabbar UI
        tabBar.layer.shadowColor = ColorsNewThem.shared.shadowTabbarColor().cgColor
        tabBar.layer.shadowOffset = CGSize(width: 0, height: -1)
        tabBar.layer.shadowRadius = 1
        tabBar.layer.shadowOpacity = 1
        tabBar.layer.masksToBounds = false
    }


    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
    }

    //MARK: - adding Tabbar VCs Items
    func TabBarViewControllersItems(){
        self.viewControllers = [Store(), Tournament(), Home(),  Social(), Profile()]
    }
    
    /// ctreat tabbar item and image
    func createItem(image: String) -> UITabBarItem{
        let item = UITabBarItem(title: "", image: .init(named: image + "_unselected")!.withRenderingMode(UIImage.RenderingMode.alwaysOriginal), tag: 0)
        item.selectedImage = .init(named: image + "_selected")!.withRenderingMode(UIImage.RenderingMode.alwaysOriginal)
        return item
    }
    
    
    /// create StoreItem tab
    /// - Returns: uinavigation controller with storeVC root View
    func Store() -> UINavigationController{
        StoreItem = createItem(image: "A_Store")
        let vc = ShopRouter.createModule() as! ShopVC
        let nav = UINavigationController(rootViewController: vc)
        nav.tabBarItem = StoreItem
        return nav
    }
    
    /// create Home tab
    /// - Returns: uinavigation controller with homeVC root View
    func Home() -> UINavigationController{
        HomeItem = createItem(image: "A_Game")
        let vc = ExploreRouter.createModule() as! ExploreVC
        let nav = UINavigationController(rootViewController: vc)
        nav.tabBarItem = HomeItem
        return nav
    }
    
    /// create events tab
    /// - Returns: uinavigation controller with eventsVC root View
    func Events() -> UINavigationController{
        EventsItem = createItem(image: "A_Trophy")
        let vc = MainEventsRouter.createModule() as! MainEventsVC
        let nav = UINavigationController(rootViewController: vc)
        nav.tabBarItem = EventsItem
        return nav
    }
    
    /// create Branded tab
    /// - Returns: uinavigation controller with brandedVC root View
    func Branded() -> UINavigationController{
        BrandedItem = createItem(image: "tournaments")
        let vc = MainBrandedRouter.createModule() as! MainBrandedVC
        let nav = UINavigationController(rootViewController: vc)
        nav.tabBarItem = BrandedItem
        return nav
    }
    
    /// create Missions tab
    /// - Returns: uinavigation controller with MissionsListVC root View
    func Missions() -> UINavigationController{
        MissionsItem = createItem(image: "missions")
        let vc = MissionsListVC.Router.createModule() as! MissionsListVC
        let nav = UINavigationController(rootViewController: vc)
        nav.tabBarItem = MissionsItem
        nav.isNavigationBarHidden = true
        return nav
    }
    
    /// create Tournament tab
    /// - Returns: uinavigation controller with TournamentsVC root View
    func Tournament() -> UINavigationController{
        tournamentItem = createItem(image: "A_Trophy")
        let vc = TournamentsVC.Router.createModule() as! TournamentsVC
        let nav = UINavigationController(rootViewController: vc)
        nav.isNavigationBarHidden = true
        nav.tabBarItem = tournamentItem
        return nav
    }
    
    /// create Profile tab
    /// - Returns: uinavigation controller with MainProfileVC root View
    func Profile() -> UINavigationController{
        profileItem = createItem(image: "A_Profile")
        let vc = MainProfileRouter.createModule(profile: .myProfile) as! MainProfileVC
        let nav = UINavigationController(rootViewController: vc)
        nav.isNavigationBarHidden = true
        nav.tabBarItem = profileItem

        return nav
    }
    
    
    /// create Social tab
    /// - Returns: uinavigation controller with SocialVC root View
    func Social() -> UINavigationController{
        SocialItem = createItem(image: "A_Users")
        let vc = SocialRouter.createModule()
        let nav = UINavigationController(rootViewController: vc)
        nav.isNavigationBarHidden = true
        nav.tabBarItem = SocialItem
        return nav
    }
    
}


import UIKit
extension UINavigationController: UIGestureRecognizerDelegate {

    open override func viewDidLoad() {
        super.viewDidLoad()
        interactivePopGestureRecognizer?.delegate = self
    }

    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        return viewControllers.count > 1
    }
}
