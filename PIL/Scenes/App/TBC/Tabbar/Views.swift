//
//  Views.swift
//  PIL
//
//  Created by <PERSON> on 9/21/21.
//

import UIKit

//MARK: - buttons
class Components{
    lazy var backNavButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
        button.layer.masksToBounds = true
        var imgName = ""
        if app_lang == "ar"{
            imgName = "dashArrow-ar"
        }else{
            imgName = "dashArrow-en"
        }
        button.setImage(UIImage(named: imgName), for: .normal)
        return button
    }()
    
    lazy var menuButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
        button.backgroundColor = .clear
        button.layer.cornerRadius = 17
        button.layer.masksToBounds = true
        button.tintColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
        button.setImage(#imageLiteral(resourceName: "Menu"), for: .normal)
        if app_lang == "ar"{
            button.flipX()
        }
        return button
    }()
    
    lazy var historyNavButton: UIButton = {
        let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
        button.setImage(UIImage(named: "history"), for: .normal)
        return button
    }()
}




var askButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "Question"), for: .normal)
    return button
}()

var infoButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "Help"), for: .normal)
    return button
}()

var myTeamsButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "collaboration"), for: .normal)
    return button
}()


var leaderBoardButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "Cup-Big"), for: .normal)
    return button
}()

var moreButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "more"), for: .normal)
    return button
}()

var addPlayerButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "add player"), for: .normal)
    return button
}()

var loveButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
    button.setImage(#imageLiteral(resourceName: "heart.empty"), for: .normal)
    return button
}()

var editProfileButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 34, height: 34))
    button.backgroundColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
    button.layer.cornerRadius = 17
    button.layer.masksToBounds = true
    button.tintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    button.setImage(#imageLiteral(resourceName: "Edit-Black"), for: .normal)
    return button
}()

var filterButton: UIButton = {
    let button = UIButton(frame: CGRect(x: 0, y: 0, width: 33, height: 9))
    button.setImage(#imageLiteral(resourceName: "filter"), for: .normal)
    return button
}()

//MARK: - imageViews
var gameImage: UIImageView = {
    let gameImg = UIImageView(frame: CGRect(x: 0, y: 0, width: 40, height: 40))
    gameImg.contentMode = .scaleAspectFit
    gameImg.backgroundColor = UIColor.clear
    gameImg.layer.cornerRadius = 8
    gameImg.clipsToBounds = true
    gameImg.widthAnchor.constraint(equalToConstant: 40) .isActive = true
    gameImg.heightAnchor.constraint(equalToConstant: 40.0).isActive = true
    return gameImg
}()

var chatUserImage: UIImageView = {
    let img = UIImageView(frame: CGRect(x: 0, y: 0, width: 32, height: 32))
    img.contentMode = .scaleToFill
    img.layer.cornerRadius = 16
    img.clipsToBounds = true
    return img
}()

var chatUserStatusMarkImage : UIImageView = {
    let img = UIImageView(frame: CGRect(x: 0, y: 0, width: 8, height: 8))
    img.contentMode = .scaleToFill
    img.image = #imageLiteral(resourceName: "ellipse797")
   return img
}()

//MARK: - Labels
var gameLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 11))
    label.font = UIFont.boldSystemFont(ofSize: 10)
    label.textColor = UIColor.black
   return label
}()

var pointsLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 13))
    label.font = UIFont.systemFont(ofSize: 10)
    label.textColor = #colorLiteral(red: 0.9960784314, green: 0.8039215686, blue: 0, alpha: 1)
    label.text = "320"
   return label
}()

var tournmentLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 13))
    label.font = UIFont.boldSystemFont(ofSize: 12)
    label.textColor = #colorLiteral(red: 0.9960784314, green: 0.8039215686, blue: 0, alpha: 1)
    label.text = "Tournment"
   return label
}()

var matchTeamsLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 13))
    label.font = UIFont.boldSystemFont(ofSize: 12)
    label.textColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
   return label
}()

var matchTimeLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 13))
    label.font = UIFont.systemFont(ofSize: 10)
    label.textColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
   return label
}()

var gameChallengeLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 70, height: 15))
    label.font = UIFont.boldSystemFont(ofSize: 16)
    label.textColor = #colorLiteral(red: 0.9960784314, green: 0.8039215686, blue: 0, alpha: 1)
    label.text = ""
   return label
}()

var winChallengeLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 70, height: 13))
    label.font = UIFont.systemFont(ofSize: 13)
    label.textColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
    label.text = ""
   return label
}()

var chatUserNameLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 19))
    label.font = UIFont.boldSystemFont(ofSize: 15)
    label.textColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
   return label
}()

var chatUserStatusLabel : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 30, height: 13))
    label.font = UIFont.systemFont(ofSize: 10)
   return label
}()

var fantsyHelpTitle : UILabel = {
    let label = UILabel(frame: CGRect(x: 0, y: 0, width: 70, height: 13))
    label.font = UIFont.boldSystemFont(ofSize: 16)
    label.textColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
   return label
}()

//MARK: - views
var conGameView: UIStackView = {
    let HStack = UIStackView(arrangedSubviews: [loveButton,pointsLabel])
    HStack.axis = .horizontal
    HStack.spacing = 5
    HStack.alignment = .center
    
    let innerVStack = UIStackView(arrangedSubviews: [gameLabel,HStack])
    innerVStack.axis = .vertical
    innerVStack.spacing = 10
    innerVStack.alignment = .leading
    
    let superHStack = UIStackView(arrangedSubviews: [gameImage,gameLabel])
    superHStack.axis = .horizontal
    superHStack.spacing = 5
    superHStack.alignment = .fill
    return superHStack
}()

var challengetGameView: UIStackView = {
    let HStack = UIStackView(arrangedSubviews: [gameChallengeLabel,winChallengeLabel])
    HStack.axis = .vertical
    HStack.spacing = 5
    HStack.alignment = .top

    
    let superHStack = UIStackView(arrangedSubviews: [gameImage,HStack])
    superHStack.axis = .horizontal
    superHStack.spacing = 5
    superHStack.alignment = .fill
    return superHStack
}()

//MARK:- views
var contestGameView: UIStackView = {
    let HStack = UIStackView(arrangedSubviews: [loveButton,pointsLabel])
    HStack.axis = .horizontal
    HStack.spacing = 5
    HStack.alignment = .center
    
    let innerVStack = UIStackView(arrangedSubviews: [gameLabel])
    innerVStack.axis = .vertical
    innerVStack.spacing = 10
    innerVStack.alignment = .leading
    
    let superHStack = UIStackView(arrangedSubviews: [gameImage,innerVStack])
    superHStack.axis = .horizontal
    superHStack.spacing = 5
    superHStack.alignment = .fill
    return superHStack
}()

var gameView: UIStackView = {
    let innerVStack = UIStackView(arrangedSubviews: [/*tournmentLabel, */gameLabel])
    innerVStack.axis = .vertical
    innerVStack.spacing = 4
    innerVStack.alignment = .leading
    
    let superHStack = UIStackView(arrangedSubviews: [gameImage,innerVStack])
    superHStack.axis = .horizontal
    superHStack.spacing = 5
    superHStack.alignment = .top
    return superHStack
}()


var MatchHeaderView: UIStackView = {
    let VStack = UIStackView(arrangedSubviews: [matchTeamsLabel, matchTimeLabel])
    VStack.spacing = 0
    VStack.alignment = .leading
    VStack.axis = .vertical
    return VStack
}()


var chatUserStatusStack: UIStackView = {
   let HStack = UIStackView(arrangedSubviews: [chatUserStatusMarkImage, chatUserStatusLabel])
    HStack.spacing = 3
    HStack.alignment = .center
    HStack.axis = .horizontal
    
    let VStack = UIStackView(arrangedSubviews: [chatUserNameLabel,HStack])
    VStack.spacing = 0
    VStack.alignment = .center
    VStack.axis = .vertical
    return VStack
}()

var ChatMemberView: UIStackView = {
    let HStack = UIStackView(arrangedSubviews: [chatUserImage, chatUserStatusStack])
     HStack.spacing = 3
     HStack.alignment = .center
     HStack.axis = .horizontal
    return HStack
}()
