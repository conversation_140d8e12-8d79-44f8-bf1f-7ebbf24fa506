//
//  CreateTournamentsVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/03/2023.
//

import UIKit

class CreateTournamentsVC: UIViewController, CreateTournametViewProtocol {

    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var backButtoon: UIButton!
    @IBOutlet weak var gameTF: UITextField!
    @IBOutlet weak var nameTF: UITextField!
    @IBOutlet weak var playersTF: UITextField!
    @IBOutlet weak var minTF: UITextField!
    
    var presenter: CreateTournamentPresenter?
    var gamePickerView: UIPickerView = .init()
    let dumpGames = ["type 1", "type 2", "type 3"]
    
    override func viewDidLoad() {
        super.viewDidLoad()
        backButtoon.setBackArrow()
        
        [gameTF, nameTF, playersTF, minTF].forEach{
            $0.attributedPlaceholder = NSAttributedString(
            string: "Placeholder Text",
            attributes: [NSAttributedString.Key.foregroundColor: UIColor.lightGray]
        ) }
        
        gameTF.placeholder = "Game".localized
        nameTF.placeholder = "Tournament name".localized
        playersTF.placeholder = "Players: 2".localized
        minTF.placeholder = "Min 20".localized
        
        setupGamePickerView()
    }
    
    func setupGamePickerView(){
        gamePickerView.delegate = self
        gamePickerView.dataSource = self
        gameTF.setupPicker(picker: gamePickerView)
    }
    
    @IBAction func backAction(_ sender: Any) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func createAction(_ sender: Any) {
    }

    
}

extension CreateTournamentsVC: UIPickerViewDelegate, UIPickerViewDataSource{
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 1
    }
    
    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        return dumpGames.count
    }
    
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        return dumpGames[row]
    }
    
    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        gameTF.text = dumpGames[row]
    }
    
}

extension CreateTournamentsVC{
    class Presenter: CreateTournamentPresenter, CreateTournamentOutputInteractor{
        var interactor: CreateTournamentInputInteractor?
        var view: CreateTournametViewProtocol?
        
        
    }
}

extension CreateTournamentsVC{
    class Interactor: CreateTournamentInputInteractor{
        var presenter: CreateTournamentOutputInteractor?
        
        
    }
}
