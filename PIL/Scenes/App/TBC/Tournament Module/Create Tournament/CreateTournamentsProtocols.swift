//
//  CreateTournamentsProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/03/2023.
//

import Foundation

protocol CreateTournametViewProtocol: AnyObject{
    
}

protocol CreateTournamentPresenter: AnyObject{
    var view: CreateTournametViewProtocol? { set get }
    var interactor: CreateTournamentInputInteractor? { set get }
}

protocol CreateTournamentInputInteractor: AnyObject{
    var presenter: CreateTournamentOutputInteractor? { set get }
}

protocol CreateTournamentOutputInteractor: AnyObject{
    
}
