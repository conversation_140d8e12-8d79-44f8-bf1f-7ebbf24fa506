//
//  TourInfoProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/03/2023.
//

import Foundation

protocol TourInfoRouter: AnyObject{
    func toShareDialog()
}

protocol TourInfoView: AnyObject{
    var presenter: TourInfoPresenter? { set get }
    func updateCollectionView()
    func refreshUI()
}

protocol TourInfoPresenter: AnyObject{
    var view: TourInfoView? { set get }
    var users: [LeaderBoardContestDataModel]? { get set}
    var interactor: TourInfoInputInteractor? { set get }
    var router: TourInfoRouter? { set get }
    var tournament: Tournament? { set get }
    func viewDidLoad()
    func shareBtnOnTap()
}

protocol TourInfoInputInteractor: AnyObject{
    var presenter: TourInfoOutputInteractor? { set get }
}

protocol TourInfoOutputInteractor: AnyObject{
    
}
