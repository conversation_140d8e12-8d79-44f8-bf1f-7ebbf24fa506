//
//  TourInfoPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/03/2023.
//

import Foundation

extension TourInfoVC{
    class Presenter: TourInfoPresenter, TourInfoOutputInteractor{
        var view: TourInfoView?
        var interactor: TourInfoInputInteractor?
        var router: TourInfoRouter?
        var tournament: Tournament?
        private var timer: Timer?
        var users: [LeaderBoardContestDataModel]?{
            didSet{
                view?.updateCollectionView()
                timer?.invalidate()
                timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(fire), userInfo: nil, repeats: true)
            }
        }
        
        func viewDidLoad() {
            users = [.init(id: 132, image: "", name: "name 1", score: 100, lose: 1, datumDefault: ""),
                     .init(id: 342, image: "", name: "name 2", score: 200, lose: 2, datumDefault: ""),
                     .init(id: 35, image: "", name: "name 3", score: 300, lose: 3, datumDefault: ""),
                     .init(id: 243, image: "", name: "name 4", score: 400, lose: 4, datumDefault: ""),
                     .init(id: 64, image: "", name: "name 5", score: 500, lose: 5, datumDefault: ""),
                     .init(id: 7656, image: "", name: "name 6", score: 600, lose: 6, datumDefault: "")]
        }
        
        @objc func fire(){
            guard tournament!.countDownInSeconds > 0 else{
                timer?.invalidate()
                view?.refreshUI()
                return
            }
            view?.refreshUI()
        }
        
        func shareBtnOnTap() {
            router?.toShareDialog()
        }
    }
}
