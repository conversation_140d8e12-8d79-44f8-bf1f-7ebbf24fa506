//
//  TourInfoRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/03/2023.
//

import Foundation

extension TourInfoVC{
    class Router: TourInfoRouter{
        var vc: TourInfoView?
        
        static func createModuleWith(_ tournament: Tournament) -> UIViewController{
            let view = SetStoryBoard.controller(controller: .init(Story: .Branded, VC: .Info)) as! TourInfoVC
            
            let presenter = TourInfoVC.Presenter()
            let interactor = TourInfoVC.Interactor()
            let router = TourInfoVC.Router()
            
            view.presenter = presenter
            presenter.view = view
            presenter.interactor = interactor
            presenter.router = router
            interactor.presenter = presenter
            presenter.tournament = tournament
            router.vc = view
            
            return view
        }
        
        func toShareDialog() {
            let view = SetStoryBoard.controller(controller: .init(Story: .Branded, VC: .dialog)) as! TournamentDialogVC
            view.modalPresentationStyle = .overCurrentContext
            if let vc = vc as? UIViewController{
                vc.present(view, animated: false)
            }
        }
    }
}
