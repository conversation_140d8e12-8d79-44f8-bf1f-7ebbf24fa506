//
//  TourInfoVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/03/2023.
//

import UIKit

class TourInfoVC: UIViewController, TourInfoView {
    
    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var backButtoon: UIButton!
    @IBOutlet weak var userCollectionView: UICollectionView!
    @IBOutlet weak var logo: UIImageView!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var userLabel: UILabel!
    @IBOutlet weak var membersLabel: UILabel!
    @IBOutlet weak var hoursLabel: UILabel!
    @IBOutlet weak var minutesLabel: UILabel!
    @IBOutlet weak var secondsLabel: UILabel!
    @IBOutlet weak var joinButton: UIButton!
    @IBOutlet weak var counterStack: UIStackView!
    
    var presenter: TourInfoPresenter?

    override func viewDidLoad() {
        super.viewDidLoad()
        backButtoon.setBackArrow()
        userCollectionView.register(UINib(nibName: "HeadLeaderBoardCell", bundle: nil), forCellWithReuseIdentifier: "HeadLeaderBoardCell")
        presenter?.viewDidLoad()
        
//        logo.sd_setImage(with: .init(string: (presenter?.tournament!.logo)!))
        logo.image = .init(named: "star-2")
        nameLabel.text = presenter?.tournament?.name
        userLabel.text = presenter?.tournament?.user
        membersLabel.text = "\(presenter?.tournament?.members ?? 0) " + "joining".localized
        
        refreshUI()
    }
    
    func refreshUI(){
        if (presenter?.tournament!.countDownInSeconds)! > 0 {
            calculateCountDown((presenter?.tournament!.countDownInSeconds)!)
            counterStack.isHidden = false
            joinButton.isHidden = true
        }else{
            counterStack.isHidden = true
            joinButton.isHidden = false
        }
    }
    
    private func calculateCountDown(_ seconds: Int){
        let counter = seconds.secondsToHoursMinutesSeconds()
        hoursLabel.text = "\(counter.0)" + "h".localized
        minutesLabel.text = "\(counter.1)" + "m".localized
        secondsLabel.text = "\(counter.2)" + "s".localized
    }
    
    @IBAction func backAction(_ sender: Any) {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func shareAction(_ sender: Any) {
        presenter?.shareBtnOnTap()
    }
    
    @IBAction func deleteAction(_ sender: Any) {
    }
     
    func updateCollectionView() {
        userCollectionView.reloadData()
    }
}

extension TourInfoVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "HeadLeaderBoardCell", for: indexPath) as! HeadLeaderBoardCell
        cell.users = presenter?.users ?? []
        cell.loadTopThree()
        cell.memberTableView.reloadData()
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return .init(width: collectionView.frame.width, height: collectionView.frame.height)
    }
    
}
