//
//  TournamentDialogVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/03/2023.
//

import UIKit

class TournamentDialogVC: UIViewController {
    
    @IBOutlet weak var moreOptionsView: UIView!
    @IBOutlet weak var userCodeView: UIView!
    @IBOutlet weak var userCode: UILabel!

    override func viewDidLoad() {
        super.viewDidLoad()

        userCodeView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTappedUserCodeView)))
        moreOptionsView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTappedMoreOptionsView)))
        
    }
    
    @objc func didTappedUserCodeView() {
        UIPasteboard.general.string = userCode.text
        self.showAlert(withTitle: false, msg: "copied".localized, compilition: nil)
    }
    
    @objc func didTappedMoreOptionsView() {
        let textShare = [ "\(UserModel.shared.get_referrallink())" ]
        let activityViewController = UIActivityViewController(activityItems: textShare , applicationActivities: nil)
        activityViewController.popoverPresentationController?.sourceView = self.view
        self.present(activityViewController, animated: true, completion: nil)
    }
    
    @IBAction func dismissAction(_ sender: Any) {
        self.dismiss(animated: false)
    }
    
    
    @IBAction func whatsappAction(_ sender: UIButton) {
        let message = "\(UserModel.shared.get_referrallink())"
        var queryCharSet = NSCharacterSet.urlQueryAllowed
        
        queryCharSet.remove(charactersIn: "+&")
        
        if let escapedString = message.addingPercentEncoding(withAllowedCharacters: queryCharSet) {
            if let whatsappURL = URL(string: "whatsapp://send?text=\(escapedString)") {
                if UIApplication.shared.canOpenURL(whatsappURL) {
                    UIApplication.shared.open(whatsappURL, options: [: ], completionHandler: nil)
                } else {
                    debugPrint("please install WhatsApp")
                }
            }
        }
    }

}
