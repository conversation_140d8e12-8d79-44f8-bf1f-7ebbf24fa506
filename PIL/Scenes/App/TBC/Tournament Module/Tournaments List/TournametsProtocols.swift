//
//  TournametsProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/03/2023.
//

import Foundation

protocol TournamentsRouterProtocol: AnyObject{
    func toCreateTournament()
    func toTournamentInfo(_ data: Tournament)
}

protocol TournamentsViewProtocol: AnyObject{
    var presenter: TournamentsPresenterProtocol? { set get }
    func shouldUpdateTableView()
}

protocol TournamentsPresenterProtocol: AnyObject{
    var view: TournamentsViewProtocol? { set get }
    var router: TournamentsRouterProtocol? { set get }
    var interactor: TournamentsInputInteractorProtocol? { set get}
    var tournaments: [Tournament]? { get }
    func viewDidLoad()
    func onTapTournamentsSegment(tag: Int)
    func onTapJoinButton(index: Int)
    func onSelectTournament(index: Int)
    func onTapCreateTournament()
}

protocol TournamentsInputInteractorProtocol: AnyObject{
    var presenter: TournamentsOutputInteractorProtocol? { set get }
    func getYourTournaments()
    func getFriendsTournaments()
}


protocol TournamentsOutputInteractorProtocol: AnyObject{
    func didFetchYourTournaments(_ data: [Tournament])
    func didFetchFriendsTournaments(_ data: [Tournament])
}
