//
//  TournamentsInteractor.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/03/2023.
//

import Foundation

extension TournamentsVC{
    class Interactor: TournamentsInputInteractorProtocol{
        
        var presenter: TournamentsOutputInteractorProtocol?
        
        func getYourTournaments() {
            let dumpTournaments: [Tournament] = [.init(name: "name 1", user: "user 1", members: 20, countDownInSeconds: 98887),
                                                 .init(name: "name 2", user: "user 2", members: 20, countDownInSeconds: 964726),
                                                 .init(name: "name 3", user: "user 3", members: 20, countDownInSeconds: 10),
                                                 .init(name: "name 4", user: "user 4", members: 20, countDownInSeconds: 0985),
                                                 .init(name: "name 5", user: "user 5", members: 20, countDownInSeconds: 52098),
                                                 .init(name: "name 6", user: "user 6", members: 20, countDownInSeconds: 70987)]
            presenter?.didFetchYourTournaments(dumpTournaments)
        }
        
        func getFriendsTournaments() {
            presenter?.didFetchYourTournaments([])
        }
        
    }
}
