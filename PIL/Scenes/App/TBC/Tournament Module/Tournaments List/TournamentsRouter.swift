//
//  TournamentsRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/03/2023.
//

import Foundation

extension TournamentsVC{
    class Router: TournamentsRouterProtocol{
        weak var vc: TournamentsViewProtocol?
        
        static func createModule() -> UIViewController{
            let view = SetStoryBoard.controller(controller: Helper(Story: .Branded, VC: .TournamentsVC)) as! TournamentsVC

            let interactor = TournamentsVC.Interactor()
            let presenter = TournamentsVC.Presenter()
            let router = TournamentsVC.Router()

            view.presenter = presenter
            router.vc = view
            interactor.presenter = presenter
            presenter.interactor = interactor
            presenter.router = router
            presenter.view = view
            
            return view
        }
        
        func toCreateTournament(){
            let view = SetStoryBoard.controller(controller: .init(Story: .Branded, VC: .Create)) as! CreateTournamentsVC
            
            let presenter = CreateTournamentsVC.Presenter()
            let interactor = CreateTournamentsVC.Interactor()
            
            view.presenter = presenter
            presenter.interactor = interactor
            interactor.presenter = presenter
            presenter.view = view
            
            guard let vc = self.vc as? UIViewController else { return }
            vc.navigationController?.pushViewController(view, animated: true)
        }
        
        func toTournamentInfo(_ data: Tournament) {
            if let view = TourInfoVC.Router.createModuleWith(data) as? TourInfoVC,
               let vc = self.vc as? UIViewController{
                vc.navigationController?.pushViewController(view, animated: true)
            }
        }
    }
}
