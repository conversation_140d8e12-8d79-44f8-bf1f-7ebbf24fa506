//
//  TournamentsPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/03/2023.
//

import Foundation

extension TournamentsVC{
    class Presenter: TournamentsOutputInteractorProtocol, TournamentsPresenterProtocol{
        
        var router: TournamentsRouterProtocol?
        var interactor: TournamentsInputInteractorProtocol?
        var view: TournamentsViewProtocol?
        private var timer: Timer?
        private(set) var tournaments: [Tournament]? = []{ didSet{
            view?.shouldUpdateTableView()
            timer?.invalidate()
            timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(fire), userInfo: nil, repeats: true)
        }}
        
        func viewDidLoad() {
            interactor?.getYourTournaments()
        }
        
        func didFetchYourTournaments(_ data: [Tournament]) {
            tournaments = data
        }
        
        func didFetchFriendsTournaments(_ data: [Tournament]) {
            tournaments = data
        }
        
        func onTapTournamentsSegment(tag: Int) {
            switch tag{
            case 0: interactor?.getYourTournaments()
            case 1: interactor?.getFriendsTournaments()
            default: break
            }
        }
        
        func onTapJoinButton(index: Int) {
            print("selected tournament to join: ", tournaments![index].name)
        }
        
        func onSelectTournament(index: Int) {
            guard let tournaments = tournaments else{ return }
            router?.toTournamentInfo(tournaments[index])
        }
        
        func onTapCreateTournament() {
            router?.toCreateTournament()
        }
        
        @objc func fire(){
            guard !tournaments!.filter({ return $0.countDownInSeconds > 0 }).isEmpty else{
                timer?.invalidate()
                return
            }
            tournaments?.forEach{
                $0.countDownInSeconds -= 1
                view?.shouldUpdateTableView()
            }
        }
        
    }
}
