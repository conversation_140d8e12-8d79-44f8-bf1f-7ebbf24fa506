//
//  TournamentsVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 07/03/2023.
//

import UIKit

class TournamentsVC: UIViewController, TournamentsViewProtocol {
    
    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var createYourTournamentButtonView: UIView!
    @IBOutlet weak var joinTournamentButtonView: UIView!
    @IBOutlet weak var segmentLeadingCnst: NSLayoutConstraint!
    @IBOutlet weak var tournamentsTableView: UITableView!
    
    var presenter: TournamentsPresenterProtocol?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        presenter?.viewDidLoad()
        joinTournamentButtonView.isHidden = true
        
        tournamentsTableView.register(TournamentTableViewCell.nib, forCellReuseIdentifier: TournamentTableViewCell.identifier)
    }
    
    @IBAction func tournamentsSegment(_ sender: UIButton) {
        switch sender.tag{
        case 0: showYourTournaments()
        case 1: showFriendsTournaments()
        default: break
        }
        presenter?.onTapTournamentsSegment(tag: sender.tag)
    }
    
    func showYourTournaments(){
        segmentLeadingCnst.constant = 7
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.view.layoutIfNeeded()
        }
        createYourTournamentButtonView.isHidden = false
        joinTournamentButtonView.isHidden = true
    }
    
    func showFriendsTournaments(){
        segmentLeadingCnst.constant = 173
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.view.layoutIfNeeded()
        }
        createYourTournamentButtonView.isHidden = true
        joinTournamentButtonView.isHidden = false
    }
    
    @IBAction func createYourTournamentAction(_ sender: UIButton) {
        presenter?.onTapCreateTournament()
    }
    
    @IBAction func tournamentCodeAction(_ sender: UITextField) {
        sender.text = ""
    }
    
    
    @IBAction func joinAction(_ sender: Any) {
        
    }
    
    func shouldUpdateTableView() {
        tournamentsTableView.reloadData()
    }
    
}


extension TournamentsVC: UITableViewDelegate, UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.tournaments?.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: TournamentTableViewCell.identifier, for: indexPath) as! TournamentTableViewCell
        let tournament = presenter?.tournaments?[indexPath.row]
        cell.loadFrom(tournament!)
        cell.joinButton.tag = indexPath.row
        cell.joinButton.addTarget(self, action: #selector(self.joinButtonAction), for: .touchUpInside)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        presenter?.onSelectTournament(index: indexPath.row)
    }
    
    @objc func joinButtonAction(_ sender: UIButton){
        presenter?.onTapJoinButton(index: sender.tag)
    }
}
