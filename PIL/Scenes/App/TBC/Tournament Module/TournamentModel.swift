//
//  TournamentModel.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/03/2023.
//

import Foundation

class Tournament{
    let name: String
    let user: String
    let members: Int
    var countDownInSeconds: Int
    let logo: String
    
    init(name: String, user: String, members: Int, countDownInSeconds: Int, logo: String = "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQib4YdM7j9JfshFMlbfvqwT19Dja94W9Bp7ExPo422TQ&s"){
        self.name = name
        self.user = user
        self.members = members
        self.countDownInSeconds = countDownInSeconds
        self.logo = logo
    }
    
}
