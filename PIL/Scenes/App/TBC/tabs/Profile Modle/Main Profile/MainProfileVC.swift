//
//  ProfileVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 29/05/2023.
//

import UIKit
import DropDown
import FirebaseDynamicLinks
import AnimatableReload
import TransitionRouter

class MainProfileVC: UIViewController, MainProfileViewProtocol {
    //MARK: - @IBOutlet
    @IBOutlet weak var mainTableView: UITableView!
    @IBOutlet weak var menuContainer: UIView!
    @IBOutlet weak var menuContainerAr: UIView!
    @IBOutlet weak var navigation: NavigationView!
    @IBOutlet weak var settingTableView: UITableView!
    @IBOutlet weak var viewSetting: UIView!
    @IBOutlet weak var heightTableView: NSLayoutConstraint!
    //MARK: - variables
    var contentList = [settingItem]()
    var listRange = 0..<3
    var selectedFilter: Int = 0
    var refreshControl: UIRefreshControl?
    var presenter: MainProfilePresenterProtocol?
    private var didAnimate: Bool = false
    let menu: DropDown = {
        let dropDown = DropDown()
        dropDown.backgroundColor = .init(named: "brown")
        dropDown.cornerRadius = 15
        dropDown.textColor = .init(named: "Black-White")!
        dropDown.selectedTextColor = .init(named: "Black-White")!
        dropDown.dataSource = ["Share".localized, "Block".localized, "Report".localized]
        dropDown.selectionBackgroundColor = .clear
        return dropDown
    }()
    let interactiveRouter = TransitionRouter(type: .right, interactive: true)
    //MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        loadXIBs()
        loadUI()
        
        presenter?.viewDidLoad()
        
        let rightRecognizer = UIScreenEdgePanGestureRecognizer()
        rightRecognizer.edges = .right
        interactiveRouter
            .add(rightRecognizer)
            .transition { [unowned self] router in
                let listChatVC = ListUsersChatRouter.createModule() as! ListUsersChatVC
                let nav = UINavigationController(rootViewController: listChatVC)
                nav.isNavigationBarHidden = true
                nav.hidesBottomBarWhenPushed = true
                nav.modalPresentationStyle = .fullScreen
                nav.transitioningDelegate = router
                self.present(nav, animated: true)
            }
        self.view.addGestureRecognizer(rightRecognizer)
        
    }
    //MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        didAnimate = false
        if UserModel.shared.get_loginAsGuest(){
            let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
            vc.onDismiss = { self.tabBarController?.selectedIndex = 2 }
            self.present(vc, animated: false, completion: nil)
        }else{
            presenter?.viewWillAppear()
//            presenter?.GetCachedPosts()
        }
        
    }
    //MARK: - viewDidDisappear
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        ASVideoPlayerController.sharedVideoPlayer.currentLayer?.removeFromSuperlayer()
        ASVideoPlayerController.sharedVideoPlayer.currentLayer?.player?.pause()
        ASVideoPlayerController.sharedVideoPlayer.currentLayer?.player = nil
        NotificationCenter.default.post(name: .init("ProfileDidDisappear"), object: nil)
    }
    //MARK: - funcs
    func loadXIBs(){
        mainTableView.register(UserCardTabTableViewCell.nib, forCellReuseIdentifier: UserCardTabTableViewCell.identifier)
        mainTableView.register(AchievementsTableViewCell.nib, forCellReuseIdentifier: AchievementsTableViewCell.identifier)
        mainTableView.register(FiltersTableViewCell.nib, forCellReuseIdentifier: FiltersTableViewCell.identifier)
        mainTableView.register(UserInfoTableViewCell.nib, forCellReuseIdentifier: UserInfoTableViewCell.identifier)
        mainTableView.register(PostsContainerTableViewCell.nib, forCellReuseIdentifier: PostsContainerTableViewCell.identifier)
        mainTableView.register(ProfileVouchersContainerTableViewCell.nib, forCellReuseIdentifier: ProfileVouchersContainerTableViewCell.identifier)
        
        settingTableView.delegate = self
        settingTableView.dataSource = self
       
        settingTableView.register(UINib(nibName: "SettingCell", bundle: Bundle.main), forCellReuseIdentifier: "SettingCell")
//        settingTableView.reloadData()

    }
    
    func loadUI(){
        navigation = navigation.loadNib() as? NavigationView
        navigation.vc = self
        navigation.setNavigationTitle(title: "Profile".localized)
        
        navigation.onNotifications = {
            if UserModel.shared.get_loginAsGuest() == true{
                let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
                self.present(vc, animated: false, completion: nil)
            }else{
                self.presenter?.didTapNotificationsButton()
            }
        }
        
        navigation.onSettings = {
//            self.presenter?.didTapSettingsButton()
            self.loadSettingData()
            self.viewSetting.isHidden = false
            self.tabBarController?.tabBar.isHidden = true
            self.showSettingAnimation()
//            self.loadSettingData()
            self.settingTableView.reloadData()
        }
        
        navigation.onTapMenu = { [self] sender in
            if UserModel.shared.get_loginAsGuest() == true{
                let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
                self.present(vc, animated: false, completion: nil)
            }else{
                switch sender.tag{
                case 0:
                    menuContainer.isHidden = false
                    menuContainerAr.isHidden = false
                    menu.show()
                    sender.tag = 1
                case 1:
                    menuContainer.isHidden = true
                    menuContainerAr.isHidden = true
                    menu.hide()
                    sender.tag = 0
                default: break
                }
                self.menu.cancelAction = { [self] in
                    menuContainer.isHidden = true
                    menuContainerAr.isHidden = true
                    menu.hide()
                    sender.tag = 0
                }
            }
        }
        
        navigation.onChat = {
            if UserModel.shared.get_loginAsGuest() == true{
                let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
                self.present(vc, animated: false, completion: nil)
            }else{
                let listChatVC = ListUsersChatRouter.createModule() as! ListUsersChatVC
                listChatVC.fromSocial = false
                let nav = UINavigationController(rootViewController: listChatVC)
                nav.isNavigationBarHidden = true
                nav.hidesBottomBarWhenPushed = true
                nav.modalPresentationStyle = .fullScreen
                let leftInteractiveRouter = TransitionRouter(type: .right)
                nav.transitioningDelegate = leftInteractiveRouter
                self.present(nav, animated: true)
                
            }
        }
        
        switch presenter?.profileType{
        case .myProfile:
            loadMyProfileUI()
        case .publicProfile(let id):
//            if id == UserModel.shared.get_id(){
//                loadMyProfileUI()
//            }else{
                loadPublicProfileUI()
//            }
        default:
            navigation.selectedAction(actions: [.back,.setting,.notification,.chat])
//            break
        }
        
        menu.anchorView = app_lang == "ar" ? menuContainerAr : menuContainer
        
//        if (navigationController?.viewControllers.count)! > 1{
////            backButton.isHidden = false
////            settingsButton.isHidden = true
//            navigation.selectedAction(actions: [.back,.setting,.notification,.chat])
//        }
        
       
        menu.selectionAction = { index,_ in
            switch index{
            case 0:
                self.shareUser()
            case 1:
                self.presenter?.blockUserPressed()
            case 2:
                self.presenter?.reportUserPressed()
            default: break
            }
        }
        
    }
    
    private func loadMyProfileUI(){
        navigation.selectedAction(actions: [.setting,.notification,.chat])
    }
    
    private func loadPublicProfileUI(){
        navigation.selectedAction(actions: [.back, .menu])
    }
    
    func onFetchData() {
        if !didAnimate{
            didAnimate = true
            mainTableView.reloadWithBounceAnimation()
        }else{
            mainTableView.reloadData()
        }
    }
    
    func onFetchDataWithAnimation() {
      //  AnimatableReload.reload(tableView: mainTableView, animationDirection: "down")
        mainTableView.reloadWithBounceAnimation()
    }
    
    func onError(_ msg: String) {
        showAlert(withTitle: false, msg: msg, compilition: nil)
    }
    
    func onSessionExpiration() {
        loginAlert(compilition: {
            UserModel.shared.logOut()
        })
    }
    
    func onConnectionLost() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
    private func shareUser(){
        let sharePickerVC = SharePickerVC.loadFromNib()
        sharePickerVC.shareType = .profile(id: Int((presenter?.profile?.id!.getValue)!)!)
        sharePickerVC.shareModel = .init(id: .init(presenter?.profile?.id?.getValue ?? "0"),
                                         title: presenter?.profile?.name,
                                         subTitle: "ID: \(presenter?.profile?.id?.getValue ?? "")",
                                         img: presenter?.profile?.image,
                                         userImg: presenter?.profile?.image)
        sharePickerVC.modalPresentationStyle = .overCurrentContext
        UIApplication.topViewController?.tabBarController?.present(sharePickerVC, animated: true)
    }
    
    
    //MARK: - @IBAction
    @IBAction func hiddenSettingViewACTION(_ sender: Any) {
        HiddenSettingAnimation()
    }
    
}
//MARK: - ProfileType
enum ProfileType{
    case myProfile
    case publicProfile(_ id: String)
}
//MARK: - UserSubscriptionType
enum UserSubscriptionType{
    case silver
    case gold
    case regular
}
