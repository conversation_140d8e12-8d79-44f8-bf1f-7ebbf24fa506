//
//  DailyRewardInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation
import Alamofire
 
class DailyRewardInteractor:DailyRewardInteractorInputProtocol {
    var presenter: DailyRewardInteractorOutputProtocol?
    var worker:DailyRewardWorker?
    var error:ErrorProtocol?
    
    func getDailyPoints() {
        ProfileWorker.shared.getDailyPoints { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                self.presenter?.didCompleteWithDailyPoints(model.data)
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        }
    }
    
    func getDailyR<PERSON>ard(){
        worker?.checkReward(compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                
                if let model = model.data{
                    self.presenter?.getDailyReward(model: model)
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    func collectReward(){
        worker?.collectReward(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status == true{
                    self.presenter?.successCollect()
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
