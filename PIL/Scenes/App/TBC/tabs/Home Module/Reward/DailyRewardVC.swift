//
//  DailyRewardVC.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import UIKit
import SwiftPopup

class DailyRewardVC: SwiftPopup , DailyRewardViewProtocol ,ErrorProtocol {

    //MARK: - outlet
    @IBOutlet weak var livesCountLable: UILabel!
    
    //MARK: - variable
    var presenter: DailyRewardPresenterProtocol?
    var countReward = 0
    var action:ReloadGetLivesAfterWatchAdsProtocol?
    
    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        livesCountLable.text = "\(countReward) " + "Tokens".localized
    }
    
    //MARK: - action
    @IBAction func collectBTN(_ sender: Any) {
        if let button = sender as? UIButton{
            presenter?.sendCollectReward()
            button.isEnabled = false
            button.alpha = 0.5
        }
    }
    
    //MARK: -
    func successCollect(){
        print("Success")
        self.dismiss(animated: true) {
            self.action?.getLiveAfterAds()
            self.presenter?.didDismiss()
        }
    }
 
    func featching(error: String) {
        
    }

}
