//
//  DailyRewardProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation

protocol DailyRewardViewProtocol{
    var presenter:DailyRewardPresenterProtocol? {get set}
    func successCollect()
}

protocol DailyRewardPresenterProtocol{
    var view:DailyRewardViewProtocol? {get set}
    func sendCollectReward()
    func didDismiss()
}

protocol DailyRewardInteractorInputProtocol{
    var presenter:DailyRewardInteractorOutputProtocol? {get set}
    func getDailyReward()
    func collectReward()
    func getDailyPoints()
}

protocol DailyRewardInteractorOutputProtocol{
    func getDailyReward(model:DailyRewarDataClass)
    func successCollect()
    func didCompleteWithDailyPoints(_ data: DailyPoints?)
}

protocol DailyRewardRouterProtocol{
    func toDailyPoints(_ data: DailyPoints)
}
