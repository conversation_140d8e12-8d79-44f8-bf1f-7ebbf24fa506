//
//  DailyRewardRouter.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation
import SwiftPopup

class DailyRewardRouter:DailyRewardRouterProtocol{
    
    var VC: DailyRewardViewProtocol?

    static func createModule(count:Int)-> UIViewController{
        
        let view = SetStoryBoard.controller(controller: Helper(Story: .DailyReward, VC: .DailyRewardVC)) as! DailyRewardVC
        let interactor = DailyRewardInteractor()
        let router = DailyRewardRouter()
        let worker = DailyRewardWorker()
        let presenter = DailyRewardPresenter(view: view,
                                             interactor: interactor,
                                             error: view,
                                             router: router)
        
        presenter.router = router
        presenter.interactor = interactor
        presenter.view = view
        view.presenter = presenter
        view.countReward = count
        router.VC = view
        interactor.worker = worker
        interactor.presenter = presenter
       
        
        return view
    }
    
    func toDailyPoints(_ data: DailyPoints) {
        let vc = PlayitStreakDialogVC.loadFromNib()
        vc.data = data
        vc.setDialogAnimation()
        vc.show(above: UIApplication.topViewController!)
    }
    
}
