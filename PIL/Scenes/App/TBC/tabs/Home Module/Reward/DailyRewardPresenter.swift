//
//  DailyRewardPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation

class DailyRewardPresenter:DailyRewardPresenterProtocol , DailyRewardInteractorOutputProtocol{
    
    var view: DailyRewardViewProtocol?
    var interactor: DailyRewardInteractorInputProtocol?
    var error:ErrorProtocol?
    var router:DailyRewardRouterProtocol?
    
    init(view: DailyRewardViewProtocol,
         interactor: DailyRewardInteractorInputProtocol,
         error:ErrorProtocol,
         router:DailyRewardRouterProtocol) {
        self.view = view
        self.router = router
        self.interactor = interactor
        self.router = router
    }

    
    func sendCollectReward() {
        self.interactor?.collectReward()
    }
    
    func getDailyReward(model: DailyRewarDataClass) {
        
    }
    
    func successCollect() {
        view?.successCollect()
    }
    
    func didDismiss() {
        interactor?.getDailyPoints()
    }
    
    func didCompleteWithDailyPoints(_ data: DailyPoints?) {
        guard let data = data else { return }
        router?.toDailyPoints(data)
    }
    
}
