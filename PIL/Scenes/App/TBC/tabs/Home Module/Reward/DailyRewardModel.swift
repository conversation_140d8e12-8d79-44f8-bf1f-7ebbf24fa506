//
//  DailyRewardModel.swift
//  PIL
//
//  Created by sameh mohammed on 05/01/2023.
//

import Foundation

struct DailyRewardModel: Codable {
    var data: DailyRewarDataClass?
}

// MARK: - DataClass
struct DailyRewarDataClass: Codable {
    var deserve: Bool?
    var userID: String?
    var reward:Int?

    enum CodingKeys: String, CodingKey {
        case deserve , reward
        case userID = "userId"
    }
}

struct collectRewardModel: Codable {
    var status: Bool?
    var message: String?
    var data: collectRewardDataClass?
}

// MARK: - DataClass
struct collectRewardDataClass: Codable {
    let id: String?
    let details: CollectDetails?
    let userID: String?
    let coins: Int?
    let playerToken, updatedAt, createdAt, battleID: String?
    let tournamentID: String?
    let lives: Int?
    let creditCard: Bool?
    let dailyUpdated: String?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case details
        case userID = "user_id"
        case coins, playerToken
        case updatedAt = "updated_at"
        case createdAt = "created_at"
        case battleID = "battleId"
        case tournamentID = "tournamentId"
        case lives, creditCard
        case dailyUpdated = "daily_updated"
    }
}

// MARK: - Details
struct CollectDetails: Codable {
    var deposit, winning, bonus: Int?
}
