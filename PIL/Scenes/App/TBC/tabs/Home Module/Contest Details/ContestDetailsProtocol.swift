//
//  ContestDetailsProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 19/12/2022.
//

import Foundation

protocol ContestDetailsViewProtocol{
    var presenter:ContestDetailsPresenterProtocol? { get set}
    func reloadData()
    func setHome(lives: Int)
    func updateGameVersion(message:String)
    func progressGameDownload(name:String,file:String, image:String)//new design
    func canNotJoinContest(reason:String)
    func reloadLive()
    func UpdateApp()
 }

protocol ContestDetailsPresenterProtocol{
    var view:ContestDetailsViewProtocol? { get set}
    var users:[LeaderBoardContestDataModel]? { get set}
    var Player:PlayerLeaderBoardContestModel? {get set}
    var endDate: String? { get set}
    func getPrize()->[PrizesDistribution]
    func getWalletInfo(obj: WalletWorkerData)
    func getContestRules()->String
    func CheckIsKnockout()->Bool
    func gettotalLose()->Int
    func getImageRules()->String
    func getGameName()->String
    func getGameImage()->String
    func getEndTime()->String
    func getJoinCount()->Int
    
    func openContestGame()
    func viewDidLoad()
    func DownloadsFIle(isUpdate:Bool)
    func FileDownloaded(pathFile:String , destination:String)
    func getLives()
}

protocol ContestDetailsInteractorInputProtocol{
    var presenter:ContestDetailsInteractorOutputProtocol? {get set}
    func getGameDetails(gameId:String)
    func getSingleChallengesGame(contestID:String)
    func getLeaderboardKnockout(contestID:String)
    func sendWalletRequest()
    func createFolderInDownloadsDirectory(name:String, files:String)
    func ValidateContest(contestID:String)
    func getLeaderBoard(contestID:String)
    func getSkills(playerID:String)
}

protocol ContestDetailsInteractorOutputProtocol{
    func getWalletInfo(obj:WalletWorkerData)
    func getGameData(data:GameDetailsDataModel)
    func getSingleContest(model:ChallengesGamesData)
    func FileDownloaded(pathFile:String , destination:String)
    func progressFileDownload(progress:Double)
    func progressGameDownload(name:String,file:String)//new design
    func FilesIFExist(path:String)
    func successValidateContest()
    func DontHaveLives(status:String)
    func canNotJoinContest(reason:String)
    func getLeaderBoard(users:[LeaderBoardContestDataModel])
    func getPlayerLeaderBoard(player:PlayerLeaderBoardContestModel)
    func getSkillsPlayer(skills:Int)
}

protocol ContestDetailsRouterProtocol{
    func openLives()
}
