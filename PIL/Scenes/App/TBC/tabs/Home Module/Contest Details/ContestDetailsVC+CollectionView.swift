//
//  ContestDetailsVC+CollectionView.swift
//  PIL
//
//  Created by sameh mohammed on 23/01/2023.
//

import Foundation
import UIKit


extension ContestDetailsVC : UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        
        if type == .prize{
            //            if presenter?.CheckIsKnockout() ?? false == false{
            let cell = self.contestDetailsTableView.dequeueReusableCell(withReuseIdentifier: "PrizeCell", for: indexPath) as! PrizeCell
            if let endDate = presenter?.endDate, !endDate.isEmpty{
                cell.loadTimer(from: endDate)
                cell.counterStack.isHidden = false
            }else{
                cell.counterStack.isHidden = true
            }
            cell.prize = presenter?.getPrize() ?? []
            cell.rankTableView.reloadWithBounceAnimation()
            return cell
            //            }else{
            //                let cell = self.contestDetailsTableView.dequeueReusableCell(withReuseIdentifier: "PrizeKnockoutCell", for: indexPath) as! PrizeKnockoutCell
            //
            //                return cell
            //            }
        }else if type == .leaderboard{
            if presenter?.CheckIsKnockout() ?? false == false{
                let cell = self.contestDetailsTableView.dequeueReusableCell(withReuseIdentifier: "HeadLeaderBoardCell", for: indexPath) as! HeadLeaderBoardCell
                cell.users = presenter?.users ?? []
                cell.loadTopThree()
                cell.registerMemberLeaderboardCell()
                cell.memberTableView.reloadWithBounceAnimation()
                return cell
            }else{
                let cell = self.contestDetailsTableView.dequeueReusableCell(withReuseIdentifier: "KnockoutLeaderBoardCell", for: indexPath) as! KnockoutLeaderBoardCell
                cell.users = presenter?.users ?? []
                cell.Player = presenter?.Player ?? PlayerLeaderBoardContestModel()
                cell.isKnockout = true
                cell.countLose = presenter?.gettotalLose() ?? 0
                cell.info(gameName: presenter?.getGameName() ?? "",
                          gameIMG: presenter?.getGameImage() ?? "",
                          endTime: presenter?.getEndTime() ?? "",
                          Joined: presenter?.getJoinCount() ?? 0)
                
                if isLeaveScreen == true{
                    cell.timer.invalidate()
                }
                cell.memberTableView.reloadWithBounceAnimation()
                return cell
            }
            
        }else{
            let cell = self.contestDetailsTableView.dequeueReusableCell(withReuseIdentifier: "ContestRulesCell", for: indexPath) as! ContestRulesCell
            cell.rulesData(img: self.presenter?.getImageRules() ?? "",
                           body: presenter?.getContestRules() ?? "")
            return cell
        }
    }
    
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: UIScreen.main.bounds.width, height: self.contestDetailsTableView.bounds.height)
    }
    
    
}

