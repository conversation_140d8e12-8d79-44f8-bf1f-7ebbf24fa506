//
//  ContestRulesCell.swift
//  PIL
//
//  Created by sameh mohammed on 20/12/2022.
//

import UIKit

protocol ContestRulesCellProtocol{
    func rulesData(img:String , body:String)
}

protocol ContestRulesAction{
    func howToAction()
}


class ContestRulesCell: UICollectionViewCell,ContestRulesCellProtocol{

    //MARK: - outlet
    @IBOutlet weak var imgRuls: UIImageView!
    @IBOutlet weak var bodyRulesTV: UITextView!
    
    //MARK: - variable
    var action:ContestRulesAction?
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
 
    //MARK: - function
    func rulesData(img: String, body: String) {
        self.imgRuls.sd_setImage(with: URL(string: img), placeholderImage: placeHolderImage)
        self.bodyRulesTV.setHTMLFromString(text: body)
    }
    
    //MARK: - action
    @IBAction func howToBTN(_ sender: Any) {
        action?.howToAction()
    }
}
