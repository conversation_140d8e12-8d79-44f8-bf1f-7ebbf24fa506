//
//  PrizeCell.swift
//  PIL
//
//  Created by sameh mohammed on 01/01/2023.
//

import UIKit

class PrizeCell: UICollectionViewCell {
    
    @IBOutlet weak var hoursLabel: UILabel!
    @IBOutlet weak var minutesLabel: UILabel!
    @IBOutlet weak var secondsLabel: UILabel!
    @IBOutlet weak var counterStack: UIStackView!
    @IBOutlet weak var entryFees: UILabel!
    
    @IBOutlet weak var rankTableView: UITableView!
    var prize = [PrizesDistribution]()
    private var timer: Timer?
    private var timeInterval: Int?
    
    override func awakeFromNib() {
        super.awakeFromNib()
        rankTableView.delegate = self
        rankTableView.dataSource = self
        rankTableView.rowHeight = 40
        rankTableView.register(UINib(nibName: "PrizeRankCell", bundle: nil), forCellReuseIdentifier: "PrizeRankCell")
    }
    
    override func prepareForReuse() {
        timer?.invalidate()
    }
    
    func loadTimer(from date: String){
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        dateFormatter.timeZone = TimeZone(abbreviation: "UTC")
        let startDate = Date()
        let endDate = dateFormatter.date(from: date)!
        timeInterval = Int(endDate - startDate)
        calculateCountDown(timeInterval!)
        timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(fire), userInfo: nil, repeats: true)
    }
    
    private func calculateCountDown(_ interval: Int){
        let counter = interval.secondsToHoursMinutesSeconds()
        if counter.0 > 24{
            let days = Int(counter.0 / 24)
            let hours = counter.0 - (Int(counter.0 / 24) * 24)
            let minutes = counter.1
            hoursLabel.text = "\(days)" + "d".localized
            minutesLabel.text = "\(hours)" + "h".localized
            secondsLabel.text = "\(minutes)" + "m".localized
        }else{
            hoursLabel.text = "\(counter.0)" + "h".localized
            minutesLabel.text = "\(counter.1)" + "m".localized
            secondsLabel.text = "\(counter.2)" + "s".localized
        }
    }

    @objc func fire(){
        guard timeInterval! > 0 else{
            timer?.invalidate()
            calculateCountDown(0)
            return
        }
        timeInterval! -= 1
        calculateCountDown(timeInterval!)
    }
 
}

extension PrizeCell:UITableViewDelegate , UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return prize.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = self.rankTableView.dequeueReusableCell(withIdentifier: "PrizeRankCell", for: indexPath) as! PrizeRankCell
        cell.prizeLable.text = "\(prize[indexPath.row].prize ?? 0)"
        cell.rankLable.text  = "\(prize[indexPath.row].rank ?? 0)"
        return cell
    }
    
    
    
}
