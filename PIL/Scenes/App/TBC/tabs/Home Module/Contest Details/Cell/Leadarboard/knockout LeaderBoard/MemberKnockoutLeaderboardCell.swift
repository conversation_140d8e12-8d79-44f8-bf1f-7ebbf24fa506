//
//  MemberKnockoutLeaderboardCell.swift
//  PIL
//
//  Created by sameh mohammed on 18/01/2023.
//

import UIKit

class MemberKnockoutLeaderboardCell: UITableViewCell , MemberLeaderboardCellProtocol {


    @IBOutlet weak var avatarImg: UIImageView!
    @IBOutlet weak var numberLable: UILabel!
    @IBOutlet weak var userNameLable: UILabel!
    @IBOutlet weak var scoreLable: UILabel!
    @IBOutlet weak var attempsCollectionView: UICollectionView!
    
    var usersLose = 0
    var countLose = 0
    
    override func awakeFromNib() {
        super.awakeFromNib()
        self.selectionStyle = .none
        attempsCollectionView.delegate = self
        attempsCollectionView.dataSource = self
        attempsCollectionView.register(UINib(nibName: "AttemptsCell", bundle: nil), forCellWithReuseIdentifier: "AttemptsCell")

    }
    
    
    func memeber(number: String, name: String, img: String, score: String) {
        self.numberLable.text = number
        self.userNameLable.text = name
        self.scoreLable.text = score
        self.avatarImg.sd_setImage(with: URL(string: img), placeholderImage: placeHolderImage)
    }
    
}




extension MemberKnockoutLeaderboardCell : UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return countLose
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = self.attempsCollectionView.dequeueReusableCell(withReuseIdentifier: "AttemptsCell", for: indexPath) as! AttemptsCell
        if usersLose > indexPath.row{
            print("users-----1" , usersLose , countLose )
            cell.isLose(status: true)
        }else{
            print("users-----2" , usersLose , countLose )
            cell.isLose(status: false)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 17, height: 17)
    }
    
}
