//
//  MemberLeaderboardCell.swift
//  PIL
//
//  Created by sameh mohammed on 20/12/2022.
//

import UIKit
protocol MemberLeaderboardCellProtocol{
    func memeber(number: String ,name:String , img:String , score:String)
}
class MemberLeaderboardCell: UITableViewCell ,MemberLeaderboardCellProtocol{

    @IBOutlet weak var prizeLabel: UILabel!
    @IBOutlet weak var avatarImg: UIImageView!
    @IBOutlet weak var numberLable: UILabel!
    @IBOutlet weak var userNameLable: UILabel!
    @IBOutlet weak var scoreLable: UILabel!
    
    
    override func awakeFromNib() {
        super.awakeFromNib()
        self.selectionStyle = .none
    }

    func memeber(number: String, name: String, img: String, score: String) {
        self.numberLable.text = number
        self.userNameLable.text = name
        self.scoreLable.text = score
        self.avatarImg.sd_setImage(with: URL(string: img), placeholderImage: placeHolderImage)
        self.prizeLabel.text = score
    }
    
}
