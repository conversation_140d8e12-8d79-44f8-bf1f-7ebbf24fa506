//
//  KnockoutLeaderBoardCell.swift
//  PIL
//
//  Created by sameh mohammed on 18/01/2023.
//

import UIKit


class KnockoutLeaderBoardCell: UICollectionViewCell {
    
    //MARK: - outlet
    @IBOutlet weak var memberTableView: UITableView!
    @IBOutlet weak var attemptsCollectionView: UICollectionView!
    
    @IBOutlet weak var imageGame: UIImageView!
    @IBOutlet weak var gameNameLable: UILabel!
    @IBOutlet weak var joiningUsersLable: UILabel!
    @IBOutlet weak var myRankLable: UILabel!
    @IBOutlet weak var winNumLable: UILabel!
    @IBOutlet weak var hoursLable: UILabel!
    @IBOutlet weak var mintuesLable: UILabel!
    @IBOutlet weak var secondLable: UILabel!
    @IBOutlet weak var finishedLable: UILabel!
    @IBOutlet weak var timerStack: UIStackView!
    
    //MARK: - variable
    var users = [LeaderBoardContestDataModel]()
    var Player:PlayerLeaderBoardContestModel?
    var countLose = 0
    var isKnockout = false
    var timer: Timer!
    var second = 0
    var ToDate = Date()
    //MARK: - did load
    override func awakeFromNib() {
        super.awakeFromNib()
        
        memberTableView.delegate = self
        memberTableView.dataSource = self
        attemptsCollectionView.delegate = self
        attemptsCollectionView.dataSource = self
        memberTableView.register(UINib(nibName: "MemberKnockoutLeaderboardCell", bundle: nil), forCellReuseIdentifier: "MemberKnockoutLeaderboardCell")
        attemptsCollectionView.register(UINib(nibName: "AttemptsCell", bundle: nil), forCellWithReuseIdentifier: "AttemptsCell")
        
        timer = Timer.scheduledTimer(timeInterval: 0.1, target: self, selector: #selector(UpdateTime), userInfo: nil, repeats: true) // Repeat "func Update() " every second and update the label
        
    }
    
    
    func info(gameName: String, gameIMG: String, endTime: String, Joined: Int) {
        gameNameLable.text = gameName
        imageGame.sd_setImage(with: URL(string: gameIMG))
        joiningUsersLable.text = "\(Joined)"
        winNumLable.text = "\(Player?.score ?? 0)"
        myRankLable.text = "\(Player?.rank ?? 0)"
        
        let date = endTime.getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss.SSSZ", from: "en")
        ToDate = date ?? Date()
        let timeStamp = date?.timeIntervalSince1970
        second = Int(timeStamp ?? 0)
    }
    
    @objc func UpdateTime() {
        let userCalendar = Calendar.current
        // Set Current Date
        let date = Date()
        let components = userCalendar.dateComponents([.hour, .minute, .month, .year, .day, .second], from: date)
        let currentDate = userCalendar.date(from: components)!
        
        // Set Event Date
        var eventDateComponents = DateComponents()
        eventDateComponents.year = 2020
        eventDateComponents.month = 01
        eventDateComponents.day = 01
        eventDateComponents.hour = 00
        eventDateComponents.minute = 00
        eventDateComponents.second = 00
        eventDateComponents.timeZone = TimeZone(abbreviation: "UTC")
        
        // Convert eventDateComponents to the user's calendar
//        let eventDate = userCalendar.date(from: Date())!
        
        // Change the seconds to days, hours, minutes and seconds
        let timeLeft = userCalendar.dateComponents([.hour, .minute, .second], from: Date(), to: ToDate)
        
        // Display Countdown
        if timeLeft.second! < 0 {
            print("Time is finished")
            finishedLable.isHidden = false
            timerStack.isHidden = true
            self.hoursLable.text =  "0" + "h".localized
            self.mintuesLable.text = "0" + "m".localized
            self.secondLable.text = "0" + "s".localized
        }else{
            finishedLable.isHidden = true
            timerStack.isHidden = false
            if timeLeft.hour! > 24{
                let days = Int(timeLeft.hour! / 24)
                let hours = timeLeft.hour! - (Int(timeLeft.hour! / 24) * 24)
                let minutes = timeLeft.minute!
                hoursLable.text = "\(days)" + "d".localized
                mintuesLable.text = "\(hours)" + "h".localized
                secondLable.text = "\(minutes)" + "m".localized
            }else{
                self.hoursLable.text =  "\(timeLeft.hour!)" + "h".localized
                self.mintuesLable.text = "\(timeLeft.minute!)" + "m".localized
                self.secondLable.text = "\(timeLeft.second!)" + "s".localized
            }
        }
       
        // Show diffrent text when the event has passed
        endEvent(currentdate: currentDate, eventdate: ToDate)
    }
    
    
    func endEvent(currentdate: Date, eventdate: Date) {
        if currentdate >= eventdate {
            timer.invalidate()
        }
    }
    
}


extension KnockoutLeaderBoardCell:UITableViewDelegate , UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        var num = 0
        if isKnockout == false{
            if users.count > 3 {
                num = users.count-3
            }
        }else{
            num = users.count
        }
        
        return num
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        var index = 0
        let cell = self.memberTableView.dequeueReusableCell(withIdentifier: "MemberKnockoutLeaderboardCell", for: indexPath) as! MemberKnockoutLeaderboardCell
        if isKnockout == false{
            if users.count > 3 {
                index = indexPath.row+3
                cell.numberLable.text = "\(index)"
                cell.avatarImg.sd_setImage(with: URL(string: users[index].image ?? "" ), placeholderImage: placeHolderImage)
                cell.userNameLable.text = users[index].name ?? ""
                cell.scoreLable.text = "\(users[index].score ?? 0)"
            }
        }else{
            index = indexPath.row
            cell.usersLose = users[index].lose ?? 0
            cell.countLose = countLose
            cell.numberLable.text = "\(index+1)"
            cell.avatarImg.sd_setImage(with: URL(string: users[index].image ?? "" ), placeholderImage: placeHolderImage)
            cell.userNameLable.text = users[index].name ?? ""
            cell.scoreLable.text = "\(users[index].score ?? 0)"
            cell.attempsCollectionView.reloadData()
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard !ChatNewCell.isSelecting else { return }
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.users[indexPath.row].id ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
    
}


extension KnockoutLeaderBoardCell : UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return countLose
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = self.attemptsCollectionView.dequeueReusableCell(withReuseIdentifier: "AttemptsCell", for: indexPath) as! AttemptsCell
        if Player?.lose ?? 0 > indexPath.row{
            cell.isLose(status: true)
        }else{
            cell.isLose(status: false)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 17, height: 17)
    }
    
}
