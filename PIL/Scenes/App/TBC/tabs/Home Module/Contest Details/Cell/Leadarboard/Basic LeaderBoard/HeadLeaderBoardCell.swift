//
//  HeadLeaderBoardCell.swift
//  PIL
//
//  Created by sameh mohammed on 20/12/2022.
//

import UIKit
protocol TopThreeLeaderBoardProtocol{
    func fristUSer(name:String , image:String)
    func secondUSer(name:String , image:String)
    func thirdUSer(name:String , image:String)
}

class HeadLeaderBoardCell: UICollectionViewCell {
    // user one
    @IBOutlet weak var firstAvatarImg: UIImageView!
    @IBOutlet weak var firstUserNameLable: UILabel!
    @IBOutlet weak var firstScoreLable: UILabel!
    
    // user two
    @IBOutlet weak var secondAvatarImg: UIImageView!
    @IBOutlet weak var secondUserNameLable: UILabel!
    @IBOutlet weak var secondScoreLable: UILabel!
    //user three
    @IBOutlet weak var thirdAvatarImg: UIImageView!
    @IBOutlet weak var thirdUserNameLable: UILabel!
    @IBOutlet weak var thirdScoreLable: UILabel!

    //Memeber user
    @IBOutlet weak var tableViewHeight: NSLayoutConstraint!
    @IBOutlet weak var memberTableView: UITableView!
    @IBOutlet weak var topTableViewConstraint: NSLayoutConstraint!
    

    @IBOutlet var mainIMG: [UIImageView]!
    @IBOutlet var mainStack: [UIStackView]!
    @IBOutlet var views: [UIView]!
    @IBOutlet var scoresLable: [UILabel]!
    
    
    //MARK: - variable
    var users = [LeaderBoardContestDataModel]()
    var isKnockout = false
    var countLose = 0
    var observe : NSKeyValueObservation?
    var MyRank : ((Int) -> Void)?
    var cellsDidAnimated: [Int] = []
    
    //MARK: - when view load
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    func registerMemberLeaderboardCell(){
        memberTableView.delegate = self
        memberTableView.dataSource = self
        memberTableView.register(UINib(nibName: "MemberLeaderboardCell", bundle: nil), forCellReuseIdentifier: "MemberLeaderboardCell")
        memberTableView.reloadData()
    }
    
    func registerMemberKnockoutLeaderboardCell(){
        memberTableView.delegate = self
        memberTableView.dataSource = self
        memberTableView.register(UINib(nibName: "MemberKnockoutLeaderboardCell", bundle: nil), forCellReuseIdentifier: "MemberKnockoutLeaderboardCell")
        memberTableView.reloadData()
    }
    
    //MARK: - function
    func loadTopThree(){
        fristUSer()
        secondUSer()
        thirdUSer()
        topThreeVisibility(hidden: users.count <= 3)
    }
    
    func topThreeVisibility(hidden: Bool){
        for i in mainIMG{
            i.isHidden = hidden
//            i.performBounceAnimation()
        }
        for i in mainStack {
            i.isHidden = hidden
//            i.performBounceAnimation()
        }
        
        for i in views {
            i.isHidden = hidden
//            i.performBounceAnimation()
        }
        
        for i in scoresLable{
            i.isHidden = hidden
//            i.performBounceAnimation()
        }
        self.topTableViewConstraint.constant = hidden ? 0 : 195
    }
    
 
    func fristUSer(){
        if users.count > 0 {
            self.firstAvatarImg.sd_setImage(with: URL(string: users[0].image ?? ""), placeholderImage: placeHolderImage)
            self.firstUserNameLable.text =  users[0].name ?? ""
            self.firstScoreLable.text = "\(users[0].score ?? 0)"
            self.firstAvatarImg.isUserInteractionEnabled = true
            self.firstAvatarImg.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.openUser1)))
        }
    }
    
    func secondUSer(){
        if users.count > 1 {
            self.secondAvatarImg.sd_setImage(with: URL(string: users[1].image ?? ""), placeholderImage: placeHolderImage)
            self.secondUserNameLable.text = users[1].name ?? ""
            self.secondScoreLable.text = "\(users[1].score ?? 0)"
            self.secondAvatarImg.isUserInteractionEnabled = true
            self.secondAvatarImg.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.openUser2)))

        }
    
    }
    
    func thirdUSer(){
        if users.count > 2{
            self.thirdAvatarImg.sd_setImage(with: URL(string: users[2].image ?? ""), placeholderImage: placeHolderImage)
            self.thirdUserNameLable.text = users[2].name ?? ""
            self.thirdScoreLable.text = "\(users[2].score ?? 0)"
            self.thirdAvatarImg.isUserInteractionEnabled = true
            self.thirdAvatarImg.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.openUser3)))
        }
    }
    
    @objc func openUser1(){
        guard !ChatNewCell.isSelecting else { return }
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.users[0].id ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
    @objc func openUser2(){
        guard !ChatNewCell.isSelecting else { return }
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.users[1].id ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
    @objc func openUser3(){
        guard !ChatNewCell.isSelecting else { return }
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.users[2].id ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
}

extension HeadLeaderBoardCell:UITableViewDelegate , UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        var num = 0
        if users.count > 3 {
            num = users.count-3
        }else{
            num = users.count // 0
        }
        return num
    }
        
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if isKnockout{
            var index = 0
            let cell = self.memberTableView.dequeueReusableCell(withIdentifier: "MemberKnockoutLeaderboardCell", for: indexPath) as! MemberKnockoutLeaderboardCell
            index = indexPath.row
            let rank = users.firstIndex(where: {$0.id == Int(UserModel.shared.get_id())}) ?? 0
            MyRank?(rank+1)
            cell.usersLose = users[index].lose ?? 0
            cell.countLose = countLose
            cell.numberLable.text = "\(index+1)"
            cell.avatarImg.sd_setImage(with: URL(string: users[index].image ?? "" ), placeholderImage: placeHolderImage)
            cell.userNameLable.text = users[index].name ?? ""
            cell.scoreLable.text = "\(users[index].score ?? 0)"
            cell.attempsCollectionView.reloadData()
            return cell
        }else{
            var index = 0
            let cell = self.memberTableView.dequeueReusableCell(withIdentifier: "MemberLeaderboardCell", for: indexPath) as! MemberLeaderboardCell
            if users.count > 3 {
                index = indexPath.row+3
                cell.numberLable.text = "\(index+1)"
                cell.avatarImg.sd_setImage(with: URL(string: users[index].image ?? "" ), placeholderImage: placeHolderImage)
                cell.userNameLable.text = users[index].name ?? ""
                cell.scoreLable.text = "\(users[index].score ?? 0)"
                cell.prizeLabel.text = "\(users[index].score ?? 0)"
                let rank = users.firstIndex(where: {$0.id == Int(UserModel.shared.get_id())}) ?? 0
                MyRank?(rank+1)
            }else{
                print("$$$$$$$$$$$$$")
                index = indexPath.row
                cell.numberLable.text = "\(index+1)"
                cell.avatarImg.sd_setImage(with: URL(string: users[index].image ?? "" ), placeholderImage: placeHolderImage)
                cell.userNameLable.text = users[index].name ?? ""
                cell.scoreLable.text = "\(users[index].score ?? 0)"
                cell.prizeLabel.text = "\(users[index].score ?? 0)"
                let rank = users.firstIndex(where: {$0.id == Int(UserModel.shared.get_id())}) ?? 0
                MyRank?(rank+1)
            }
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        var index = 0
        if users.count > 3{
            index = indexPath.row+3
        }else{
            index = indexPath.row
        }
        guard !ChatNewCell.isSelecting else { return }
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.users[index].id ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard !cellsDidAnimated.contains(indexPath.row) else { return }
        cellsDidAnimated.append(indexPath.row)
        cell.animateFromBothSides(tableView, indexPath)
    }
  
}
 
