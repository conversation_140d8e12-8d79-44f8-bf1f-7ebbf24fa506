//
//  ContestDetailsRouter.swift
//  PIL
//
//  Created by sameh mohammed on 19/12/2022.
//

import Foundation
class ContestDetailsRouter:ContestDetailsRouterProtocol , ReloadGetLivesAfterWatchAdsProtocol{
    
    var VC: ContestDetailsViewProtocol?

    static func createModule(contestID:String,gameID:String,endDate: String)-> UIViewController{
        
        let view = SetStoryBoard.controller(controller: Helper(Story: .ContestST, VC: .ContestDetailsVC)) as! ContestDetailsVC
        let interactor = ContestDetailsInteractor()
        let router = ContestDetailsRouter()
        let workerGame = ChallengesGameWorker()
        let workerWallet = WalletWorker()
        let gamesWorker =  GamesWorker()

        let presenter = ContestDetailsPresenter(view: view,
                                              interactor: interactor,
                                              router: router,
                                              error: view)
        
        view.presenter = presenter
        presenter.view = view
        presenter.interactor = interactor
        presenter.router = router
        presenter.gameID = gameID
        presenter.contestID = contestID
        router.VC = view
        interactor.presenter = presenter
        interactor.workerContest = workerGame
        interactor.walletWorker = workerWallet
        interactor.gamesWorker = gamesWorker
        presenter.endDate = endDate
        return view
    }
    
    func openLives(){
        let lives = YouWantMoreLivesRouter.createModule(type: .token) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
         print("Reload lives")
        VC?.reloadLive()
    }
}
