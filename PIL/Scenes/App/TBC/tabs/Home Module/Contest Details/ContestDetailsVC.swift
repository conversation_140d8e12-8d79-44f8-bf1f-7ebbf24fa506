//
//  ContestDetailsVC.swift
//  PIL
//
//  Created by sameh mohammed on 19/12/2022.
//

import UIKit
import Lottie
import FirebaseCore
import FirebaseDynamicLinks
//import AppLovinSDK

enum ContestDetails{
    case prize
    case leaderboard
    case rules
}

class ContestDetailsVC: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er , ContestDetailsViewProtocol , ErrorProtocol{

    //MARK: - outlet
    @IBOutlet weak var backBu: UIButton!
    @IBOutlet weak var contestDetailsTableView: UICollectionView!
    @IBOutlet var headrButtons: [UIButton]!
    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var viweAds: UIView!

    //MARK: - variable
    var headerIndex = 0
    var type:ContestDetails?
    var presenter:ContestDetailsPresenterProtocol?
    var isLeaveScreen = false
//    var adView: MAAdView!

    //MARK: - view life cycle
    override func viewDidLoad() {
        super.viewDidLoad()

        type = .prize
//        contestDetailsTableView.rowHeight =  contestDetailsTableView.layer.bounds.height
        setXIB()
        headerSelected()
        presenter?.viewDidLoad()
//        self.createBanner()

        NotificationCenter.default.addObserver(self, selector: #selector(ContestChallengesVC.ReloadLives), name: NSNotification.Name("ReloadLives"), object: nil)

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.tabBarController?.tabBar.isHidden = true

    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        isLeaveScreen = true
        contestDetailsTableView.reloadData()
    }
    //MARK: - action
    @IBAction func backBTN(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    @IBAction func prizeBTN(_ sender: Any) {
        self.headerIndex = 0
        headerSelected()
        type = .prize
        contestDetailsTableView.reloadData()
    }
    
    @IBAction func leaderBoardBTN(_ sender: Any) {
        presenter?.viewDidLoad()
        self.headerIndex = 1
        headerSelected()
        type = .leaderboard
        contestDetailsTableView.reloadData()
    }
    
    @IBAction func rulesBTN(_ sender: Any) {
        self.headerIndex = 2
        headerSelected()
        type = .rules
        contestDetailsTableView.reloadData()
    }
    
    
    @IBAction func playGameBTN(_ sender: Any) {
        type = .leaderboard
        presenter?.openContestGame()
    }
    
    
    //MARK: - function
    
    @objc func ReloadLives(notification: Notification) {
        reloadLive()
    }
    
    func reloadLive(){
        self.presenter?.getLives()
    }
    
    
    func UpdateApp() {
        let alert = UIAlertController(title: "Notice".localized, message: "App Not Updated".localized, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Open App Store".localized, style: .default, handler: { action in
            if let url = URL(string: "itms-apps://itunes.apple.com/app/id1555945981"),
                UIApplication.shared.canOpenURL(url)
            {
                if #available(iOS 10.0, *) {
                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
                } else {
                    UIApplication.shared.openURL(url)
                }
            }
        }))
        alert.addAction(UIAlertAction(title: "Cancel".localized, style: .cancel, handler: nil))
        present(alert, animated: true, completion: nil)
    }
    
    func headerSelected(){
        for i in  0..<self.headrButtons.count {
            if self.headerIndex == i {
                self.headrButtons[i].backgroundColor = #colorLiteral(red: 0.3137254902, green: 0.1960784314, blue: 0.1647058824, alpha: 1)
            }else{
                self.headrButtons[i].backgroundColor = .clear
            }
        }
    }
    
    func setXIB(){
        self.contestDetailsTableView.register(UINib(nibName: "ContestRulesCell", bundle: nil), forCellWithReuseIdentifier: "ContestRulesCell")
        self.contestDetailsTableView.register(UINib(nibName: "HeadLeaderBoardCell", bundle: nil), forCellWithReuseIdentifier: "HeadLeaderBoardCell")
        self.contestDetailsTableView.register(UINib(nibName: "PrizeCell", bundle: nil), forCellWithReuseIdentifier: "PrizeCell")
        self.contestDetailsTableView.register(UINib(nibName: "KnockoutLeaderBoardCell", bundle: nil), forCellWithReuseIdentifier: "KnockoutLeaderBoardCell")
        self.contestDetailsTableView.register(UINib(nibName: "PrizeKnockoutCell", bundle: nil), forCellWithReuseIdentifier: "PrizeKnockoutCell")
    }
    
    
    // MARK: - delegate
    
    func canNotJoinContest(reason:String) {
        self.showAlert(withTitle: false, msg: reason) {}
    }
    
    func reloadData() {
        self.contestDetailsTableView.reloadData()
    }
    
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }

    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            UserModel.shared.logOut()
        })
    }
    
    func setHome(lives: Int){
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
            livesCount.attributedText = imageString
        }else{
            livesCount.text = "\(lives)"
        }
    }
 
    
    func updateGameVersion(message: String) {
        self.showAlertUpdateGame(title: "Alert".localized, msg: message) {
            print("Update")
            self.presenter?.DownloadsFIle(isUpdate: true)
        }
    }
    
    func progressGameDownload(name: String, file: String, image: String) {
        let progress = UIStoryboard.init(name: "DownloadGame", bundle: nil).instantiateViewController(withIdentifier: "DownloadGameVC") as! DownloadGameVC
        progress.nameGame = name
        progress.imageURL = image
        progress.fileURL = file
        progress.action = self
        self.present(progress, animated: false, completion: nil)
    }
    
}

extension ContestDetailsVC:DownloadGamFinished{
    func DownloadGamFinished(Path: String) {
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "--->",Path)
        self.presenter?.FileDownloaded(pathFile: Path, destination: newFolderURL.path)
 
    }
    
}
