////
////  ContestDetailsVC+Banner.swift
////  PIL
////
////  Created by sameh mohammed on 09/02/2023.
////
//
//import Foundation
//import AppLovinSDK
//
//extension ContestDetailsVC:  MAAdViewAdDelegate{
//    
//    
//    func createBanner(){
//        self.viweAds.isHidden = false
//        self.viweAds.backgroundColor = .clear
//        adView = MAAdView(adUnitIdentifier: unitIDBanner)
//        adView.delegate = self
//        adView.frame = CGRect(x: 0, y: 0, width: self.viweAds.bounds.width, height: 50)
//        adView.backgroundColor = .clear
//        viweAds.addSubview(adView)
//        adView.loadAd()
//    }
//    
//    func didExpand(_ ad: MAAd) {
//         
//    }
//    
//    func didCollapse(_ ad: MAAd) {
//         
//    }
//    
//    func didLoad(_ ad: MAAd) {
//        print("didLoad")
//    }
//    
//    func didFailToLoadAd(forAdUnitIdentifier adUnitIdentifier: String, withError error: MAError) {
//        print("didFailToLoadAd",adUnitIdentifier , error)
//
//    }
//    
//    func didDisplay(_ ad: MAAd) {
//         
//    }
//    
//    func didHide(_ ad: MAAd) {
//         
//    }
//    
//    func didClick(_ ad: MAAd) {
//         
//    }
//    
//    func didFail(toDisplay ad: MAAd, withError error: MAError) {
//        print("didFail",error)
//
//    }
//    
//    
//}
//    
//
//
