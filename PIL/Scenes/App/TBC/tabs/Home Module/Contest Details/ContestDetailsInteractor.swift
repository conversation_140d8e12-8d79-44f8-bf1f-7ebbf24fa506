//
//  ContestDetailsInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 19/12/2022.
//

import Foundation
class ContestDetailsInteractor: ContestDetailsInteractorInputProtocol{
    
    var presenter:ContestDetailsInteractorOutputProtocol?
    var workerContest:ChallengesGameWorker?
    var error:ErrorProtocol?
    var walletWorker:WalletWorker?
    var gamesWorker: GamesWorkerProtocol?

    
    //MARK: - validate contest
    func ValidateContest(contestID:String){
        self.workerContest?.canJoinContest(contestID: contestID, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{
                    if model.data?.canJoin ?? false == true{
                        self.presenter?.successValidateContest()
                    }else{
                        self.presenter?.canNotJoinContest(reason: model.data?.reason ?? "")
                    }
                }else{
                    self.error?.featching(error: model.data?.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    //MARK: - get Game Details
    func getGameDetails(gameId:String){
        self.workerContest?.getGameDetails(gameID: gameId, compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200{
                    if let model = model.data{
                        self.presenter?.getGameData(data: model)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    //MARK: - get all contest in game
    func getSingleChallengesGame(contestID:String){
        self.workerContest?.getSingleChallengesGame(contestID: contestID,  compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200{
                    if let data = model.data{
                        self.presenter?.getSingleContest(model: data)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    
    func getSkills(playerID:String){
        self.gamesWorker?.getSKills(model: PlayerIDModelRequest(playerId: playerID,language: UserModel.shared.getLanguage()), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 ==  201 ||  model.status ?? 0 ==  200{
                    self.presenter?.getSkillsPlayer(skills: model.data?.skill ?? 5)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    //MARK: - get Wallet conins info
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.getWalletInfo(obj: model)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    //MARK: - create folder games
    func createFolderInDownloadsDirectory(name:String, files:String) {
        
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "Name is",name , files)
        
        if !manager.fileExists(atPath: url.appendingPathComponent(name).path) {
            do {
                
                
                // should be go new progress VC
                self.presenter?.progressGameDownload(name: name, file: files)
                print("progressGameDownload",name , files)
                
            }catch let error{
                // file is created before open folder
                print("--->",error)
                
            }
        }else{
            print("Folder is created Before","\(newFolderURL.path)/\(name)")
            self.presenter?.FilesIFExist(path: "\(newFolderURL.path)/\(name)")
            
        }
        
    }
    
    
    
    //MARK: - get leaderBoard
    func getLeaderBoard(contestID:String){
        workerContest?.getLeaderBoard(contestID: contestID, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{
                    if let model = model.data{
                        self.presenter?.getLeaderBoard(users: model)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    func getLeaderboardKnockout(contestID:String){
        workerContest?.getLeaderBoardKnockout(contestID: contestID, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{
                    if let model = model.data?.ledearboard{
                        self.presenter?.getLeaderBoard(users: model)
                    }
                    if let player = model.data?.player {
                        self.presenter?.getPlayerLeaderBoard(player: player)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
