//
//  ContestChallengesVC.swift
//  PIL
//
//  Created by sameh mohammed on 18/12/2022.
//

import UIKit
//import AppLovinSDK

class ContestChallengesVC: UIViewController ,ContestChallengesViewProtocol,ErrorProtocol{

    //MARK: - outlet
    @IBOutlet weak var backBu: UIButton!
    @IBOutlet weak var contestTableView: UITableView!
    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var viweAds: UIView!

    //MARK: - variable
    var presenter: ContestChallengesPresenterProtocol?
//    var adView: MAAdView!
    var refreshControl: UIRefreshControl?

    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()
        
        refreshControl = UIRefreshControl()
        refreshControl?.addTarget(self, action: #selector(self.refresh(_:)), for: .valueChanged)
        contestTableView.addSubview(refreshControl!)

        backBu.setBackArrow()
        setXIB()
        presenter?.viewDidLoad()
        print("Token is",UserModel.shared.get_token())
//        self.createBanner()

        NotificationCenter.default.addObserver(self, selector: #selector(ContestChallengesVC.ReloadLives), name: NSNotification.Name("ReloadLives"), object: nil)

    }
    
    @objc func refresh(_ sender: AnyObject) {
        presenter?.viewDidLoad()
    }
    
    @objc func ReloadLives(notification: Notification) {
        reloadLive()
    }
    
    func reloadLive(){
        self.presenter?.getLives()
    }
    
 //MARK: - functions
    func setXIB(){
        contestTableView.register(UINib(nibName: "ContestChallengeCell", bundle: nil), forCellReuseIdentifier: "ContestChallengeCell")
    }
    
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            UserModel.shared.logOut()
        })
    }
    
    func UpdateApp() {
        let alert = UIAlertController(title: "Notice".localized, message: "App Not Updated".localized, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Open App Store".localized, style: .default, handler: { action in
            if let url = URL(string: "itms-apps://itunes.apple.com/app/id1555945981"),
                UIApplication.shared.canOpenURL(url)
            {
                if #available(iOS 10.0, *) {
                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
                } else {
                    UIApplication.shared.openURL(url)
                }
            }
        }))
        alert.addAction(UIAlertAction(title: "Cancel".localized, style: .cancel, handler: nil))
        present(alert, animated: true, completion: nil)
    }
    
    func setHome(lives: Int){
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
            livesCount.attributedText = imageString
        }else{
            livesCount.text = "\(lives)"
        }
    }
    
    func reloadData() {
        refreshControl?.endRefreshing()
        self.contestTableView.reloadData()
    }
    
    //MARK: - action
    @IBAction func backBTN(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    
    
    func updateGameVersion(message: String) {
        self.showAlertUpdateGame(title: "Alert".localized, msg: message) {
            print("Update")
            self.presenter?.DownloadsFIle(isUpdate: true)
        }
    }
    
    func progressGameDownload(name: String, file: String, image: String) {
        let progress = UIStoryboard.init(name: "DownloadGame", bundle: nil).instantiateViewController(withIdentifier: "DownloadGameVC") as! DownloadGameVC
        progress.nameGame = name
        progress.imageURL = image
        progress.fileURL = file
        progress.action = self
        self.present(progress, animated: false, completion: nil)
    }
    
    func canNotJoinContest(reason:String) {
        self.showAlert(withTitle: false, msg: reason) {}
    }
    
   
}


extension ContestChallengesVC:UITableViewDelegate , UITableViewDataSource , ContestChallengeCellAction{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.challengesCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = self.contestTableView.dequeueReusableCell(withIdentifier: "ContestChallengeCell", for: indexPath) as! ContestChallengeCell
        presenter?.configuerChallenges(cell: cell, index: indexPath.row)
        cell.action = self
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        presenter?.openContestDetails(index: indexPath.row)

    }
    
    
    func play(index:Int){
        self.presenter?.selectcontest(index: index)
//        self.presenter?.DownloadsFIle(isUpdate: false)
    }
}


extension ContestChallengesVC:DownloadGamFinished{
    func DownloadGamFinished(Path: String) {
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "--->",Path)
        self.presenter?.FileDownloaded(pathFile: Path, destination: newFolderURL.path)
 
    }
    
}
