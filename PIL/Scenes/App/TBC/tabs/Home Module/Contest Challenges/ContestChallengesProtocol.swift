//
//  ContestChallengesProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 18/12/2022.
//

import Foundation

protocol ContestChallengesViewProtocol{
    var presenter:ContestChallengesPresenterProtocol? {get set}
    func setHome(lives: Int)
    func reloadData()
    func updateGameVersion(message:String)
    func progressGameDownload(name:String,file:String, image:String)//new design
    func reloadLive()
    func canNotJoinContest(reason:String)
    func UpdateApp()
}

protocol ContestChallengesPresenterProtocol{
    var view:ContestChallengesViewProtocol? {get set}
    func configuerChallenges(cell:ContestChallengeCellProtocol , index:Int)
    func challengesCount()->Int
    func getWalletInfo(obj: WalletWorkerData)
    func viewDidLoad()
    func selectcontest(index:Int)
    func DownloadsFIle(isUpdate:Bool)
    func FileDownloaded(pathFile:String , destination:String)
    func openContestDetails(index:Int)
    func getLives()
    func checkVersion()
}

protocol ContestChallengesInteractorInputProtocol{
    var presenter:ContestChallengesInteractorOutputProtocol? {get set}
    func getChallengesGame(gameId:String)
    func getGameDetails(gameId:String)
    func sendWalletRequest()
    func createFolderInDownloadsDirectory(name:String, files:String)
    func ValidateContest(contestID:String)
    func getSkills(playerID:String)

}


protocol ContestChallengesInteractorOutputProtocol{
    func getChallenges(model:[ChallengesGamesData])
    func getWalletInfo(obj:WalletWorkerData)
    func getGameData(data:GameDetailsDataModel)

    func FileDownloaded(pathFile:String , destination:String)
    func progressFileDownload(progress:Double)
    func progressGameDownload(name:String,file:String)//new design
    func FilesIFExist(path:String)
    func successValidateContest()
    func canNotJoinContest(reason:String)
    func getSkillsPlayer(skills:Int)

}

protocol  ContestChallengesRouterProtocol{
    func openContestDetails(gameID:String , contestID:String, endDate: String)
    func openLives()
}
