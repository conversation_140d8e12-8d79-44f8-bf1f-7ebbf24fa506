//
//  ContestChallengesRouter.swift
//  PIL
//
//  Created by sameh mohammed on 18/12/2022.
//

import Foundation
 
class ContestChallengesRouter:ContestChallengesRouterProtocol , ReloadGetLivesAfterWatchAdsProtocol {
    
    var VC:ContestChallengesViewProtocol?
    
    static func createModule(gameID:String) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ContestST, VC: .ContestChallengesVC)) as! ContestChallengesVC

        let router = ContestChallengesRouter()
        let interactor = ContestChallengesInteractor()
        let workerChallenge = ChallengesGameWorker()
        let workerWallet = WalletWorker()
        let workerGame = GameDetailsWorker()
        let gamesWorker =  GamesWorker()

        let presenter = ContestChallengesPresenter(view: view,
                                                   error: view,
                                                   router: router,
                                                   interactor: interactor)
        
        view.presenter = presenter
        presenter.gameID = gameID
        presenter.view = view
        presenter.interactor = interactor
        presenter.router = router
        interactor.walletWorker = workerWallet
        interactor.workerContest = workerChallenge
        interactor.presenter = presenter
        interactor.workerGame = workerGame
        interactor.gamesWorker = gamesWorker
        router.VC = view
        
        return view
    }
    
 
    func openContestDetails(gameID:String , contestID:String, endDate: String){
        let challenge = ContestDetailsRouter.createModule(contestID: contestID, gameID: gameID, endDate: endDate) as! ContestDetailsVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(challenge, animated: true)
        }
    }
    
    
    func openLives(){
        let lives = YouWantMoreLivesRouter.createModule(type: .token) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
         print("Reload lives")
        VC?.reloadLive()
    }
}
