//
//  ContestChallengeCell.swift
//  PIL
//
//  Created by sameh mohammed on 18/12/2022.
//

import UIKit
import Alamofire

protocol ContestChallengeCellProtocol{
    func contestData(image:String , title:String , content:String , users:String , lives:String )
    func index(index:Int)
}

protocol ContestChallengeCellAction{
    func play(index:Int)
}

class ContestChallengeCell: UITableViewCell , ContestChallengeCellProtocol {
    @IBOutlet weak var imageContest: UIImageView!
    @IBOutlet weak var titleContestLable: UILabel!
    @IBOutlet weak var DescContestLable: UILabel!
    
    @IBOutlet weak var stackPrize: UIStackView!
    @IBOutlet weak var userCountLable: UILabel!
    @IBOutlet weak var prizeCountLable: UILabel!
    @IBOutlet weak var playBu: UIButton!
    @IBOutlet weak var leadingCard: NSLayoutConstraint!
    @IBOutlet weak var trailingCard: NSLayoutConstraint!
    
    var index = 0
    var action:ContestChallengeCellAction?
    
    override func awakeFromNib() {
        super.awakeFromNib()

        self.selectionStyle = .none
        if UserModel.shared.getLanguage() == "ar"{
            stackPrize.alignment = .leading
            DescContestLable.textAlignment = .right
        }
        
        if UIScreen.main.bounds.width > 500{
            leadingCard.constant = 100
            trailingCard.constant = 100
        }
    }

    func index(index: Int) {
        self.index = index
    }
    
    func contestData(image: String, title: String, content: String, users: String, lives: String) {
        self.imageContest.sd_setImage(with: URL(string: image), placeholderImage: placeHolderImage)
        self.titleContestLable.text = title
        self.DescContestLable.text = content
        self.userCountLable.text = users
        self.prizeCountLable.text = lives
    }
    
    
    @IBAction func playBTN(_ sender: Any) {
        self.action?.play(index: index)
    }
    
}
