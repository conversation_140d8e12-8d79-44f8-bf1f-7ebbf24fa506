//
//  GamesSectionsCell.swift
//  PIL
//
//  Created by <PERSON> on 9/4/21.
//

import UIKit

protocol GamesSectionsCellViewDelegate{
    func setGames(numberOfSections: Int)
}

class GamesSectionsCell: UITableViewCell, UITableViewDelegate, UITableViewDataSource, GamesSectionsCellViewDelegate {

    //MARK: - outlets
    @IBOutlet weak var gamesTableList: UITableView!
    @IBOutlet weak var gamesTableListHeight: NSLayoutConstraint!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        gamesTableList.delegate = self
        gamesTableList.dataSource = self
        gamesTableList.register(UINib(nibName: "GamesCell", bundle: Bundle.main), forCellReuseIdentifier: "GamesCell")
    }
    
    //MARK: -  view delegate functions
    func setGames(numberOfSections: Int) {
        gamesTableListHeight.constant = 180.0 * CGFloat(numberOfSections)
        print("TOTAL Height is",180.0 * CGFloat(numberOfSections))
        gamesTableList.reloadData()
    }
    
    //MARK: - table delegate functions
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 6
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "GamesCell", for: indexPath) as! GamesCell
        
        return cell
    }
    
}
