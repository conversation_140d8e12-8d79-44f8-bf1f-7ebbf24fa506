//
//  GamesCell + collection delegate.swift
//  PIL
//
//  Created by <PERSON> on 9/23/21.
//

import UIKit

extension GamesCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if source == .Profile{
            if records.count == 0{
                let collectionBounds = collectionView.bounds
                return CGSize(width: collectionBounds.width, height: collectionBounds.height)
            }else{
                return CGSize(width: 115, height: 115)
            }
            
        }else{
//            let horizontalInsets = 20.0
//            let width = (collectionView.bounds.width - horizontalInsets) / 2
//            let height = (((width + 5) * 114.0) / 170.0) + 20
//            return CGSize(width: (width ?? 0.0), height: (height ?? 0.0))
            var width = 0.0
            if UIScreen.main.bounds.width > 500 {
                width = (UIScreen.main.bounds.width/2)-30
            }else{
                width = (UIScreen.main.bounds.width)-30
            }
            
            let height = (width/2)-15
            print("WIDTH GAME CELL IS",width , height)

            return CGSize(width:  width, height: height)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if source == .Profile && records.count == 0{
            return 1
        }else{
            return records.count
        }
        
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        collectionView.isScrollEnabled = true
        let gameCell = collectionView.dequeueReusableCell(withReuseIdentifier: "GameCell", for: indexPath) as! GameCell
        gameCell.source(source: source ?? .Home)
        if indexPath.row%2 == 0{
            gameCell.gameName.isHidden = true
            gameCell.rightGameName.isHidden = false
        }else{
            gameCell.gameName.isHidden = false
            gameCell.rightGameName.isHidden = true
        }
        let gameObj = records[indexPath.item]
        print("game link -> ", gameObj.gameIcons ?? "not found")
        gameCell.setGame(name: gameObj.gameName ?? "",
                             img: gameObj.gameBackground ?? "")
        gameCell.setGame(type: .individual)

        return gameCell
           
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        actionDelegate?.selectGameAt(row: rowIndex ?? 0, item: indexPath.item)
    }
}

