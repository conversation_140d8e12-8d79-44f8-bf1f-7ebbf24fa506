//
//  GameCell.swift
//  PIL
//
//  Created by <PERSON> on 9/4/21.
//

import UIKit

enum gameType{
    case contest
    case individual
}

protocol GameCellViewDelegate{
    func setGame(name: String ,img: String)
    func setGame(type: gameType)
    func source(source: GamesCellSource)
    func setCategory(name: String, img: String)
    func setFav(number: String)
}

class GameCell: UICollectionViewCell, GameCellViewDelegate {

    //MARK: - outlets
    @IBOutlet weak var contestView: UIView!
    @IBOutlet weak var contestImgView: UIImageView!
    @IBOutlet weak var contestTextImgView: UIImageView!
    @IBOutlet weak var gameImg: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet weak var categoryStack: UIStackView!
    @IBOutlet weak var categoryIcon: UIImageView!
    @IBOutlet weak var ctaegoryName: UILabel!
    @IBOutlet weak var rightGameName: UILabel!
    @IBOutlet weak var backgroundImage: UIImageView!
    
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        if app_lang == "ar"{
//            contestImgView.transform = CGAffineTransform(scaleX: 0, y: 1)
            contestImgView.flipX()
        }
    }
    
    //MARK: - delegate functions
    func source(source: GamesCellSource) {
//        categoryStack.isHidden = source == .Home
        categoryStack.isHidden = true
    }
    
    func setGame(type: gameType) {
        contestImgView.isHidden = type == .individual
//        contestTextImgView.isHidden = type == .individual
    }
    
    func setGame(name: String, img: String) {
        backgroundImage.setIMG(img: img)
//        gameImg.setIMG(img: img)
//        gameName.text = name
//        self.rightGameName.text = name
        print("Game name is",name)
    }
    
    func setCategory(name: String, img: String){
        categoryIcon.setIMG(img: img)
        ctaegoryName.text = name
    }
    
    func setFav(number: String) {
        categoryIcon.image = #imageLiteral(resourceName: "heart.fill")
        ctaegoryName.text = number
    }

}
