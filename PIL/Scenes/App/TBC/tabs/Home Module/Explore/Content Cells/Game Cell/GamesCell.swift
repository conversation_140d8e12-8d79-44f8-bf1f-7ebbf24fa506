//
//  GameCell.swift
//  PIL
//
//  Created by <PERSON> on 9/4/21.
//

import UIKit


protocol GamesCellViewDelegate{
    func setRow(index: Int)
    func setRow(title: String, records: [Game], icon: String?)
    func hasMore(value: Bool)
    func setCell(source: GamesCellSource)
}

@objc protocol GamesCellActionDelegate{
    func selectGameAt(row: Int, item: Int)
}

enum GamesCellSource{
    case Home
    case Profile
}

class GamesCell: UITableViewCell, GamesCellViewDelegate {
    
    static let identifier = "GamesCell"
    static let nib = UINib.init(nibName: "GamesCell", bundle: nil)
    
    //MARK: - variables
    var rowIndex: Int?
    var actionDelegate: GamesCellActionDelegate?
    var records = [Game]()
    var source: GamesCellSource?
    
    let horizontalInsets = 20.0
    var width: CGFloat?
    var height: CGFloat?
    
    //MARK: - outlets
    @IBOutlet weak var sectionTitle: UILabel!
    @IBOutlet weak var gamesCollection: UICollectionView!
    @IBOutlet weak var gamesCollectionHeight: NSLayoutConstraint!
    @IBOutlet weak var categoryImg: UIImageView!
    @IBOutlet weak var viewAllBtn: UIButton!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        sectionTitle.setDirection()
        viewAllBtn.underline()
        viewAllBtn.isHidden = true
        prepareCollection()
    }
    
    //MARK: - functions
    func prepareCollection(){
        gamesCollection.delegate = self
        gamesCollection.dataSource = self
        gamesCollection.register(UINib(nibName: "GameCell", bundle: Bundle.main), forCellWithReuseIdentifier: "GameCell")
        gamesCollection.register(UINib(nibName: "EmptyGamesCell", bundle: Bundle.main), forCellWithReuseIdentifier: "EmptyGamesCell")
        gamesCollection.contentInset = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 10)
    }
    
    //MARK: - view delegate functions
    func setCell(source: GamesCellSource) {
        self.source = source
        
        gamesCollection.isScrollEnabled = (source != .Home)
        if let layout = gamesCollection.collectionViewLayout as? UICollectionViewFlowLayout {
            layout.scrollDirection = (source == .Home) ? .vertical : .horizontal
        }
        
    }
    
    func hasMore(value: Bool) {
        viewAllBtn.isHidden = !value
    }
    
    func setRow(index: Int) {
        rowIndex = index
//        width = (UIScreen.main.bounds.width - CGFloat(horizontalInsets)) / 2
//        height = ((width ?? 0.0) * 134.0) / 170.0
        
        var width = 0.0
        if UIScreen.main.bounds.width > 500 {
            width = (UIScreen.main.bounds.width/2)-30
            height = (width)-15
        }else{
            width = (UIScreen.main.bounds.width)-30
            height = (width/2)-15
        }
        
//            width = (UIScreen.main.bounds.width - CGFloat(horizontalInsets)) / 2
         // * 134.0) / 160.0
        
        print("height 3333" , height , "width",width)
    }
    
    func setRow(title: String, records: [Game], icon: String?) {
        if let icon = icon {
            categoryImg.isHidden = false
            categoryImg.setIMG(img: icon)
        }else{
            categoryImg.isHidden = true
        }
        sectionTitle.text = title
        self.records = records
        if source != .Home{ 
            gamesCollectionHeight.constant = 200
        }else{
            // set the height of the collection depends on items
            // ''' set two coloumns and expand rows depends on the number of the items
//            let itemHeight = 140.0
            var numberOfRows = 0.0
            if records.count % 2 == 0{
                numberOfRows = Double(records.count )
            }else{
                let result = Double(((records.count ) + 1))
                numberOfRows = result.rounded(.down)
            }
            gamesCollectionHeight.constant = (CGFloat(numberOfRows) * (height ?? 0.0)) //- (height ?? 0.0)
        }
        gamesCollection.reloadData()
    }
    
    
    //MARK: - actions
    @IBAction func viewAllAction(_ sender: UIButton) {
    }
}


