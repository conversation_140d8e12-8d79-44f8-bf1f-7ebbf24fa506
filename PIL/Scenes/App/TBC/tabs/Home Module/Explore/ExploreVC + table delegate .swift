//
//  ExploreVC + table delegate .swift
//  PIL
//
//  Created by <PERSON> on 9/4/21.
//

import UIKit

extension ExploreVC: UITableViewDelegate, UITableViewDataSource{
    
    /// get number of games
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.gamesList().count ?? 0
    }
    
    /// display games data
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        let gamesCell = tableView.dequeueReusableCell(withIdentifier: NewGameTableViewCell.identifier, for: indexPath) as! NewGameTableViewCell
        gamesCell.gameImage.sd_setImage(with: .init(string: presenter?.gamesList()[indexPath.row].gameBackground ?? ""))
        return gamesCell
        
//        if let searchIndex = presenter?.searchIndex{
//            if indexPath.row == 0{
//                let searchCell = tableView.dequeueReusableCell(withIdentifier: "SearchCell", for: indexPath) as! SearchCell
//                return searchCell
//            }
//        }
        
        
//        if let gamesRange = presenter?.gamesCategoryRange{
//            if gamesRange.contains(indexPath.row){
//                let gamesCell = tableView.dequeueReusableCell(withIdentifier: "GamesCell", for: indexPath) as! GamesCell
//                gamesCell.actionDelegate = self
//                presenter?.configureGamesSections(cell: gamesCell, at: indexPath.row)
//                return gamesCell
//            }
//        }
        
                
        
//        let emptyCell = UITableViewCell()
//        emptyCell.backgroundColor = .clear
        
//        return emptyCell
    }
    
//    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        
////        if let searchIndex = presenter?.searchIndex{
//            if indexPath.row == 0{
//                presenter?.search()
//            }
////        }
//        
//    }
    
    /// animations cell
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard !cellsDidAnimated.contains(indexPath.row) else { return }
        cellsDidAnimated.append(indexPath.row)
        cell.animateFromBothSides(tableView, indexPath)
    }
    
    func tableView(_ tableView: UITableView, didEndDisplaying cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard !tableView.visibleCells.contains(where: { return $0 == cell }) else{ return }
        if let index = cellsDidAnimated.firstIndex(of: indexPath.row){
            cellsDidAnimated.remove(at: index)
        }
    }
    
    /// size cell (Iphone - Ipad)
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if UIScreen.main.bounds.width > 500 {
            return 300
        }else{
            return 180
        }
    }
    
    
    /// selecte game
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let cell = tableView.cellForRow(at: indexPath) as! NewGameTableViewCell
        self.selectedImage = cell.gameImage
        presenter?.selectGameAt(row: 0, item: indexPath.row)
    }
    
}



