//
//  ExploreInteractor.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//  
//

import Foundation

class ExploreInteractor: ExploreInteractorInputProtocol {

    //MARK: - variables
    weak var presenter: ExploreInteractorOutputProtocol?
    var error: ErrorProtocol?
    var walletWorker: WalletWorkerProtocol?
    var gamesWorker: GamesWorkerProtocol?
    var missionWorker: OfferWorkerProtocol?
    var categoriesWorker: CategoriesWorker?
    var workerReward:DailyRewardWorker?
    var workerVersion:UpdateVersionWorker?
    var authWorker: AuthWorkerProtocol?
    var userWorker: UserWorkerProtocol?
    var user: UserDataModel?
    
    func getProfile() {
        ProfileWorker.shared.getProfile(userId: nil){ [self] (result, statusCode) in
            switch result{
            case .success(let response):
                if 200...299 ~= response.statusCode,
                   let data = response.userRecords?.first{
                    presenter?.profileDidCompletWith(data, nil)
                    let model = deviceRequestModel(model: UIDevice.current.model,
                                                 release: "12.2",
                                                 brand: UIDevice.current.model,
                                                 broad: UIDevice.current.name,
                                                 base: "",
                                                 manufacturer: "Apple",
                                                 platform: "IOS",
                                                 userId: data.id?.getValue,
                                                 sdk: UIDevice.current.systemVersion,
                                                 type: "user")
                    setDevice(model: model)
                }else{
                    presenter?.profileDidCompletWith(nil, response.message)
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
            }
        }
    }
    
    func setDevice(model: deviceRequestModel) {
        authWorker?.setDevice(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
//                    self.presenter?.setDeviceSuccessfully(model: model.data)
                    print(model.data?.voip_token)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func getPost(by id: Int) {
        let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                         postRecord: .init(id: id))
        SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
            switch result{
            case .success(let response):
                if let data = response.postRecords.first{
                    self.presenter?.didCompleteWithPost(data)
                }
            default: break
            }
        }
    }
    
    func getSubscription() {
        authWorker?.getUserSubscription(nil){ [weak self]  result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                guard statusCode != 403 || statusCode != 401 else{
                    self.error?.sessionExpired?()
                    return
                }
                guard let response = model.data else{
                    self.error?.featching(error: "An error occured, please try againg later")
                    return
                }
                self.presenter?.didCompleteWithSubscription(response)
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        }
    }
    
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
                   guard let self = self else { return }
                   switch result{
                       case .success(let model):
                       if statusCode == 403 || statusCode == 401{
//                           self.error?.sessionExpired?()
                       }
                           if model.status ?? false{
                             if let model = model.data{
                                self.presenter?.getWalletInfo(obj: model)
                            }
                           }

                           break
                       
                       case .failure(let error):
                       ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                           self.error?.featching(error: localizedError)
                       } sessionExpired: {
                           self.error?.sessionExpired?()
                       } noInternet: {
                           self.error?.noInternet?()
                       }
                           break
            }
        })
    }
    
    func searchGames(_ query: String) {
        SearchWorker.shared.searchGames(query){ [weak self] result, statusCode in
            switch result{
            case .success(let response):
                if 200...299 ~= statusCode!{
                    guard !(response.data?.isEmpty ?? true) else{
                        self?.presenter?.emptyResults()
                        return
                    }
                    self?.presenter?.didCompleteWithSearchResult(response.data!, nil)
                }else{
                    self?.presenter?.didCompleteWithSearchResult(nil, response.message)
                }
            case  .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self?.error?.featching(error: localizedError)
                } sessionExpired: {
                    self?.error?.sessionExpired?()
                } noInternet: {
                    self?.error?.noInternet?()
                }
            }
        }
    }
    
    //===========================================================================//
    
    //MARK:- get all games
    func getAllGames(){
        gamesWorker?.GetMainGames(compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                
               
            case .success(let model):
                
                if statusCode == 403{
                    self.error?.sessionExpired?()
                }else{
                    if model.status ?? 0 == 200{
                        // get response
                       
                        do { // save data in local
                            // Create JSON Encoder
                            let encoder = JSONEncoder()
                            // Encode Note
                            let data = try encoder.encode(model)
                            // Write/Set Data
                            UserDefaults.standard.set(data, forKey: "HomeGames")
                        } catch {
                            print("Unable to Encode Array of Notes (\(error))")
                        }
                        
                        self.presenter?.gamesFeatchedSuccessfully(model: model.data ?? [])
                        
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                 break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func GetDevice_login(_ secretID : GetDeviceLoginModel) {
        print(secretID)
        HelpWorker.shared.getDeviceLogin(model: secretID , compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                print("---->jobhjivhvuhvhvhj  \(model.data?.status)")
//                self.presenter?.GoToRestore(message : model.message ?? "" ,type: model.user?.status ?? "")
                self.presenter?.GoToRestore(message: model.message ?? "", type: "\(model.data?.status ?? "")")
//                if model.data?.status == "pending_deleted" {
//                    // Go to Restore
//                    self.presenter?.GoToRestore(message : model.message ?? "" ,type: model.user?.status ?? "")
//                }else if model.data?.status == "deleted" {
//                    // Go to Restore
//                    self.presenter?.GoToRestore(message : model.message ?? "" ,type: model.user?.status ?? "")
//                }else{
//                    // Go to Terms
//                    self.presenter?.GoToRestore(message : model.message ?? "" ,type: model.user?.status ?? "")
//                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func getLinkOfTerms() {
        HelpWorker.shared.getLink(model: PILHelpTopics.terms.requestModel , compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.getTermsLinkSuccessfully(model: model)
                }else{
                    print(model.message ?? "")
                    self.error?.featching(error: model.message ?? "")
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    print(localizedError)
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}


extension ExploreInteractor{
    func getDailyReward(){
        workerReward?.checkReward(compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                if let model = model.data{
                    self.presenter?.getDailyReward(model: model)
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func checkVersionApp(){
        workerVersion?.updateVersion(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                
                if let model = model.data{
                    if model.status == true{
                        self.presenter?.checkVerseionApp(status: model.updateType ?? "")
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
}
