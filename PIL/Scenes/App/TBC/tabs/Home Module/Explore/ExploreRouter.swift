//
//  ExploreRouter.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//  
//

import Foundation
import UIKit
import AlertTransition
import SwiftPopup

class ExploreRouter: ExploreRouterProtocol, ReloadGetLivesAfterWatchAdsProtocol {
  
    weak var VC: ExploreViewProtocol?

    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ExploreST, VC: .ExploreVC)) as! ExploreVC
        let interactor = ExploreInteractor()
        let router = ExploreRouter()
        let walletWorker = WalletWorker()
        let presenter = ExplorePresenter(view: view, router: router, interactor: interactor, error: view)
        let gamesWorker = GamesWorker()
        let missionWorker = OfferWorker()
        let categoriesWorker = CategoriesWorker()
        let workerReward = DailyRewardWorker()
        let workerVersion = UpdateVersionWorker()
        let authWorker = AuthWorker()
        let userWorker = UserWorker()

        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.gamesWorker = gamesWorker
        interactor.missionWorker = missionWorker
        interactor.categoriesWorker = categoriesWorker
        interactor.walletWorker = walletWorker
        interactor.workerReward = workerReward
        interactor.workerVersion = workerVersion
        interactor.authWorker = authWorker
        interactor.userWorker = userWorker
        router.VC = view
        return view
    }
    
    func search() {
        let search = SearchRouter.createModule() as! SearchVC
        push(to: search)
    }

    func openLives(type:typeMorLive){
        let lives = YouWantMoreLivesRouter.createModule(type: type) as! YouWantMoreLivesViewController
        lives.action = self
//        lives.setDialogAnimation()
        if let vc = VC as? UIViewController{
            lives.show(above: vc)
        }
    }
    
    func toChat(_ userId: String, _ groupId: String) {
        let chatVC = ChatNewRotur.createModule(userId: Int(userId)!, groupID: Int(groupId)!, chatId: nil, isMeet: false)
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(chatVC, animated: true)
        }
    }
    
    func openReward(count:Int){
        let reward = DailyRewardRouter.createModule(count:count) as! DailyRewardVC
        reward.action = self
//        reward.setDialogAnimation()
        if let vc = VC as? UIViewController{
            reward.show(above: vc)
        }
    }
    
    func openTermsAndPolicy(title: String, link: String){
        let terms = TermsDialogRouter.createModule(.custom(title, link)) as! TermsDialogVC
        terms.modalPresentationStyle = .overFullScreen
        if let vc = VC as? UIViewController{
//            vc.present(vc, animated: false, completion: nil)
            vc.navigationController?.pushViewController(terms, animated: true)
        }
    }
    
    func GoToRestore(message : String,type : String) {
        let storyBoard = UIStoryboard(name: "TermsDialog", bundle: Bundle.main)
        let vc = storyBoard.instantiateViewController(withIdentifier: "RestoreAccountViewController") as! RestoreAccountViewController
        vc.deleted = type
        vc.message = message
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    func getLiveAfterAds() {
        VC?.ReloadLives()
    }
    
    
    func openContest(gameID:String) {
        let contest = ContestGamesRouter.createModule(gameID: gameID) as! ContestGamesVC
//        contest.transitioningDelegate = (VC as! UIViewControllerTransitioningDelegate)
        contest.modalPresentationStyle = .fullScreen
        contest.modalTransitionStyle = .crossDissolve
//        (VC as! UIViewController).present(contest, animated: true)
        (VC as! UIViewController).navigationController?.pushViewController(contest, animated: true)
    }
    
    func openNotification(){
        let noti = NotificationsRouter.createModule() as! NotificationsVC
        push(to: noti)

    }
    
    
    func openLoading(){
        let loadingVC = LoadingMeetRouter.createModule() as! LoadingMeetVC
        loadingVC.hidesBottomBarWhenPushed = true
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(loadingVC, animated: true)
        }
    }
    
    func push(to view: UIViewController) {
        if let vc = VC as? UIViewController{
            view.hidesBottomBarWhenPushed = true
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    
    
    func toViewRewards() {
        let mission = MissionsListVC.Router.createModule() as! MissionsListVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(mission, animated: true)
        }
    }
    
    func toProfile(_ id: String) {
        let profileVC = MainProfileRouter.createModule(profile: .publicProfile(id))
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(profileVC, animated: true)
        }
    }
    
    func toUserPost(_ data: PostRecord) {
        let userPostVC = UserPostRouter.createModule(data)
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(userPostVC, animated: true)
        }
    }
    
    func toSearch() {
        let vc = SocialSearchRouter.createModule(SocialSearchVC.SeachType.home([]))
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    func toCreateMeetAndPlayRoom() {
        let vc = GameChallangeRouter.createModule() as! GameChallangeVC
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
}
