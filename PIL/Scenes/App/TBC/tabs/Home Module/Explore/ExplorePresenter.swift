//
//  ExplorePresenter.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//  
//

import Foundation
import UIKit
import KeychainAccess

class ExplorePresenter: ExplorePresenterProtocol, ExploreInteractorOutputProtocol, ErrorProtocol{


    //MARK: - variables
    weak var view: ExploreViewProtocol?
    var error: ErrorProtocol?
    var router: ExploreRouterProtocol?
    var interactor: ExploreInteractorInputProtocol?
    var contentCount: Int?
    var gamesSectionsList = [RowsRecordDataModel]()
    var gamesCategoryRange: Range<Int>?
    var allCategoriesIndex: Int?
    var livesModel: WalletWorkerData?
    
    var apiGroup: DispatchGroup?
    var noInternetFlag = false
    var secret = ""
    let keychain = Keychain(service: "com.gt.playitleague")
    
    private var tempRecords = [RowsRecordDataModel]()
    
    //MARK: - init
    init(view: ExploreViewProtocol,
         router: ExploreRouterProtocol,
         interactor: ExploreInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        /// get user profile
        interactor?.getProfile()
        /// get Subscription data
        interactor?.getSubscription()
     
        /// checl the iser is login to send requst
        if UserModel.shared.get_loginAsGuest() == false{
            self.interactor?.getDailyReward()
        }
        
        /// set all api's in group to show all one time
        apiGroup = DispatchGroup()

        /// get all games
        if let group = apiGroup{
            group.enter()
            interactor?.getAllGames()
        }else{
            return
        }
        
        /// get version app to show update app if need it
        if let group = apiGroup{
            group.enter()
            interactor?.checkVersionApp()
        }

        /*
        - when all api's get data
         hidden indicator
         reload screen
         */
        if let group = apiGroup{
            group.notify(queue: .main) { [weak self]  in
                guard let self = self else { return }
                Indicator.shared.hideProgressView()
                if self.noInternetFlag{
                    self.error?.noInternet?()
                    self.noInternetFlag = false
                }else{
//                    self.setCellsIndecies()
//                    self.view?.reloadPageContentWithoutAnimation()
                    self.apiGroup = nil
                    self.GetLives()
                }
            }
        }else{
            return
        }
            
        
        /// Deep Link
        if !UserModel.shared.get_GameIdFromDeepLink().isNilOrEmpty{
            /// navigate to contest
            router?.openContest(gameID: UserModel.shared.get_GameIdFromDeepLink()!)
            UserDefaults.standard.removeObject(forKey: UserModelKeys().gameIdFromDeepLink)
        }else if !UserModel.shared.get_UserIdFromDeepLink().isNilOrEmpty{
            /// navigate to  profile
            router?.toProfile(UserModel.shared.get_UserIdFromDeepLink()!)
            UserDefaults.standard.removeObject(forKey: UserModelKeys().userIdFromDeepLink)
        }else if !UserModel.shared.get_PostIdFromDeepLink().isNilOrEmpty{
            /// navigate to post
            interactor?.getPost(by: Int(UserModel.shared.get_PostIdFromDeepLink() ?? "0")!)
            UserDefaults.standard.removeObject(forKey: UserModelKeys().postIdFromDeepLink)
        }else if !UserModel.shared.get_ChatIdFromDeepLink().isNilOrEmpty{
            /// navigate to chat
            router?.toChat(UserModel.shared.get_ChatIdFromDeepLink()!, "0")
            UserDefaults.standard.removeObject(forKey: UserModelKeys().chatIdFromDeepLink)
        }else if UserDefaults.standard.bool(forKey: UserModelKeys().didReceiveGift){
            /// navigate to profile
            router?.toProfile(UserModel.shared.get_id())
        }else if UserDefaults.standard.bool(forKey: UserModelKeys().didReceiveLevelUp){
            router?.toProfile(UserModel.shared.get_id())
            UserModel.shared.didReceiveLevelUpNotification(false)
        }
        
    }
    
    /* 
     return list of games
     the game in categories but now all games in one category take first section it have al games
     */
    func gamesList()-> [Game]{
        return gamesSectionsList.first?.games ?? []
    }
    
    /// navtigate to post
    func didCompleteWithPost(_ data: PostRecord) {
        router?.toUserPost(data)
    }
    
    /// pull to refresh
    func refresh(){
        gamesSectionsList.removeAll()
        contentCount = nil
        gamesCategoryRange = nil
        allCategoriesIndex = nil
        view?.reloadPageContent()
        self.viewDidLoad()
    }
    
    /// send request to get wallet info
    func GetLives(){
        interactor?.sendWalletRequest()
    }
    
    /// Get Terms when load app at first time
    func GetTerms() {
        self.secret = "\(keychain["uniqueId"]?.encryptAES() ?? "")"
        interactor?.GetDevice_login(GetDeviceLoginModel(secret_id: self.secret , language: UserModel.shared.getLanguage() , platform: "IOS", version: (Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String)!))
    }
    
    func GoToRestore(message : String,type: String) {
        print("ppppp----ppp--->>>> \(type)")
        if type == "pending_deleted" {
            router?.GoToRestore(message: message, type: type)
        }else if type == "deleted" {
            router?.GoToRestore(message: message, type: type)
        }else{
            interactor?.getLinkOfTerms()
        }
    }
    
    /// save Unique ID in keychain
    func saveUniqueID() {
        let keychain = Keychain(service: "com.gt.playitleague")
        let check = try? keychain.contains("uniqueId")
        if check == false {
            let DeviceID = UIDevice.current.identifierForVendor?.uuidString
            keychain["uniqueId"] = DeviceID
        }
    }
    
    /// request get terms and conditions
    func GetDeviceLogin() {
        interactor?.getLinkOfTerms()
    }
    
    /// respose ge tTerms
    func getTermsLinkSuccessfully(model: HelpModel) {
        if let obj = model.data?.body?.first{
            router?.openTermsAndPolicy(title: obj.title ?? "", link: obj.content?.iframeSrc ?? "")
//            view?.ShowTermsDialog(title: obj.title ?? "", link: obj.content?.iframeSrc ?? "")
        }
    }
    
    /// respose get wallet info and save data in local
    func getWalletInfo(obj: WalletWorkerData) {
        livesModel = obj
        self.view?.updateCoins(with: obj.playerToken ?? 0)
        if let count = self.livesModel?.lives{
            UserModel.shared.setCountLives(count: count)
            self.view?.setHome(lives: count)
        }
        if let countTokens = self.livesModel?.playerToken{
            UserModel.shared.setPlayerTokens(count: countTokens)
        }
    }
    
    /// navigate to search
    func didTapSearchBtn() {
        router?.toSearch()
    }
    
    // check if have a new version
    func checkVerseionApp(status:String){
        self.view?.statusAppVersion(status: status)
    }
    
    /// respose user info
    func profileDidCompletWith(_ data: UserDataModel?, _ error: String?) {
        guard error == nil else{
            return
        }
        guard let data = data else{ return }
        UserModel.shared.setData(model: data)
    }
    
    //MARK: - content cells
    
    func setCellsIndecies(){
        var countIndecies = 0

        if !(gamesSectionsList.isEmpty ){
            gamesCategoryRange = countIndecies..<((gamesSectionsList.count )+countIndecies)
            countIndecies+=(gamesSectionsList.count)
        }
    }
    
    //MARK: - games Section
    /// configuer games Cell
    func configureGamesSections(cell: GamesCellViewDelegate, at index: Int) {
        let finalIndex = index - (gamesCategoryRange?.startIndex ?? 0)
        let section = gamesSectionsList[finalIndex]
        cell.setCell(source: .Home)
        cell.setRow(index: index)
        cell.setRow(title: section.title ?? "", records: section.games ?? [], icon: section.icons)
    }
    
    /// when select game navigate to contest
    func selectGameAt(row: Int, item: Int) {
        //search edit
//        if let _ = searchIndex{
//            substractionValue+=1
//        }
        let gameID = "\(gamesSectionsList[row].games?[item].id ?? 0)"
        router?.openContest(gameID: gameID)
    }
    
    //MARK: - Search
    func didStartSearch(_ query: String) {
        tempRecords = gamesSectionsList
        interactor?.searchGames(query)
    }
    
    /// remove old search
    func resetSearch() {
        gamesSectionsList = tempRecords
        if !(gamesSectionsList.isEmpty ){
            contentCount = (contentCount ?? 0) + (gamesSectionsList.count  )
//            self.setCellsIndecies()
            self.view?.reloadPageContent()
        }
    }
    
    /// search games in same screen
    func didCompleteWithSearchResult(_ data: [Game]?, _ error: String?) {
        var section: RowsRecordDataModel = .init()
        section.games = data
        gamesSectionsList = [section]
        if !(gamesSectionsList.isEmpty ){
            contentCount = (contentCount ?? 0) + (gamesSectionsList.count  )
//            self.setCellsIndecies()
            self.view?.reloadPageContent()
        }
    }
    
    func emptyResults(){
        print("No data in search")
    }
    
    //MARK: - Games fetching
    /// response games home
    func gamesFeatchedSuccessfully(model: [RowsRecordDataModel]) {
        gamesSectionsList = model
        if !(gamesSectionsList.isEmpty ){
            contentCount = gamesSectionsList.count
//            self.setCellsIndecies()
            self.view?.reloadPageContent()
        }
        if let group = apiGroup{
            group.leave()
        }
    }
    
    /// load home screen from local storage
    func loadDateFromLocalStorage(){
        if let data = UserDefaults.standard.data(forKey: "HomeGames") {
            do {
                // Create JSON Decoder
                let decoder = JSONDecoder()

                // Decode Note
                let games = try decoder.decode(MainAllGamesModel.self, from: data)
//                print("---->" , games.data?[0].rowRecords?[0].game?[0].gameName ?? "")
                
                gamesSectionsList = games.data ?? []
                if !(gamesSectionsList.isEmpty ){
                    contentCount = gamesSectionsList.count
                }
//                self.setCellsIndecies()
                self.view?.reloadPageContent()            } catch {
                print("Unable to Decode Notes (\(error))")
            }
        }
    }
    
    /// reload screen data
    func reload() {
        if !gamesSectionsList.isEmpty{
            gamesSectionsList.removeAll()
            loadDateFromLocalStorage()
        }
    }
    
    //MARK: - error handler and  delegate functions
    func featching(error: String) {
        if let group = apiGroup{
            group.leave()
        }
        print("Test Error",error )
//        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        noInternetFlag = true
    }
    
    //MARK: - actions
    func logout() {
        UserModel.shared.logOut()
    }
    
    /// navigate to  search games screen
    func search() {
        router?.search()
    }
    
    /// navigate to  rewards
    func getDailyReward(model:DailyRewarDataClass){
        if model.deserve == true{
            if let count = model.reward {
                self.router?.openReward(count: count)
            }
        }
    }
    
    /// navigate to lives popup screen
    func openLives(type:typeMorLive) {
        self.router?.openLives(type: type)
    }
    
    /// navigate to notifications screen
    func openNotification() {
        router?.openNotification()
    }
    
    func openLoading() {
        router?.openLoading()
    }
    
    
    /// navigate to popup
    func onTapViewRewards() {
        router?.toViewRewards()
    }
    
    ///  Save Subscription in local storage
    func didCompleteWithSubscription(_ data: SubscriptionResponse) {
        UserModel.shared.setUserSubscription_Subscribe(data.subscribed)
        UserModel.shared.setUserSubscription(data)
    }
}
