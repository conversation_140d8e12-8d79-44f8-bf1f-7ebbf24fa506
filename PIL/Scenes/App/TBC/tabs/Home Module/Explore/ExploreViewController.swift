//
//  ExploreViewController.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//  
//

import Foundation
import UIKit
//import AppLovinSDK
import IQKeyboardManager

class ExploreVC: UIViewController, ExploreViewProtocol, ErrorProtocol, UITabBarControllerDelegate , UIViewControllerTransitioningDelegate{

    
    //MARK: - variables
    var presenter: ExplorePresenterProtocol?
    var refresh = UIRefreshControl()
    let transition = PopAnimator()
    var selectedImage: UIImageView?
    var cellsDidAnimated: [Int] = []
//    var adView: MAAdView!

    //MARK: - outlets
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var navigationViewBackGroundImg: UIImageView!
    @IBOutlet weak var menuBtn: UIButton!
    @IBOutlet weak var contentTableList: UITableView!
    @IBOutlet var swipeElement: UISwipeGestureRecognizer!
    @IBOutlet weak var livesCount: UILabel!
    @IBOutlet weak var coinsCount: UILabel!
    @IBOutlet weak var viweAds: UIView!
    @IBOutlet weak var searchTF: UITextField!
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        self.tabBarController?.delegate = self
        presenter?.saveUniqueID() /// save data in local
        /// show terms in frist time use application agter install from app stpre
        guard UserModel.shared.getFirstTimeV() else {
            presenter?.GetTerms()
            return
        }
        UI()
        setUpNavigationView()// UI
        setUpTableList() // UI
       
//        if UserModel.shared.get_tutorialHome() == false{
//            Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { timer in
//                let st = UIStoryboard.init(name: "TutorialStart", bundle: nil).instantiateViewController(withIdentifier: "TutorialStartingVC") as! TutorialStartingVC
//                self.present(st, animated: false)
//            }
//        }
        
//        createBanner()
        
//        self.tabBarController?.view.transform = .init(scaleX: 0.3, y: 0.3)
//        UIView.animate(withDuration: 1) {
//            self.tabBarController?.view.transform = .init(scaleX: 1, y: 1)
//        }
//        
        
        /// Load data from local storage and call api letter and if found any update reload tableview again
        self.presenter?.loadDateFromLocalStorage()
        DispatchQueue.main.asyncAfter(deadline: .now()+1, execute: {
            self.presenter?.viewDidLoad()
        })
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.tabBarController?.tabBar.isHidden = false
        Style(navigation: false) // UI
        presenter?.reload() // Load home data
        presenter?.GetLives() // get wallet info
        cellsDidAnimated.removeAll()
    }
    
    /// navigate to search screen
    @IBAction func searchAction(_ sender: Any) {
        presenter?.didTapSearchBtn()
    }
    
    //MARK: - functions

    // send requset to get wallet info
    func ReloadLives(){
        presenter?.GetLives()
    }
    
    /// get status app version and show popup
    func statusAppVersion(status: String) {
        let popup = UIStoryboard.init(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "UpdateAppVersionVC") as! UpdateAppVersionVC
        /// can skip
        if status == AppUpdateType.FLEXIBLE.rawValue {
            popup.type = .FLEXIBLE
            
        /// must be update
        }else if status == AppUpdateType.IMMEDIATE.rawValue{
            popup.type = .IMMEDIATE
        }
        self.present(popup, animated: true)
    }
    
    func setUpNavigationView(){
        // Corners raduios to some posion in view
        navigationView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        navigationViewBackGroundImg.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
        
        if app_lang == "en"{
            menuBtn.flipX()
        }
        
        if app_lang == "en"{
            swipeElement.direction = .right
        }else{
            swipeElement.direction = .left
        }
        /// add pull refresh to screen
        refresh.addTarget(self, action: #selector(ExploreVC.refreshPage), for: .valueChanged)
        refresh.tintColor = .clear
        contentTableList.addSubview(refresh)
    }
    
    /// Settep naviageion and keyboard
    func UI(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.misson , .live , .token])
        self.navigationView.setNavigationTitle(title: "Games")
        self.navigationView.ShowLogo(ishidden: true)
        IQKeyboardManager.shared().disabledDistanceHandlingClasses.add(ExploreVC.self)
        IQKeyboardManager.shared().disabledToolbarClasses.add(ExploreVC.self)
        searchTF.returnKeyType = .search
    }
    
    /// UI Cell
    func setUpTableList(){
//        contentTableList.register(UINib(nibName: "SearchCell", bundle: Bundle.main), forCellReuseIdentifier: "SearchCell")
//        contentTableList.register(UINib(nibName: "GamesCell", bundle: Bundle.main), forCellReuseIdentifier: "GamesCell")
        contentTableList.register(NewGameTableViewCell.nib, forCellReuseIdentifier: NewGameTableViewCell.identifier)
    }
    
    //MARK: - presenter delegate functions
    /// refresh page
    func reloadPageContent() {
        refresh.endRefreshing()
        contentTableList.reloadData()
    }
    
    
    /// check subscribed and display number of lives and tokebs and
    func setHome(lives: Int) {
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
            livesCount.attributedText = imageString
        }else{
            livesCount.text = "\(lives)"
        }
    }
    
    func updateCoins(with coins: Int) {
        coinsCount.text = "\(coins)"
    }
    
    
    /// Show dialog terms
    func ShowTermsDialog(title: String, link: String) {
        let terms = TermsDialogRouter.createModule(.custom(title, link)) as! TermsDialogVC
        terms.modalPresentationStyle = .overFullScreen
        navigationController?.pushViewController(terms, animated: true)
//        self.present(terms, animated: true)
    }
    
    
    //MARK: - error delegate functions
    func featching(error: String) {
        refresh.endRefreshing()
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    

    
    
    func sessionExpired() {
        refresh.endRefreshing()
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    /// check internet connections
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
    //MARK: - actions
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        if viewController == self{
            self.navigationController?.popToRootViewController(animated: false)
        }
    }
    ///navigate to search screen
    @objc func searchGesterAction(){
        presenter?.search()
    }
    
    @objc func refreshPage(){
//        refresh.beginRefreshing()
        presenter?.refresh()
    }
    
    @IBAction func meetplayBtnAction(_ sender: Any) {
        presenter?.openLoading()
    }
    ///navigate to notification
    @IBAction func notificationBTN(_ sender: Any) {
        presenter?.openNotification()
    }
    
    @IBAction func swipeAction(_ sender: UISwipeGestureRecognizer) {
//        presenter?.openMenu()
    }
    
    @IBAction func openMenu(_ sender: UIButton) {
    }
    
    ///navigate to popup lives
    @IBAction func openLivesBTN(_ sender: Any) {
        self.presenter?.openLives(type: .live)
        
    }
    ///navigate to reward
    @IBAction func rewardBTN(_ sender: Any) {
        if UserModel.shared.get_loginAsGuest(){
            let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
            self.present(vc, animated: false, completion: nil)
        }else{
            presenter?.onTapViewRewards()
        }
    }
    
    ///navigate to tokens popup
    @IBAction func piTokenBTN(_ sender: Any) {
        self.presenter?.openLives(type: .token)

    }
    
}


///MARK: - animations cell
extension ExploreVC{
    func animationController(forPresented presented: UIViewController, presenting: UIViewController, source: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        transition.originFrame = selectedImage!.superview!.convert(selectedImage!.frame, to: source.view)
        transition.presenting = true
        return transition
    }
    
    func animationController(forDismissed dismissed: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        transition.presenting = false
        return nil
    }
    
}


//MARK: - textfiled delegate
/*
 user in search
 */
extension ExploreVC: UITextFieldDelegate{
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.text.isNilOrEmpty{
            view.endEditing(true)
            presenter?.resetSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        guard !textField.text.isNilOrEmpty else{
            view.endEditing(true)
            return true
        }
        guard !textField.text!.trimmingCharacters(in: .whitespaces).isEmpty else{
            textField.text = ""
            view.endEditing(true)
            return true
        }
        presenter?.didStartSearch(textField.text!)
        view.endEditing(true)
        return true
    }
}
