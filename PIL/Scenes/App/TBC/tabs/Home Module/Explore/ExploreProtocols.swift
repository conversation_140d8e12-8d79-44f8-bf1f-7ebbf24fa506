//
//  ExploreProtocols.swift
//  PIL
//
//  Created by <PERSON> on 9/1/21.
//  
//

import Foundation

protocol ExploreViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: ExplorePresenterProtocol? { get set }
    func reloadPageContent()
    func setHome(lives: Int)
    func updateCoins(with coins: Int)
    func ReloadLives()
    func statusAppVersion(status:String)
    func ShowTermsDialog(title: String, link: String)
}

protocol ExplorePresenterProtocol: AnyObject {

    var view: ExploreViewProtocol? { get set }
    var gamesCategoryRange: Range<Int>? { get set }
    var allCategoriesIndex: Int? { get set }
    func viewDidLoad()
    func refresh()
    var contentCount: Int? { get set }
    func configureGamesSections(cell: GamesCellViewDelegate, at index: Int)
    func selectGameAt(row: Int, item: Int)
    func search()
    func logout()
    func GetLives()
    func GetTerms()
    func saveUniqueID()
    func GetDeviceLogin()
    func openLives(type:typeMorLive)
    func openNotification()
    func openLoading()
    func onTapViewRewards()
    func didTapSearchBtn()
    func didStartSearch(_ query: String)
    func resetSearch()
    func gamesList()-> [Game]
    func reload()
    func loadDateFromLocalStorage()
}

protocol ExploreInteractorInputProtocol {
    var presenter: ExploreInteractorOutputProtocol? { get set }
    func getAllGames()
    func sendWalletRequest()
    func getLinkOfTerms()
    func getDailyReward()
    func checkVersionApp()
    func getSubscription()
    func getPost(by id: Int)
    func getProfile()
    func searchGames(_ query: String)
    func GetDevice_login(_ secretID : GetDeviceLoginModel)
}

protocol ExploreInteractorOutputProtocol: AnyObject {
    func gamesFeatchedSuccessfully(model: [RowsRecordDataModel])
    func getWalletInfo(obj:WalletWorkerData)
    func getDailyReward(model:DailyRewarDataClass)
    func checkVerseionApp(status:String)
    func didCompleteWithSubscription(_ data: SubscriptionResponse)
    func didCompleteWithPost(_ data: PostRecord)
    func profileDidCompletWith(_ data: UserDataModel?,_ error: String?)
    func didCompleteWithSearchResult(_ data: [Game]?,_ error: String?)
    func emptyResults()
    func getTermsLinkSuccessfully(model: HelpModel)
    func GoToRestore(message : String,type: String)
}

protocol ExploreRouterProtocol {
    func openLives(type:typeMorLive)
    func search()
    func openContest(gameID:String)    
    func openReward(count:Int)
    func openNotification()
    func openLoading()
    func toViewRewards()
    func toProfile(_ id: String)
    func toUserPost(_ data: PostRecord)
    func toSearch()
    func toCreateMeetAndPlayRoom()
    func toChat(_ userId: String,_ groupId: String)
    func openTermsAndPolicy(title: String, link: String)
    func GoToRestore(message : String,type: String)
}

