//
//  Explore+Enum.swift
//  PIL
//
//  Created by sameh mohammed on 15/03/2023.
//

import Foundation

/// Status App Type when open APP
enum  AppUpdateType: String {
    case FLEXIBLE = "FLEXIBLE"
    case IMMEDIATE = "IMMEDIATE"
}


/// Status when join game 
enum JoinContestReasons: String{
    case ENDED = "ENDED"
    case BALANCE = "BALANCE"
    case MAXIMUM_ATTEMPT = "MAXIMUM_ATTEMPT"
    case NOT_JOINED = "NOT_JOINED"
    case UPDATE_APP = "UPDATE_APP"
}
