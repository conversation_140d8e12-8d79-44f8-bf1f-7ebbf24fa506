//
//  DownloadGameVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 5/26/22.
//

import UIKit

/*
 show progress download
 when finish download back with path file
 */
protocol DownloadGamFinished {
    func DownloadGamFinished(Path:String)
}

class DownloadGameVC: UIViewController {

    //MARK: - outlet
    @IBOutlet weak var gameImage: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet weak var downloadInfoLable: UILabel!
    @IBOutlet weak var progressDownload: UIProgressView!
    
    
    //MARK: - variable
    private var observation: NSKeyValueObservation?
    var fileURL = String()
    var nameGame = String()
    var imageURL = String()
    var action:DownloadGamFinished?
    let state = UIApplication.shared.applicationState
    var isDownload = false
    
    deinit {
      observation?.invalidate()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        gameName.text = nameGame
        gameImage.setIMG(img: imageURL)
//        testSpeed { speed in
//            print("speed---->", speed)
//            if speed < 0.500{
//                DispatchQueue.main.async {
//                    let alert = UIAlertController(title: "Alert".localized, message: "Internet speed is not good".localized, preferredStyle: .alert)
//                    let ok = UIAlertAction(title: "continuation".localized, style: .destructive){ (_) in
//                        self.downloadFile(files: self.fileURL)
//                    }
//
//                    let cancle = UIAlertAction(title: "Cancel".localized, style: .cancel){ (_) in
//                        self.dismiss(animated: false)
//                    }
//
//                    alert.addAction(ok)
//                    alert.addAction(cancle)
//                    self.present(alert, animated: true)
//                }
//
//            }else{
                self.downloadFile(files: self.fileURL)
//            }
//        }
        
    }

    
    func downloadFile(files:String){
        print("FILES" , files)
        if   let url = URL(string: files ) {
            let task = Foundation.URLSession.shared.dataTask(with: url)

         observation = task.progress.observe(\.fractionCompleted) { progress, _ in
           print("progress: ", progress.fractionCompleted)
            DispatchQueue.main.async {
                if (Int(progress.fractionCompleted*100)) == 100 || (Int(progress.fractionCompleted*100)) == 99{
                    self.downloadInfoLable.text = "Please wait".localized

                }else{
                    let downloadtxt = "Download :".localized
                    self.downloadInfoLable.text = "\(downloadtxt) \(Int(progress.fractionCompleted*100)) %"
                }
                self.progressDownload.progress = Float(progress.fractionCompleted)
                if  progress.fractionCompleted == 1{
                    if self.state == .background {
                        print("App in Background")
                    }else{
                        if  let urlFile = URL(string: files){
                            
                            DispatchQueue.global().async {
                                // Some background task
                                FileDownloader.loadFileSync(url: urlFile) { (path, error) in
                                    //                                print("File downloaded to : \(path!)")
                                    DispatchQueue.main.async {
                                        self.dismiss(animated: false) {
                                            self.action?.DownloadGamFinished(Path: path!)
                                        }
                                    }
                                }
                            }
                            
                            
                            
//                            FileDownloader.downloadFile(from: urlFile) { (path, error) in
////                                print("File downloaded to : \(path!)")
//                                DispatchQueue.main.async {
//                                    self.dismiss(animated: false) {
//                                        self.action?.DownloadGamFinished(Path: path!)
//                                    }
//                                }
//                            }
                        }
                
                    }
                }
            }
      
         }

         task.resume()
        }
    }
 
    func testSpeed(completion:@escaping (_ speed:Double)->Void)  {
        let url = URL(string: "https://pilservices.s3.eu-central-1.amazonaws.com/Offers/XMazkQP5RcRQ1wtcPY4DHRXqTAKBYLmYjmoPRZhG.png")
        let request = URLRequest(url: url!)
        let session = URLSession.shared
        let startTime = Date()
        let task =  session.dataTask(with: request) { (data, resp, error) in
            guard error == nil && data != nil else{
                print("connection error or data is nill")
                return
            }
            guard resp != nil else{
                print("respons is nill")
                return
            }
            let length  = CGFloat( (resp?.expectedContentLength)!) / 1000000.0
            print(length)
            let elapsed = CGFloat( Date().timeIntervalSince(startTime))
            print("elapsed: \(elapsed)")
            print("Speed: \(length/elapsed) Mb/sec")
            let speed =  Double(length/elapsed)
            completion(speed)
        }
        task.resume()
    }
    
}
