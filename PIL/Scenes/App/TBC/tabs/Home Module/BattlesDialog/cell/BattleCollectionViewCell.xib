<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="BattleCollectionViewCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="244" height="194"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="244" height="194"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8Lt-lY-i6U">
                        <rect key="frame" x="0.0" y="0.0" width="244" height="194"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WcA-Ez-Rlf">
                                <rect key="frame" x="62" y="42" width="120" height="110"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="120" id="7ah-DV-Yf1"/>
                                    <constraint firstAttribute="height" constant="110" id="QIC-7l-M5X"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                        <color key="value" name="White"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                        <real key="value" value="1"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="W8i-d7-GSf">
                                <rect key="frame" x="63" y="53.666666666666664" width="118" height="86.666666666666686"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zpb-8Q-LnI">
                                        <rect key="frame" x="0.0" y="0.0" width="118" height="40"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Header Btn Type(2)" translatesAutoresizingMaskIntoConstraints="NO" id="lQM-Bv-cVV">
                                                <rect key="frame" x="39" y="0.0" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="i86-0Y-aib"/>
                                                    <constraint firstAttribute="height" constant="40" id="n3Z-M5-kwu"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="lQM-Bv-cVV" firstAttribute="top" secondItem="zpb-8Q-LnI" secondAttribute="top" id="7vh-kc-IE9"/>
                                            <constraint firstItem="lQM-Bv-cVV" firstAttribute="centerX" secondItem="zpb-8Q-LnI" secondAttribute="centerX" id="Sbl-DL-Rbv"/>
                                            <constraint firstAttribute="bottom" secondItem="lQM-Bv-cVV" secondAttribute="bottom" id="pSq-4X-c7W"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Win 500 tokens" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5ZB-Dj-zAh">
                                        <rect key="frame" x="0.0" y="45.000000000000007" width="118" height="18.333333333333336"/>
                                        <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="13"/>
                                        <color key="textColor" name="Withe"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isShadowOnText" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pay 250" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GXL-EG-Vwt">
                                        <rect key="frame" x="0.0" y="68.333333333333343" width="118" height="18.333333333333329"/>
                                        <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="13"/>
                                        <color key="textColor" name="Withe"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isShadowOnText" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="W8i-d7-GSf" firstAttribute="centerY" secondItem="8Lt-lY-i6U" secondAttribute="centerY" id="fHa-qx-H8e"/>
                            <constraint firstItem="WcA-Ez-Rlf" firstAttribute="centerY" secondItem="8Lt-lY-i6U" secondAttribute="centerY" id="jhN-ii-EZD"/>
                            <constraint firstItem="W8i-d7-GSf" firstAttribute="centerX" secondItem="8Lt-lY-i6U" secondAttribute="centerX" id="mBs-JR-7h3"/>
                            <constraint firstItem="WcA-Ez-Rlf" firstAttribute="centerX" secondItem="8Lt-lY-i6U" secondAttribute="centerX" id="tPZ-ch-Pgc"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="8Lt-lY-i6U" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Po0-N2-Esy"/>
                <constraint firstItem="8Lt-lY-i6U" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="eIB-Du-2RY"/>
                <constraint firstAttribute="bottom" secondItem="8Lt-lY-i6U" secondAttribute="bottom" id="gfD-Mu-oRv"/>
                <constraint firstAttribute="trailing" secondItem="8Lt-lY-i6U" secondAttribute="trailing" id="p55-s1-auo"/>
            </constraints>
            <size key="customSize" width="244" height="194"/>
            <connections>
                <outlet property="pay" destination="GXL-EG-Vwt" id="0xu-3W-7wh"/>
                <outlet property="win" destination="5ZB-Dj-zAh" id="CBs-Xe-r7e"/>
            </connections>
            <point key="canvasLocation" x="213.74045801526717" y="70.422535211267615"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="A_Header Btn Type(2)" width="44" height="44"/>
        <namedColor name="White">
            <color red="0.93699997663497925" green="0.92500001192092896" blue="0.95300000905990601" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Withe">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
