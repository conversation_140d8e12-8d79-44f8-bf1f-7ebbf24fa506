//
//  BattleCollectionViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 24/12/2023.
//

import UIKit

class BattleCollectionViewCell: UICollectionViewCell {

    static let identfier = "BattleCollectionViewCell"
    static let nib = UINib(nibName: "BattleCollectionViewCell", bundle: nil)
    
    @IBOutlet weak var win: UILabel!
    @IBOutlet weak var pay: UILabel!
    
    var battle: ChallengesGamesData?{
        didSet{
            pay.text = "Pay".localized + " \(battle?.entryfeeDeposit ?? 0)"
            win.text = "Win".localized + " \((battle?.entryfeeDeposit ?? 0)*2) " + "Tokens".localized
        }
    }

}
