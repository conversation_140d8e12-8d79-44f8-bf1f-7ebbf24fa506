//
//  BattlesDialog.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 24/12/2023.
//

import UIKit

protocol BattlesDialogProtocol{
    func didSelectBattle(_ battle: ChallengesGamesData)
}

class BattlesDialog: UIViewController {
    
    @IBOutlet weak var battlesCollectionView: UICollectionView!
    @IBOutlet weak var gameImage: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet var arrows: [UIButton]!
    
    var gameDate: (String, String)?
    var battles: [ChallengesGamesData]?
    var currentIndex: Int = 0
    var delegate: BattlesDialogProtocol?

    override func viewDidLoad() {
        super.viewDidLoad()
        if UserModel.shared.getLanguage() == "ar" || app_lang == "ar"{
            for i in arrows{
                i.transform = i.transform.rotated(by: .pi)
            }
        }
        battlesCollectionView.register(BattleCollectionViewCell.nib, forCellWithReuseIdentifier: BattleCollectionViewCell.identfier)
        gameImage.sd_setImage(with: .init(string: gameDate?.1 ?? ""))
        gameName.text = gameDate?.0
    }
    
    @IBAction func findBattle(_ sender: Any) {
        delegate?.didSelectBattle(battles![currentIndex])
        self.dismiss(animated: false)
    }
    
    @IBAction func next(){
        guard battles!.count > 1 else { return }
        if currentIndex < (battles!.count-1){
            battlesCollectionView.scrollToItem(at: .init(item: currentIndex+1, section: 0), at: .centeredHorizontally, animated: true)
        }
    }
    
    @IBAction func previous(){
        guard battles!.count > 1 else { return }
        if currentIndex > 0{
            battlesCollectionView.scrollToItem(at: .init(item: currentIndex-1, section: 0), at: .centeredHorizontally, animated: true)
        }
    }

    @IBAction func dismissDialog(_ sender: Any) {
        self.dismiss(animated: false)
    }
}

extension BattlesDialog: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return battles?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: BattleCollectionViewCell.identfier, for: indexPath) as! BattleCollectionViewCell
        cell.battle = battles![indexPath.row]
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        self.currentIndex = indexPath.row
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return .init(width: collectionView.frame.width, height: collectionView.frame.height)
    }
    
}
