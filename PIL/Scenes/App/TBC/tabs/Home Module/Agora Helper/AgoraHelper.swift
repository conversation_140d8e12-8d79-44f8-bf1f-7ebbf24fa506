//
//  AgoraHelper.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 22/01/2024.
//

import Foundation
import SignalRClient
import AgoraRtcKit

class AgoraHelper:UIViewController{
    
    let serverUrl = "https://socialapp.pil.live/ws?User=\(UserModel.shared.get_id())"
    var chatHubConnection: HubConnection?
    var chatHubConnectionDelegate: HubConnectionDelegate?
    var agoraKit: AgoraRtcEngineKit! // agora
    weak var logVC: LogViewController? // agora
    var isMute = false

    override func viewDidLoad() {
        super.viewDidLoad()
       
    }
    
    func initCall(){
        initializeAgoraEngine()
        setupAudio()
        agoraKit.muteLocalAudioStream(false)
        configuerSocket()
    }
    
    func configuerSocket(){
        print("serverUrl",serverUrl)
        self.chatHubConnectionDelegate = AgoraHubConnectionChatDelegate(controller: self)
        self.chatHubConnection = HubConnectionBuilder(url: URL(string: self.serverUrl)!)
            .withLogging(minLogLevel: .debug)
            .withAutoReconnect()
            .withHubConnectionDelegate(delegate: self.chatHubConnectionDelegate!)
            .build()
        self.chatHubConnection!.start()
        ReceiveMuteUserMic()
        self.CheckLeaveORJoinUSer()
    }
    
    
    //MARK: - Receive Leave or join Room
    func CheckLeaveORJoinUSer(){
        self.chatHubConnection?.on(method: "LeaveOrJoinRoom", callback: { (messageOBJ:SendMessageRoomModel?)  in
            print("LeaveOrJoinRoom DATA",messageOBJ?.isJoin ?? ""  , messageOBJ?.name ?? "")
//            if self.self.presenter?.roomID ?? 0 == messageOBJ?.toRoomId {
                
                if messageOBJ?.isJoin == true  { // if join in room
                    print("Join Room Unity")
                }else if  messageOBJ?.isJoin == false { // if user leave from roo,
                    print("Leave Room Unity")
                    
                }
                
                
//            }
        })
    }
    

    
    func ReceiveMuteUserMic(){
        self.chatHubConnection?.on(method: "receiveMuteUserMic", callback: { (Message:MessageOBJ)  in
//            if self.self.presenter?.roomID ?? 0 == Message.toRoomId ?? 0{
                print("user name",Message)
                if Message.fromUserId ?? 0 != Int(UserModel.shared.get_id()){
                    if Message.on ?? false == true{
                        print("Open my Mic")
                        self.agoraKit.muteLocalAudioStream(false)

                    }else{
                        print("Close my Mic")
                        self.agoraKit.muteLocalAudioStream(true)


                    }
                }
//            }
        })

    }
    
    
    
    //MARK: - Socket Delegate
    fileprivate func connectionDidOpen() {
        print("-----connectionDidOpen")
    }
    
    fileprivate func connectionDidFailToOpen(error: Error) {
        print("-----connectionDidOpen")
        print(error.localizedDescription )
    }
    
    fileprivate func connectionDidClose(error: Error?) {
  
    }
    
    fileprivate func connectionWillReconnect(error: Error?) {
        print("Alert already present. This is unexpected.")
        print(error?.localizedDescription )
    }
    
    fileprivate func connectionDidReconnect() {
        print("---- connectionDidReconnect")
    }
    
}



//MARK: - Agora
extension AgoraHelper{
    
    
    func initializeAgoraEngine() {
        // init AgoraRtcEngineKit
        agoraKit = AgoraRtcEngineKit.sharedEngine(withAppId:  KeyCenter.AppId ?? "" , delegate: self)
    }
    
    
    func setupAudio() {
        // make myself a broadcaster
        agoraKit.setClientRole(.broadcaster)
        // disable video module
        agoraKit.disableVideo()
        // enable audio module
        agoraKit.enableAudio()
        // Set audio route to speaker
        agoraKit.setDefaultAudioRouteToSpeakerphone(true)
        // enable volume indicator
    }
    
    
    func joinChannel() {
        agoraKit.setDefaultAudioRouteToSpeakerphone(true)
        print("Token",KeyCenter.Token , "Channel",KeyCenter.ChannelID ?? "")
        agoraKit.joinChannel(byToken:  KeyCenter.Token,
                             channelId: KeyCenter.ChannelID ?? "",
                             info: nil,
                             uid: UInt(Int(UserModel.shared.get_id()) ?? 0)) { [unowned self] (channel, uid, elapsed) -> Void in
            
            print("---Guest")
            agoraKit.setChannelProfile(.liveBroadcasting)
            let x = AgoraClientRoleOptions()
            x.audienceLatencyLevel = .lowLatency
            agoraKit.setClientRole(.audience, options: x)
            
            self.logVC?.log(type: .info, content: "did join channel")
        }
        
    }
    
}

//MARK: - functions agora
@available(iOS 13.0, *)
extension AgoraHelper: AgoraRtcEngineDelegate {
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinChannel channel: String, withUid uid: UInt, elapsed: Int) {
        //           isJoined = true
        print("Join \(channel) with uid \(uid) elapsed \(elapsed)ms")
    }
    
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didLeaveChannelWith stats: AgoraChannelStats) {
        print("Leave From Channel",stats.description)
//        agoraKit.leaveChannel(nil)
//        LiveOneToOnePresenter.HaveCall = true
//        chatHubConnection?.stop()
//        self.navigationController?.popViewController(animated: true)
    }
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinedOfUid uid: UInt, elapsed: Int) {
        print("remote user join: \(uid) \(elapsed)ms")
        
    }
    
    /// Occurs when a remote user (Communication)/host (Live Broadcast) leaves a channel.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - uid: ID of the user or host who leaves a channel or goes offline.
    ///   - reason: Reason why the user goes offline
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOfflineOfUid uid:UInt, reason:AgoraUserOfflineReason) {
        print("Leave channel",reason)
//        agoraKit.leaveChannel(nil)
//        LiveOneToOnePresenter.HaveCall = true
//        chatHubConnection?.stop()
//        self.navigationController?.popViewController(animated: true)
    }
    
    /// Occurs when a remote user’s video stream playback pauses/resumes.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - muted: YES for paused, NO for resumed.
    ///   - byUid: User ID of the remote user.
    func rtcEngine(_ engine: AgoraRtcEngineKit, didVideoMuted muted:Bool, byUid:UInt) {
        //        isRemoteVideoRender = !muted
    }
    
    /// Reports a warning during SDK runtime.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - warningCode: Warning code
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurWarning warningCode: AgoraWarningCode) {
        logVC?.log(type: .warning, content: "did occur warning, code: \(warningCode.rawValue)")
        print( "did occur warning, code: \(warningCode.rawValue)")
    }
    
    /// Reports an error during SDK runtime.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - errorCode: Error code
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurError errorCode: AgoraErrorCode) {
        logVC?.log(type: .error, content: "did occur error, code: \(errorCode.rawValue)")
        print( "did occur warning, code: \(errorCode.rawValue)")
        
    }
}






class AgoraHubConnectionChatDelegate: HubConnectionDelegate {
    weak var controller: AgoraHelper?
    
    init(controller: AgoraHelper) {
        self.controller = controller
    }
    
    func connectionDidOpen(hubConnection: HubConnection) {
        controller?.connectionDidOpen()
    }
    
    func connectionDidFailToOpen(error: Error) {
        controller?.connectionDidFailToOpen(error: error)
    }
    
    func connectionDidClose(error: Error?) {
        controller?.connectionDidClose(error: error)
    }
    
    func connectionWillReconnect(error: Error) {
        controller?.connectionWillReconnect(error: error)
    }
    
    func connectionDidReconnect() {
        controller?.connectionDidReconnect()
    }
}
