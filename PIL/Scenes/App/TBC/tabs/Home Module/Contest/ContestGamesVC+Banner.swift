////
////  ContestGamesVC+Banner.swift
////  PIL
////
////  Created by sameh mohammed on 09/02/2023.
////
//
//import Foundation
//import AppLovinSDK
//
//
//extension ContestGamesVC: MARewardedAdDelegate  , MAAdViewAdDelegate{
//    
//    
//    func createBanner(){
//        self.viweAds.isHidden = false
//        self.viweAds.backgroundColor = .clear
//        adView = MAAdView(adUnitIdentifier: unitIDBanner)
//        adView.delegate = self
//        adView.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 50)
//        adView.backgroundColor = .clear
//        viweAds.addSubview(adView)
//        adView.loadAd()
//    }
//    
//    func createRewardedAd() {
//        rewardedAd = MARewardedAd.shared(withAdUnitIdentifier: unitID)
//        rewardedAd.delegate = self
//        rewardedAd.load()
//    }
//    
//    // MARK: MAAdDelegate Protocol
//
//    func didLoad(_ ad: MAAd){
//        retryAttempt = 0
//    }
//
//    func didFailToLoadAd(forAdUnitIdentifier adUnitIdentifier: String, withError error: MAError){
//        print("ERROR LOAD ADS",error)
//    }
//
//    func didFail(toDisplay ad: MAAd, withError error: MAError){
//        // Rewarded ad failed to display. We recommend loading the next ad
//        print("ERROR LOAD ADS toDisplay",error)
////        rewardedAd.load()
//    }
//
//    func didDisplay(_ ad: MAAd) {}
//
//    func didClick(_ ad: MAAd) {}
//
//    func didHide(_ ad: MAAd){
////         Rewarded ad is hidden. Pre-load the next ad
//        rewardedAd.load()
//    }
////     MARK: - MARewardedAdDelegate Protocol
//
//    func didStartRewardedVideo(for ad: MAAd) {}
//
//    func didCompleteRewardedVideo(for ad: MAAd) {}
//
//    func didRewardUser(for ad: MAAd, with reward: MAReward){
////        ContestGamesVC.isFree = false
//    }
//    
//    func didExpand(_ ad: MAAd) {   }
//    func didCollapse(_ ad: MAAd) { }
//
//
//}
//    
//
// 
