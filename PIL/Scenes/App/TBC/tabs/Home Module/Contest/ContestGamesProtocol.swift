//
//  ContestGamesProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 12/12/2022.
//

import Foundation

protocol ContestGamesViewProtocol{
    var presenter:ContestGamesPresenterProtocol? {get set}
    func getContestInfo(name: String, image: String,lives:Int,tokens: Int)
    func setHome(lives: Int)
    
    func updateGameVersion(message:String)
    func progressGameDownload(name:String,file:String, image:String)//new design
    func canOpenFeedBack(Status:Bool)
    func canNotJoinContest(reason:String)
    func UpdateApp()
    func reloadLive()
    func showVideoAds()
//    func updateLives(with lives: Int)
//    func updateCoins(with coins: Int)
    func reloadTable()
    func setupMyUserView()
}

protocol  ContestGamesPresenterProtocol{
    var view:ContestGamesViewProtocol? {get set}
    func viewdidload()
    var users:[LeaderBoardContestDataModel]? { get set}
    func getGameID()->String
    func GetLives()
    func getContestRules()->String
    func openContest()
    func openFeedBack()
    func DownloadsFIle(isUpdate:Bool)
    func FileDownloaded(pathFile:String , destination:String)
    func getListTutorial()->[String]
    func canSendFeedBack()
    func checkVersion()
    func openContestGame(_ tag: Int)
    func CheckIsKnockout()->Bool
    func onTapLives(type:typeMorLive)
    func reloadLives()
    var type:typeMorLive? {get set}
    func PlayDirectGame()->Bool
}

protocol  ContestGamesInteractorInputProtocol{
    var presenter:ContestGamesInteractorOutputProtocol? { get set}
    func getGameDetails(gameId:String)
    func sendWalletRequest()
    func createFolderInDownloadsDirectory(name:String, files:String)
    func getChallengesGame(gameId:String)
    func canSendFeedback(model:FeedBackGameRequstModel)
    func getSkills(playerID:String)
    func ValidateContest(contestID:String)
    func checkPlayerAds()
    func getLives()
}


protocol  ContestGamesInteractorOutputProtocol{
    func getGameData(data:GameDetailsDataModel)
    func getWalletInfo(obj:WalletWorkerData)

    
    func FileDownloaded(pathFile:String , destination:String)
    func progressFileDownload(progress:Double)
    func progressGameDownload(name:String,file:String)//new design
    func FilesIFExist(path:String)
    func getChallenges(model:[ChallengesGamesData])
    func checkCanSendFeedback(status:Bool)
    func getSkillsPlayer(skills:Int)
    func successValidateContest()
    func canNotJoinContest(reason:String)
    func checkShowPlayerAds(status:Bool)
    func didFetchLives(_ data: WalletWorkerData)
}

protocol  ContestGamesRouterProtocol{
    func openGame(gameID:String)
    func openFeedBack(gameID:Int)
    func toLives(type:typeMorLive)
    func openBattlesDialog(_ gameDate: (String, String),_ battles: [ChallengesGamesData],_ delegate: BattlesDialogProtocol)
}
