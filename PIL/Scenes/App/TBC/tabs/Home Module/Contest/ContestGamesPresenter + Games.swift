//
//  ContestGamesPresenter + Games.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 29/07/2024.
//

import Foundation
import CoreData
import Zip

extension ContestGamesPresenter: UnityFrameworkDelegate{
    /// prossess game
    /// when press play button
    /// step 1 : checl in local this game is downloaded before or not ( if exist oepn or update version and open)
    ///              before update veriosn we must remove old version
    /// step 2: if assest not exist download assets and show view with progress bar
    /// step 3:  have to option  1- after download asstes uncompress  file and  open game
    ///                  2- if file is  exist open game
    /// step 4: save game in local
    ///
    
    
    ///step 1
    func checkVersion(){
        for i in self.localGamesArray{
            if i.sceneName ?? "" == self.gameDetails?.game?.sceneName ?? "" {
                if i.version !=  self.gameDetails?.game?.version ?? "" {
                    print("Alert  update version to \(self.gameDetails?.game?.version ?? "") - \(i.sceneName ?? "")")
                    self.view?.updateGameVersion(message: "New version".localized+"\(i.version ?? "")")
                    return
                }else{
                    print("exist version is \(self.gameDetails?.game?.version ?? "") - \(i.sceneName ?? "")")
                    self.DownloadsFIle(isUpdate: false)
                    return
                }
            }
        }
        self.DownloadsFIle(isUpdate: false)
    }
    
    ///step 1  remove zip file after download
    func removeFile(filePath:URL){
        let fileManager = FileManager.default
        do{
            try  fileManager.removeItem(at: filePath)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    /// remove old version and download new one
    func removeOldVersion(){
        //remove from core data (Local)
        let index = localGamesArray.firstIndex{ $0.sceneName == self.gameDetails?.game?.sceneName ?? "" } ?? 0
        let managedContext = (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext
        let note = localGames[index]
        managedContext.delete(note)
        do {
            try managedContext.save()
        } catch let error as NSError {
            print("Error While Deleting Note: \(error.userInfo)")
        }
        
        //Delete Game Folder
        let sceneName = localGamesArray[index].sceneName ?? ""
        let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let path = "\(documentsUrl)\(sceneName)".replacingOccurrences(of: "file://",with: "")
        
        let fileManager = FileManager()
        do{
            try  fileManager.removeItem(atPath: path)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    
    /// step 2
    func DownloadsFIle(isUpdate:Bool){
        //        self.view?.startLoading()
        self.isUpdate = isUpdate
        if isUpdate == true{
            self.removeOldVersion()
        }
        print("ASSEST IS",gameDetails?.game?.assetsbundle ?? "")
        self.interactor?.createFolderInDownloadsDirectory(name: self.gameDetails?.game?.sceneName ?? "" , files: gameDetails?.game?.assetsbundle ?? "" )
    }
    
    //MARK: - download zip file if not exist folder step 2
    func FileDownloaded(pathFile: String, destination: String) {
        let sourceURL: URL
        if pathFile.hasPrefix("file://") {
            sourceURL = URL(string: pathFile)!.standardizedFileURL
        } else {
            sourceURL = URL(fileURLWithPath: pathFile).standardizedFileURL
        }
        
        let destinationURL = URL(fileURLWithPath: destination).standardizedFileURL
        
        print("File Path is", sourceURL, destinationURL)
        
        guard FileManager.default.fileExists(atPath: sourceURL.path) else {
            print("❌ ملف الـ ZIP مش موجود")
            return
        }
        
        if !FileManager.default.fileExists(atPath: destinationURL.path) {
            try? FileManager.default.createDirectory(at: destinationURL, withIntermediateDirectories: true)
        }
        
        do {
            try Zip.unzipFile(sourceURL,
                              destination: destinationURL,
                              overwrite: true,
                              password: nil) { progress in
                print("--->", progress)
                if progress == 1 {
                    self.removeFile(filePath: sourceURL)
                    
                    self.save(name: self.gameDetails?.game?.gameName ?? "",
                              sceneName: self.gameDetails?.game?.sceneName ?? "",
                              image: self.gameDetails?.game?.icons ?? "",
                              version: self.gameDetails?.game?.version ?? "",
                              filePath: destinationURL.appendingPathComponent(self.gameDetails?.game?.sceneName ?? "").path,
                              gameID: self.gameID)
                    
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        self.OpenGame(path: destinationURL.appendingPathComponent(self.gameDetails?.game?.sceneName ?? "").path)
                    }
                }
            }
        } catch {
            print("❌ فشل فك الضغط: \(error.localizedDescription)")
        }
    }
    
    func progressFileDownload(progress: Double) {
        //        self.view?.changeProgress(progress: progress)
    }
    
    // if file is Exist
    func FilesIFExist(path:String){
        print("File Path is",path)
        DispatchQueue.main.async {
            self.OpenGame(path:"\(path)")
        }
    }
    

    
    

    
    //MARK:- Open Game SDK
    /// step 3
    func OpenGame(path:String){
        
        (UIApplication.shared.delegate as! AppDelegate).orientationMask = .all
        
        var request = [
            "GameId": gameID,
            "GameName": self.gameDetails?.game?.sceneName ?? "",
            "SceneName": self.gameDetails?.game?.sceneName ?? "",
            "EntryFee": (freeGame.entryfeeBouns ?? 0)+(freeGame.entryfeeDeposit ?? 0),// total deposit and bouns
            "DepositEntryFee":freeGame.entryfeeDeposit ?? 0 ,// deposit
            "BonusEntryFee": freeGame.entryfeeBouns ?? 0 ,//bouns
            "MaximimAttempt": self.freeGame.maximumAttempt ?? 0,
            "MaximumPlayer":freeGame.maximumPlayers ?? 0,
            "MaxMatchMakingRetries": freeGame.knockout_tries ?? 0,
            "MaxPongDelay": 20,
            "MaxPauseDuration": 30,
            "NumberOfWinners": freeGame.noOfWinners ?? 0,
            "PlayerSkill":self.skillsPlayer,
            "PlayerSkillRange":self.freeGame.skillRange ?? 0,
            "Landscape": getLandscape(),
            "PrizeTable": getDicPrize() ,
            "AuthToken": "Bearer \(UserModel.shared.get_token())",
            "ConnectionRetryTimeout": 30,
            "IsInDebugMode": false,
            "PingInterval": 10,
            "AppVersionCode": "1",//Build number APP
            "AppVersionName": "1.0.20",//version number APP
            "Profile": [
                "id": UserModel.shared.get_id(),
                "phone": UserModel.shared.get_phone(),
                "name": UserModel.shared.get_username(),
                "username": UserModel.shared.get_username(),
                "image": UserModel.shared.get_image(),
                "token":"Bearer \(UserModel.shared.get_token())",
                "email":UserModel.shared.get_email(),
                "bio":UserModel.shared.get_bio(),
                "birthdate":UserModel.shared.get_birthdate(),
                "cashWin":UserModel.shared.get_cashWin(),
                "followers":UserModel.shared.get_followers(),
                "following":UserModel.shared.get_following(),
                "gender":UserModel.shared.get_gender(),
                "helpdeskId":UserModel.shared.get_HelpdeskId(),
                "level": Int(UserModel.shared.get_level()) ?? 0 ,
                "operator":UserModel.shared.getOperator(),
                "type":UserModel.shared.get_loginAsGuestTypeGame(),
            ] as [String : Any],
            "Host": URls().SmartFoxHost,
            "Zone": "PIL",
            "LobbyId": "",
            "ContestId": "\(freeGame.id ?? 0)",
            "ContestName": freeGame.contestType ?? "",
            "ContestType": freeGame.contestType ?? "",
            "EndDateTime":EndTimeContest(),
            "StartDateTime":StartTimeContest(),
            "IsTournament": isTournament(),
            "EnableLog": true,
            "DeveloperConsoleURL": URls().BASE_URL_INDIA,
            "UserURL": URls().BASE_URL,
            "AssetBundleURL": "\(path)",
            "Language": UserModel.shared.getLanguage(),
            "GameSoundOn":UserModel.shared.getSound(),
            "IsKnockout": false
        ] as [String : Any]
        
        let files = try? FileManager.default.contentsOfDirectory(atPath: path)
        print("Contents of game folder:", files ?? [])
        
        request["game_type"] = self.gameDetails?.contest?.game_type ?? ""
        request["player_number"] = self.gameDetails?.contest?.player_number ?? ""
        
        let jsonData = try? JSONSerialization.data(withJSONObject: request, options: [])
        let jsonString = String(data: jsonData!, encoding: .utf8)
        
        Unity.shared.show()
        Unity.shared.sendMessage("PILNativeInterface","SetGameConfig", "\( jsonString ?? "")")
        Unity.shared.delegate = self
        Indicator.shared.hideProgressView()
        print("Game Config " , request)
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        
        GoogleAnalyticsHelper.shared.start_game(start_game_time: stringDate,
                                                game_name: self.gameDetails?.game?.sceneName ?? "",
                                                event_id: "",
                                                game_entree_fee: "0",
                                                game_event_type: self.freeGame.type ?? "",
                                                game_event_name: "")
        
        GoogleAnalyticsHelper.shared.trackJoinPracticeEvent(gameName: self.gameDetails?.game?.gameName ?? "")
        
    }
    
    /// reload leader board after finsh contest
    func unityDidUnload() {
        interactor?.getGameDetails(gameId: self.gameID)
        interactor?.checkPlayerAds()
        //        (UIApplication.shared.delegate as! AppDelegate).orientationMask = .portrait
    }
    
    /// get  prize  from tournament  game info and convert it to dictionary
    func getDicPrize()->[[String:Any]]{
        var DicPrize = [[String:Any]]()
        for i in self.freeGame.prizesDistribution ?? []{
            var dic =  [String:Any]()
            dic["prize"] = i.prize ?? 0
            dic["currency"] = i.currency ?? ""
            dic["rank"] = i.rank ?? 0
            DicPrize.append(dic)
        }
        return DicPrize
    }
    
    /// get  iLandscape from game info
    func getLandscape()->Bool{
        var Landscape = false
        if self.gameDetails?.game?.gameOrientation ?? "" == "LandScape"{
            Landscape = true
        }else{
            Landscape = false
        }
        return Landscape
    }
    
    func isTournament()->Bool{
        if freeGame.type ?? "" == "tournament" {
            return true
        }
        return false
    }
    
    // end time
    
    /// get  start time  from tournament  game info nd convert it to time stamp
    func StartTimeContest()->Int{
        let endTimeBattle = Int((freeGame.startDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
    }
    /// get  end time  from tournament  game info and convert it to time stamp
    func EndTimeContest()->Int{
        let endTimeBattle = Int((freeGame.endDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
    }
    
    
    func progressGameDownload(name: String, file: String) {
        self.view?.progressGameDownload(name: name, file: file, image: self.gameDetails?.game?.icons ?? "" )
    }
}

extension ContestGamesPresenter{
    
    //MARK: - save game in local
    
    func save(name: String , sceneName:String , image:String , version:String , filePath:String , gameID:String) {
        print("Game ID Local", gameID)
        guard let appDelegate =
                UIApplication.shared.delegate as? AppDelegate else {
            return
        }
        
        // 1
        let managedContext =   appDelegate.persistentContainer.viewContext
        
        // 2
        let entity =  NSEntityDescription.entity(forEntityName: "Games",
                                                 in: managedContext)!
        
        let person = NSManagedObject(entity: entity,
                                     insertInto: managedContext)
        
        // 3
        person.setValue(name, forKeyPath: "name")
        person.setValue(sceneName, forKeyPath: "sceneName")
        person.setValue(image, forKeyPath: "image")
        person.setValue(version, forKeyPath: "version")
        person.setValue(filePath, forKey: "filePath")
        person.setValue(gameID, forKey: "gameID")
        // 4
        do {
            try managedContext.save()
            print("Game save successfully in local storage")
        } catch let error as NSError {
            print("Could not save. \(error), \(error.userInfo)")
        }
    }
    
    
    func getLocalGames(){
        self.localGamesArray.removeAll()
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {  return }
        let managedContext = appDelegate.persistentContainer.viewContext
        let fetchRequest =    NSFetchRequest<NSManagedObject>(entityName: "Games")
        
        do {
            localGames = try managedContext.fetch(fetchRequest)
            
            for i in localGames{
                let gameName = i.value(forKey: "name") as? String
                let sceneName = i.value(forKey: "sceneName") as? String
                let image = i.value(forKey: "image") as? String
                let version = i.value(forKey: "version") as? String
                let filePath = i.value(forKey: "filePath") as? String
                let gameID = i.value(forKey: "gameID") as? String
                var size = "10"
                let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let path = "\(documentsUrl)\(sceneName ?? "")".replacingOccurrences(of: "file://",with: "")
                
                let url = NSURL.fileURL(withPath: path)
                print("the url = \(url)")
                do {
                    
                    if let sizeOnDisk = try url.sizeOnDisk() {
                        size = sizeOnDisk
                        print("Size:", sizeOnDisk) // Size: 3.15 GB on disk
                    }
                } catch {
                    print(error)
                }
                
                let game = LocalGames(name: gameName, sceneName: sceneName, image: image, version: version , filePath:filePath,gameID: gameID,sizeGame: size)
                localGamesArray.append(game)
            }
            //            checkVersion()
            
        } catch let error as NSError {
            print("Could not fetch. \(error), \(error.userInfo)")
        }
    }
    
  
 
}
