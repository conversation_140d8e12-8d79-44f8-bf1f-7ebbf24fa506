//
//  ContestGamesVC.swift
//  PIL
//
//  Created by sameh mohammed on 12/12/2022.
//

import UIKit
import Alamofire
import FirebaseCore
import FirebaseDynamicLinks
//import AppLovinSDK
import GoogleMobileAds


class ContestGamesVC: GameAudioHelper , ContestGamesViewProtocol , ErrorProtocol{
    
    //MARK: - outlet
    @IBOutlet weak var rankLabel: UILabel!
    @IBOutlet weak var myPointsLabrl: UILabel!
    @IBOutlet weak var myUserNameLabel: UILabel!
    @IBOutlet weak var myUserImg: UIImageView!
    @IBOutlet weak var myUserView: UIView!
    @IBOutlet weak var imageGame: UIImageView!
    @IBOutlet weak var viweAds: UIView!
    @IBOutlet weak var leaderBoardTableView: UICollectionView!
    @IBOutlet weak var countPlayLiveLable: UILabel!
    @IBOutlet weak var NoLeaderBoardLable: UILabel!
    @IBOutlet weak var coinsLable: UILabel!
    @IBOutlet weak var livesLable: UILabel!
    @IBOutlet weak var practicesStaclView: UIStackView!
    @IBOutlet weak var practiceFeesStack: UIStackView!
    @IBOutlet weak var playView: UIView!
    @IBOutlet weak var stackActions: UIStackView!
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var heightTableView: NSLayoutConstraint!
    @IBOutlet weak var containerView: UIView!
    @IBOutlet weak var buttonsStack: UIStackView!
    
    //Constrains
    @IBOutlet weak var headerHeight: NSLayoutConstraint!
    @IBOutlet weak var imageHeight: NSLayoutConstraint!
    @IBOutlet weak var scrollView: UIScrollView!
    


    //MARK: - variable
    var presenter: ContestGamesPresenterProtocol?
//    var adView: MAAdView!
//    var rewardedAd: MARewardedAd!
    private var interstitial: GADInterstitialAd?
    var retryAttempt = 0.0
    static var isFree = false
    var imageURL = String()
    var nameGame = String()
    let refreshControl = UIRefreshControl()

    //MARK: - view didload
    override func viewDidLoad() {
        super.viewDidLoad()
        /// UI Navigation
        setUpNavigation()
        scrollView.delegate = self
        presenter?.viewdidload() /// Call API's
        setXIB() // UI Cell
        /// call reqquest wallet after finish game to update data
        NotificationCenter.default.addObserver(self, selector: #selector(ContestGamesVC.ReloadLives), name: NSNotification.Name("ReloadLives"), object: nil)
        
        practiceFeesStack.isHidden = UserModel.shared.get_userSubscription()?.subscribed ?? false
//        
//        containerView.transform = .init(scaleX: 0, y: 0)
//        UIView.animate(withDuration: 2, delay: 3, options: .curveEaseIn) {
//            self.containerView.transform = CGAffineTransformIdentity
//        }

        
        /// UI Ipad
        if UIScreen.main.bounds.width > 500 {
            headerHeight.constant = 400
            imageHeight.constant = 250
        }
        // pull refresh
        refreshControl.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        scrollView.addSubview(refreshControl)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        self.createBanner()
//        self.createRewardedAd()

        /// check the user was subscribed or not
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            practicesStaclView.isHidden = true
        }else{
            practicesStaclView.isHidden = false
        }
//        self.imageGame.isHidden = true
//        self.buttonsStack.isHidden = true
//        DispatchQueue.main.asyncAfter(deadline: .now()+1, execute: {
//            self.imageGame.performBounceAnimation()
//            self.buttonsStack.performBounceAnimation()
//        })
    }
    

    //MARK: - function
    
    // pull refresh
    @objc func refreshData() {
        print("Refreshing data...")
        self.presenter?.viewdidload()
           DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
               self.refreshControl.endRefreshing()
           }
       }
    
    // setup navigation
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Contest".localized)
//        self.navigationView.customPresentation = true
        myUserView.isHidden = true
    }
    
    /// reload wallet data
    func reloadLives() {
        presenter?.reloadLives()
    }
    
    /// UI Cell
    func setXIB(){
        leaderBoardTableView.backgroundColor = .clear
        self.leaderBoardTableView.register(UINib(nibName: "KnockoutLeaderBoardCell", bundle: nil), forCellWithReuseIdentifier: "KnockoutLeaderBoardCell")
        self.leaderBoardTableView.register(UINib(nibName: "HeadLeaderBoardCell", bundle: nil), forCellWithReuseIdentifier: "HeadLeaderBoardCell")
    }
    
    @objc func ReloadLives(notification: Notification) {
        reloadLive()
    }
    
    //MARK: - actions
    @IBAction func backBTN(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    /// navigate to feedback
    @IBAction func feedbackBTN(_ sender: Any) {
        if UserModel.shared.get_loginAsGuest() == true{
            let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
            self.present(vc, animated: false, completion: nil)
        }else{
            self.presenter?.canSendFeedBack()
        }
    }
    
    ///reload to totoutia
    @IBAction func totoutialBTN(_ sender: Any) {
        let tutorial = self.storyboard?.instantiateViewController(withIdentifier: "TutorialVC") as! TutorialVC
        tutorial.list = presenter?.getListTutorial() ?? []
        self.navigationController?.pushViewController(tutorial, animated: true)
    }
    
    /// share contest deeplinl
    @IBAction func shareBTN(_ sender: Any) {
        self.shareDeepLink(sender: self.view)
        
    }
    
    func shareDeepLink(sender: UIView){
        let sharePicker = SharePickerVC.loadFromNib()
        sharePicker.shareType = .games(id: Int((presenter?.getGameID())!)!)
        sharePicker.modalPresentationStyle = .overCurrentContext
        UIApplication.topViewController?.tabBarController?.present(sharePicker, animated: true)
    }
    
    
//        @IBAction func playBTN(_ sender: Any) {
//            ContestGamesVC.isFree = true
//            presenter?.checkVersion()
//        }
    
    /// opne popup lives
    @IBAction func openLives(_ sender: UIButton) {
        presenter?.onTapLives(type:.live)
    }
    
    /// opne popup lives
    @IBAction func piTokenBTN(_ sender: Any) {
        presenter?.onTapLives(type:.token)
        
    }
    
  
    /// play geme
    @IBAction func playDirectBTN(_ sender: Any) {
//        ContestGamesVC.isFree = true
        self.presenter?.checkVersion() // check version and open game
    }
    
    
    /// play game
    @IBAction func playEnteryBTN(_ sender: UIButton) {
        //        if UserModel.shared.get_loginAsGuest() == true{
        //            let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
        //            self.present(vc, animated: false, completion: nil)
        //        }else{
        presenter?.openContestGame(sender.tag)
        //        }
        
    }
    

    //MARK: - error
    func featching(error: String) {
        print("Error",error)
    }
    
    func canNotJoinContest(reason:String) {
        self.showAlert(withTitle: false, msg: reason) {}
    }
    

    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            UserModel.shared.logOut()
        })
    }
    
    /*
     If play game and have rank in game show view in footer screen have my rank
     and my ranl it will be hidden if the total player less than 4
     */
    func setupMyUserView() {
        var myId = presenter?.users?.first(where: {$0.id == Int(UserModel.shared.get_id())})
        if myId != nil {
            let rank = presenter?.users?.firstIndex(where: {$0.id == Int(UserModel.shared.get_id())})
            myId?.rank = rank! + 1
            if myId?.rank ?? 0 < 4  {
                myUserView.isHidden = true
            }else{
                rankLabel.text = "\(myId?.rank ?? 0)"
                myUserView.isHidden = false
                myUserNameLabel.text = myId?.name
                myUserImg.sd_setImage(with: .init(string: myId?.image ?? ""))
                myPointsLabrl.text = "\(myId?.score ?? 0)"
            }
        }else{
            myUserView.isHidden = true
        }
    }
    
    
    /// Update App Alert
    func UpdateApp() {
        let alert = UIAlertController(title: "Notice".localized, message: "App Not Updated".localized, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Open App Store".localized, style: .default, handler: { action in
            if let url = URL(string: "itms-apps://itunes.apple.com/app/id1555945981"),
                UIApplication.shared.canOpenURL(url)
            {
                if #available(iOS 10.0, *) {
                    UIApplication.shared.open(url, options: [:], completionHandler: nil)
                } else {
                    UIApplication.shared.openURL(url)
                }
            }
        }))
        alert.addAction(UIAlertAction(title: "Cancel".localized, style: .cancel, handler: nil))
        present(alert, animated: true, completion: nil)
    }
    
    
    /// Get Contest Info
    func getContestInfo(name: String, image: String,lives:Int,tokens: Int) {
        setupMyUserView()
        self.imageGame.sd_setImage(with: URL(string: image))
        self.navigationView.setNavigationTitle(title: name)
        self.countPlayLiveLable.text = "\(lives)"
        if presenter?.users?.count == 0 {
            self.NoLeaderBoardLable.isHidden = false
        }else{
            self.NoLeaderBoardLable.isHidden = true
        }
        
        self.imageURL = image
        self.nameGame = name
//        showTutorial()
        self.livesLable.text = "\(lives)"
        self.coinsLable.text = "\(tokens)"
        
        // show play button only in case game is ( Ludo - Chess )
        if self.presenter?.PlayDirectGame() == true{
            self.playView.isHidden = false
            self.stackActions.isHidden = true
        }
    }
    
    /// reload table
    func reloadTable(){
        self.leaderBoardTableView.reloadData()
        leaderBoardTableView.isHidden = UserModel.shared.get_loginAsGuest()
    }
    
    /// show tutorial in screen to guide the screen future
    func showTutorial(){
        if UserModel.shared.get_tutorialContest() == false{
            Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { timer in
                let st = UIStoryboard.init(name: "TutorialStart", bundle: nil).instantiateViewController(withIdentifier: "TutorialContestVC") as! TutorialContestVC
                st.gameImage = self.imageURL
                st.gameName = self.nameGame
                self.present(st, animated: false)
            }
        }
    }
    
    
    func setHome(lives: Int) {
       
    }
    
    /// request lives
    func reloadLive(){
        self.presenter?.GetLives()
    }
    
    /// if i download game before and the game need to update
    func updateGameVersion(message: String) {
        self.showAlertUpdateGame(title: "Alert".localized, msg: message) {
            print("Update")
            self.presenter?.DownloadsFIle(isUpdate: true)
        }
    }
    
    /// progress download game
    func progressGameDownload(name: String, file: String, image: String) {
        let progress = UIStoryboard.init(name: "DownloadGame", bundle: nil).instantiateViewController(withIdentifier: "DownloadGameVC") as! DownloadGameVC
        progress.nameGame = name
        progress.imageURL = image
        progress.fileURL = file
        progress.action = self
        self.present(progress, animated: false, completion: nil)
    }
    
    /// navigate to feedback
    func canOpenFeedBack(Status: Bool) {
        if Status == true{
            presenter?.openFeedBack()
        }else{
            let vc = self.storyboard?.instantiateViewController(withIdentifier: "PopUpAlertVC") as! PopUpAlertVC
            vc.type = .feedback
            self.present(vc, animated: false)
        }
    }
    
    /// show video googela ads when finish game if i am not subscribe
    func showVideoAds() {
//        if rewardedAd.isReady{
//            rewardedAd.show()
//        }
        let request = GADRequest()
        
        GADInterstitialAd.load(withAdUnitID: "ca-app-pub-3762158595762039/3426448039",
                               request: request,
                               completionHandler: { [self] ad, error in
            
            if let error = error {
                print("Failed to load interstitial ad with error: \(error.localizedDescription)")
                return
            }
            interstitial = ad
            interstitial?.fullScreenContentDelegate = self
            
        })
        Timer.scheduledTimer(withTimeInterval: 1, repeats: false) { timer in
            if self.interstitial != nil {
                self.interstitial?.present(fromRootViewController: self)
            } else {
                print("Ad wasn't ready")
            }
        }
    }
    
}

/// configuratein google ads
extension ContestGamesVC:GADFullScreenContentDelegate{
    /// Tells the delegate that the ad failed to present full screen content.
    func ad(_ ad: GADFullScreenPresentingAd, didFailToPresentFullScreenContentWithError error: Error) {
        print("Ad did fail to present full screen content.")
    }
    /// Tells the delegate that the ad will present full screen content.
    
    func adWillPresentFullScreenContent(_ ad: GADFullScreenPresentingAd) {
        print("Ad will present full screen content.")
    }
    /// Tells the delegate that the ad dismissed full screen content.
    func adDidDismissFullScreenContent(_ ad: GADFullScreenPresentingAd) {
        print("Ad did dismiss full screen content.")
        
    }
    
}


/// path file assests download
extension ContestGamesVC:DownloadGamFinished{
    func DownloadGamFinished(Path: String) {
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "--->",Path)
        self.presenter?.FileDownloaded(pathFile: Path, destination: newFolderURL.path)
    }
    
}


/// when scroll screen if my rank in leaderboard show hidden my rank viwe
extension ContestGamesVC:  UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let currentScrollPosition = scrollView.contentOffset.y
        print("Current scroll position: \(currentScrollPosition)")
        
        var myId = presenter?.users?.first(where: {$0.id == Int(UserModel.shared.get_id())})
        let myRank = Int(self.rankLabel.text ?? "") ?? 0//myId?.rank ?? 0
        let height = myRank*80+400 - Int(UIScreen.main.bounds.height)
        print("My rank position", myRank  , height ,"111",currentScrollPosition)
        if height < Int(currentScrollPosition)  {
            self.myUserView.isHidden = true
        }else{
            self.myUserView.isHidden = false
        }
        
    }
}
