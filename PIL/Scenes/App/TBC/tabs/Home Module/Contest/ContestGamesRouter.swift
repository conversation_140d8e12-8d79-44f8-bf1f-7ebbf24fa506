//
//  ContestGamesRouter.swift
//  PIL
//
//  Created by sameh mohammed on 12/12/2022.
//

import Foundation
import UIKit

class ContestGamesRouter:ContestGamesRouterProtocol,ReloadGetLivesAfterWatchAdsProtocol{
    
    var VC: ContestGamesViewProtocol?
    
    static func createModule(gameID:String)-> UIViewController{
        
        let view = SetStoryBoard.controller(controller: Helper(Story: .ContestST, VC: .ContestGamesVC)) as! ContestGamesVC
        let router = ContestGamesRouter()
        let interactor = ContestGamesInteractor()
        let gameWorker = GameDetailsWorker()
        let walletworker = WalletWorker()
        let workerContest = ChallengesGameWorker()
        let gamesWorker =  GamesWorker()
        let presenter = ContestGamesPresenter(view: view,
                                              interactor: interactor,
                                              router: router,
                                              error: view)
        view.presenter = presenter
        presenter.view = view
        presenter.gameID = gameID
        presenter.interactor = interactor
        presenter.router = router
        interactor.presenter = presenter
        interactor.worker = gameWorker
        interactor.walletWorker = walletworker
        interactor.workerContest = workerContest
        interactor.gamesWorker = gamesWorker
        router.VC = view

        return view
    }
    
    func openGame(gameID: String) {
        let challenge = ContestChallengesRouter.createModule(gameID: gameID) as! ContestChallengesVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(challenge, animated: true)
        }
    }
    
    func openFeedBack(gameID:Int){
        let feedback = FeedbackRouter.createModule(gameID: gameID) as! FeedbackVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(feedback, animated: true)
        }
    }
    
    func toLives(type:typeMorLive) {
        let lives = YouWantMoreLivesRouter.createModule(type: type) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = self.VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
        VC?.reloadLive()
    }
    
    func openBattlesDialog(_ gameDate: (String, String), _ battles: [ChallengesGamesData], _ delegate: BattlesDialogProtocol) {
        let battlesDialog = BattlesDialog.loadFromNib()
        battlesDialog.gameDate = gameDate
        battlesDialog.battles = battles
        battlesDialog.delegate = delegate
        battlesDialog.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            vc.navigationController?.present(battlesDialog, animated: false)
        }
    }
}
