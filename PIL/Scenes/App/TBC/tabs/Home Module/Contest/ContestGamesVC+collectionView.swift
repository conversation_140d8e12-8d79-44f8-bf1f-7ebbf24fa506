//
//  ContestGamesVC+collectionView.swift
//  PIL
//
//  Created by sameh mohammed on 07/05/2023.
//

import Foundation
import UIKit

extension ContestGamesVC : UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        /*
         have two type of leaderboard
         1- rank
         2 - Knockout
         */
        let cell = self.leaderBoardTableView.dequeueReusableCell(withReuseIdentifier: "HeadLeaderBoardCell", for: indexPath) as! HeadLeaderBoardCell
        cell.users = presenter?.users ?? []
        cell.isKnockout = presenter?.CheckIsKnockout() ?? false
        cell.loadTopThree()
        cell.backgroundColor = .clear
        if  presenter?.users?.count ?? 0 > 3{
            let users =  (presenter?.users?.count ?? 0)-3
            /// calcuc new height tableview
            self.heightTableView.constant = CGFloat(users*80)+CGFloat(200)
            cell.tableViewHeight.constant =  CGFloat(users*80)
            print("My rank position 2 ", cell.tableViewHeight.constant)
        }else{
            let users =  (presenter?.users?.count ?? 0)
            /// calcuc new height tableview
            self.heightTableView.constant = CGFloat(users*80)
            cell.tableViewHeight.constant =  CGFloat(users*80)
        }
        self.view.layoutIfNeeded()
        cell.memberTableView.isScrollEnabled = false
        DispatchQueue.main.asyncAfter(deadline: .now()+0.5, execute: {
            if self.presenter?.CheckIsKnockout() ?? false == true{
                cell.registerMemberKnockoutLeaderboardCell()
            }else{
                cell.registerMemberLeaderboardCell()
            }
        })
        return cell

    }
    
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: UIScreen.main.bounds.width, height: self.leaderBoardTableView.bounds.height)
    }
    
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
            // This method is called just before the cell is displayed
            print("Will display cell at row: \(indexPath.row)")
        }
    
    
}

