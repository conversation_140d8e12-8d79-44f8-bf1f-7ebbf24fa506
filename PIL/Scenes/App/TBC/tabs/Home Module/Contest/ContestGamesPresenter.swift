//
//  ContestGamesPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 12/12/2022.
//

import Foundation
import CoreData
import Zip
class ContestGamesPresenter: ContestGamesPresenterProtocol, ContestGamesInteractorOutputProtocol{

    var view:ContestGamesViewProtocol?
    var interactor:ContestGamesInteractorInputProtocol?
    var router:ContestGamesRouterProtocol?
    var error: ErrorProtocol?
    var gameID = String()
    var livesModel: WalletWorkerData?
    var localGamesArray = [LocalGames]()
    var localGames = [NSManagedObject]()
    var isUpdate = false
    var freeGame = ChallengesGamesData()
    var gameDetails:GameDetailsDataModel?
    var skillsPlayer = 0
    var users:[LeaderBoardContestDataModel]?
    var type:typeMorLive?
    private var purchasedProductID: String{
        return (UserModel.shared.get_userSubscription()?.subscription?.first?.productId ) ?? ""
    }
    
    init(view:ContestGamesViewProtocol,
         interactor:ContestGamesInteractorInputProtocol,
         router:ContestGamesRouterProtocol,
         error: ErrorProtocol) {
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    
    func viewdidload() {
        if let GameInfo = LocalStorageManger.GetAllGameDetails().first(where: {$0.game?.id == Int(gameID)}){
            self.view?.getContestInfo(name: GameInfo.game?.gameName ?? "" , image: GameInfo.game?.icons ?? "",lives: GameInfo.contest?.entryfeeDeposit ?? 0, tokens: GameInfo.battle?.entryfeeDeposit ?? 0)
        }
        
        if Connectivity.isConnectedToInternet == false {
            guard let gameDetails = LocalStorageManger.GetAllGameDetails().first(where: {$0.game?.id == Int(gameID)}) else {return}
            self.gameDetails = gameDetails
            self.users = gameDetails.leaderboard ?? []
            print("dqwiodjqwoid ", gameDetails.game?.icons)
            self.view?.getContestInfo(name: gameDetails.game?.gameName ?? "" , image: gameDetails.game?.icons ?? "",lives: gameDetails.contest?.entryfeeDeposit ?? 0, tokens: gameDetails.battle?.entryfeeDeposit ?? 0)
            self.view?.reloadTable()
        }else{
            interactor?.getGameDetails(gameId: self.gameID)
        }
//        interactor?.getGameDetails(gameId: self.gameID)
        self.GetLives()
        self.interactor?.getChallengesGame(gameId: self.gameID)
        self.interactor?.getSkills(playerID: UserModel.shared.get_id())
        getLocalGames()
    }
    
    // check if user login as a guest and play more one time show ads
    func checkShowPlayerAds(status: Bool) {
        print("Show Ads To Users", status , purchasedProductID )
        if status == true && purchasedProductID == ""{
            self.view?.showVideoAds()
        }
    }
    
    //MARK: - chek Validate Contest
    //check the user have money to open game or not
    func openContestGame(_ tag: Int){
        switch tag{
        case 0:
            if let contest = gameDetails?.contest{
                self.type = .live
                self.freeGame = contest
                self.interactor?.ValidateContest(contestID: "\(contest.id ?? 0)")
                ContestGamesVC.isFree = true
            }
        case 1:
            if let contest = gameDetails?.battle{
                self.type = .live
                self.freeGame = contest
                self.interactor?.ValidateContest(contestID: "\(contest.id ?? 0)")
            }
//            router?.openBattlesDialog((gameDetails?.game?.gameName ?? "", gameDetails?.game?.icons ?? ""), gameDetails?.battles ?? [], self)
        default: break
        }
    }
    
    func onTapLives(type:typeMorLive) {
        router?.toLives(type: type)
    }
    
    func reloadLives(){
        
    }
    
    func didFetchLives(_ data: WalletWorkerData) {
        UserModel.shared.setCountLives(count: data.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: data.playerToken ?? 0)
//        view?.updateLives(with: data.lives ?? 0)
//        view?.updateCoins(with: data.coins ?? 0)
    }
    
    func successValidateContest() {
        self.checkVersion()
//        self.DownloadsFIle(isUpdate: false)
    }
    
    func DontHaveLives(status:String){
        print("Status")
    }
    
    func canNotJoinContest(reason:String){
        if reason == JoinContestReasons.BALANCE.rawValue {
            router?.toLives(type: self.type ?? .token)
        }else if reason == JoinContestReasons.MAXIMUM_ATTEMPT.rawValue{
            self.view?.canNotJoinContest(reason: "You've exceeded the maximum number of chances, good luck next time".localized)
        }else if reason  == JoinContestReasons.ENDED.rawValue{
            self.view?.canNotJoinContest(reason: "The tournament is ended".localized)
        }else if reason == JoinContestReasons.UPDATE_APP.rawValue{
            self.view?.UpdateApp()
            
//            let alert = UIAlertController(title: "Notice", message: "App Not Updated", preferredStyle: .alert)
//            alert.addAction(UIAlertAction(title: "Open App Store", style: .default, handler: { action in
//                if let url = URL(string: "itms-apps://itunes.apple.com/app/id1555945981"),
//                    UIApplication.shared.canOpenURL(url)
//                {
//                    if #available(iOS 10.0, *) {
//                        UIApplication.shared.open(url, options: [:], completionHandler: nil)
//                    } else {
//                        UIApplication.shared.openURL(url)
//                    }
//                }
//            }))
//            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))
//            present(alert, animated: true, completion: nil)
        }
    }
    
    func getSkillsPlayer(skills: Int) {
        self.skillsPlayer = skills
        print("Player skills is",skills)
    }
    
    func getGameID()->String{
        return self.gameID
    }
    func getGameData(data: GameDetailsDataModel) {
        LocalStorageManger.SaveGameDetails(game: data)
        self.gameDetails = data
        self.users = gameDetails?.leaderboard ?? []
        self.view?.getContestInfo(name: data.game?.gameName ?? "" , image: data.game?.icons ?? "",lives: data.contest?.entryfeeDeposit ?? 0, tokens: data.battle?.entryfeeDeposit ?? 0)
        self.view?.reloadTable()
//        self.view?.setupMyUserView()
    }
    
    func PlayDirectGame()->Bool{
        if self.gameDetails?.game?.sceneName ?? "" == "Ludo" || self.gameDetails?.game?.sceneName ?? "" == "Chess"{
            return true
        }
        return false
    }
    
    func getContestRules()->String{
        return self.gameDetails?.game?.rule ?? ""
    }
    
    func GetLives(){
        interactor?.sendWalletRequest()
    }
    
    func getWalletInfo(obj: WalletWorkerData) {
        livesModel = obj
        UserModel.shared.setCountLives(count: obj.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: obj.playerToken ?? 0)
//        self.view?.setHome(lives: self.livesModel?.lives ?? 0)
//        self.view?.updateCoins(with: self.livesModel?.playerToken ?? 0)
    }
    
    func getChallenges(model: [ChallengesGamesData]) {
//        for i in model {
//            let deposit  = i.entryfeeDeposit ?? 0
//            let entery = i.entryfeeBouns ?? 0
//            if deposit+entery == 0 {
//                self.freeGame = i
//                return
//            }
//        }
    }
    
    func canSendFeedBack(){
        interactor?.canSendFeedback(model: FeedBackGameRequstModel(gameID: Int(self.gameID) ?? 0 , playerID: UserModel.shared.get_id()))
    }
    
    func checkCanSendFeedback(status:Bool){
        self.view?.canOpenFeedBack(Status: status)
    }
    
    func openContest(){
        router?.openGame(gameID: self.gameID)
    }
    
    func getListTutorial()->[String]{
        return self.gameDetails?.game?.tutorialcards ?? []
    }
    
    
    func openFeedBack(){
        router?.openFeedBack(gameID: Int(self.gameID) ?? 0)
    }
    
    func CheckIsKnockout()->Bool{
        return  self.gameDetails?.contest?.is_knockout ?? false
    }
}



extension ContestGamesPresenter: BattlesDialogProtocol{
    func didSelectBattle(_ battle: ChallengesGamesData) {
        self.type = .token
        self.freeGame = battle
        self.interactor?.ValidateContest(contestID: "\(battle.id ?? 0)")
    }
}
