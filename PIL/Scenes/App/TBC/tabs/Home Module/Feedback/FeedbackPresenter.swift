//
//  FeedbackPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 13/12/2022.
//

import Foundation

class FeedbackPresenter: FeedbackPresenterProtocol  , FeedbackInteractorOutputProtocol , ErrorProtocol{

    
    
    var error: ErrorProtocol?
    var view: FeedbackViewProtocol?
    var router:FeedbackRouterProtocol?
    var interactor:FeedbackInteractorInputProtocol?
    var gameID = Int()
    
    init(view: FeedbackViewProtocol,
         router: FeedbackRouterProtocol,
         interactor: FeedbackInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
 
    
    func GetLives() {
        interactor?.sendWalletRequest()
    }
    
    func getWalletInfo(obj: WalletWorkerData) {
        UserModel.shared.setCountLives(count: obj.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: obj.playerToken ?? 0)

        self.view?.setHome(lives: obj.lives ?? 0)
    }
    
    func sendFeedBack(star:Double , message:String){
        let request = FeedBackGameRequstModel(gameID: self.gameID, playerID: UserModel.shared.get_id(), stars: star, message: message)
        interactor?.sendFeedBacl(model: request)
    }
    
    
    func sendSuccessFully() {
        self.view?.sendSuccessfully()
    }

    func checkCanSendFeedback() {
         
    }
    
    func featching(error: String) {
        print("ERROR")
    }
}
