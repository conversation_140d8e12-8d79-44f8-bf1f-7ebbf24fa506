//
//  FeedbackRouter.swift
//  PIL
//
//  Created by sameh mohammed on 13/12/2022.
//

import Foundation
import UIKit

class FeedbackRouter:FeedbackRouterProtocol{
    
    var VC: FeedbackViewProtocol?
    
    
    static func createModule(gameID:Int) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ContestST, VC: .FeedbackVC)) as! FeedbackVC
        
        let router = FeedbackRouter()
        let interactor = FeedbackInteractor()
        let workerGame = ChallengesGameWorker()
        let workerWallet = WalletWorker()
        let presenter = FeedbackPresenter(view: view,
                                          router: router,
                                          interactor: interactor,
                                          error: view)
        
        presenter.view = view
        presenter.gameID = gameID
        presenter.interactor = interactor
        presenter.router = router
        view.presenter = presenter
        interactor.walletWorker = workerWallet
        interactor.worker = workerGame
        interactor.presenter = presenter
        router.VC = view
        
        return view
    }
    
    
    

}
