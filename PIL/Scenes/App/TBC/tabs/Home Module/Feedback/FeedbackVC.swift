//
//  FeedbackVC.swift
//  PIL
//
//  Created by sameh mohammed on 13/12/2022.
//

import UIKit
import Cosmos

/*
 
 send feed back in game
    if rate  < 4 ---> must be write comment
    and in general the message must be at least 10 characters
 */

class FeedbackVC: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , <PERSON>edback<PERSON>iewProtocol , ErrorProtocol{

    //MARK: - outlet
    @IBOutlet weak var starRate: CosmosView!
    @IBOutlet weak var messageTV: UITextView!
    @IBOutlet weak var viewSendFeedback: UIView!
    @IBOutlet weak var navigationView: NavigationView!

    //MARK: - variable
    var presenter: FeedbackPresenterProtocol?
    var rating = 1.0

    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()
        setUpNavigation()
        starRate.didTouchCosmos = { rating in
            self.rating = rating
        }
        
        viewSendFeedback.isHidden = true


    }
    
    //MARK: - function
    
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Feedback".localized)
    }
    
    func sendSuccessfully() {
        print("-----------")
        viewSendFeedback.isHidden = false
        self.navigationController?.popViewController(animated: true)
    }
    
    
    func setHome(lives: Int) {
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
//            livesCountLable.attributedText = imageString
        }else{
//            livesCountLable.text = "\(lives)"
        }
    }
    
    
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            UserModel.shared.logOut()
        })
    }
    
    //MARK: - action
    @IBAction func submitBTN(_ sender: Any) {
        if rating == 5 {
            self.presenter?.sendFeedBack(star: rating, message: messageTV.text ?? "")
        }else  if rating <= 4 && messageTV.text ?? "" == ""{
            self.showAlert(withTitle: false, msg: "Please write a comment".localized) {}
        }else{
            if  messageTV.text.count < 10{
                self.showAlert(withTitle: false, msg: "Please write at least 10 characters".localized) { }
            }else{
                self.presenter?.sendFeedBack(star: rating, message: messageTV.text ?? "")
            }
        }
    }
    
   
    @IBAction func CancelBTN(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    
}
