//
//  FeedbackInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 13/12/2022.
//

import Foundation

class FeedbackInteractor:FeedbackInteractorInputProtocol{
    
    var presenter:FeedbackInteractorOutputProtocol?
    var error:ErrorProtocol?
    var worker:ChallengesGameWorker?
    var walletWorker: WalletWorkerProtocol?
 
    func sendFeedBacl(model:FeedBackGameRequstModel){
        worker?.feedBack(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{
                    self.presenter?.sendSuccessFully()
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
   
    
    func canSendFeedback(model:FeedBackGameRequstModel){
        worker?.cansSendFeedBack(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{

                    if model.data ?? false == true{
                        self.presenter?.checkCanSendFeedback()
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
                   guard let self = self else { return }
                   switch result{
                       case .success(let model):
                           if model.status ?? false{
                             if let model = model.data{
                                self.presenter?.getWalletInfo(obj: model)
                            }
                           }
                           break
                       case .failure(let error):
                       ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                           self.error?.featching(error: localizedError)
                       } sessionExpired: {
                           self.error?.sessionExpired?()
                       } noInternet: {
                           self.error?.noInternet?()
                       }
                           break
            }
        })
    }
    
}
