//
//  FeedbackProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 13/12/2022.
//

import Foundation

protocol FeedbackViewProtocol: AnyObject{
    var presenter:FeedbackPresenterProtocol? {get set}
    func sendSuccessfully()
    func setHome(lives: Int)

}


protocol FeedbackPresenterProtocol: AnyObject{
    var view:FeedbackViewProtocol? {get set}
    func sendFeedBack(star:Double , message:String)
    func GetLives()
}


protocol FeedbackInteractorInputProtocol{
    func sendFeedBacl(model:FeedBackGameRequstModel)
    func sendWalletRequest()
    func canSendFeedback(model:FeedBackGameRequstModel)
}

protocol FeedbackInteractorOutputProtocol{
    func sendSuccessFully()
    func getWalletInfo(obj:WalletWorkerData)
    func checkCanSendFeedback()

}


protocol FeedbackRouterProtocol{
    
    
}
