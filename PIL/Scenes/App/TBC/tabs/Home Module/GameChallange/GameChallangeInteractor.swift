//
//  GameChallangeInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 07/11/2024.
//

import Foundation

class GameChallangeInteractor: GameChallangeInputInteractorProtocol {

    //MARK: - variables
    weak var presenter: GameChallangeInteractorOutputProtocol?
    var error: ErrorProtocol?
    var workerRooms:RoomsWorker?
    
    //MARK: - Leave Meet & Play
    func leaveRoom(model: RoomJoinLeaveRM){
        RoomsWorker.shared.LeaveRoom(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success leave Room
                    presenter?.successLeaveRoom()
                }else{
                    self.presenter?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.presenter?.featching(error: localizedError)
                } sessionExpired: {
                    self.presenter?.sessionExpired()
                } noInternet: {
                    self.presenter?.noInternet()
                }
                break
            }
        })
    }
}
