<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Light.ttf">
            <string>Poppins-Light</string>
        </array>
        <array key="Poppins-Medium.ttf">
            <string>Poppins-Medium</string>
        </array>
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="GameChallangeVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="animationUser" destination="pZY-Az-mGQ" id="Xlm-VX-wXw"/>
                <outlet property="avatarImg" destination="hcN-AR-l55" id="5E0-Si-ZvP"/>
                <outlet property="challangeStack" destination="69O-Hr-yr1" id="NtQ-NX-COM"/>
                <outlet property="chatView" destination="rbW-2A-AWA" id="vpU-37-kOv"/>
                <outlet property="createChallangeBtn" destination="2Op-Xy-hyP" id="mVR-jg-0ry"/>
                <outlet property="createChallangeBtnView" destination="N6Q-ud-Btc" id="Yo5-1D-uza"/>
                <outlet property="muteView" destination="mX7-ai-lrv" id="X6j-pV-1TB"/>
                <outlet property="navigationView" destination="VPb-Fw-QAi" id="HDD-WZ-30t"/>
                <outlet property="roomControlView" destination="12S-hB-6pi" id="Gk2-No-VXV"/>
                <outlet property="sendChallangeBtn" destination="Rpn-ft-oU3" id="9VL-sY-SvV"/>
                <outlet property="sendChallangeBtnView" destination="UgB-6O-HTa" id="gSh-TE-XlW"/>
                <outlet property="speakerView" destination="UO4-bV-hks" id="RuF-6x-W1B"/>
                <outlet property="timerLabel" destination="ncQ-1s-Lyq" id="AFx-i3-qDj"/>
                <outlet property="topViewtoScrollConstraint" destination="ZEs-OH-DLa" id="KXX-kJ-Qkh"/>
                <outlet property="userNameLabel" destination="2Gh-4T-ZMZ" id="muh-0W-fDi"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pxs-kq-2xD" customClass="MainBackgroundGradientView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VPb-Fw-QAi" customClass="NavigationView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="120"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="120" id="65y-su-anh"/>
                    </constraints>
                </view>
                <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k7k-bi-NHf">
                    <rect key="frame" x="0.0" y="441.66666666666674" width="393" height="376.33333333333326"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1dk-s6-UJD">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="255"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="plB-YN-mzA">
                                    <rect key="frame" x="181.66666666666666" y="5" width="30" height="5"/>
                                    <color key="backgroundColor" red="0.55294120309999995" green="0.54509806630000002" blue="0.54901963470000004" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="30" id="2vd-7a-0f3"/>
                                        <constraint firstAttribute="height" constant="5" id="iFI-W0-SUj"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="2.5"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="EP6-yA-b1R">
                                    <rect key="frame" x="10" y="20" width="373" height="100"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="12S-hB-6pi">
                                            <rect key="frame" x="0.0" y="0.0" width="373" height="100"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="pBu-JM-TVo">
                                                    <rect key="frame" x="26.666666666666657" y="10" width="320" height="80"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5dT-5r-PYj">
                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="80"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mX7-ai-lrv">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mute_mic" translatesAutoresizingMaskIntoConstraints="NO" id="jyJ-44-sfB">
                                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="Grey_Btn"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="bottom" secondItem="jyJ-44-sfB" secondAttribute="bottom" id="7Fr-J0-aDC"/>
                                                                        <constraint firstAttribute="height" constant="50" id="7sh-Vn-qOl"/>
                                                                        <constraint firstItem="jyJ-44-sfB" firstAttribute="leading" secondItem="mX7-ai-lrv" secondAttribute="leading" id="AWA-HS-xBj"/>
                                                                        <constraint firstItem="jyJ-44-sfB" firstAttribute="top" secondItem="mX7-ai-lrv" secondAttribute="top" id="C7Z-NT-d7H"/>
                                                                        <constraint firstAttribute="trailing" secondItem="jyJ-44-sfB" secondAttribute="trailing" id="emM-rK-qZg"/>
                                                                        <constraint firstAttribute="width" constant="50" id="qKY-Ta-TVg"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="25"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="mute" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sSd-uD-dAy">
                                                                    <rect key="frame" x="6.3333333333333357" y="49.999999999999943" width="37.333333333333336" height="30"/>
                                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                                                    <color key="textColor" name="Black-White"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Lca-6D-vLN">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="80"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <connections>
                                                                        <action selector="muteBtn:" destination="-1" eventType="touchUpInside" id="ACS-8q-oAK"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="Lca-6D-vLN" secondAttribute="trailing" id="AXm-8N-Zdu"/>
                                                                <constraint firstAttribute="height" constant="80" id="EPD-xP-Nwx"/>
                                                                <constraint firstItem="sSd-uD-dAy" firstAttribute="top" secondItem="mX7-ai-lrv" secondAttribute="bottom" id="WYI-7V-q7e"/>
                                                                <constraint firstItem="sSd-uD-dAy" firstAttribute="centerX" secondItem="5dT-5r-PYj" secondAttribute="centerX" id="bnV-hj-rT0"/>
                                                                <constraint firstItem="mX7-ai-lrv" firstAttribute="centerX" secondItem="5dT-5r-PYj" secondAttribute="centerX" id="cZk-h4-173"/>
                                                                <constraint firstAttribute="width" constant="50" id="ehI-D7-VDG"/>
                                                                <constraint firstAttribute="bottom" secondItem="sSd-uD-dAy" secondAttribute="bottom" id="g6u-pP-aHM"/>
                                                                <constraint firstItem="Lca-6D-vLN" firstAttribute="leading" secondItem="5dT-5r-PYj" secondAttribute="leading" id="jUw-Ny-9be"/>
                                                                <constraint firstAttribute="bottom" secondItem="Lca-6D-vLN" secondAttribute="bottom" id="oKo-T9-0Sk"/>
                                                                <constraint firstItem="mX7-ai-lrv" firstAttribute="top" secondItem="5dT-5r-PYj" secondAttribute="top" id="v6J-Cl-t5C"/>
                                                                <constraint firstItem="Lca-6D-vLN" firstAttribute="top" secondItem="5dT-5r-PYj" secondAttribute="top" id="zcs-ce-Qoz"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fXR-7f-kjy">
                                                            <rect key="frame" x="90" y="0.0" width="50" height="80"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UO4-bV-hks">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_sound" translatesAutoresizingMaskIntoConstraints="NO" id="USN-82-JLF">
                                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="Grey_Btn"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="50" id="FI9-g5-I27"/>
                                                                        <constraint firstItem="USN-82-JLF" firstAttribute="leading" secondItem="UO4-bV-hks" secondAttribute="leading" id="JcS-RL-gIk"/>
                                                                        <constraint firstAttribute="bottom" secondItem="USN-82-JLF" secondAttribute="bottom" id="QyS-f7-jTT"/>
                                                                        <constraint firstItem="USN-82-JLF" firstAttribute="top" secondItem="UO4-bV-hks" secondAttribute="top" id="Rhw-Wf-JYD"/>
                                                                        <constraint firstAttribute="trailing" secondItem="USN-82-JLF" secondAttribute="trailing" id="aD3-x5-7wB"/>
                                                                        <constraint firstAttribute="height" constant="50" id="tTj-UJ-KKa"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="25"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="speaker" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="J5g-D9-bPU">
                                                                    <rect key="frame" x="-3.3333333333333428" y="49.999999999999943" width="56.333333333333336" height="30"/>
                                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                                                    <color key="textColor" name="Black-White"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GRM-fU-37f">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="80"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <connections>
                                                                        <action selector="speakerBtn:" destination="-1" eventType="touchUpInside" id="tll-gX-loS"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="J5g-D9-bPU" firstAttribute="top" secondItem="UO4-bV-hks" secondAttribute="bottom" id="04p-kI-Xzb"/>
                                                                <constraint firstItem="J5g-D9-bPU" firstAttribute="centerX" secondItem="fXR-7f-kjy" secondAttribute="centerX" id="1qq-Et-m7u"/>
                                                                <constraint firstAttribute="bottom" secondItem="J5g-D9-bPU" secondAttribute="bottom" id="DpG-TR-S2K"/>
                                                                <constraint firstItem="UO4-bV-hks" firstAttribute="centerX" secondItem="fXR-7f-kjy" secondAttribute="centerX" id="FGT-yn-XjA"/>
                                                                <constraint firstAttribute="height" constant="80" id="Q07-7u-o3f"/>
                                                                <constraint firstItem="GRM-fU-37f" firstAttribute="leading" secondItem="fXR-7f-kjy" secondAttribute="leading" id="Rp5-ib-enH"/>
                                                                <constraint firstAttribute="bottom" secondItem="GRM-fU-37f" secondAttribute="bottom" id="bfn-kU-WGK"/>
                                                                <constraint firstAttribute="trailing" secondItem="GRM-fU-37f" secondAttribute="trailing" id="crc-Gf-Qu9"/>
                                                                <constraint firstAttribute="width" constant="50" id="jiW-GU-xJq"/>
                                                                <constraint firstItem="UO4-bV-hks" firstAttribute="top" secondItem="fXR-7f-kjy" secondAttribute="top" id="jsj-dE-4fq"/>
                                                                <constraint firstItem="GRM-fU-37f" firstAttribute="top" secondItem="fXR-7f-kjy" secondAttribute="top" id="nmG-ca-kxS"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ClO-3B-5lb">
                                                            <rect key="frame" x="180" y="0.0" width="50" height="80"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rbW-2A-AWA">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_chat" translatesAutoresizingMaskIntoConstraints="NO" id="KPA-Bb-VWv">
                                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="Grey_Btn"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="trailing" secondItem="KPA-Bb-VWv" secondAttribute="trailing" id="9Ht-iF-YrR"/>
                                                                        <constraint firstAttribute="width" constant="50" id="Fo0-cB-9sR"/>
                                                                        <constraint firstItem="KPA-Bb-VWv" firstAttribute="top" secondItem="rbW-2A-AWA" secondAttribute="top" id="PhB-bE-K2Y"/>
                                                                        <constraint firstAttribute="bottom" secondItem="KPA-Bb-VWv" secondAttribute="bottom" id="f8h-Tt-M4N"/>
                                                                        <constraint firstAttribute="height" constant="50" id="hSI-PG-8oO"/>
                                                                        <constraint firstItem="KPA-Bb-VWv" firstAttribute="leading" secondItem="rbW-2A-AWA" secondAttribute="leading" id="m0x-d7-FV8"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="25"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="chat" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xZn-ws-Ht7">
                                                                    <rect key="frame" x="8.6666666666666856" y="49.999999999999943" width="32.333333333333336" height="30"/>
                                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                                                    <color key="textColor" name="Black-White"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gLZ-8M-Z9L">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="80"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <connections>
                                                                        <action selector="chatBtn:" destination="-1" eventType="touchUpInside" id="LqM-Sp-a5P"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="gLZ-8M-Z9L" firstAttribute="leading" secondItem="ClO-3B-5lb" secondAttribute="leading" id="2hf-v2-m5H"/>
                                                                <constraint firstAttribute="trailing" secondItem="gLZ-8M-Z9L" secondAttribute="trailing" id="4jh-wu-BT2"/>
                                                                <constraint firstAttribute="bottom" secondItem="gLZ-8M-Z9L" secondAttribute="bottom" id="75y-h0-jVn"/>
                                                                <constraint firstAttribute="bottom" secondItem="xZn-ws-Ht7" secondAttribute="bottom" id="Bg8-yQ-CE4"/>
                                                                <constraint firstItem="rbW-2A-AWA" firstAttribute="centerX" secondItem="ClO-3B-5lb" secondAttribute="centerX" id="H1e-MH-5Jw"/>
                                                                <constraint firstItem="xZn-ws-Ht7" firstAttribute="top" secondItem="rbW-2A-AWA" secondAttribute="bottom" id="Lod-Ih-DxC"/>
                                                                <constraint firstItem="rbW-2A-AWA" firstAttribute="top" secondItem="ClO-3B-5lb" secondAttribute="top" id="NRd-xM-FJS"/>
                                                                <constraint firstItem="xZn-ws-Ht7" firstAttribute="centerX" secondItem="ClO-3B-5lb" secondAttribute="centerX" id="ZJy-ea-qcO"/>
                                                                <constraint firstAttribute="height" constant="80" id="dCY-e4-SMl"/>
                                                                <constraint firstAttribute="width" constant="50" id="fdY-Dh-68q"/>
                                                                <constraint firstItem="gLZ-8M-Z9L" firstAttribute="top" secondItem="ClO-3B-5lb" secondAttribute="top" id="uha-Mx-dQG"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nWh-GA-JZk">
                                                            <rect key="frame" x="270" y="0.0" width="50" height="80"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lse-hj-agD">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                    <subviews>
                                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_next" translatesAutoresizingMaskIntoConstraints="NO" id="Bt9-JQ-cW8">
                                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                        </imageView>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="Orange Primary Color"/>
                                                                    <constraints>
                                                                        <constraint firstItem="Bt9-JQ-cW8" firstAttribute="leading" secondItem="Lse-hj-agD" secondAttribute="leading" id="JFb-uU-Wsd"/>
                                                                        <constraint firstAttribute="trailing" secondItem="Bt9-JQ-cW8" secondAttribute="trailing" id="KA6-sL-sTJ"/>
                                                                        <constraint firstAttribute="width" constant="50" id="KOp-Vr-QBA"/>
                                                                        <constraint firstAttribute="bottom" secondItem="Bt9-JQ-cW8" secondAttribute="bottom" id="Zme-sS-cwV"/>
                                                                        <constraint firstAttribute="height" constant="50" id="g3o-tz-scC"/>
                                                                        <constraint firstItem="Bt9-JQ-cW8" firstAttribute="top" secondItem="Lse-hj-agD" secondAttribute="top" id="lXN-1K-mp4"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="25"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="skip" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Tji-Hq-ng8">
                                                                    <rect key="frame" x="11.000000000000002" y="49.999999999999943" width="27.666666666666671" height="30"/>
                                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                                                    <color key="textColor" name="Black-White"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hRm-zf-BEg">
                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="80"/>
                                                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                                    <connections>
                                                                        <action selector="skipBtn:" destination="-1" eventType="touchUpInside" id="hDw-Dz-eli"/>
                                                                    </connections>
                                                                </button>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="bottom" secondItem="Tji-Hq-ng8" secondAttribute="bottom" id="A8L-Sj-y1I"/>
                                                                <constraint firstItem="hRm-zf-BEg" firstAttribute="leading" secondItem="nWh-GA-JZk" secondAttribute="leading" id="Eyd-uA-AnL"/>
                                                                <constraint firstAttribute="height" constant="80" id="Izu-Nh-gpX"/>
                                                                <constraint firstItem="Tji-Hq-ng8" firstAttribute="top" secondItem="Lse-hj-agD" secondAttribute="bottom" id="Nh8-8R-HJz"/>
                                                                <constraint firstItem="hRm-zf-BEg" firstAttribute="top" secondItem="nWh-GA-JZk" secondAttribute="top" id="PVm-w0-tKm"/>
                                                                <constraint firstAttribute="trailing" secondItem="hRm-zf-BEg" secondAttribute="trailing" id="SSN-Nh-ylv"/>
                                                                <constraint firstAttribute="bottom" secondItem="hRm-zf-BEg" secondAttribute="bottom" id="hI9-Nm-MCu"/>
                                                                <constraint firstItem="Tji-Hq-ng8" firstAttribute="centerX" secondItem="nWh-GA-JZk" secondAttribute="centerX" id="jOE-QL-jvh"/>
                                                                <constraint firstAttribute="width" constant="50" id="rvf-Hh-CXn"/>
                                                                <constraint firstItem="Lse-hj-agD" firstAttribute="centerX" secondItem="nWh-GA-JZk" secondAttribute="centerX" id="sdB-dP-V2N"/>
                                                                <constraint firstItem="Lse-hj-agD" firstAttribute="top" secondItem="nWh-GA-JZk" secondAttribute="top" id="yM1-DG-bDw"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="100" id="AlS-76-gVD"/>
                                                <constraint firstAttribute="bottom" secondItem="pBu-JM-TVo" secondAttribute="bottom" constant="10" id="Xza-p4-b3F"/>
                                                <constraint firstItem="pBu-JM-TVo" firstAttribute="top" secondItem="12S-hB-6pi" secondAttribute="top" constant="10" id="kHv-LK-wIT"/>
                                                <constraint firstItem="pBu-JM-TVo" firstAttribute="centerX" secondItem="12S-hB-6pi" secondAttribute="centerX" id="sn0-mG-ARd"/>
                                            </constraints>
                                        </view>
                                        <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="69O-Hr-yr1">
                                            <rect key="frame" x="0.0" y="0.0" width="373" height="232"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Game" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sJS-9L-I7x">
                                                    <rect key="frame" x="0.0" y="0.0" width="373" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                    <color key="textColor" name="Gray-White"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3pQ-hF-S9G">
                                                    <rect key="frame" x="0.0" y="20" width="373" height="44"/>
                                                    <subviews>
                                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Choose your Game" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="AA0-15-GhA" customClass="NoPasteTextField" customModule="PIL" customModuleProvider="target">
                                                            <rect key="frame" x="12" y="7" width="349" height="30"/>
                                                            <color key="textColor" name="Black-White"/>
                                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits"/>
                                                        </textField>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Dax-VJ-PVj">
                                                            <rect key="frame" x="0.0" y="0.0" width="373" height="44"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="Dax-VJ-PVj" secondAttribute="bottom" id="28q-nS-Gzn"/>
                                                        <constraint firstItem="Dax-VJ-PVj" firstAttribute="top" secondItem="3pQ-hF-S9G" secondAttribute="top" id="2Lx-7w-aYO"/>
                                                        <constraint firstAttribute="bottom" secondItem="AA0-15-GhA" secondAttribute="bottom" constant="7" id="8BF-Tl-Lpi"/>
                                                        <constraint firstAttribute="trailing" secondItem="Dax-VJ-PVj" secondAttribute="trailing" id="EXY-Se-baS"/>
                                                        <constraint firstItem="AA0-15-GhA" firstAttribute="top" secondItem="3pQ-hF-S9G" secondAttribute="top" constant="7" id="WmR-m1-vZT"/>
                                                        <constraint firstAttribute="height" constant="44" id="ZGj-El-9Je"/>
                                                        <constraint firstItem="AA0-15-GhA" firstAttribute="leading" secondItem="3pQ-hF-S9G" secondAttribute="leading" constant="12" id="hAc-IV-Wp6"/>
                                                        <constraint firstAttribute="trailing" secondItem="AA0-15-GhA" secondAttribute="trailing" constant="12" id="ide-sd-mZe"/>
                                                        <constraint firstItem="Dax-VJ-PVj" firstAttribute="leading" secondItem="3pQ-hF-S9G" secondAttribute="leading" id="l2Z-Xk-dhJ"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                            <real key="value" value="22"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                            <color key="value" name="border"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                            <real key="value" value="1"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Game Mode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GW9-xl-fHg">
                                                    <rect key="frame" x="0.0" y="83.999999999999943" width="373" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                    <color key="textColor" name="Gray-White"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="40" translatesAutoresizingMaskIntoConstraints="NO" id="FF3-Of-clO">
                                                    <rect key="frame" x="0.0" y="103.99999999999994" width="373" height="44"/>
                                                    <subviews>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oPx-r2-g0A">
                                                            <rect key="frame" x="0.0" y="0.0" width="166.66666666666666" height="44"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="44" id="h5z-zb-87U"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="DeathMatch">
                                                                <color key="titleColor" name="Black-White"/>
                                                            </state>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColorButton">
                                                                    <color key="value" name="border"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="buttonRadius">
                                                                    <real key="value" value="22"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidthButton">
                                                                    <real key="value" value="1"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YPi-RA-KCS">
                                                            <rect key="frame" x="206.66666666666663" y="0.0" width="166.33333333333337" height="44"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="44" id="k4m-tB-Rkc"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Timebomb">
                                                                <color key="titleColor" name="Black-White"/>
                                                            </state>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColorButton">
                                                                    <color key="value" name="border"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="buttonRadius">
                                                                    <real key="value" value="22"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidthButton">
                                                                    <real key="value" value="1"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </button>
                                                    </subviews>
                                                </stackView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Number of Tokens" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rVq-Cv-tMC">
                                                    <rect key="frame" x="0.0" y="167.99999999999994" width="373" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                    <color key="textColor" name="Gray-White"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Qy-f9-7V6">
                                                    <rect key="frame" x="0.0" y="187.99999999999994" width="373" height="44"/>
                                                    <subviews>
                                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="bbf-tn-lnp">
                                                            <rect key="frame" x="12" y="3" width="349" height="38"/>
                                                            <color key="textColor" name="Black-White"/>
                                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits" keyboardType="numberPad"/>
                                                        </textField>
                                                    </subviews>
                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="bbf-tn-lnp" secondAttribute="bottom" constant="3" id="DaU-tz-Uc1"/>
                                                        <constraint firstItem="bbf-tn-lnp" firstAttribute="top" secondItem="5Qy-f9-7V6" secondAttribute="top" constant="3" id="Z1r-T4-MTb"/>
                                                        <constraint firstAttribute="trailing" secondItem="bbf-tn-lnp" secondAttribute="trailing" constant="12" id="f44-IN-fgd"/>
                                                        <constraint firstItem="bbf-tn-lnp" firstAttribute="leading" secondItem="5Qy-f9-7V6" secondAttribute="leading" constant="12" id="i86-dd-twb"/>
                                                        <constraint firstAttribute="height" constant="44" id="trv-9T-UNP"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                            <real key="value" value="22"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                            <real key="value" value="1"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                            <color key="value" name="border"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                </stackView>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bwH-np-Yb5">
                                    <rect key="frame" x="10" y="139.99999999999994" width="373" height="75"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="s1M-bB-JRN">
                                            <rect key="frame" x="20" y="-6.3333333333332575" width="333" height="88"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N6Q-ud-Btc">
                                                    <rect key="frame" x="0.0" y="0.0" width="333" height="44"/>
                                                    <subviews>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2Op-Xy-hyP">
                                                            <rect key="frame" x="0.0" y="0.0" width="333" height="44"/>
                                                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Create Challange">
                                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="createChallangeBtnAction:" destination="-1" eventType="touchUpInside" id="W7t-Cj-IRI"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" name="Grey_Btn"/>
                                                    <constraints>
                                                        <constraint firstItem="2Op-Xy-hyP" firstAttribute="leading" secondItem="N6Q-ud-Btc" secondAttribute="leading" id="5R5-3b-IbY"/>
                                                        <constraint firstAttribute="trailing" secondItem="2Op-Xy-hyP" secondAttribute="trailing" id="LHc-e0-pAn"/>
                                                        <constraint firstItem="2Op-Xy-hyP" firstAttribute="top" secondItem="N6Q-ud-Btc" secondAttribute="top" id="NX7-LA-wEW"/>
                                                        <constraint firstAttribute="bottom" secondItem="2Op-Xy-hyP" secondAttribute="bottom" id="WUH-u0-8zC"/>
                                                        <constraint firstAttribute="height" constant="44" id="s3d-yc-hzc"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                            <real key="value" value="22"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UgB-6O-HTa">
                                                    <rect key="frame" x="0.0" y="44" width="333" height="44"/>
                                                    <subviews>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Rpn-ft-oU3">
                                                            <rect key="frame" x="0.0" y="0.0" width="333" height="44"/>
                                                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                            <state key="normal" title="Send Challange">
                                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="sendChallangeBtnAction:" destination="-1" eventType="touchUpInside" id="3bc-Sx-exQ"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" name="Orange Primary Color"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="Rpn-ft-oU3" secondAttribute="trailing" id="F87-93-Z7M"/>
                                                        <constraint firstItem="Rpn-ft-oU3" firstAttribute="top" secondItem="UgB-6O-HTa" secondAttribute="top" id="FCa-bt-nWi"/>
                                                        <constraint firstAttribute="height" constant="44" id="HeD-Ys-EeG"/>
                                                        <constraint firstItem="Rpn-ft-oU3" firstAttribute="leading" secondItem="UgB-6O-HTa" secondAttribute="leading" id="ntt-8T-1Zj"/>
                                                        <constraint firstAttribute="bottom" secondItem="Rpn-ft-oU3" secondAttribute="bottom" id="wQq-gN-w1T"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                            <real key="value" value="22"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="s1M-bB-JRN" secondAttribute="trailing" constant="20" id="1DP-eX-hQF"/>
                                        <constraint firstItem="s1M-bB-JRN" firstAttribute="leading" secondItem="bwH-np-Yb5" secondAttribute="leading" constant="20" id="CLx-Jc-9rk"/>
                                        <constraint firstAttribute="height" constant="75" id="Ukr-KV-woH"/>
                                        <constraint firstItem="s1M-bB-JRN" firstAttribute="centerY" secondItem="bwH-np-Yb5" secondAttribute="centerY" id="tju-0x-97M"/>
                                    </constraints>
                                </view>
                            </subviews>
                            <color key="backgroundColor" name="Dialog"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="EP6-yA-b1R" secondAttribute="trailing" constant="10" id="2Qc-g8-Qwv"/>
                                <constraint firstItem="EP6-yA-b1R" firstAttribute="top" secondItem="plB-YN-mzA" secondAttribute="bottom" constant="10" id="6M9-CK-5Z7"/>
                                <constraint firstItem="bwH-np-Yb5" firstAttribute="top" secondItem="EP6-yA-b1R" secondAttribute="bottom" constant="20" id="8km-WY-qsH"/>
                                <constraint firstAttribute="trailing" secondItem="bwH-np-Yb5" secondAttribute="trailing" constant="10" id="Dvb-QA-1Zk"/>
                                <constraint firstItem="EP6-yA-b1R" firstAttribute="leading" secondItem="1dk-s6-UJD" secondAttribute="leading" constant="10" id="Fuk-4k-NZF"/>
                                <constraint firstItem="plB-YN-mzA" firstAttribute="top" secondItem="1dk-s6-UJD" secondAttribute="top" constant="5" id="IwL-zb-rNA"/>
                                <constraint firstAttribute="bottom" secondItem="bwH-np-Yb5" secondAttribute="bottom" constant="40" id="N8s-xe-BZK"/>
                                <constraint firstItem="plB-YN-mzA" firstAttribute="centerX" secondItem="1dk-s6-UJD" secondAttribute="centerX" id="NB8-lO-Vke"/>
                                <constraint firstItem="bwH-np-Yb5" firstAttribute="leading" secondItem="1dk-s6-UJD" secondAttribute="leading" constant="10" id="Poq-Ow-8oL"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="20"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="1dk-s6-UJD" firstAttribute="width" secondItem="k7k-bi-NHf" secondAttribute="width" id="7wZ-oU-lUf"/>
                        <constraint firstItem="1dk-s6-UJD" firstAttribute="leading" secondItem="k7k-bi-NHf" secondAttribute="leading" id="Bvp-er-eoS"/>
                        <constraint firstAttribute="bottom" secondItem="1dk-s6-UJD" secondAttribute="bottom" id="CuO-Hz-i4G"/>
                        <constraint firstItem="1dk-s6-UJD" firstAttribute="top" secondItem="k7k-bi-NHf" secondAttribute="top" id="ZEs-OH-DLa"/>
                        <constraint firstAttribute="trailing" secondItem="1dk-s6-UJD" secondAttribute="trailing" id="rIj-rT-pmQ"/>
                        <constraint firstItem="1dk-s6-UJD" firstAttribute="height" secondItem="k7k-bi-NHf" secondAttribute="height" priority="250" id="uB8-hq-oOZ"/>
                    </constraints>
                </scrollView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uwe-39-v8E">
                    <rect key="frame" x="0.0" y="120" width="393" height="240"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pZY-Az-mGQ" customClass="LottieAnimationView" customModule="Lottie">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="240"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="string" keyPath="animationName" value="meetLoader"/>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar2" translatesAutoresizingMaskIntoConstraints="NO" id="hcN-AR-l55">
                            <rect key="frame" x="126.66666666666669" y="30" width="140" height="140"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="140" id="3Qk-Ch-0Bn"/>
                                <constraint firstAttribute="height" constant="140" id="7TD-pn-OHt"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="pZY-Az-mGQ" secondAttribute="trailing" id="7Yt-cf-Au5"/>
                        <constraint firstAttribute="bottom" secondItem="pZY-Az-mGQ" secondAttribute="bottom" id="CXL-9q-QPm"/>
                        <constraint firstItem="pZY-Az-mGQ" firstAttribute="top" secondItem="uwe-39-v8E" secondAttribute="top" id="H5Z-yC-jon"/>
                        <constraint firstAttribute="height" constant="240" id="W2J-li-kAl"/>
                        <constraint firstItem="pZY-Az-mGQ" firstAttribute="leading" secondItem="uwe-39-v8E" secondAttribute="leading" id="jYn-Qc-gdk"/>
                        <constraint firstItem="hcN-AR-l55" firstAttribute="centerY" secondItem="uwe-39-v8E" secondAttribute="centerY" constant="-20" id="nag-o4-vfl"/>
                        <constraint firstItem="hcN-AR-l55" firstAttribute="centerX" secondItem="uwe-39-v8E" secondAttribute="centerX" id="szb-Zp-eMI"/>
                    </constraints>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="mbW-tt-RKg">
                    <rect key="frame" x="105.33333333333333" y="360" width="182.33333333333337" height="71.666666666666686"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="John Smith" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2Gh-4T-ZMZ">
                            <rect key="frame" x="0.0" y="0.0" width="182.33333333333334" height="45"/>
                            <fontDescription key="fontDescription" name="Poppins-Medium" family="Poppins" pointSize="32"/>
                            <color key="textColor" name="Black-White"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ncQ-1s-Lyq">
                            <rect key="frame" x="0.0" y="52" width="182.33333333333334" height="19.666666666666671"/>
                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                            <color key="textColor" name="Black-White"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="mbW-tt-RKg" firstAttribute="top" secondItem="uwe-39-v8E" secondAttribute="bottom" id="GfL-N6-WSf"/>
                <constraint firstItem="VPb-Fw-QAi" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="HBm-l4-HBd"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="k7k-bi-NHf" secondAttribute="trailing" id="MJs-7l-4ot"/>
                <constraint firstItem="k7k-bi-NHf" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="W7i-gx-17D"/>
                <constraint firstItem="VPb-Fw-QAi" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="YoE-Up-KGe"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="pxs-kq-2xD" secondAttribute="trailing" id="dXU-qE-OoP"/>
                <constraint firstItem="uwe-39-v8E" firstAttribute="trailing" secondItem="fnl-2z-Ty3" secondAttribute="trailing" id="gKu-5b-CQC"/>
                <constraint firstItem="uwe-39-v8E" firstAttribute="top" secondItem="VPb-Fw-QAi" secondAttribute="bottom" id="hBA-f1-Bnu"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="VPb-Fw-QAi" secondAttribute="trailing" id="iXs-dD-JmK"/>
                <constraint firstAttribute="bottom" secondItem="pxs-kq-2xD" secondAttribute="bottom" id="is7-m7-ieM"/>
                <constraint firstItem="uwe-39-v8E" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="kAp-9c-lcI"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="k7k-bi-NHf" secondAttribute="bottom" id="m9o-zZ-VAA"/>
                <constraint firstItem="pxs-kq-2xD" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="osu-mx-GdX"/>
                <constraint firstItem="k7k-bi-NHf" firstAttribute="top" secondItem="mbW-tt-RKg" secondAttribute="bottom" constant="10" id="rxy-VW-z8S"/>
                <constraint firstItem="mbW-tt-RKg" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="sFe-TX-lao"/>
                <constraint firstItem="pxs-kq-2xD" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="uyJ-DL-oKB"/>
            </constraints>
            <point key="canvasLocation" x="58.778625954198468" y="19.718309859154932"/>
        </view>
    </objects>
    <designables>
        <designable name="AA0-15-GhA">
            <size key="intrinsicContentSize" width="135.33333333333334" height="21.333333333333332"/>
        </designable>
    </designables>
    <resources>
        <image name="avatar2" width="800" height="800"/>
        <image name="ic_chat" width="21" height="21"/>
        <image name="ic_next" width="19" height="19"/>
        <image name="ic_sound" width="19.333333969116211" height="13.666666984558105"/>
        <image name="mute_mic" width="21" height="24.666666030883789"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray-White">
            <color red="0.53299999237060547" green="0.53299999237060547" blue="0.53299999237060547" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey_Btn">
            <color red="0.24300000071525574" green="0.23499999940395355" blue="0.24300000071525574" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TF-Placholder">
            <color red="0.98000001907348633" green="0.98000001907348633" blue="0.98000001907348633" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
