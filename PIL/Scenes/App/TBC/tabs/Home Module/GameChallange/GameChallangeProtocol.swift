//
//  GameChallangeProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 07/11/2024.
//

import Foundation


protocol GameChallangeRouterProtocol: AnyObject{
    func showWaitingDialog()
    func openLoading(_ delegate : BackMeetAndPlay)
    func BackToHome()
    func toChatWithUser(_ userId: Int , _ roomId : Int)
    func openExitGame()
}

protocol GameChallangeViewProtocol: AnyObject{
    func showAlertWith(_ message: String)
    func onError(_ msg: String)
    func askPermissionIfNeeded()
    func OpenMeet()
    func LeaveRoomView()
    func UpdateUI(_ data: RoomModel)
    func userData(_ socketData : SendMessageRoomModel)
}

protocol GameChallangePresenterProtocol: AnyObject{
    var view: GameChallangeViewProtocol? { set get }
    var interactor: GameChallangeInputInteractorProtocol? { set get }
    var router: GameChallangeRouterProtocol? { set get }
    var meetData : RoomModel? {set get}
    func viewDidLoad()
    func LeaveRoom()
    func openExitGameDialog()
    func roomDataUI(_ socketData : SendMessageRoomModel)
    func GoToChat(_ id : Int)
}

protocol GameChallangeInputInteractorProtocol: AnyObject{
    var presenter: GameChallangeInteractorOutputProtocol? { set get }
    func leaveRoom(model: RoomJoinLeaveRM)

}

protocol GameChallangeInteractorOutputProtocol: AnyObject{
    func successLeaveRoom()
    func featching(error: String)
    func sessionExpired()
    func noInternet() 
}
