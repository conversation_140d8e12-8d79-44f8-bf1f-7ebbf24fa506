//
//  GameChallangeVC.swift
//  PIL
//
//  Created by sameh mohammed on 03/11/2024.
//

import UIKit
import AgoraRtcKit
import SignalRClient
import Lottie

class GameChallangeVC: UIViewController , GameChallangeViewProtocol, ErrorProtocol {
    //MARK: - @IBOutlet
    @IBOutlet weak var chatView: UIView!
    @IBOutlet weak var speakerView: UIView!
    @IBOutlet weak var muteView: UIView!
    @IBOutlet weak var topViewtoScrollConstraint: NSLayoutConstraint!
    @IBOutlet weak var sendChallangeBtn: UIButton!
    @IBOutlet weak var sendChallangeBtnView: UIView!
    @IBOutlet weak var createChallangeBtn: UIButton!
    @IBOutlet weak var roomControlView: UIView!
    @IBOutlet weak var createChallangeBtnView: UIView!
    @IBOutlet weak var challangeStack: UIStackView!
    @IBOutlet weak var userNameLabel: UILabel!
    @IBOutlet weak var timerLabel: UILabel!
    @IBOutlet weak var animationUser: LottieAnimationView!
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var avatarImg: UIImageView!
    //MARK: - Variables
    var presenter: GameChallangePresenter?
    var agoraKit: AgoraRtcEngineKit! // agora
    weak var logVC: LogViewController? // agora
    var callTimer: Timer?
    var elapsedTime = 0
    var isMute = false
    var isSpeaker = true
    //MARK: - override funcs
    override func viewDidLoad() {
        super.viewDidLoad()
        setUI()
        presenter?.viewDidLoad()
    }
    //MARK: - private funcs
    private func setUI() {
        self.navigationController?.interactivePopGestureRecognizer?.isEnabled = false
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [ .back ])
        self.navigationView.setNavigationTitle(title: "Meet & Play".localized)
        self.navigationView.onBack = {
            self.stopCallTimer()
            self.presenter?.LeaveRoom()
            self.presenter?.GoToHome()
        }
        challangeStack.isHidden = true
        sendChallangeBtnView.isHidden = true
        topViewtoScrollConstraint.constant = 200
    }
    
    func askPermissionIfNeeded() {
        switch AVAudioSession.sharedInstance().recordPermission {
        case .undetermined:
            //            askMicrophoneAuthorization()
            print("Cccccc")
        case .denied:
            let alert = UIAlertController(title: "Error", message: "Please allow microphone usage from settings", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "Open settings", style: .default, handler: { action in
                UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!)
            }))
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))
            self.present(alert, animated: true, completion: nil)
        case .granted:
            //            goToNextStep()
            print("")
        default:
            print("")
        }
    }
    
    func OpenMeet() {
        initializeAgoraEngine()
        setupAudio()
        joinChannel()
        agoraKit.muteLocalAudioStream(false)
        configuerSocket()
        UserModel.shared.setMeetRoomId(id: "\(presenter?.meetData?.roomRecord?.id ?? 0)")
    }
    
    func StartTimer() {
        callTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.elapsedTime += 1
            self?.updateCallTimeDisplay()
        }
    }
    
    func updateCallTimeDisplay() {
        let minutes = elapsedTime / 60
        let seconds = elapsedTime % 60
        timerLabel.text = String(format: "%02d:%02d", minutes, seconds)
        print(String(format: "Call Time: %02d:%02d", minutes, seconds))
    }
    
    func stopCallTimer() {
        callTimer?.invalidate()
        callTimer = nil
    }
    
    func LeaveRoomView() {
        agoraKit.leaveChannel(nil)
        agoraKit.disableAudio()
    }
    
    func UpdateUI(_ data: RoomModel) {
        if presenter?.socketData?.fromUserId == nil {
            self.userNameLabel.text = data.roomRecord?.createdName ?? ""
            self.avatarImg.sd_setImage(with: URL(string: data.roomRecord?.createdImageURL ?? ""))
        }else{
            self.userNameLabel.text = presenter?.socketData?.name
            self.avatarImg.sd_setImage(with: URL(string: presenter?.socketData?.image ?? ""))
        }
    }
    
    func userData(_ socketData : SendMessageRoomModel) {
        self.userNameLabel.text = socketData.name
        self.avatarImg.sd_setImage(with: URL(string: socketData.image ?? ""))
    }

    func BackToSearch() {
        navigationController?.popViewController(animated: true)
    }
    
    func showAlertWith(_ message: String) {
        showActionAlert(msg: message)
    }
    
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    func sessionExpired() {
        loginAlert{
            UserModel.shared.logOut()
        }
    }
    
    func onError(_ msg: String) {
        self.showAlert(withTitle: false, msg: msg) {}
    }
    
    @IBAction func muteBtn(_ sender: Any) {
        if isMute == false {
            muteView.backgroundColor = UIColor(named: "Orange Primary Color")
            agoraKit.muteLocalAudioStream(true)
            isMute = true
        }else{
            muteView.backgroundColor = UIColor(named: "Grey_Btn")
            agoraKit.muteLocalAudioStream(false)
            isMute = false
        }
        
    }
    @IBAction func speakerBtn(_ sender: Any) {
        if isSpeaker == true {
            agoraKit.setDefaultAudioRouteToSpeakerphone(false)
            agoraKit.setEnableSpeakerphone(false)
            speakerView.backgroundColor = UIColor(named: "Orange Primary Color")
            isSpeaker = false
        }else{
            agoraKit.setDefaultAudioRouteToSpeakerphone(true)
            agoraKit.setEnableSpeakerphone(true)
            speakerView.backgroundColor = UIColor(named: "Grey_Btn")
            isSpeaker = true
        }
    }
    @IBAction func chatBtn(_ sender: Any) {
        // Go To Chat
        if presenter?.socketData?.fromUserId == nil {
            presenter?.GoToChat(presenter?.meetData?.roomRecord?.createdBy ?? 0)
        }else{
            presenter?.GoToChat(presenter?.socketData?.fromUserId ?? 0)
        }
    }
    @IBAction func skipBtn(_ sender: Any) {
        self.stopCallTimer()
        presenter?.LeaveRoom()
        BackToSearch()
    }
    
    @IBAction func createChallangeBtnAction(_ sender: Any) {
        roomControlView.isHidden = true
        challangeStack.isHidden = false
        createChallangeBtnView.isHidden = true
        sendChallangeBtnView.isHidden = false
        topViewtoScrollConstraint.constant = 0
    }
    
    @IBAction func sendChallangeBtnAction(_ sender: Any) {
        sendChallangeBtn.setTitle("Waiting For Opponent ...", for: .normal)
        roomControlView.isHidden = false
        challangeStack.isHidden = true
        topViewtoScrollConstraint.constant = 200
    }
    

}
