//
//  GameChallangePresenter.swift
//  PIL
//
//  Created by sameh mohammed on 07/11/2024.
//

import Foundation

class GameChallangePresenter: GameChallangePresenterProtocol, GameChallangeInteractorOutputProtocol, ErrorProtocol {
    //MARK: - variables
    weak var view: GameChallangeViewProtocol?
    var router: GameChallangeRouterProtocol?
    var interactor: GameChallangeInputInteractorProtocol?
    var error: ErrorProtocol?
    var meetData : RoomModel?
    var socketData : SendMessageRoomModel?
    
    //MARK: - init
    init(view: GameChallangeViewProtocol,
         router: GameChallangeRouterProtocol,
         interactor: <PERSON>ChallangeInputInteractorProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
//        meetData = meetData
        InitAgora()
        view?.UpdateUI(meetData!)
    }
    
    func openWaitingDialog() {
        router?.openLoading(self)
    }
    
    func openExitGameDialog() {
        router?.openExitGame()
    }
    
    func InitAgora() {
        view?.askPermissionIfNeeded()
        KeyCenter.Token = meetData?.roomRecord?.token ?? ""
        KeyCenter.ChannelID = meetData?.roomRecord?.channelName ?? ""
        KeyCenter.AppId = meetData?.roomRecord?.appID
        view?.OpenMeet()
    }
    
    func LeaveRoom() {
        let request = RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: self.meetData?.roomRecord?.id),
                                     createdBy:  Int(UserModel.shared.get_id()) ?? 0)
        interactor?.leaveRoom(model: request)
    }
    
    func roomDataUI(_ socketData : SendMessageRoomModel) {
    }
    
    func successLeaveRoom() {
        self.view?.LeaveRoomView()
        KeyCenter.ChannelID = ""
        KeyCenter.Token = ""
        meetData = nil
    }
    
    
    func GoToHome() {
        router?.BackToHome()
    }
    
    func GoToChat(_ id : Int) {
        router?.toChatWithUser(id, self.meetData?.roomRecord?.id ?? 0)
    }
    
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
   
}


extension GameChallangePresenter : BackMeetAndPlay {
    func roomData(data: RoomModel) {
        print("room backkkkk \(data)")
        
    }
}
