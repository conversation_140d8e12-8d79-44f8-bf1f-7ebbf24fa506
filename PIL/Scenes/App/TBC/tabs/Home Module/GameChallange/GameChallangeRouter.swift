//
//  GameChallangeRouter.swift
//  PIL
//
//  Created by sameh mohammed on 07/11/2024.
//

import Foundation
import UIKit

class GameChallangeRouter: GameChallangeRouterProtocol {
    
    weak var VC: GameChallangeViewProtocol?
    var presenter: GameChallangePresenterProtocol?
    
    static func createModule() -> UIViewController{
        let view = GameChallangeVC.loadFromNib()
        view.hidesBottomBarWhenPushed = true
        let interactor = GameChallangeInteractor()
        let router = GameChallangeRouter()
        let presenter = GameChallangePresenter(view: view, router: router, interactor: interactor, error: view)
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        router.VC = view
        router.presenter = presenter
        presenter.error = view
        return view
    }
    
    private func push(_ vc: UIViewController){
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    func showWaitingDialog() {
        
    }
    
    func openLoading(_ delegate : BackMeetAndPlay){
//        let loadingVC = LoadingMeetVC.loadFromNib()
//        loadingVC.modalPresentationStyle = .overCurrentContext
//        loadingVC.delegate = delegate
//        if let vc = VC as? UIViewController{
//            loadingVC.show(above: vc)
//        }
    }
    
    func openExitGame() {
        let exitVC = ExitGameVC.loadFromNib()
        exitVC.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            exitVC.show(above: vc)
        }
    }
    
    func BackToHome() {
        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = TBC
    }
    
    func toChatWithUser(_ userId: Int , _ roomId : Int){
//        let vc = SocialChatRotur.createModule(roomID:roomID, isMyRoom:isMyRoom) as! SocialChatVC
        let vc = MeetChatRouter.createModule(userId, roomId) as! MeetChatViewController
        vc.modalPresentationStyle = .fullScreen
        if let view = VC as? UIViewController {
            view.navigationController?.pushViewController(vc, animated: true)
        }
        
//        let vc = ChatNewRotur.createModule(userId: id, groupID: 0 ,chatId: nil , isMeet : true)
//        push(vc)
    }
    
    
}
