//
//  GameChallangeSockit.swift
//  PIL
//
//  Created by sameh mohammed on 10/11/2024.
//

import Foundation
import UIKit
import Alamofire
import SignalRClient
import CallKit

extension GameChallangeVC{
    
    func configuerSocket(){
        self.CheckLeaveORJoinUSer()
    }
    
    
    //MARK: - Receive Leave or join Room
    func CheckLeaveORJoinUSer(){
        AppDelegate.notificationsHubConnection?.on(method: "LeaveOrJoinRoom", callback: { (response : SendMessageRoomModel ) in
            print("LeaveOrJoinRoom DATA1", response)
            if self.presenter?.meetData?.roomRecord?.id == response.toRoomId ?? 0 {
                
                if response.isJoin == true  { // if join in room
                    
                    
                }else if  response.isJoin == false { // if user leave from room,
                    print("User name is ",response.name ?? "" , response.image ?? "")
                    if response.fromUserId ?? 0 != Int(UserModel.shared.get_id()) ?? 0 {
                        self.presenter?.LeaveRoom()
                        self.presenter?.openExitGameDialog()
                    }
                }
            }
        })
    }
}
