//
//  MeetChatRouter.swift
//  PIL
//
//  Created by sameh mohammed on 28/11/2024.
//

import Foundation
import UIKit

class MeetChatRouter: MeetChatRouterProtocol {
    
    weak var VC: MeetChatViewProtocol?
    var presenter: MeetChatPresenterProtocol?
    
    static func createModule(_ userId: Int , _ roomId : Int) -> UIViewController{
        let view = MeetChatViewController.loadFromNib()
        view.hidesBottomBarWhenPushed = true
        let interactor = MeetChatInteractor()
        let router = MeetChatRouter()
        let presenter = MeetChatPresenter(view: view, router: router, interactor: interactor, error: view)
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        router.VC = view
        router.presenter = presenter
        presenter.error = view
        return view
    }
    
    private func push(_ vc: UIViewController){
        if let view = VC as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    
    func openExitGame() {
        let exitVC = ExitGameVC.loadFromNib()
        exitVC.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            exitVC.show(above: vc)
        }
    }
    
    
}
