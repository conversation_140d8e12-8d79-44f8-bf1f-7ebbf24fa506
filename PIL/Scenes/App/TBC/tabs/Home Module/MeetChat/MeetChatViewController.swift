//
//  MeetChatViewController.swift
//  PIL
//
//  Created by sameh mohammed on 28/11/2024.
//

import UIKit
import AgoraRtcKit
import IQKeyboardManager

class MeetChatViewController: UI<PERSON>iewController , MeetChatViewProtocol, ErrorProtocol {
    //MARK: - @IBOutlet
    @IBOutlet weak var speakerBtnView: UIView!
    @IBOutlet weak var buttomConstraint: NSLayoutConstraint!
    @IBOutlet weak var muteBtnView: UIView!
    @IBOutlet weak var chatTableView: UITableView!
    @IBOutlet weak var backBtn: UIButton!
    @IBOutlet weak var messageTF: UITextField!
    //MARK: - Variables
    var presenter: MeetChatPresenter?
    var agoraKit: AgoraRtcEngineKit! /// agora
    weak var logVC: LogViewController?
    var imagereceiver = String()
    var namereceiver = String()
    override func viewDidLoad() {
        super.viewDidLoad()
        setXIB()
        
        ///when tap tableview hidden keyboard
        chatTableView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.tableViewTap)))
        
        ///keyboard setting
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        IQKeyboardManager.shared().disabledDistanceHandlingClasses.add(SocialChatVC.self)
        IQKeyboardManager.shared().disabledToolbarClasses.add(SocialChatVC.self)
        chatTableView.delegate = self
    }
    override func viewWillAppear(_ animated: Bool) {
        presenter?.viewDidLoad() /// call api
    }
    
    /// UI Cell
    func setXIB(){
        chatTableView.transform = CGAffineTransform(rotationAngle: (-.pi))
        self.chatTableView.register(UINib(nibName: "ChatRoomCell", bundle: nil), forCellReuseIdentifier: "ChatRoomCell")
    }
    
    /// hidden keyboard
    @objc func tableViewTap(){
        view.endEditing(true)
    }
    
    /// get height keyboard to change buttom viwe constraint
    @objc func keyboardWillShow(notification: NSNotification) {
        if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            let height = keyboardSize.height
            UIView.animate(withDuration: 0.5) {
                self.buttomConstraint.constant = height - 30
                self.view.layoutIfNeeded()
            }
        }
    }

    func showAlertWith(_ message: String) {
        showActionAlert(msg: message)
    }
    
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    func sessionExpired() {
        loginAlert{
            UserModel.shared.logOut()
        }
    }
    
    func onError(_ msg: String) {
        self.showAlert(withTitle: false, msg: msg) {}
    }
    
    func LeaveRoomView() {
        agoraKit.leaveChannel(nil)
        agoraKit.disableAudio()
    }
    
    
    @IBAction func sendMsgBtnAction(_ sender: Any) {
        if messageTF.text ?? "" != ""{
            self.sendMessage(fromUserId: Int(UserModel.shared.get_id()) ?? 0,
                             message: messageTF.text ?? "",
                             typeID: .message,
                             toUserId: 0,
                             toImageUrl: self.imagereceiver,
                             ImageUrl: UserModel.shared.get_image(),
                             toname: self.namereceiver,
                             toRoomId: presenter?.roomID ?? 0)
        }
    }
    
    @IBAction func muteBtnAction(_ sender: Any) {
    }
    
    @IBAction func speakerBtnAction(_ sender: Any) {
    }
    
    
}
