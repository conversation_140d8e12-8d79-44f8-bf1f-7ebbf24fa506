//
//  MeetChat+Sockit.swift
//  PIL
//
//  Created by sameh mohammed on 01/12/2024.
//

import Foundation
import UIKit
import SignalRClient

extension MeetChatViewController {
    /// Sockit configuertion
    /// lestion some event when join rom
    /// leave or join room , recive request to open
    func configuerSocket(){
        CheckLeaveORJoinUSer()
//        ReceiveMicRequest()
//        ReceiveMicStatus()
        /// when recive new message in chat room add it in list of messages and reload tableview
        AppDelegate.notificationsHubConnection?.on(method: "NewLiveMessage", callback: { (messageOBJ:MessageOBJ?)  in
            if messageOBJ?.toRoomId ?? 0 == self.presenter?.roomID ?? 0  && messageOBJ?.fromUserId ?? 0 != Int(UserModel.shared.get_id()){
                    let messageOBJ = UserChatRecord(id: messageOBJ?.fromUserId,
                                                    createdBy: messageOBJ?.fromUserId,
                                                    creationDate: "",
                                                    creationDateStr: "",
                                                    userID: messageOBJ?.toUserId,
                                                    connectionID: "",
                                                    message:  messageOBJ?.message,
                                                    isSeen: false,
                                                    isOnline: false,
                                                    createdName: messageOBJ?.toname ?? "",
                                                    createdImageURL: messageOBJ?.image ?? "",
                                                    toUserID:  messageOBJ?.toUserId,
                                                    itemID: 0,
                                                    typeID:  messageOBJ?.typeID ?? 0 ,
                                                    itemCount: 0,
                                                    groupName: "",
                                                    groupID: 0,
                                                    isGroupChat: false,
                                                    toGroupId:messageOBJ?.toGroupId ?? 0)
                    
                    self.presenter?.setNewMessage(obj: messageOBJ)
                }
                 self.chatTableView.reloadData()
            })

    }
    //MARK: - Receive Leave or join Room
    /// when new user joined --  case true -> add user to list of user in top view  and list of all member view
    ///                 -- case false -> remove it form list of user in top view and list of all member view
    func CheckLeaveORJoinUSer(){
        AppDelegate.notificationsHubConnection?.on(method: "LeaveOrJoinRoom", callback: { (messageOBJ:SendMessageRoomModel?)  in
            print("user name",messageOBJ?.isJoin ?? ""  , messageOBJ?.name ?? "")
            if self.self.presenter?.roomID ?? 0 == messageOBJ?.toRoomId {
                if messageOBJ?.isJoin == true  {
                    if messageOBJ?.fromUserId ?? 0 != Int(UserModel.shared.get_id()){
//                        self.presenter?.addUser(image: messageOBJ?.image ?? "" ,
//                                                 name: messageOBJ?.name ?? "",
//                                                 id: messageOBJ?.fromUserId ?? 0)
                        }
                    
                }else if  messageOBJ?.isJoin == false {
                    // Leave Room
                    self.presenter?.LeaveRoom()
                    self.presenter?.openExitGameDialog()
//                    self.presenter?.removeUser(id: messageOBJ?.fromUserId ?? 0)
//                    if messageOBJ?.fromUserId ?? 0 == Int(UserModel.shared.get_id()) ?? 0{
//                        self.navigationController?.popViewController(animated: true)
//                    }
                }
            }
        })
    }
    //MARK: -  send  message chat  socket
    func sendMessage(fromUserId:Int,
                     message:String,
                     typeID:TypeMessage,
                     toUserId:Int,
                     toImageUrl:String = "",
                     ImageUrl:String,
                     toname:String,
                     toRoomId:Int,
                     toVoiceData: Data? = nil){
        let messageRequest = MessageOBJ(fromUserId: fromUserId ,
                                    message: message,
                                    typeID: typeID.rawValue,
                                    toRoomId: toRoomId ,
                                    toImageUrl: toImageUrl ,
                                    ImageUrl: ImageUrl,
                                    toname: toname)

        print("Request",messageRequest)
        AppDelegate.notificationsHubConnection?.send(method: "SendLiveMessage" ,messageRequest ) { error in
            DispatchQueue.main.async {
                self.messageTF.text = ""
               
            }
            if let e = error {
                print("Error is : \(e)")
            }
        }
        
        guard typeID == .message else { return }
        
        let messageOBJ = UserChatRecord(id: fromUserId,
                                        createdBy: fromUserId,
                                        creationDate: "",
                                        creationDateStr: "",
                                        userID: toUserId,
                                        connectionID: "",
                                        message: message,
                                        isSeen: false,
                                        isOnline: false,
                                        createdName: UserModel.shared.get_name(),
                                        createdImageURL: UserModel.shared.get_image(),
                                        toUserID: toUserId,
                                        itemID: 0,
                                        typeID: typeID.rawValue ,
                                        itemCount: 0,
                                        groupName: "",
                                        groupID: 0,
                                        isGroupChat: false,
                                        toGroupId: 0,
                                        voiceData: toVoiceData)
        
        self.presenter?.setNewMessage(obj: messageOBJ)
    }
}
