<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MeetChatViewController" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="backBtn" destination="1fK-PA-fT5" id="ciH-oz-Xaz"/>
                <outlet property="buttomConstraint" destination="HsR-3c-dea" id="UXx-qP-Gie"/>
                <outlet property="chatTableView" destination="JRn-SR-x5a" id="LBE-YH-cUc"/>
                <outlet property="messageTF" destination="Y3U-KA-npH" id="JJN-i3-NpJ"/>
                <outlet property="muteBtnView" destination="X0T-Tm-3Ba" id="rqc-uI-e8d"/>
                <outlet property="speakerBtnView" destination="4sw-Cm-xAc" id="7iT-wD-8MT"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="x6w-Pi-q7k" customClass="MainBackgroundGradientView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cGN-eq-g02">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="100"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Header Shape" translatesAutoresizingMaskIntoConstraints="NO" id="YxO-O8-SK9">
                            <rect key="frame" x="0.0" y="0.0" width="393" height="100"/>
                            <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1fK-PA-fT5">
                            <rect key="frame" x="10" y="50" width="50" height="50"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="50" id="ojf-hk-YHS"/>
                                <constraint firstAttribute="width" constant="50" id="qyE-Ni-1rT"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="A_BackNavigation-ar"/>
                        </button>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="iHk-M8-nER">
                            <rect key="frame" x="293" y="55" width="90" height="40"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NOw-5S-Add">
                                    <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X0T-Tm-3Ba">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mute_mic" translatesAutoresizingMaskIntoConstraints="NO" id="Dr5-TR-ggx">
                                                    <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" name="Grey_Btn"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="Dr5-TR-ggx" secondAttribute="bottom" id="54U-Bq-LdK"/>
                                                <constraint firstAttribute="height" constant="40" id="F0m-Ax-KGN"/>
                                                <constraint firstAttribute="trailing" secondItem="Dr5-TR-ggx" secondAttribute="trailing" id="OWM-cg-2J6"/>
                                                <constraint firstAttribute="width" constant="40" id="XMV-mi-yY6"/>
                                                <constraint firstItem="Dr5-TR-ggx" firstAttribute="leading" secondItem="X0T-Tm-3Ba" secondAttribute="leading" id="m5p-fT-4Ic"/>
                                                <constraint firstItem="Dr5-TR-ggx" firstAttribute="top" secondItem="X0T-Tm-3Ba" secondAttribute="top" id="obA-bk-CY3"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                    <real key="value" value="25"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="n9d-sn-Xa1">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <connections>
                                                <action selector="muteBtnAction:" destination="-1" eventType="touchUpInside" id="pTz-SK-Drv"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="40" id="2Ui-cs-JnR"/>
                                        <constraint firstAttribute="trailing" secondItem="n9d-sn-Xa1" secondAttribute="trailing" id="5ft-cD-Fq0"/>
                                        <constraint firstItem="n9d-sn-Xa1" firstAttribute="top" secondItem="NOw-5S-Add" secondAttribute="top" id="TF0-v0-6pm"/>
                                        <constraint firstAttribute="bottom" secondItem="n9d-sn-Xa1" secondAttribute="bottom" id="UYD-uI-b6L"/>
                                        <constraint firstItem="X0T-Tm-3Ba" firstAttribute="centerX" secondItem="NOw-5S-Add" secondAttribute="centerX" id="Zwg-yX-P6g"/>
                                        <constraint firstItem="n9d-sn-Xa1" firstAttribute="leading" secondItem="NOw-5S-Add" secondAttribute="leading" id="dDh-IA-35I"/>
                                        <constraint firstItem="X0T-Tm-3Ba" firstAttribute="top" secondItem="NOw-5S-Add" secondAttribute="top" id="eYB-MX-3C9"/>
                                        <constraint firstAttribute="height" constant="40" id="hx5-1z-ksk"/>
                                    </constraints>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zYl-PK-s9m">
                                    <rect key="frame" x="50" y="0.0" width="40" height="40"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4sw-Cm-xAc">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_sound" translatesAutoresizingMaskIntoConstraints="NO" id="0FI-3X-yec">
                                                    <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                </imageView>
                                            </subviews>
                                            <color key="backgroundColor" name="Grey_Btn"/>
                                            <constraints>
                                                <constraint firstItem="0FI-3X-yec" firstAttribute="leading" secondItem="4sw-Cm-xAc" secondAttribute="leading" id="4nu-Ai-y6B"/>
                                                <constraint firstAttribute="trailing" secondItem="0FI-3X-yec" secondAttribute="trailing" id="9Ln-ol-HPo"/>
                                                <constraint firstItem="0FI-3X-yec" firstAttribute="top" secondItem="4sw-Cm-xAc" secondAttribute="top" id="Kxj-zm-bK0"/>
                                                <constraint firstAttribute="bottom" secondItem="0FI-3X-yec" secondAttribute="bottom" id="Lox-un-tVs"/>
                                                <constraint firstAttribute="width" constant="40" id="SfZ-6X-SB9"/>
                                                <constraint firstAttribute="height" constant="40" id="mw5-Bm-COa"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                    <real key="value" value="25"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QPc-C7-T2W">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <connections>
                                                <action selector="speakerBtnAction:" destination="-1" eventType="touchUpInside" id="nIb-eK-viS"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="4sw-Cm-xAc" firstAttribute="top" secondItem="zYl-PK-s9m" secondAttribute="top" id="0WS-Ng-SRL"/>
                                        <constraint firstItem="QPc-C7-T2W" firstAttribute="top" secondItem="zYl-PK-s9m" secondAttribute="top" id="0b6-CM-fOp"/>
                                        <constraint firstAttribute="width" constant="40" id="2pw-uI-SPb"/>
                                        <constraint firstItem="4sw-Cm-xAc" firstAttribute="centerX" secondItem="zYl-PK-s9m" secondAttribute="centerX" id="3hR-R5-4lg"/>
                                        <constraint firstAttribute="bottom" secondItem="QPc-C7-T2W" secondAttribute="bottom" id="ABP-CA-xht"/>
                                        <constraint firstItem="QPc-C7-T2W" firstAttribute="leading" secondItem="zYl-PK-s9m" secondAttribute="leading" id="O0e-3t-ufQ"/>
                                        <constraint firstAttribute="trailing" secondItem="QPc-C7-T2W" secondAttribute="trailing" id="VP0-zp-58I"/>
                                        <constraint firstAttribute="height" constant="40" id="oTQ-gv-FhD"/>
                                    </constraints>
                                </view>
                            </subviews>
                        </stackView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="xuG-hH-V0x">
                            <rect key="frame" x="157" y="55.666666666666657" width="79.333333333333314" height="44.333333333333343"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="ucb-ks-AVw">
                                    <rect key="frame" x="0.0" y="0.0" width="79.333333333333329" height="30"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_back_avatar" translatesAutoresizingMaskIntoConstraints="NO" id="6cl-l2-b4Q">
                                            <rect key="frame" x="0.0" y="0.0" width="30" height="30"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="30" id="TSf-Yk-3Xd"/>
                                                <constraint firstAttribute="height" constant="30" id="jn1-VO-VUf"/>
                                            </constraints>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gn7-x3-ofw">
                                            <rect key="frame" x="40" y="0.0" width="39.333333333333343" height="30"/>
                                            <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="16"/>
                                            <color key="textColor" name="Orange Primary Color"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </stackView>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wc2-D6-nUd">
                                    <rect key="frame" x="0.0" y="30.000000000000004" width="79.333333333333329" height="14.333333333333332"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" name="Black"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="100" id="4Yr-T8-7Hh"/>
                        <constraint firstAttribute="trailing" secondItem="iHk-M8-nER" secondAttribute="trailing" constant="10" id="D6a-wr-PTJ"/>
                        <constraint firstItem="YxO-O8-SK9" firstAttribute="top" secondItem="cGN-eq-g02" secondAttribute="top" id="Gg9-i3-Pbs"/>
                        <constraint firstItem="YxO-O8-SK9" firstAttribute="leading" secondItem="cGN-eq-g02" secondAttribute="leading" id="Kbg-28-NNU"/>
                        <constraint firstAttribute="bottom" secondItem="xuG-hH-V0x" secondAttribute="bottom" id="MSp-yf-YOh"/>
                        <constraint firstItem="1fK-PA-fT5" firstAttribute="leading" secondItem="cGN-eq-g02" secondAttribute="leading" constant="10" id="PlQ-hB-3Jj"/>
                        <constraint firstItem="xuG-hH-V0x" firstAttribute="centerX" secondItem="cGN-eq-g02" secondAttribute="centerX" id="Vuf-nd-EG7"/>
                        <constraint firstItem="iHk-M8-nER" firstAttribute="centerY" secondItem="1fK-PA-fT5" secondAttribute="centerY" id="aFA-6p-H67"/>
                        <constraint firstAttribute="bottom" secondItem="YxO-O8-SK9" secondAttribute="bottom" id="d1g-Vr-7aS"/>
                        <constraint firstAttribute="bottom" secondItem="1fK-PA-fT5" secondAttribute="bottom" id="dRy-j8-FSl"/>
                        <constraint firstAttribute="trailing" secondItem="YxO-O8-SK9" secondAttribute="trailing" id="gZX-5A-Yjb"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Atv-UX-q1r">
                    <rect key="frame" x="0.0" y="772" width="393" height="80"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="BsJ-3s-RwS">
                            <rect key="frame" x="10" y="10" width="373" height="44"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ors-pH-7hN" customClass="ViewCorners" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="0.0" y="0.0" width="319" height="44"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="stM-J3-yLT">
                                            <rect key="frame" x="7" y="7" width="305" height="30"/>
                                            <subviews>
                                                <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="write a message" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Y3U-KA-npH">
                                                    <rect key="frame" x="0.0" y="0.0" width="305" height="30"/>
                                                    <color key="textColor" name="Black-White"/>
                                                    <fontDescription key="fontDescription" name="HelveticaNeue" family="Helvetica Neue" pointSize="14"/>
                                                    <textInputTraits key="textInputTraits"/>
                                                </textField>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" name="Dialog"/>
                                    <constraints>
                                        <constraint firstItem="stM-J3-yLT" firstAttribute="top" secondItem="ors-pH-7hN" secondAttribute="top" constant="7" id="dUa-Qb-XK9"/>
                                        <constraint firstAttribute="height" constant="44" id="eOK-fm-KQj"/>
                                        <constraint firstItem="stM-J3-yLT" firstAttribute="leading" secondItem="ors-pH-7hN" secondAttribute="leading" constant="7" id="oau-T3-Lro"/>
                                        <constraint firstAttribute="trailing" secondItem="stM-J3-yLT" secondAttribute="trailing" constant="7" id="ppx-qx-hk5"/>
                                        <constraint firstAttribute="bottom" secondItem="stM-J3-yLT" secondAttribute="bottom" constant="7" id="zdg-j5-i7J"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedTopLeft" value="YES"/>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedBottomLeft" value="YES"/>
                                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedBottomRight" value="YES"/>
                                        <userDefinedRuntimeAttribute type="number" keyPath="singleCornerRaduis">
                                            <real key="value" value="20"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="X1p-dt-b9p" customClass="LocalizedButton" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="329" y="0.0" width="44" height="44"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="44" id="bzS-cg-w5e"/>
                                    </constraints>
                                    <color key="tintColor" name="Yellow"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" image="A_ChatSend"/>
                                    <connections>
                                        <action selector="sendMsgBtnAction:" destination="-1" eventType="touchUpInside" id="Dyb-8U-fku"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" name="Gray-Black"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="BsJ-3s-RwS" secondAttribute="trailing" constant="10" id="Mq2-8V-ikO"/>
                        <constraint firstAttribute="height" constant="80" id="UUy-Zf-bI4"/>
                        <constraint firstItem="BsJ-3s-RwS" firstAttribute="leading" secondItem="Atv-UX-q1r" secondAttribute="leading" constant="10" id="foR-wI-Rjw"/>
                        <constraint firstItem="BsJ-3s-RwS" firstAttribute="top" secondItem="Atv-UX-q1r" secondAttribute="top" constant="10" id="xcN-19-VJp"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="JRn-SR-x5a">
                    <rect key="frame" x="0.0" y="100" width="393" height="665"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="sectionIndexBackgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </tableView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="Atv-UX-q1r" secondAttribute="trailing" id="1Fv-Gc-4V5"/>
                <constraint firstItem="cGN-eq-g02" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="AWI-Bo-MdF"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="cGN-eq-g02" secondAttribute="trailing" id="F8I-K8-xtR"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="JRn-SR-x5a" secondAttribute="trailing" id="GhE-zN-dPO"/>
                <constraint firstAttribute="bottom" secondItem="Atv-UX-q1r" secondAttribute="bottom" id="HsR-3c-dea"/>
                <constraint firstItem="Atv-UX-q1r" firstAttribute="top" secondItem="JRn-SR-x5a" secondAttribute="bottom" constant="7" id="NFz-Er-8z2"/>
                <constraint firstItem="cGN-eq-g02" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="O2D-vR-Xvd"/>
                <constraint firstItem="x6w-Pi-q7k" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="Uez-bv-tT7"/>
                <constraint firstAttribute="bottom" secondItem="x6w-Pi-q7k" secondAttribute="bottom" id="ad7-sJ-Ej8"/>
                <constraint firstItem="JRn-SR-x5a" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="jR6-ER-3ga"/>
                <constraint firstItem="x6w-Pi-q7k" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="lZA-d6-x2c"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="x6w-Pi-q7k" secondAttribute="trailing" id="uVG-GP-rdU"/>
                <constraint firstItem="Atv-UX-q1r" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="vKn-rs-Qf2"/>
                <constraint firstItem="JRn-SR-x5a" firstAttribute="top" secondItem="cGN-eq-g02" secondAttribute="bottom" id="ySK-Rp-uFc"/>
            </constraints>
            <point key="canvasLocation" x="58.778625954198468" y="19.718309859154932"/>
        </view>
    </objects>
    <designables>
        <designable name="X1p-dt-b9p">
            <size key="intrinsicContentSize" width="176" height="168"/>
        </designable>
    </designables>
    <resources>
        <image name="A_BackNavigation-ar" width="44" height="44"/>
        <image name="A_ChatSend" width="176" height="168"/>
        <image name="A_Header Shape" width="375" height="110"/>
        <image name="ic_back_avatar" width="31" height="31"/>
        <image name="ic_sound" width="19.333333969116211" height="13.666666984558105"/>
        <image name="mute_mic" width="21" height="24.666666030883789"/>
        <namedColor name="Black">
            <color red="0.87800002098083496" green="0.87800002098083496" blue="0.87800002098083496" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray-Black">
            <color red="0.63499999046325684" green="0.63499999046325684" blue="0.63499999046325684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Grey_Btn">
            <color red="0.24300000071525574" green="0.23499999940395355" blue="0.24300000071525574" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Yellow">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
