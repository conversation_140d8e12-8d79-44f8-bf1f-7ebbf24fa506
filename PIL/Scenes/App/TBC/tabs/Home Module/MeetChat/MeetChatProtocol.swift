//
//  File.swift
//  PIL
//
//  Created by sameh mohammed on 28/11/2024.
//

import Foundation

protocol MeetChatRouterProtocol: AnyObject{
    func openExitGame()
}

protocol MeetChatViewProtocol: AnyObject{
    func showAlertWith(_ message: String)
    func onError(_ msg: String)
    func LeaveRoomView()
}

protocol MeetChatPresenterProtocol: AnyObject{
    var view: MeetChatViewProtocol? { set get }
    var interactor: MeetChatInputInteractorProtocol? { set get }
    var router: MeetChatRouterProtocol? { set get }
    func viewDidLoad()
    func openExitGameDialog()
    func LeaveRoom()
}

protocol MeetChatInputInteractorProtocol: AnyObject{
    var presenter: MeetChatInteractorOutputProtocol? { set get }
    func leaveRoom(model: RoomJoinLeaveRM)
}

protocol MeetChatInteractorOutputProtocol: AnyObject{
    func successLeaveRoom()
    func featching(error: String)
    func sessionExpired()
    func noInternet()
}
