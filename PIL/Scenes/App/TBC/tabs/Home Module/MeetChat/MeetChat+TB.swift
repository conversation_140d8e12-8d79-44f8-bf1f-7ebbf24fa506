//
//  MeetChat+Delegates.swift
//  PIL
//
//  Created by sameh mohammed on 01/12/2024.
//

import Foundation
import SignalRClient
import AVFoundation
import IQKeyboardManager
import DropDown


extension MeetChatViewController : UITableViewDelegate , UITableViewDataSource {
    
    // number rows messages and users room
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.messageCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
            /// chat row display message info and user image
            let cell = self.chatTableView.dequeueReusableCell(withIdentifier: "ChatRoomCell", for: indexPath) as! ChatRoomCell
            presenter?.configuer(cell: cell, index: indexPath.row)
            cell.MessageSide(mySide: presenter?.isMyMessage(index: indexPath.row) ?? false)
//            cell.userProfileGuestBu.tag = indexPath.row
//            cell.userProfileGuestBu.addTarget(self, action: #selector(SelectUser(_:)), for: .touchUpInside)
//            cell.actions = self
            return cell

    }
    
    /// if tab aother user in view all member
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//                self.presenter?.onTapUser(indexPath)
        }
    }
    
    func tableView(_ tableView: UITableView, contextMenuConfigurationForRowAt indexPath: IndexPath, point: CGPoint) -> UIContextMenuConfiguration? {
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil , actionProvider: nil)
    }

    
    /// when start scroll down in chat room  hidden keyboard
//    func scrollViewDidScroll(_ scrollView: UIScrollView) {
//        var lastVelocityYSign = 0
//        let currentVelocityY =  scrollView.panGestureRecognizer.velocity(in: scrollView.superview).y
//        let currentVelocityYSign = Int(currentVelocityY).signum()
//        if currentVelocityYSign != lastVelocityYSign &&
//            currentVelocityYSign != 0 {
//            lastVelocityYSign = currentVelocityYSign
//        }
//        if lastVelocityYSign < 0 {
//            print("SCROLLING DOWN")
//        } else if lastVelocityYSign > 0 {
//            print("SCOLLING UP")
//            view.endEditing(true)
//        }
//    }
    
    



///Test filed delegate
extension MeetChatViewController : UITextFieldDelegate{
    func textFieldDidEndEditing(_ textField: UITextField) {
        UIView.animate(withDuration: 0.4) {
            self.buttomConstraint.constant = 0
            self.view.layoutIfNeeded()
        }
    }
}
