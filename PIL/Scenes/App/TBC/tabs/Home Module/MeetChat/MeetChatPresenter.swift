//
//  MeetChatPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 28/11/2024.
//

import Foundation


class MeetChatPresenter: MeetChatPresenterProtocol, MeetChatInteractorOutputProtocol, ErrorProtocol {
    //MARK: - variables
    weak var view: MeetChatViewProtocol?
    var router: MeetChatRouterProtocol?
    var interactor: MeetChatInputInteractorProtocol?
    var error: ErrorProtocol?
    var userChatRecords = [UserChatRecord]()
    var roomID:Int?
//    var meetData : RoomModel?
//    var socketData : SendMessageRoomModel?
    
    //MARK: - init
    init(view: MeetChatViewProtocol,
         router: MeetChatRouterProtocol,
         interactor: MeetChatInputInteractorProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        
    }
    
    
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    //MARK: - chat and users Chat
    
    /// respons get users rooms
    func getListChat(data: listUserChatModel) {
        self.userChatRecords.append(contentsOf: data.userChatRecords ?? [])
    }
    
    /// get user id
    func getUserID(index:Int)->Int{ // when click user in chat
        return self.userChatRecords[index].createdBy ?? 0
    }
       
    /// get number of messages in chat
    func messageCount()->Int{
        return self.userChatRecords.count
    }
    
    /// configuer cell caht
    func configuer(cell:ChatCellProtocol, index:Int){
        cell.index(index: index)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEE, MMM d, yyyy - h:mm a"
        let date = dateFormatter.date(from: userChatRecords[index].creationDate ?? "")
        dateFormatter.dateFormat = "h:mm a"
        cell.dateMessage(date: dateFormatter.string(from: date ?? Date()))
        cell.textMessage(txt: userChatRecords[index].message ?? "")
        cell.typeMessage(type: userChatRecords[index].typeID ?? 0 , url: userChatRecords[index].message ?? "", voice: userChatRecords[index].voiceData, localMediaUrl: userChatRecords[index].localMediaUrl)
        if userChatRecords[index].createdBy ?? 0 == Int(UserModel.shared.get_id()) {
            cell.userData(image: userChatRecords[index].createdImageURL ?? "" , myImage: true)
        }else{
            cell.userData(image: userChatRecords[index].createdImageURL ?? "" , myImage: false)
        }
    }

    /// get user image caht
    func getImagegURL(index:Int)->String{
        return self.userChatRecords[index].message ?? ""
    }
    
    /// to know this message from me or other users
    func isMyMessage(index:Int)->Bool{
        if userChatRecords[index].createdBy ?? 0 == Int(UserModel.shared.get_id()) {
            return true
        }else{
            return false
        }
    }
    
    /// get tyep of message ( image - text - video - record - ...... )
    func getTypeMessage(index:Int)->Int{
        return self.userChatRecords[index].typeID ?? 0
    }
    
    /// get message text
    func getMessage(index:Int)->String{
        return self.userChatRecords[index].message ?? ""
    }
    
    /// add new message in caht list
    func setNewMessage(obj:UserChatRecord){
        self.userChatRecords.insert(obj, at: 0)
    }
    
    ///  retuen get all messages
    func getAllMessage()-> [UserChatRecord]{
        return self.userChatRecords
    }
    
    func LeaveRoom() {
        let request = RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: self.roomID ),
                                     createdBy:  Int(UserModel.shared.get_id()) ?? 0)
        interactor?.leaveRoom(model: request)
    }
    
    func successLeaveRoom() {
        self.view?.LeaveRoomView()
        KeyCenter.ChannelID = ""
        KeyCenter.Token = ""
//        meetData = nil
    }
    
    func openExitGameDialog() {
        router?.openExitGame()
    }
    
   
}
