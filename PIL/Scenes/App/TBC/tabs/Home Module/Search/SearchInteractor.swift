//
//  SearchInteractor.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//  
//

import Foundation

class SearchInteractor: SearchInteractorInputProtocol {

    //MARK: - variables
    weak var presenter: SearchInteractorOutputProtocol?
    var error: ErrorProtocol?
    var categoriesWorker: CategoriesWorkerProtocol?
    var gamesWorker: GamesWorkerProtocol?
    
    func getCategories() {
        categoriesWorker?.getCategories(compilition: { [weak self] (result) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                self.presenter?.categoriesFeatchedSuccessfully(model: model)
                break
            
            case .failure(let error):
                self.error?.featching(error: error.localizedDescription)
                break
            }
        })
    }
    
    func getGames(model:SearchGameRequstModel){
        self.gamesWorker?.searchGames(model: model, compilition:   { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()
                }else{
                    if model.status ?? false{
                        // get response
                        
                        self.presenter?.getGamesList(model: model.data ?? [])
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                 break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
