//
//  SearchRouter.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//  
//

import Foundation
import UIKit

class SearchRouter: SearchRouterProtocol {

    weak var VC: SearchViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ExploreST, VC: .SearchVC)) as! SearchVC
        let interactor = SearchInteractor()
        let router = SearchRouter()
        let workerGame = GamesWorker()
        let presenter = SearchPresenter(view: view, router: router, interactor: interactor, error: view)
        let categoriesWorker = CategoriesWorker()
        view.presenter = presenter
        interactor.gamesWorker = workerGame
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.categoriesWorker = categoriesWorker
        router.VC = view
        return view
    }
    
    
    func openContest(gameID:String) {
//        let contest = ContestRouter.createModule(gameId:gameID) as! ContestVC
//        push(to: contest)
    }
    
    func push(to view: UIViewController) {
        if let vc = VC as? UIViewController{
            view.hidesBottomBarWhenPushed = true
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    
    
}
