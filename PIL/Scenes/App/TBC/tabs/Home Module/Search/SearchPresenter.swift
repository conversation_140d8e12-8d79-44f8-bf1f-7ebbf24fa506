//
//  SearchPresenter.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//  
//

import Foundation

class SearchPresenter: SearchPresenterProtocol, SearchInteractorOutputProtocol, ErrorProtocol {
    

    //MARK: - variables
    weak var view: SearchViewProtocol?
    var router: SearchRouterProtocol?
    var interactor: SearchInteractorInputProtocol?
    var error: ErrorProtocol?
    
    var contentSectionsCount = 1
    var categoryList = [CategoriesDataModel]()
    var gamesList = [Game]()
    
    //MARK: - init
    init(view: SearchViewProtocol,
         router: SearchRouterProtocol,
         interactor: SearchInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        gamesList.removeAll()
//        interactor?.getCategories()
    }
    
    func getScreenContentCount() -> Int {
        return contentSectionsCount
    }
    
    
    func selectCategoryt(at index: Int) {
        
    }
    
    //MARK: - games
    func searchRequest(textSearch:String){
//        Indicator.shared.showProgressView()
        gamesList.removeAll()
        GoogleAnalyticsHelper.shared.search_game(game_name: textSearch)

        self.interactor?.getGames(model: SearchGameRequstModel(language: app_lang, filter_text: textSearch,filter_field: "gameName"))
    }
    
    func getGamesList(model: [Game]) {
        gamesList.removeAll()
//        Indicator.shared.hideProgressView()
        self.gamesList.append(contentsOf: model)
        self.view?.reloadContentList()
 
    }
    
    func configure(cell: GamesCollectionCellViewDlegate) {
        cell.setGames(list: gamesList)
    }
    
    func selectGame(at index: Int) {
        self.router?.openContest(gameID: "\(self.gamesList[index].id ?? 0)")
    }
    
    func isGamesListEmpty() -> Bool{
        return gamesList.isEmpty
    }
    
    //MARK: - interactor delegate functions
    func categoriesFeatchedSuccessfully(model: CategoriesModel) {
        categoryList = model.Categories ?? []
        view?.reloadContentList()
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    func logout(){
        UserModel.shared.logOut()
    }
}
