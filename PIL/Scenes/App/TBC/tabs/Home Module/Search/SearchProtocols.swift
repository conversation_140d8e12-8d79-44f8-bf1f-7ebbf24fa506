//
//  SearchProtocols.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//  
//

import Foundation

protocol SearchViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: SearchPresenterProtocol? { get set }
    func reloadContentList()
}

protocol SearchPresenterProtocol {
    // TODO: Declare presentation methods
    var view: SearchViewProtocol? { get set }
    func viewDidLoad()
    func getScreenContentCount() -> Int
        
    func configure(cell: GamesCollectionCellViewDlegate)
    func selectGame(at index: Int)
    func isGamesListEmpty() -> Bool
    func searchRequest(textSearch:String)
    func logout()
}

protocol SearchInteractorInputProtocol {
    var presenter: SearchInteractorOutputProtocol? { get set }
    func getCategories()
    func getGames(model:SearchGameRequstModel)
}

protocol SearchInteractorOutputProtocol: AnyObject {
    func categoriesFeatchedSuccessfully(model: CategoriesModel)
    func getGamesList(model:[Game])
}

protocol SearchRouterProtocol {
    func openContest(gameID:String)
}

