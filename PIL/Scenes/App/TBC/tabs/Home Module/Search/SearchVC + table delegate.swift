//
//  SearchVC + table delegate.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//

import UIKit

extension SearchVC: UITableViewDelegate, UITableViewDataSource{
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.row{
//        case 0: return 72
        case 0: return tableView.bounds.height
        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.getScreenContentCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch indexPath.row{

        case 0:
            tableView.isScrollEnabled = !(presenter?.isGamesListEmpty() ?? false)
            if presenter?.isGamesListEmpty() ?? false{
                let emptyGamesCell = tableView.dequeueReusableCell(withIdentifier: "EmptySearchCell", for: indexPath) as! EmptySearchCell
                return emptyGamesCell
            }else{
                let gamesCell = tableView.dequeueReusableCell(withIdentifier: "GamesCollectionCell", for: indexPath) as! GamesCollectionCell
                gamesCell.actionDelegate = self
                presenter?.configure(cell: gamesCell)
                return gamesCell
            }
        default: return UITableViewCell()
        }
    }
}




extension SearchVC: GamesCollectionCellActionDelegate{
    func selectGame(at index: Int) {
        presenter?.selectGame(at: index)
    }
    
}
