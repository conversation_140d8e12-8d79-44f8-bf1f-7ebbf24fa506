//
//  CategoryCollectionCell + collection delegate.swift
//  PIL
//
//  Created by <PERSON> on 10/2/21.
//

import UIKit

//extension CategoryCollectionCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
//
//    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
//        return CGSize(width: 76, height: 42)
//    }
//
//    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
//        return categories.count
//    }
//
//    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
//        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "CategoryCell", for: indexPath) as! CategoryCell
//        let obj = categories[indexPath.row]
//        cell.setCategory(title: obj.title_EN ?? "",
//                         img: obj.icon ?? "")
//        cell.setCategoryCell(source: .search)
//        return cell
//    }
//
//    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//        actionDelegate?.selectCategory(at: indexPath.item)
//    }
//}
