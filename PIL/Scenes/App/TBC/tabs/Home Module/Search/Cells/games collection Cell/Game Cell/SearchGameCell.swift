//
//  SearchGameCell.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//

import UIKit

protocol SearchGameCellViewDelegate{
    func setGame(name: String, img: String)
}

class SearchGameCell: UICollectionViewCell, SearchGameCellViewDelegate {

    
    //MARK: - outlets
    @IBOutlet weak var gameImg: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    
    //MARK: - functions

    func setGame(name: String, img: String) {
        gameImg.setIMG(img: img)
        gameName.text = name
    }
}
