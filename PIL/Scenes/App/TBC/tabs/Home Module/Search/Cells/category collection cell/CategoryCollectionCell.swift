//
//  CategoryCollectionCell.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//

import UIKit

//protocol CategoryCollectionCellViewDelegate{
//    func setCategories(list: [CategoriesDataModel])
//}
//
//protocol CategoryCollectionCellActionDelegate{
//    func selectCategory(at index: Int)
//}
//
//class CategoryCollectionCell: UITableViewCell, CategoryCollectionCellViewDelegate {
//    
//    //MARK: - variables
//    var categories = [CategoriesDataModel]()
//    var actionDelegate: CategoryCollectionCellActionDelegate?
//    
//    //MARK: - outlets
//    @IBOutlet weak var categoryCollection: UICollectionView!
//    
//    //MARK: - didLoad
//    override func awakeFromNib() {
//        super.awakeFromNib()
////        categoryCollection.delegate = self
////        categoryCollection.dataSource = self
////        categoryCollection.register(UINib(nibName: "CategoryCell", bundle: Bundle.main), forCellWithReuseIdentifier: "CategoryCell")
//    }
//    
//    //MARK: - functions
//    func setCategories(list: [CategoriesDataModel]) {
//        categories = list
//        categoryCollection.reloadData()
//    }
//    
//}
