//
//  GamesCollectionCell.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//

import UIKit

protocol GamesCollectionCellViewDlegate {
    func setGames(list: [Game])
}

protocol GamesCollectionCellActionDelegate{
    func selectGame(at index: Int)
}


class GamesCollectionCell: UITableViewCell, GamesCollectionCellViewDlegate {
    
    //MARK: - variables
    var games = [Game]()
    var actionDelegate: GamesCollectionCellActionDelegate?
    
    //MARK: - outlets
    @IBOutlet weak var gamesCollection: UICollectionView!
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        gamesCollection.delegate = self
        gamesCollection.dataSource = self
        gamesCollection.register(UINib(nibName: "SearchGameCell", bundle: Bundle.main), forCellWithReuseIdentifier: "SearchGameCell")
    }
    
    //MARK: - functions
    func setGames(list: [Game]) {
        games = list
        gamesCollection.reloadData()
    }
    
}

//MARK: - GamesCollectionCell + collection deleagte
extension GamesCollectionCell: UICollectionViewDelegateFlowLayout, UICollectionViewDelegate, UICollectionViewDataSource{
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = collectionView.bounds.width-36
        return CGSize(width: width/3, height: 153)
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return games.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "SearchGameCell", for: indexPath) as! SearchGameCell
        cell.setGame(name: games[indexPath.item].gameName ?? "", img: games[indexPath.item].gameBackground ?? "")
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        actionDelegate?.selectGame(at: indexPath.item)
    }
}
