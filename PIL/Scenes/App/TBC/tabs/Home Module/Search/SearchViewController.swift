//
//  SearchViewController.swift
//  PIL
//
//  Created by <PERSON> on 9/9/21.
//  
//

import Foundation
import UIKit

class SearchVC: UIViewController, SearchViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: SearchPresenterProtocol?
    
    var backItemButton = Components().backNavButton
    
    //MARK: - outlets
    @IBOutlet weak var searchTF: UITextField!
    @IBOutlet weak var contentTableList: UITableView!
    @IBOutlet var viewSearch: UIView!
    

    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: true)
        viewSearch.shadow()
        setUpUI()
        setXIBs()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
        createNaviagtion()

    }
    
    //MARK: - functions
    func createNaviagtion(){
        navigationController?.createClearNaviagtion()
         
        navigationItem.leftBarButtonItems = [UIBarButtonItem(customView: backItemButton)]
        backItemButton.removeTarget(nil, action: nil, for: .allEvents)
        backItemButton.addTarget(self, action: #selector(SearchVC.backAction(_:)), for: .touchUpInside)
    }
    
    func setUpUI(){
        searchTF.setDirection()
        
        searchTF.attributedPlaceholder = NSAttributedString(string: searchTF.placeholder!,
                                     attributes: [NSAttributedString.Key.foregroundColor: #colorLiteral(red: 0.6745098039, green: 0.6745098039, blue: 0.6745098039, alpha: 0.5)])
    }
    
    func setXIBs(){
        contentTableList.register(UINib(nibName: "GamesCollectionCell", bundle: Bundle.main), forCellReuseIdentifier: "GamesCollectionCell")
        
        contentTableList.register(UINib(nibName: "EmptySearchCell", bundle: Bundle.main), forCellReuseIdentifier: "EmptySearchCell")
        
    }
    
    
    //MARK: - deleagte functions
    func reloadContentList() {
        contentTableList.reloadData()
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
    //MARK: - actions
    @objc func backAction(_ sender : UIButton){
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func searchTextChangedAction(_ sender: UITextField) {
        print("Text search is",sender.text ?? "")
        self.presenter?.searchRequest(textSearch: sender.text ?? "")
    }
    
}
