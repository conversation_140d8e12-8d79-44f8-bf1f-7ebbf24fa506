//
//  EventCell.swift
//  PIL
//
//  Created by sameh mohammed on 05/02/2023.
//

import UIKit
protocol EventsCellProtocol{
    func contestData(image:String , title:String , prize:String , users:String , time:Int)
    func index(index:Int)
}

class MainEventsCell: UITableViewCell ,EventsCellProtocol{

    
    var index = 0
    var action:ContestChallengeCellAction?

    @IBOutlet weak var imgEvent: UIImageView!
    @IBOutlet weak var titleEventLable: UILabel!
    @IBOutlet weak var numberJoinLable: UILabel!
    
    @IBOutlet weak var stackTime: UIStackView!
    @IBOutlet weak var eventStartLable: UILabel!
    
    @IBOutlet weak var hoursLabel: UILabel!
    @IBOutlet weak var minutesLabel: UILabel!
    @IBOutlet weak var secondsLabel: UILabel!
    
    
    override func prepareForReuse() {
        hoursLabel.text = "0" + "h".localized
        minutesLabel.text = "0" + "m".localized
        secondsLabel.text = "0" + "s".localized
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        self.selectionStyle = .none
    }
 
    func contestData(image: String, title: String, prize: String, users: String,time: Int) {
        self.imgEvent.sd_setImage(with: URL(string: image), placeholderImage: placeHolderImage)
        self.titleEventLable.text = title
        self.numberJoinLable.text = users
        
        if time > 0 {
            calculateCountDown(time)
            stackTime.isHidden = false
            eventStartLable.isHidden = true
        }else{
            stackTime.isHidden = true
            eventStartLable.isHidden = false
        }
    }
    
    private func calculateCountDown(_ interval: Int){
        let counter = interval.secondsToHoursMinutesSeconds()
        if counter.0 > 24{
            let days = Int(counter.0 / 24)
            let hours = counter.0 - (Int(counter.0 / 24) * 24)
            let minutes = counter.1
            hoursLabel.text = "\(days)" + "d".localized
            minutesLabel.text = "\(hours)" + "h".localized
            secondsLabel.text = "\(minutes)" + "m".localized
        }else{
            hoursLabel.text = "\(counter.0)" + "h".localized
            minutesLabel.text = "\(counter.1)" + "m".localized
            secondsLabel.text = "\(counter.2)" + "s".localized
        }
    }
    
    func index(index: Int) {
        self.index = index
    }
    
    @IBAction func playBTN(_ sender: Any) {
        self.action?.play(index: index)
    }
    
}
