//
//  MainEventsRouter.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation

class MainEventsRouter:MainEventsRouterProtocl , ReloadGetLivesAfterWatchAdsProtocol{
    
    var  VC:MainEventsViewProtocol?
    
    static func createModule()-> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .Event, VC: .MainEventsVC)) as! MainEventsVC

        let router = MainEventsRouter()
        let interactor = MainEventsInteractor()
        let presenter = MainEventsPresenter(view: view, router: router, interactor: interactor, error: view)
        let workerReward = DailyRewardWorker()
        let walletWorker = WalletWorker()
        let workerContest = ChallengesGameWorker()

        view.preseter = presenter
        router.VC = view
        interactor.presenter = presenter
        interactor.walletWorker = walletWorker
        interactor.workerReward = workerReward
        interactor.workerContest = workerContest
        presenter.interactor = interactor
        presenter.router = router
        presenter.view = view
        
        return view
        
    }

    func openMenu() {
    
    }
    
    func openLives(){
        let lives = YouWantMoreLivesRouter.createModule(type: .live) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
        VC?.ReloadLives()
    }
    
    func push(to view: UIViewController) {
        if let vc = VC as? UIViewController{
            view.hidesBottomBarWhenPushed = true
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    
    
    func openReward(count:Int){
        let reward = DailyRewardRouter.createModule(count:count) as! DailyRewardVC
        if let vc = VC as? UIViewController{
            vc.present(reward, animated: false) {}
        }
    }
    
    
    func openContestDetails(gameID:String , contestID:String, endDate: String){
        let challenge = ContestDetailsRouter.createModule(contestID: contestID, gameID: gameID, endDate: endDate) as! ContestDetailsVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(challenge, animated: true)
        }
    }
    
    
    func openGame(gameID: String) {
        let challenge = ContestChallengesRouter.createModule(gameID: gameID) as! ContestChallengesVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(challenge, animated: true)
        }
    }
    
}
