//
//  MainEventsPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation
import Alamofire
import CoreData
import Zip

class MainEventsPresenter: MainEventsPresenterProtocol , MainEventsInteractorOutputProtocol{
    
    var view:MainEventsViewProtocol?
    var router:MainEventsRouterProtocl?
    var interactor:MainEventsInteractorInputProtocol?
    var error:ErrorProtocol?
    var livesModel: WalletWorkerData?
    var gameID = String()
    var contest =  [ChallengesGamesData]()
    var localGamesArray = [LocalGames]()
    var localGames = [NSManagedObject]()
    var isUpdate = false
    var selectedContest = ChallengesGamesData()
    var gameDetails:GameDataModel?
    var skillsPlayer = 0
    private var timer: Timer?

    init(view:MainEventsViewProtocol,
         router:MainEventsRouterProtocl,
         interactor:MainEventsInteractorInputProtocol,
         error:ErrorProtocol
    ) {
        self.view =  view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    func viewDidLoad(){
        self.contest.removeAll()
        self.interactor?.sendWalletRequest()
        self.interactor?.getChallengesGame(gameId: self.gameID)
        self.interactor?.getSkills(playerID: UserModel.shared.get_id())
    }
    
    func getWalletInfo(obj: WalletWorkerData) {
        livesModel = obj
        self.view?.updateCoins(with: obj.playerToken ?? 0)
        UserModel.shared.setCountLives(count: self.livesModel?.lives ?? 0)
        UserModel.shared.setPlayerTokens(count: self.livesModel?.playerToken ?? 0)
        self.view?.setHome(lives: self.livesModel?.lives ?? 0)

    }
    
    func getSkillsPlayer(skills: Int) {
        self.skillsPlayer = skills
        print("Player skills is",skills)
    }
    
    func GetLives(){
        interactor?.sendWalletRequest()
    }
    
    func openMenu() {
        router?.openMenu()
    }
    
    func getDailyReward(model:DailyRewarDataClass){
        print("reward",model.reward ?? 0)
        if model.deserve == true{
            self.router?.openReward(count: model.reward ?? 0)
        }
    }
    
    func openLives() {
        self.router?.openLives()
    }
    
    
    
    // get contest game
    func getChallenges(model: [ChallengesGamesData]) {
        print("----->",model.count)
        for i in model{
            let deposit = i.entryfeeDeposit ?? 0
            let entery = i.entryfeeBouns ?? 0
            if deposit+entery != 0 {
                i.setCountDownInSeconds()
                self.contest.append(i)
            }
        }
        timer?.invalidate()
        timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(fire), userInfo: nil, repeats: true)
        self.view?.reloadData()
    }
    
    @objc func fire(){
        guard !contest.filter({ return $0.timeInterval! > 0 }).isEmpty else{
            timer?.invalidate()
            return
        }
        contest.forEach{
            $0.timeInterval! -= 1
            self.view?.reloadData()
        }
    }
    
    // select the contest
    func selectcontest(index:Int){
        self.selectedContest = contest[index]
        self.gameDetails = contest[index].game
        self.interactor?.ValidateContest(contestID: "\(contest[index].id ?? 0)")
    }
    
    func configuerEvents(cell:EventsCellProtocol , index:Int){
        let data = self.contest[index]
        cell.index(index: index)
        cell.contestData(image: data.game?.icons ?? "" ,
                         title: data.game?.gameName ?? "",
                         prize: "\(data.prize ?? 0)" ,
                         users: "\(data.joinedPlayers ?? 0)",
                         time: data.timeInterval!)
    }
    
    func challengesCount()->Int{
        return self.contest.count
    }
    
    func getContestID(index:Int)->Int{
        return self.contest[index].id ?? 0
    }
    
    
    func featching(error: String) {
        print("ERROR")
    }
     
    //MARK: - chek Validate Contest
    func successValidateContest() {
        self.DownloadsFIle(isUpdate: false)
    }
    
    
    // router
    func openContestDetails(index:Int){
        router?.openContestDetails(gameID: "\(self.contest[index].game?.id ?? 0)", contestID: "\(self.contest[index].id ?? 0)", endDate: self.contest[index].endDateTime ?? "")
    }
    
    func openContest(index:Int){
        router?.openGame(gameID: "\(self.contest[index].game?.id ?? 0)")
    }
}




//MARK: - open game
extension MainEventsPresenter{
    
    func DownloadsFIle(isUpdate:Bool){
        //        self.view?.startLoading()
        self.isUpdate = isUpdate
        if isUpdate == true{
            self.removeOldVersion()
        }
        self.interactor?.createFolderInDownloadsDirectory(name: self.gameDetails?.sceneName ?? "" , files:gameDetails?.assetsbundle ?? "" )
    }
    
    //MARK: - download zip file if not exist folder step 2
    func FileDownloaded(pathFile:String , destination:String){
        print("File Path is",pathFile , destination)
        let sourceURL = URL(string:pathFile)!
        let destinationURL = URL(string: "\(destination)")!
        do {
            
            try Zip.unzipFile(sourceURL, destination: destinationURL, overwrite: true, password: nil, progress: { (progress) -> () in
                print("--->",progress)
                if progress == 1{
                    self.removeFile(filePath: sourceURL)
                    
                    //send request model to open game
                    
                    self.save(name: self.gameDetails?.gameName ?? "" ,
                              sceneName: self.gameDetails?.sceneName ?? "",
                              image: self.gameDetails?.icons ?? "",
                              version: self.gameDetails?.version ?? "",
                              filePath: "\(destination)/\(self.gameDetails?.sceneName ?? "")",
                              gameID: self.gameID)
                    
                    if self.isUpdate == false{
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            self.OpenGame( path: "\(destination)/\(self.gameDetails?.sceneName ?? "")")
                        }
                    }else{
//                        self.view?.stopLoading()
                    }
                    
                    
                }
            }) // Unzip
            
        } catch let error {
            print("Extraction of ZIP archive failed with error:\(error)")
        }
    }
    
    func progressFileDownload(progress: Double) {
//        self.view?.changeProgress(progress: progress)
    }
    
    // if file is Exist
    func FilesIFExist(path:String){
        print("File Path is",path)
        DispatchQueue.main.async {
            self.OpenGame(path:"\(path)")
        }
        //        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
        //            self.OpenGame(PrizeTable: [] , path: "\(path)" , SceneName:self.gameDetails?.data?.sceneName ?? "")
        //        }
        
    }
    
    //MARK:- remove zip file after download
    func removeFile(filePath:URL){
        let fileManager = FileManager.default
        do{
            try  fileManager.removeItem(at: filePath)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    
    
    func progressGameDownload(name: String, file: String) {
        self.view?.progressGameDownload(name: name, file: file, image: self.gameDetails?.icons ?? "" )
    }
    
    //MARK:- Open Game SDK
    func OpenGame(path:String){
//        self.view?.stopLoading()
        
        let request = [
            "GameId": gameID,
            "GameName": self.gameDetails?.sceneName ?? "",
            "SceneName": self.gameDetails?.sceneName ?? "",
            "EntryFee": 0,// total deposit and bouns
            "DepositEntryFee":0 ,// deposit
            "BonusEntryFee": 0 ,//bouns
            "MaximimAttempt": self.selectedContest.maximumAttempt ?? 0,
            "NumberOfWinners": selectedContest.noOfWinners ?? 0,
            "BattleScoreSyncInSec":" 1.0",
            "PlayerSkill":self.skillsPlayer,
            "PlayerSkillRange":self.selectedContest.skillRange ?? 0,
            "MaximumPlayer":selectedContest.maximumPlayers ?? 0,
            "Landscape":  getLandscape() ,
            "MaxMatchMakingRetries": selectedContest.knockout_tries ?? 0,
            "PrizeTable": getDicPrize(),
            "AuthToken": "Bearer \(UserModel.shared.get_token())",
            "MaxPauseDuration": 30,
            "ConnectionRetryTimeout": 30,
            "IsInDebugMode": false,
            "PingInterval": 10,
            "MaxPongDelay": 20,
            "AppVersionCode": "1",//Build number APP
            "AppVersionName": "1.0.20",//version number APP
            "Profile": [
                "id": UserModel.shared.get_id(),
                "phone": UserModel.shared.get_phone(),
                "name": UserModel.shared.get_username(),
                "username": UserModel.shared.get_username(),
                "image": UserModel.shared.get_image(),
                "token": "Bearer \(UserModel.shared.get_token())",
                "email":UserModel.shared.get_email(),
                "bio":UserModel.shared.get_bio(),
                "birthdate":UserModel.shared.get_birthdate(),
                "cashWin":UserModel.shared.get_cashWin(),
                "followers":UserModel.shared.get_followers(),
                "following":UserModel.shared.get_following(),
                "gender":UserModel.shared.get_gender(),
                "helpdeskId":UserModel.shared.get_HelpdeskId(),
                "level": Int(UserModel.shared.get_level()) ?? 0 ,
                "operator":UserModel.shared.getOperator(),
                "type":UserModel.shared.get_loginAsGuestTypeGame(),
            ] as [String : Any],
            "Host": URls().SmartFoxHost,
            "Zone": "PIL",
            "LobbyId": "",
            "ContestId": "\(selectedContest.id ?? 0)",
            "ContestName": selectedContest.contestType ?? "",
            "ContestType": selectedContest.contestType ?? "",
            "EndDateTime":EndTimeContest(),
            "StartDateTime":StartTimeContest(),
            "IsTournament": isTournament(),
            "EnableLog": true,
            "DeveloperConsoleURL": URls().BASE_URL_INDIA,
            "UserURL": URls().BASE_URL,
            "AssetBundleURL": "\(path)",
            "Language": UserModel.shared.getLanguage(),
            "GameSoundOn":UserModel.shared.getSound(),
            "IsKnockout": self.selectedContest.is_knockout ?? false
        ] as [String : Any]
        
        let jsonData = try? JSONSerialization.data(withJSONObject: request, options: [])
        let jsonString = String(data: jsonData!, encoding: .utf8)
        
        Unity.shared.show()
        Unity.shared.sendMessage("PILNativeInterface","SetGameConfig", "\( jsonString ?? "")")
        
        print("Game Config " , request)
        
        
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        
        GoogleAnalyticsHelper.shared.start_game(start_game_time: stringDate,
                                                game_name: self.gameDetails?.sceneName ?? "",
                                                event_id: "",
                                                game_entree_fee: "0",
                                                game_event_type: self.selectedContest.type ?? "",
                                                game_event_name: "")
    }
    
    
    
    func getDicPrize()->[[String:Any]]{
        var DicPrize = [[String:Any]]()
        for i in self.selectedContest.prizesDistribution ?? []{
            var dic =  [String:Any]()
            dic["prize"] = i.prize ?? 0
            dic["currency"] = i.currency ?? ""
            dic["rank"] = i.rank ?? 0
            DicPrize.append(dic)
        }
        return DicPrize
    }
    
    
    func getLandscape()->Bool{
        var Landscape = false
        if self.gameDetails?.gameOrientation ?? "" == "LandScape"{
            Landscape = true
        }else{
            Landscape = false
        }
        return Landscape
    }
    
    func isTournament()->Bool{
        if selectedContest.type ?? "" == "tournament" {
            return true
        }
        return false
    }
     
    // end time
    
    
    func StartTimeContest()->Int{
        let endTimeBattle = Int((selectedContest.startDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
     }
    
    func EndTimeContest()->Int{
        let endTimeBattle = Int((selectedContest.endDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
     }
}

extension MainEventsPresenter{
    
    //MARK: - save game in local
    
    func save(name: String , sceneName:String , image:String , version:String , filePath:String , gameID:String) {
      print("Game ID Local", gameID)
        guard let appDelegate =
                UIApplication.shared.delegate as? AppDelegate else {
            return
        }
      
      // 1
        let managedContext =   appDelegate.persistentContainer.viewContext
      
      // 2
      let entity =  NSEntityDescription.entity(forEntityName: "Games",
                                   in: managedContext)!
      
      let person = NSManagedObject(entity: entity,
                                   insertInto: managedContext)
      
      // 3
        person.setValue(name, forKeyPath: "name")
        person.setValue(sceneName, forKeyPath: "sceneName")
        person.setValue(image, forKeyPath: "image")
        person.setValue(version, forKeyPath: "version")
        person.setValue(filePath, forKey: "filePath")
        person.setValue(gameID, forKey: "gameID")
      // 4
      do {
        try managedContext.save()
          print("Game save successfully in local storage")
      } catch let error as NSError {
        print("Could not save. \(error), \(error.userInfo)")
      }
    }
  
    
    func getLocalGames(){
        self.localGamesArray.removeAll()
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {  return }
        let managedContext = appDelegate.persistentContainer.viewContext
        let fetchRequest =    NSFetchRequest<NSManagedObject>(entityName: "Games")
        
        do {
            localGames = try managedContext.fetch(fetchRequest)
            
            for i in localGames{
                let gameName = i.value(forKey: "name") as? String
                let sceneName = i.value(forKey: "sceneName") as? String
                let image = i.value(forKey: "image") as? String
                let version = i.value(forKey: "version") as? String
                let filePath = i.value(forKey: "filePath") as? String
                let gameID = i.value(forKey: "gameID") as? String
                var size = "10"
                let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let path = "\(documentsUrl)\(sceneName ?? "")".replacingOccurrences(of: "file://",with: "")
                
                //                print("Size file is" , filePath?.sizeto)
                let url = NSURL.fileURL(withPath: path)
                print("the url = \(url)")
                do {
                    
                    if let sizeOnDisk = try url.sizeOnDisk() {
                        size = sizeOnDisk
                        print("Size:", sizeOnDisk) // Size: 3.15 GB on disk
                    }
                } catch {
                    print(error)
                }
                
                let game = LocalGames(name: gameName, sceneName: sceneName, image: image, version: version , filePath:filePath,gameID: gameID,sizeGame: size)
                localGamesArray.append(game)
            }
            checkVersion()
            
        } catch let error as NSError {
            print("Could not fetch. \(error), \(error.userInfo)")
        }
    }
    
    func checkVersion(){
        for i in self.localGamesArray{
            if i.sceneName ?? "" == self.gameDetails?.sceneName ?? "" {
                if i.version != self.gameDetails?.version ?? "" {
                    print("Alert  update version to \(self.gameDetails?.version ?? "") - \(i.sceneName ?? "")")
                    self.view?.updateGameVersion(message: "New version V\(i.version ?? "")")
                }else{
                    print("exist version is \(self.gameDetails?.version ?? "") - \(i.sceneName ?? "")")
                }
            }
        }
    }
    
    func removeOldVersion(){
        //remove from core data (Local)
        let index = localGamesArray.firstIndex{ $0.sceneName == self.gameDetails?.sceneName ?? "" } ?? 0
        let managedContext = (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext
        let note = localGames[index]
        managedContext.delete(note)
        do {
            try managedContext.save()
        } catch let error as NSError {
            print("Error While Deleting Note: \(error.userInfo)")
        }
        
        //Delete Game Folder
        let sceneName = localGamesArray[index].sceneName ?? ""
        let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let path = "\(documentsUrl)\(sceneName)".replacingOccurrences(of: "file://",with: "")
        
        let fileManager = FileManager()
        do{
            try  fileManager.removeItem(atPath: path)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    
    
    
}
