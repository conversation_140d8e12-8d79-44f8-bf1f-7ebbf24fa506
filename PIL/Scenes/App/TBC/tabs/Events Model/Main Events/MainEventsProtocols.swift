//
//  MainEventsProtocols.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation

protocol MainEventsViewProtocol{
    var preseter:MainEventsPresenterProtocol? {get set}
    func setHome(lives: Int)
    func updateCoins(with coins: Int)
    func ReloadLives()
    func reloadData()

    func updateGameVersion(message:String)
    func progressGameDownload(name:String,file:String, image:String)//new design
}

protocol MainEventsPresenterProtocol{
    var view:MainEventsViewProtocol? {get set}
    func GetLives()
    func configuerEvents(cell:EventsCellProtocol , index:Int)
    func challengesCount()->Int
    func openMenu()
    func openLives()
    func openContest(index:Int)
    func viewDidLoad()

    
    func selectcontest(index:Int)
    func DownloadsFIle(isUpdate:Bool)
    func FileDownloaded(pathFile:String , destination:String)
    func openContestDetails(index:Int)

}

protocol MainEventsInteractorInputProtocol{
    var presenter:MainEventsInteractorOutputProtocol? { get set}
    func sendWalletRequest()
    func getDailyReward()
    
    func getChallengesGame(gameId:String)
    func createFolderInDownloadsDirectory(name:String, files:String)
    func ValidateContest(contestID:String)
    func getSkills(playerID:String)
}

protocol MainEventsInteractorOutputProtocol{
    func getWalletInfo(obj:WalletWorkerData)
    func getDailyReward(model:DailyRewarDataClass)
    
    func getChallenges(model:[ChallengesGamesData])

    func FileDownloaded(pathFile:String , destination:String)
    func progressFileDownload(progress:Double)
    func progressGameDownload(name:String,file:String)//new design
    func FilesIFExist(path:String)
    func successValidateContest()
    func getSkillsPlayer(skills:Int)
}

protocol MainEventsRouterProtocl{
    func openMenu()
    func openLives()
    func openReward(count:Int)
    func openContestDetails(gameID:String , contestID:String, endDate: String)
    func openGame(gameID:String)
}
