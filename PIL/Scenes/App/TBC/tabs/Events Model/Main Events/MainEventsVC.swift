//
//  MainEventsVC.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import UIKit

class MainEventsVC: UIViewController ,MainEventsViewProtocol , ErrorProtocol{

    //MARK: - outlet
    @IBOutlet weak var eventsTableView: UITableView!
    @IBOutlet weak var livesCount: UILabel!
//    @IBOutlet weak var coinsCount: UILabel!
    @IBOutlet weak var StackNoData: UIStackView!
    @IBOutlet weak var imageNoData: UIImageView!
    
    //MARK: - variable
    var preseter: MainEventsPresenterProtocol?

    //MARK: - view will apper
    override func viewDidLoad() {
        super.viewDidLoad()
        setXIB()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        preseter?.viewDidLoad()
        self.navigationController?.navigationBar.isHidden = true
        self.tabBarController?.tabBar.isHidden = false
        
        self.StackNoData.isHidden = true
        self.imageNoData.isHidden = true
    }
    
    
    //MARK: - function
    func setXIB(){
        eventsTableView.register(UINib(nibName: "MainEventsCell", bundle: nil), forCellReuseIdentifier: "MainEventsCell")
    }
    

      
    //MARK: - action
    @IBAction func ShowLivesBTN(_ sender: Any) {
        self.preseter?.openLives()

    }
    
    @IBAction func openMenuBTN(_ sender: Any) {
        self.preseter?.openMenu()

    }
    
    @IBAction func swipeAction(_ sender: UISwipeGestureRecognizer) {
        preseter?.openMenu()
    }
    
    
    
    //MARK: - delegate
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    
    func ReloadLives(){
        preseter?.GetLives()
    }
    
    func reloadData() {
        self.eventsTableView.reloadData()
        
        if preseter?.challengesCount() ?? 0 == 0 {
            self.StackNoData.isHidden = false
            self.imageNoData.isHidden = false
        }
    }
    func setHome(lives: Int) {
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
            livesCount.attributedText = imageString
        }else{
            livesCount.text = "\(lives)"
        }
    }
    
    func updateCoins(with coins: Int) {
//        coinsCount.text = "\(coins)"
    }
    
    func updateGameVersion(message: String) {
        self.showAlertUpdateGame(title: "Alert".localized, msg: message) {
            print("Update")
            self.preseter?.DownloadsFIle(isUpdate: true)
        }
    }
    
    func progressGameDownload(name: String, file: String, image: String) {
        let progress = UIStoryboard.init(name: "DownloadGame", bundle: nil).instantiateViewController(withIdentifier: "DownloadGameVC") as! DownloadGameVC
        progress.nameGame = name
        progress.imageURL = image
        progress.fileURL = file
        progress.action = self
        self.present(progress, animated: false, completion: nil)
    }
    
}


extension MainEventsVC:DownloadGamFinished{
    func DownloadGamFinished(Path: String) {
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "--->",Path)
        self.preseter?.FileDownloaded(pathFile: Path, destination: newFolderURL.path)
 
    }
    
}


extension MainEventsVC:UITableViewDataSource , UITableViewDelegate , ContestChallengeCellAction{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return preseter?.challengesCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = self.eventsTableView.dequeueReusableCell(withIdentifier: "MainEventsCell", for: indexPath) as! MainEventsCell
        preseter?.configuerEvents(cell: cell, index: indexPath.row)
        cell.action = self
        return cell
    }
    
    func play(index:Int){
        self.preseter?.selectcontest(index: index)
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        self.preseter?.openContest(index: indexPath.row)
        preseter?.openContestDetails(index: indexPath.row)
    }
    
}
