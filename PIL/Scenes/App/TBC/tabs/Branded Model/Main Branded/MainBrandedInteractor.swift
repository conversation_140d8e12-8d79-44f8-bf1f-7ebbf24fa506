//
//  MainBrandedInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation

class MainBrandedInteractor:MainBrandedInteractorInputProtocol{
    var presenter: MainBrandedInteractorOutputProtocol?
    var walletWorker: WalletWorkerProtocol?
    var error: ErrorProtocol?

    //MARK: - sendWalletRequest
    /// - Parameters:
    ///   - userID: String
    ///   - completion: sendWalletRequest call back completion
    func sendWalletRequest() {
        Indicator.shared.showProgressView()
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            Indicator.shared.hideProgressView()
            
            switch result{
            case .success(let model):
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.getWalletInfo(obj: model)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
}
