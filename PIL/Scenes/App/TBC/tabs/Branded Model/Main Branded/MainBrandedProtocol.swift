//
//  MainBrandedProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation
protocol MainBrandedViewProtocol{
    var preseter:MainBrandedPresenterProtocol? {get set}
    func setHome(lives: Int)
    func ReloadLives()
}

protocol MainBrandedPresenterProtocol{
    var view:MainBrandedViewProtocol? {get set}
    func GetLives()
    func openLives()
    func viewDidLoad()
    func onTapRewardsButton()
}

protocol MainBrandedInteractorInputProtocol{
    var presenter:MainBrandedInteractorOutputProtocol? { get set}
    func sendWalletRequest()

}

protocol MainBrandedInteractorOutputProtocol{
    func getWalletInfo(obj:WalletWorkerData)

    
}

protocol MainBrandedRouterProtocl{
    func openLives()
    func toViewRewards()
}
