//
//  MainBrandedPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation
class MainBrandedPresenter: MainBrandedPresenterProtocol , MainBrandedInteractorOutputProtocol{
    
    var view:MainBrandedViewProtocol?
    var router:MainBrandedRouterProtocl?
    var interactor:MainBrandedInteractorInputProtocol?
    var error:ErrorProtocol?
    var livesModel: WalletWorkerData?

    init(view:MainBrandedViewProtocol,
         router:MainBrandedRouterProtocl,
         interactor:MainBrandedInteractorInputProtocol,
         error:ErrorProtocol
    ) {
        self.view =  view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    
    func viewDidLoad(){
        self.interactor?.sendWalletRequest()
    }
    
    func getWalletInfo(obj: WalletWorkerData) {
        livesModel = obj
        UserModel.shared.setCountLives(count: self.livesModel?.lives ?? 0)
        UserModel.shared.setPlayerTokens(count: self.livesModel?.playerToken ?? 0)
        self.view?.setHome(lives: self.livesModel?.lives ?? 0)

    }
    
    
    func GetLives(){
        interactor?.sendWalletRequest()
    }
    
    func openLives() {
        self.router?.openLives()
    }
    
    func onTapRewardsButton() {
        router?.toViewRewards()
    }
    
}
