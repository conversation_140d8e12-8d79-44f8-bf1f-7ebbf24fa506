//
//  MainBrandedVC.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import UIKit

class MainBrandedVC: UIViewController ,MainBrandedViewProtocol , ErrorProtocol{

    //MARK: - outlet
    @IBOutlet weak var livesCount: UILabel!
    
    //MARK: - variable
    var preseter: MainBrandedPresenterProtocol?

    //MARK: - view life cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        
     }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        preseter?.viewDidLoad()
        self.navigationController?.navigationBar.isHidden = true
        self.tabBarController?.tabBar.isHidden = false
    }
    


    //MARK: - actions
    @IBAction func menuBTN(_ sender: Any) {
    }
    
    
    @IBAction func ShowLivesBTN(_ sender: Any) {
        self.preseter?.openLives()

    }
    
    @IBAction func rewardsButtonAction(_ sender: Any) {
        preseter?.onTapRewardsButton()
    }
    
    
    //MARK: - Delegate
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    
    
    func ReloadLives(){
        preseter?.GetLives()
    }
     
    func setHome(lives: Int) {
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
            livesCount.attributedText = imageString
        }else{
            livesCount.text = "\(lives)"
        }
    }

}
