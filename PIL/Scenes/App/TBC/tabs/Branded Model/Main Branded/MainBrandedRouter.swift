//
//   MainBrandedRouter.swift
//  PIL
//
//  Created by sameh mohammed on 25/01/2023.
//

import Foundation
import UIKit

class MainBrandedRouter: MainBrandedRouterProtocl, ReloadGetLivesAfterWatchAdsProtocol{
    var  VC: MainBrandedViewProtocol?
    
    static func createModule()-> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .Branded, VC: .MainBrandedVC)) as! MainBrandedVC

        let router =  MainBrandedRouter()
        let interactor = MainBrandedInteractor()
        let presenter = MainBrandedPresenter(view: view, router: router, interactor: interactor, error: view)
        let walletWorker = WalletWorker()

        view.preseter = presenter
        router.VC = view
        interactor.presenter = presenter
        interactor.walletWorker = walletWorker
        presenter.interactor = interactor
        presenter.router = router
        presenter.view = view
        
        return view
        
    }
    
    func openLives(){
        let lives = YouWantMoreLivesRouter.createModule(type: .live) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
        VC?.ReloadLives()
    }
    
    
    func push(to view: UIViewController) {
        if let vc = VC as? UIViewController{
            view.hidesBottomBarWhenPushed = true
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    
    func toViewRewards() {
        let rewardsVC = MissionRewardsVC.Router.createModule()
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(rewardsVC, animated: true)
        }
    }
    
}
