//
//  SignUpProtocols.swift
//  PIL
//
//  Created by <PERSON> on 8/30/21.
//  
//

import Foundation

protocol SignUpViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: SignUpPresenterProtocol? { get set }
    func setKey(num: String)
    func successfullyAdded()
}

protocol SignUpPresenterProtocol: AnyObject {
    // TODO: Declare presentation methods
    var view: SignUpViewProtocol? { get set }
    func viewDidLoad()
    func signUpWith(email: String, phone: String, referralCode: String)
    func setNumber(codeKey: DialNumbersDataModel)
    func selectDialKey()
    func openTermsAndPolicy()
    func onTapGoogleSignIn(_ view: UIViewController)
    func onTapAppleSignIn()
    func getVBerificationCode(with phone: String)
}

protocol SignUpInteractorInputProtocol {
    // TODO: Declare use case methods
    var presenter: SignUpInteractorOutputProtocol? { get set }
    var services: SocialAuthServices? { set get }
    var userWorker: UserWorker? { set get }
    func signUp(with mode: signUpRequestModel)
    func getLinkOfTerms()
    func signInWithGoogle(_ view: UIViewController)
    func signInWithApple()
    func setUserinFirebase(userID:String, model: UserChatModel)
    func setDevice(model: deviceRequestModel)
    func getCode(for phone: String, code: String)
}

protocol SignUpInteractorOutputProtocol: AnyObject {
    // TODO: Declare interactor output methods
    func signUpSuccessfully()
    func getTermsLinkSuccessfully(model: HelpModel)
    func successfullyLoggedIn(_ data: loginModel)
    func setDeviceSuccessfully(model: UserDeviceDataModel?)
    func generateCodeSuccessfully(code: String)
}

protocol SignUpRouterProtocol {
    func openPhoneCodeKeys(selected: DialNumbersDataModel?)
    func openTermsAndPolicy(title: String, link: String)
    func goToHome()
    func chooseAvatar()
    func goStartSub(_ delegate: StartSubDelegat)
    func openVerification(phoneKey: String, number: String, code: String)
}

