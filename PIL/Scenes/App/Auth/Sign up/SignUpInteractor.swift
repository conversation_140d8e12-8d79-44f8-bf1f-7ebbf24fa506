//
//  SignUpInteractor.swift
//  PIL
//
//  Created by <PERSON> on 8/30/21.
//  
//

import Foundation
import FirebaseAuth
import BranchSDK

class SignUpInteractor: SignUpInteractorInputProtocol, SocialAuthDelegate {
    //MARK: - variables
    weak var presenter: SignUpInteractorOutputProtocol?
    weak var error: ErrorProtocol?
    var authWorker: AuthWorkerProtocol?
    var helpWorker: HelpWorkerProtocol?
    var services: SocialAuthServices?
    var userWorker: UserWorker?
    /// signInWithGoogle
    func signInWithGoogle(_ view: UIViewController) {
        services?.delegate = self
        services?.signInWithGoogle(view)
    }
    /// signInWithApple
    func signInWithApple(){
        services?.delegate = self
        services?.signInWithApple()
    }
    //MARK: - getCode
    /// - Parameters:
    ///   - phone: String
    ///   - code: String
    ///   - completion: getCode call back completion
    func getCode(for phone: String, code: String) {
        authWorker?.getPhoneCode(for: phone, code: code, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status == true{
                        self.presenter?.generateCodeSuccessfully(code: model.message ?? "")
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }  
    //MARK: - signUp
    /// - Parameters:
    ///   - mode: signUpRequestModel
    ///   - completion: signUp call back completion
    func signUp(with mode: signUpRequestModel) {
        authWorker?.SignUp(model: mode, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        let currentDate = Date()
                        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")

                        GoogleAnalyticsHelper.shared.Register_success(Registeration_datetime: stringDate, Register_success: "true")
                        let event = BranchEvent.standardEvent(.completeRegistration)
                        event.customData = [
                            "email": mode.email,
                            "phone": mode.phone
                        ]
                        event.logEvent()
                        self.presenter?.signUpSuccessfully()
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - getLinkOfTerms
    /// - Parameters:
    ///   - model: PILHelpTopics.terms.requestModel
    ///   - completion: getLinkOfTerms call back completion
    func getLinkOfTerms() {
        helpWorker?.getLink(model: PILHelpTopics.terms.requestModel, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getTermsLinkSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - setDevice
    /// - Parameters:
    ///   - model: deviceRequestModel
    ///   - completion: setDevice call back completion
    func setDevice(model: deviceRequestModel) {
        authWorker?.setDevice(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.setDeviceSuccessfully(model: model.data)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - setUserinFirebase
    /// - Parameters:
    ///   - userID: String
    ///   - model: UserChatModel
    ///   - completion: setUserinFirebase call back completion
    func setUserinFirebase(userID: String, model: UserChatModel) {
        userWorker?.uploadChatUser(userID: userID, model: model, compilition: { (Status, Error) in
            if let error = Error{
                print(error)
            }else{
                print("Success")
            }
        })
    }
    //MARK: - didCompleteSignInToServers
    /// - Parameters:
    ///   - response: loginModel
    ///   - error: Error
    ///   - completion: didCompleteSignInToServers call back completion
    func didCompleteSignInToServers(_ response: loginModel?, _ error: Error?) {
        guard let response = response,
        200...299 ~= response.code ?? 200
        else {
            self.error?.featching(error: response?.message ?? "Something went wrong, please try again later")
            return
        }
        guard let user = response.user else { return }
        self.presenter?.successfullyLoggedIn(response)
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        GoogleAnalyticsHelper.shared.Login_success(login_datetime: stringDate, login_success: "true")
        GoogleAnalyticsHelper.shared.trackLoginEvent(user: user)
        
        UserModel.shared.loginAsGuest(status: false)
    }
    /// didCompleteSignInWithCredentials
    func didCompleteSignInWithCredentials(_ result: AuthDataResult?, _ error: (any Error)?) {
        
    }
    
}
