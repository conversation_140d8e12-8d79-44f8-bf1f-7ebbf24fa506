//
//  SignUpViewController.swift
//  PIL
//
//  Created by <PERSON> on 8/30/21.
//  
//

import Foundation
import UIKit

class SignUpVC: UIViewController, SignUpViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: SignUpPresenterProtocol?
    var termsAgree = false
    

    //MARK: - outlets
    @IBOutlet weak var backBtn: UIButton!
    @IBOutlet weak var codeNumber: UILabel!
    @IBOutlet weak var emailTFView: UIView!
    @IBOutlet weak var emailTF: UITextField!
    @IBOutlet weak var numberTFView: UIView!
    @IBOutlet weak var numberTF: UITextField!
    @IBOutlet weak var TermsAndConditionsBtn: UIButton!
    @IBOutlet weak var confirmBtn: UIButton!
    @IBOutlet weak var checkBoxBtn: UIButton!
    @IBOutlet weak var referralCodeView: UIView!
    @IBOutlet weak var referralCodeTF: UITextField!
    
    //MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        endTap()
    }
    //MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
        setUpUI()
    }
    
    //MARK: - functions
    func setUpUI(){
        if app_lang == "ar"{
            self.TermsAndConditionsBtn.contentHorizontalAlignment = .right
        }else{
            self.TermsAndConditionsBtn.contentHorizontalAlignment = .left
        }
        
        backBtn.setBackArrow()
        TermsAndConditionsBtn.underline()
        emailTF.setDirection()
        numberTF.setDirection()
        referralCodeTF.setDirection()
        emailTF.keyboardType = .emailAddress
        numberTF.keyboardType = .asciiCapableNumberPad

     
    }
    
    func setCheckBtnStyle(){
        if termsAgree{
            checkBoxBtn.setImage(UIImage(named: "A_BU_CheckBox"), for: .normal)
        }else{
            checkBoxBtn.setImage(UIImage(named: "uncheck"), for: .normal)
        }
    }
    
    func setKey(num: String) {
        codeNumber.text = num
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }

        }
    }
    
    func successfullyAdded() {
//        showAlert(withTitle: false, msg: "User Added Successfully") {
//            self.navigationController?.popViewController(animated: true)
//        }
        
        let success = SetStoryBoard.controller(controller: Helper(Story: .ResetPhoneST, VC: .ResetSuccessVC)) as! ResetSuccessVC
        success.isSignUp = true
        self.navigationController?.pushViewController(success, animated: true)
    }
    
    //MARK: - actions
    @IBAction func backAction(_ sender: UIButton) {
        if CheckLoginVC.checkRegister == true{
            CheckLoginVC.checkRegister = false
            let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
            let Delegate = UIApplication.shared.delegate as! AppDelegate
            Delegate.window?.rootViewController = TBC
            
        }else{
            if (self.navigationController?.viewControllers.count ?? 0) < 2{
                self.dismiss(animated: true)
            }else{
                navigationController?.popViewController(animated: true)
            }
        }

    }
    
    @IBAction func openTermsAction(_ sender: UIButton) {
        presenter?.openTermsAndPolicy()
    }
    
    @IBAction func checkBoxAction(_ sender: UIButton) {
        termsAgree = !termsAgree
        setCheckBtnStyle()
    }
    
    @IBAction func chooseCodeNumber(_ sender: UIButton) {
        presenter?.selectDialKey()
    }
    
    @IBAction func confirmAction(_ sender: UIButton) {
        guard termsAgree else{
            showAlertWithTitle(title: "", msg: "You must agree to our terms and conditions first".localized, compilition: {})
            return
        }
        presenter?.signUpWith(email: (emailTF.text?.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!)!,
                              phone: numberTF.text!,
                              referralCode: referralCodeTF.text!)
    }
    
    @IBAction func googleSignInAction(){
        presenter?.onTapGoogleSignIn(self)
    }
    
    @IBAction func appleSignInAction(){
        presenter?.onTapAppleSignIn()
    }
}
