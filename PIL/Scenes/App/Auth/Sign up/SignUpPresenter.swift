//
//  SignUpPresenter.swift
//  PIL
//
//  Created by <PERSON> on 8/30/21.
//  
//

import Foundation

class SignUpPresenter: SignUpPresenterProtocol, SignUpInteractorOutputProtocol, ErrorProtocol {
    
     
    //MARK: - variables
    weak var view: SignUpViewProtocol?
    var router: SignUpRouterProtocol?
    var interactor: SignUpInteractorInputProtocol?
    weak var error: ErrorProtocol?
    var validator: AuthValidatorProtocol?
    var numberCodeKey: DialNumbersDataModel?
    var phone: String?
    var email: String?
    
    //MARK: - init
    init(view: SignUpViewProtocol,
         router: SignUpRouterProtocol,
         interactor: SignUpInteractorInputProtocol,
         error: ErrorProtocol,
         validator: AuthValidatorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
        self.validator = validator
    }
    
    //MARK: - functions
    func viewDidLoad(){
        setNumber(codeKey: DialNumbersDataModel(_id: "623d0d277d34e60012e40a2b",
                                                name: "Egypt",
                                                alph_code: "EG",
                                                num_code: "+20"))
        UserModel.shared.SetCountryCode(countryName: "Egypt", DialCode:  "+20", countryCode: "EG",name_ar: "مصر" , name_en: "Egypt")
    }
    
    func setNumber(codeKey: DialNumbersDataModel) {
        numberCodeKey = codeKey
        let keyNum = codeKey.num_code ?? ""
        view?.setKey(num: "\(keyNum)")
    }
    
    //MARK: - interactor delegate functions
    func signUpSuccessfully(){
//        self.view?.successfullyAdded()
        self.getVBerificationCode(with: phone!)
    }
    
    func getTermsLinkSuccessfully(model: HelpModel) {
        if let obj = model.data?.body?.first{
            router?.openTermsAndPolicy(title: obj.title ?? "", link: obj.content?.iframeSrc ?? "")
        }
        
    }
    
    //MARK: - error deleagte function
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    
    //MARK: - actions
    func openTermsAndPolicy(){
        interactor?.getLinkOfTerms()
    }
    
    func selectDialKey() {
        router?.openPhoneCodeKeys(selected: numberCodeKey)
    }
    
    func signUpWith(email: String, phone: String, referralCode: String) {
        
        let phoneValidator = validator?.validate(number: phone)
        let emailvalidator = validator?.validate(email: email)
        
        print("emailvalidator",emailvalidator?.msg ?? "")
//        if phoneValidator?.error ?? false{
//            error?.featching(error: phoneValidator?.msg ?? "")
//            return
//        }
//
//        if emailvalidator?.error ?? false{
//            error?.featching(error: phoneValidator?.msg ?? "")
//            return
//        }
        
        self.phone = phone
        self.email = email
        
//        let keyNum = numberCodeKey?.num_code?.dropFirst() ?? ""
//        var phoneFinal = phone
//        if phone.first == "0"{
//            phoneFinal = "\(phone.dropFirst())"
//        }
//        let filnalFormOfPhone = "\(keyNum)\(phoneFinal)"
        print("Reques is",signUpRequestModel(email: email.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!,
                                             country_code: numberCodeKey?.num_code ?? "",
                                             phone: phone,
                                             referral: referralCode ,
                                             language: UserModel.shared.getLanguage(),
                                             fcm_token: UserModel.shared.get_FCMToken()))
        interactor?.signUp(with: signUpRequestModel(email: email.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed)!,
                                                    country_code: numberCodeKey?.num_code ?? "",
                                                    phone: phone,
                                                    referral: referralCode ,
                                                    language: UserModel.shared.getLanguage(),
                                                    fcm_token: UserModel.shared.get_FCMToken()))
    }
    
    func onTapGoogleSignIn(_ view: UIViewController) {
        interactor?.signInWithGoogle(view)
    }
    
    func onTapAppleSignIn(){
        interactor?.signInWithApple()
    }
    
    func successfullyLoggedIn(_ data: loginModel) {
        UserModel.shared.loginAsGuest(status: false)
        UserModel.shared.setData(model: data.user!)
        UserModel.shared.setLogin(value: true)
        setFireBaseUser()
        if data.user?.first_login ?? "0" == "0" {
            router?.goStartSub(self)
        }else{
            router?.goToHome()
        }
        interactor?.setDevice(model: getDeviceData(userId: (data.user?.id?.getValue)!))
    }
    
    func setFireBaseUser(){
        DispatchQueue.global(qos: .background).async{ [weak self] in
            guard let self = self else { return }
            let userModel = UserChatModel(name: UserModel.shared.get_username(),
                                          image: UserModel.shared.get_image(),
                                          FCM: UserModel.shared.get_FCMToken(),
                                          online: true)
            self.interactor?.setUserinFirebase(userID: UserModel.shared.get_id() ,model: userModel)
        }
    }
    
    func setDeviceSuccessfully(model: UserDeviceDataModel?) {
        
    }
    
    func getDeviceData(userId: String) -> deviceRequestModel{
        print(UserModel.shared.get_FCMToken())
        let obj = deviceRequestModel(model: UIDevice.current.model,
                                     release: "12.2",
                                     brand: UIDevice.current.model,
                                     broad: UIDevice.current.name,
                                     base: "",
                                     manufacturer: "Apple",
                                     platform: "IOS",
                                     userId: userId,
                                     sdk: UIDevice.current.systemVersion,
                                     type: "user")
        return obj
    }
    
    func getVBerificationCode(with phone: String) {
        
        let phoneValidator = validator?.validate(number: phone)
        
//        if phoneValidator?.error ?? false{
//            error?.featching(error: phoneValidator?.msg ?? "")
//            return
//        }
        
        self.phone = phone
        
        let keyNum = numberCodeKey?.num_code ?? ""
        interactor?.getCode(for: phone, code: keyNum)
    }
    
    func generateCodeSuccessfully(code: String) {
        router?.openVerification(phoneKey: numberCodeKey?.num_code ?? "",
                                 number: phone ?? "",
                                 code: code)
    }
    
}

extension SignUpPresenter: StartSubDelegat{
    func onStart() {
        router?.chooseAvatar()
    }
}
