//
//  SignUpRouter.swift
//  PIL
//
//  Created by <PERSON> on 8/30/21.
//  
//

import Foundation
import UIKit

class SignUpRouter: SignUpRouterProtocol, PhoneCodeKeySelectionDelegate {

    weak var VC: SignUpViewProtocol?
    var presenter: SignUpPresenterProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .SignUpST, VC: .SignUpVC)) as! SignUpVC
        let interactor = SignUpInteractor()
        let router = SignUpRouter()
        let authValidator = AuthValidator()
        let presenter = SignUpPresenter(view: view, router: router, interactor: interactor, error: view, validator: authValidator)
        let authWorker = AuthWorker()
        let helpWorker = HelpWorker()
        let userWorker = UserWorker()
        let services = SocialAuthServices(authWorker: authWorker)
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.authWorker = authWorker
        interactor.helpWorker = helpWorker
        interactor.error = presenter
        interactor.services = services
        interactor.userWorker = userWorker
        router.VC = view
        router.presenter = presenter
        return view
    }
 
    func openTermsAndPolicy(title: String, link: String){
        SetStoryBoard.controller(controller: Helper(Story: .Help, VC: .WebViewControllerVC)) { (controller) in
            let viewController = controller as! WebViewController
            viewController.type = .custom(title, link)
            if let vc = VC as? UIViewController{
                vc.navigationController?.pushViewController(viewController, animated: true)
            }
        }
    }
    
    func openVerification(phoneKey: String, number: String, code: String) {
        let codeCheck = CheckCodeRouter.createModule(phoneKey: phoneKey, phoneNumber: number, code: code) as! CheckCodeVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(codeCheck, animated: true)
        }
    }
    
    func goStartSub(_ delegate: StartSubDelegat) {
        let startVc = StartSilverSubVC.loadFromNib()
        startVc.delegate = delegate
        startVc.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            vc.navigationController?.present(startVc, animated: false)
        }
    }
    
    func openPhoneCodeKeys(selected: DialNumbersDataModel?){
        let phoneCodeKeys = PhoneCodeKeyRouter.createModule(selectedCodeKey: selected) as! PhoneCodeKeyVC
        phoneCodeKeys.actionDelegate = self
        if let vc = VC as? UIViewController{
            vc.present(phoneCodeKeys, animated: false, completion: nil)
        }
    }
    
    //MARK: - openPhoneCodeKeys delegate
    func select(item: DialNumbersDataModel) {
        if let presenter = presenter as? SignUpPresenter{
            presenter.setNumber(codeKey: item)
        }
    }
    
    func goToHome() {
        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = TBC
    }
    
    func chooseAvatar() {
        goToHome()
    }
}
