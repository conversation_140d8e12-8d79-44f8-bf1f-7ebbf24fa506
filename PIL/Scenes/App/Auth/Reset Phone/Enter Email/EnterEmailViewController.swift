//
//  EnterEmailViewController.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation
import UIKit

class EnterEmailVC: UIViewController, EnterEmailViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: EnterEmailPresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var emailTFView: UIView!
    @IBOutlet weak var emailTF: UITextField!
    @IBOutlet weak var getCodeBtn: UIButton!
    @IBOutlet weak var backBtn: UIButton!


    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        setUpUI()
    }
    
    //MARK: - functions
    func setUpUI(){
        emailTFView.shadow(color: shadowColor)
        backBtn.setBackArrow()
        emailTF.setDirection()
        getCodeBtn.inValid()
    }
    
    
    //MARK: - error deleagte functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }

        }
    }

    //MARK: - Actions
    @IBAction func typeEmail(_ sender: UITextField) {
        if emailTF.text!.isEmpty{
            getCodeBtn.inValid()
        }else{
            getCodeBtn.valid()
        }
    }
    
    @IBAction func getCodeAction(_ sender: UIButton) {
        presenter?.getCode(email: emailTF.text!)
    }
    
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
}
