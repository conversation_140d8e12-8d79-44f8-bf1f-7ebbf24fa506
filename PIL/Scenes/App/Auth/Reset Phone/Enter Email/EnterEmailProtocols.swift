//
//  EnterEmailProtocols.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

protocol EnterEmailViewProtocol: AnyObject {
    var presenter: EnterEmailPresenterProtocol? { get set }
}

protocol EnterEmailPresenterProtocol: AnyObject {
    var view: EnterEmailViewProtocol? { get set }
    func getCode(email: String)
}

protocol EnterEmailInteractorInputProtocol {
    var presenter: EnterEmailInteractorOutputProtocol? { get set }
    func getCode(model: emialVerificationCodeRequestModel)
}

protocol EnterEmailInteractorOutputProtocol: AnyObject {
    func getCodeSuccessfully(code: String , userID:String)
}

protocol EnterEmailRouterProtocol {
    func openCode(code: String, email: String , userID:String)
}

