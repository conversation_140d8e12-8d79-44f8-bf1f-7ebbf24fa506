//
//  EnterEmailRouter.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation
import UIKit

class EnterEmailRouter: EnterEmailRouterProtocol {

    weak var VC: EnterEmailViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ResetPhoneST, VC: .EnterEmailVC)) as! EnterEmailVC
        let interactor = EnterEmailInteractor()
        let router = EnterEmailRouter()
        let presenter = EnterEmailPresenter(view: view, router: router, interactor: interactor, error: view)
        let authWorker = AuthWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.authWorker = authWorker
        router.VC = view
        return view
    }
    
    func openCode(code: String, email: String , userID:String) {
        let checkCode = CheckCodeRouter.createModule(email: email, code: code, userID: userID) as! CheckCodeVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(checkCode, animated: true)
        }
    }
}
