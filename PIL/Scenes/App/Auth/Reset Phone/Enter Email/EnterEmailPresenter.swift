//
//  EnterEmailPresenter.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

class EnterEmailPresenter: EnterEmailPresenterProtocol, EnterEmailInteractorOutputProtocol, ErrorProtocol {

    //MARK: - variables
    weak var view: EnterEmailViewProtocol?
    var error: ErrorProtocol?
    var router: EnterEmailRouterProtocol?
    var interactor: EnterEmailInteractorInputProtocol?
    
    
    var email: String?
    
    //MARK: - init
    init(view: EnterEmailViewProtocol,
         router: EnterEmailRouterProtocol,
         interactor: EnterEmailInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func getCode(email: String) {
        self.email = email
        interactor?.getCode(model: emialVerificationCodeRequestModel(username: email , language: UserModel.shared.getLanguage()))
    }
    
    //MARK: - delegate functions
    func getCodeSuccessfully(code: String , userID:String) {
        router?.openCode(code: code, email: email ?? "", userID: userID)
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
}
