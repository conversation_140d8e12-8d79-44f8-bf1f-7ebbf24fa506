//
//  EnterEmailInteractor.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

class EnterEmailInteractor: EnterEmailInteractorInputProtocol {

    //MARK: - variables
    weak var presenter: EnterEmailInteractorOutputProtocol?
    var error: ErrorProtocol?
    var authWorker: AuthWorkerProtocol?
    //MARK: - getCode
    /// - Parameters:
    ///   - model: emialVerificationCodeRequestModel
    ///   - completion: getCode call back completion
    func getCode(model: emialVerificationCodeRequestModel){
        authWorker?.getEmailCode(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status == true{
                        self.presenter?.getCodeSuccessfully(code: "\(model.message ?? 0)", userID: "\(model.data?.dataID ?? 0)")
                    }else{
                        self.error?.featching(error: model.error ?? "")
                    }
                    break
                
                case .failure(let error):
                    if statusCode == 422{
                        self.error?.featching(error: "This email does not exist".localized)
                    }else{
                    
                        ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                        self.error?.featching(error: localizedError)
                        } sessionExpired: {
                            self.error?.sessionExpired?()
                        } noInternet: {
                            self.error?.noInternet?()
                        }
                    }
                    break
            }
        })
    }
    
}
