//
//  EnterPhoneInteractor.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

class EnterPhoneInteractor: EnterPhoneInteractorInputProtocol {
    //MARK: - variables
    weak var presenter: EnterPhoneInteractorOutputProtocol?
    weak var error: ErrorProtocol?
    var authWorker: AuthWorkerProtocol?
    //MARK: - getDialCodesListData
    ///   - completion: getDialCodesListData call back completion
    func getDialCodesListData(){
        authWorker?.dialCodes( compiliation: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.getDialCodesSuccessfully(model: model.data ?? [])
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - NewPhoneNumber
    /// - Parameters:
    ///   - model: NewPhoneNumberRequestModel
    ///   - userID : string
    ///   - completion: NewPhoneNumber call back completion
    func NewPhoneNumber(model:NewPhoneNumberRequestModel,userID:String){
        authWorker?.NewPhoneNumber(model: model, userID: userID, compiliation: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status == true{
                        self.presenter?.getDataResetPhone()
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
}
