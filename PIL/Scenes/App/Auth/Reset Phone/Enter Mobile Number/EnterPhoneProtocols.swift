//
//  EnterPhoneProtocols.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

protocol EnterPhoneViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: EnterPhonePresenterProtocol? { get set }
    func setKey(num: String)

}

protocol EnterPhonePresenterProtocol: AnyObject {
    // TODO: Declare presentation methods
    var view: EnterPhoneViewProtocol? { get set }
//    func setNumber(codeKey: String)
    func selectDialKey()
    func confirm(phone: String)
    func setNumber(codeKey: DialNumbersDataModel)
    func viewDidLoad()
}

protocol EnterPhoneInteractorInputProtocol {
    // TODO: Declare use case methods
    var presenter: EnterPhoneInteractorOutputProtocol? { get set }
    func getDialCodesListData()
    func NewPhoneNumber(model:NewPhoneNumberRequestModel,userID:String)
}

protocol EnterPhoneInteractorOutputProtocol: AnyObject {
    func getDialCodesSuccessfully(model: [DialNumbersDataModel])
    func getDataResetPhone()
}

protocol EnterPhoneRouterProtocol {
    func openSuccessRest()
    func openPhoneCodeKeys(selected: DialNumbersDataModel?)
}

