//
//  EnterPhoneRouter.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation
import UIKit

class EnterPhoneRouter: EnterPhoneRouterProtocol ,PhoneCodeKeySelectionDelegate{
   
    
  
    weak var VC: EnterPhoneViewProtocol?
    var presenter: EnterPhonePresenterProtocol?

    
    static func createModule(ueserID:String) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ResetPhoneST, VC: .EnterNewPhoneVC)) as! EnterPhoneVC
        let interactor = EnterPhoneInteractor()
        let router = EnterPhoneRouter()
        let validator = AuthValidator()
        let presenter = EnterPhonePresenter(view: view, router: router, interactor: interactor, validator: validator, error: view)
        presenter.userID = ueserID
        let authWorker = AuthWorker()
        view.presenter = presenter
        router.presenter = presenter
        interactor.presenter = presenter
        interactor.authWorker = authWorker
        interactor.error = presenter
        router.VC = view
        return view
    }
    
    func openSuccessRest() {
        let success = SetStoryBoard.controller(controller: Helper(Story: .ResetPhoneST, VC: .ResetSuccessVC)) as! ResetSuccessVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(success, animated: true)
        }
    }
    
    func openPhoneCodeKeys(selected: DialNumbersDataModel?){
        let phoneCodeKeys = PhoneCodeKeyRouter.createModule(selectedCodeKey: selected) as! PhoneCodeKeyVC
        phoneCodeKeys.actionDelegate = self
        if let vc = VC as? UIViewController{
            vc.present(phoneCodeKeys, animated: false, completion: nil)
        }
    }
    
    //MARK: - openPhoneCodeKeys delegate
    func select(item: DialNumbersDataModel) {
        if let presenter = presenter as? EnterPhonePresenter{
            presenter.setNumber(codeKey: item)
        }
    }
}
    
