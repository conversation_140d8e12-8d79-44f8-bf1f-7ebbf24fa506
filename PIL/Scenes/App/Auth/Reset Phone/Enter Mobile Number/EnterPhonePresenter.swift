//
//  EnterPhonePresenter.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation

class EnterPhonePresenter: EnterPhonePresenterProtocol, EnterPhoneInteractorOutputProtocol, ErrorProtocol {

    //MARK: - variables
    weak var view: EnterPhoneViewProtocol?
    var error: ErrorProtocol?
    var router: EnterPhoneRouterProtocol?
    var interactor: EnterPhoneInteractorInputProtocol?
//    var numberCodeKey: String? = "20"
    var phone: String?
    var userID:String?
    var validator: AuthValidatorProtocol?
    var numberCodeKey: DialNumbersDataModel?

    //MARK: - init
    init(view: EnterPhoneViewProtocol,
         router: EnterPhoneRouterProtocol,
         interactor: EnterPhoneInteractorInputProtocol,
         validator: AuthValidatorProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.validator = validator
        self.error = error
    }
    
    //MARK: - functions
//    func setNumber(codeKey: String) {
//        numberCodeKey = codeKey
//    }
    func viewDidLoad(){
        setNumber(codeKey: DialNumbersDataModel(_id: "623d0d277d34e60012e40a2b",
                                                name: "Egypt",
                                                alph_code: "EG",
                                                num_code: "+20" ,
                                                name_ar: "مصر",
                                                name_en: "Egypt"))
    }
    
    //MARK: - interactor delegate
    func getDialCodesSuccessfully(model: [DialNumbersDataModel]) {
        
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    //MARK: - actions
    func selectDialKey() {
        router?.openPhoneCodeKeys(selected: numberCodeKey)
    }
    
    
    func setNumber(codeKey: DialNumbersDataModel) {
        numberCodeKey = codeKey
        let keyNum = codeKey.num_code ?? ""
        view?.setKey(num: "\(keyNum)")
    }
    
    //send request
    func confirm(phone: String) {
        let phoneValidator = validator?.validate(number: phone)
        
//        if phoneValidator?.error ?? false{
//            error?.featching(error: phoneValidator?.msg ?? "")
//            return
//        }
        
        self.phone = phone
//        var phoneFinal = phone
//        if phone.first == "0"{
//            phoneFinal = "\(phone.dropFirst())"
//        }
//        let filnalFormOfPhone = "\(numberCodeKey ?? "")\(phoneFinal)"
        
        
        let request = NewPhoneNumberRequestModel(country_code: numberCodeKey?.num_code ?? "", phone: phone)
        self.interactor?.NewPhoneNumber(model: request, userID: userID ?? "")
        
    }
    
    
    // get response
    func getDataResetPhone() {
        router?.openSuccessRest()
    }
}
