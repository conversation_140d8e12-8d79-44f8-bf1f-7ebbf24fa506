//
//  EnterPhoneViewController.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//  
//

import Foundation
import UIKit

class EnterPhoneVC: UIViewController, EnterPhoneViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: EnterPhonePresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var codeNumber: UILabel!
    @IBOutlet weak var numberTFView: UIView!
    @IBOutlet weak var numberTF: UITextField!
    @IBOutlet weak var confirmBtn: UIButton!
    @IBOutlet weak var backBtn: UIButton!

    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        
        setUpUI()
        endTap()
        presenter?.viewDidLoad()
    }
    
    //MARK: - functions
    func setUpUI(){
        backBtn.setBackArrow()
        numberTFView.shadow(color: shadowColor)
        numberTF.setDirection()
        numberTF.keyboardType = .asciiCapableNumberPad
        confirmBtn.inValid()
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }

        }
    }

    func setKey(num: String) {
        codeNumber.text = num
    }
    
    //MARK: - actions
    @IBAction func editingChanged(_ sender: UITextField) {
        if sender.text!.isEmpty{
            confirmBtn.inValid()
        }else{
            confirmBtn.valid()
        }
    }
    
    @IBAction func chooseCodeNumberAction(_ sender: UIButton) {
        presenter?.selectDialKey()
    }
    
    @IBAction func confirmAction(_ sender: UIButton) {
        endEditing()
        presenter?.confirm(phone: numberTF.text!)
    }
    
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
    
}
