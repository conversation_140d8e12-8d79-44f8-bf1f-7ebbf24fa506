//
//  ResetSuccessVC.swift
//  PIL
//
//  Created by <PERSON> on 8/31/21.
//

import UIKit

class ResetSuccessVC: UIViewController {
    
    @IBOutlet weak var messageLable: UILabel!
    var isSignUp = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        if isSignUp == true{
            messageLable.text = "Your account has been created".localized
        }
    }
    @IBAction func goToLoginAction(_ sender: UIButton) {
//        let login = LoginRouter.createModule() as! LoginVC
        self.navigationController?.popToRootViewController(animated: true)
        GoogleAnalyticsHelper.shared.reset_account(reset_account: "true")
    }
    
}
