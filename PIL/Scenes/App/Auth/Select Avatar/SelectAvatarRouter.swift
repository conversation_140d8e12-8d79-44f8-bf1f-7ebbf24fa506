//
//  SelectAvatarRouter.swift
//  PIL
//
//  Created by sameh mohammed on 28/12/2022.
//

import Foundation
import UIKit

class SelectAvatarRouter: SelectAvatarRouterProtocol{
    //MARK: - variables
    var VC:SelectAvatarViewProtocol?
    var viewController:UIViewController?
    //MARK: - Funcs
    static func createModule(type:TypeAvatar) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .MainST, VC: .SelectAvatarVC)) as! SelectAvatarVC
        
        let interactor = SelectAvatarInteractor()
        let worker = AuthWorker()
        let router = SelectAvatarRouter()
        let presenter = SelectAvatarPresenter(view: view,
                                              interactor: interactor,
                                              router: router,
                                              error: view)
        
        presenter.interactor = interactor
        presenter.router = router
        presenter.view = view
        interactor.presenter = presenter
        interactor.worker = worker
        view.presenter = presenter
        view.type = type
        
        return view
    }
    /// openHome
    func openHome(){
        let controller = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC)) as! BaseTabbarController
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = controller
    }
    /// popViewController
    func popViewController() {
        if let vc = VC as? UIViewController{
            vc.navigationController?.popViewController(animated: true)
        }
     }
}
