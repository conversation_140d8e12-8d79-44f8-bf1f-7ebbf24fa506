//
//  SelectAvatarInteractor.swift
//  PIL
//
//  Created by sameh mohammed on 28/12/2022.
//

import Foundation

class  SelectAvatarInteractor:SelectAvatarInputInteractorProtocol{
    //MARK: - variables
    var error:ErrorProtocol?
    var presenter: SelectAvatarOutputInteractorProtocol?
    var worker:<PERSON>th<PERSON><PERSON><PERSON>?
    //MARK: - avatarList
    ///   - completion: avatarList call back completion
    func avatarList(){
        worker?.avatarList(compiliation:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                    if model.status ?? false{
                        self.presenter?.avatarList(list: model.data ?? [])
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - assignAvatar
    /// - Parameters:
    ///   - model: AssignAvatarRequestModel
    ///   - completion: assignAvatar call back completion
    func assignAvatar(model:AssignAvatarRequestModel){
        worker?.assignAvatar(model: model, compiliation:   { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.selectedAvatarSuccessfully()
                    }
                    break
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
}
