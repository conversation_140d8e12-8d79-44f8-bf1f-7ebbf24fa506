//
//  SelectAvatarPresenter.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 28/12/2022.
//

import Foundation

class SelectAvatarPresenter: SelectAvatarPresenterProtocol , SelectAvatarOutputInteractorProtocol{
    //MARK: - variables
    var view: SelectAvatarViewProtocol?
    var interactor:SelectAvatarInputInteractorProtocol?
    var router:SelectAvatarRouterProtocol?
    var error:ErrorProtocol?
    var list = [AvatarData]()
    //MARK: - init
    init(view: SelectAvatarViewProtocol,
         interactor:SelectAvatarInputInteractorProtocol,
         router:SelectAvatarRouterProtocol,
         error:ErrorProtocol){
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    //MARK: - viewDidload
    func viewDidload(){
        interactor?.avatarList()
    }
    //MARK: - Funcs
    /// avatarList(list: [AvatarData])
    func avatarList(list: [AvatarData]) {
        self.list.append(contentsOf: list)
        view?.reloadData()
    }
    /// avatarCount
    func avatarCount()->Int{
        return self.list.count
    }
    /// avatarID
    func avatarID(index:Int)->Int{
        return self.list[index].id ?? 0
    }
    /// configuer
    func configuer(cell:avatarCellProtocol, index:Int){
        cell.avatar(img: list[index].image ?? "")
    }
    // selected avatar and send request
    func avatarselected(index:Int){
        UserModel.shared.setAvatar(img: list[index].image ?? "")
        interactor?.assignAvatar(model: AssignAvatarRequestModel(playerId: UserModel.shared.get_id(),
                                                                 avatar_id: list[index].id ?? 0))
    }
    // avatar saved successfully
    func selectedAvatarSuccessfully() {
//        if view?.type == .profile {
//            router?.popViewController()
//        }else{
//            router?.openHome()
//
//        }
        self.view?.successSelectAvatar()
    }
    /// openHome
    func openHome() {
        router?.openHome()
    }
    
}
