<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="AvatarCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="130" height="150"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="130" height="150"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fZa-sz-4dD">
                        <rect key="frame" x="0.0" y="10" width="130" height="140"/>
                        <color key="backgroundColor" white="1" alpha="0.14867665816326531" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="8"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Uux-XH-hQX">
                        <rect key="frame" x="9" y="0.0" width="112" height="141"/>
                    </imageView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="Uux-XH-hQX" secondAttribute="trailing" constant="9" id="1n2-2Y-ixe"/>
                <constraint firstItem="Uux-XH-hQX" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="HEE-hv-Nz7"/>
                <constraint firstItem="fZa-sz-4dD" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="LmI-Yj-LIc"/>
                <constraint firstItem="Uux-XH-hQX" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="9" id="Xnd-Dg-PvW"/>
                <constraint firstAttribute="bottom" secondItem="fZa-sz-4dD" secondAttribute="bottom" id="adV-Nr-09A"/>
                <constraint firstAttribute="trailing" secondItem="fZa-sz-4dD" secondAttribute="trailing" id="b2D-Xk-iuy"/>
                <constraint firstItem="fZa-sz-4dD" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="qMq-W8-RwG"/>
                <constraint firstAttribute="bottom" secondItem="Uux-XH-hQX" secondAttribute="bottom" constant="9" id="vad-ZE-FkK"/>
            </constraints>
            <size key="customSize" width="122" height="154"/>
            <connections>
                <outlet property="avatarimg" destination="Uux-XH-hQX" id="kFz-my-zUc"/>
            </connections>
            <point key="canvasLocation" x="188.40579710144928" y="168.75"/>
        </collectionViewCell>
    </objects>
</document>
