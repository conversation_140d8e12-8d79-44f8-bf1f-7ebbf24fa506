//
//  AvatarCell.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 28/12/2022.
//

import UIKit
//MARK: - avatarCellProtocol
protocol avatarCellProtocol{
    func avatar(img:String)
}

class AvatarCell: UICollectionViewCell,avatarCellProtocol {
    //MARK: - @IBOutlet
    @IBOutlet weak var avatarimg: UIImageView!
    //MARK: - @awakeFromNib
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    //MARK: - @avatar
    /// Parameters :
    /// img : string
    func avatar(img: String) {
        print("image avatar",img)
        self.avatarimg.sd_setImage(with: URL(string: img), placeholderImage: placeHolderImage)
    }
}
