//
//  SelectAvatarVC.swift
//  PIL
//
//  Created by sameh mohammed on 28/12/2022.
//

import UIKit

enum TypeAvatar{
    case profile
    case none
}

class SelectAvatarVC: UIViewController, SelectAvatarViewProtocol , ErrorProtocol {
    //MARK: - outlet
    @IBOutlet weak var nextBu: UIButton!
    @IBOutlet weak var avatarCollectionView: UICollectionView!
    //MARK: - variable
    var presenter: SelectAvatarPresenterProtocol?
    var type:TypeAvatar?
    //MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()

        presenter?.viewDidload()
        avatarCollectionView.register(UINib(nibName: "AvatarCell", bundle: nil), forCellWithReuseIdentifier: "AvatarCell")

        if type == .profile{
            self.nextBu.setTitle("Back".localized, for: .normal)
        }
    }
    //MARK: - function
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error) {}
    }
    //MARK: - Funcs
    func reloadData() {
        self.avatarCollectionView.reloadData()
    }
    func successSelectAvatar(){
        if type == .profile{
            self.navigationController?.popViewController(animated: true)
        }else{
            presenter?.openHome()
        }
    }
    //MARK: - action
    @IBAction func nextBTN(_ sender: Any) {
        if type == .profile{
            self.navigationController?.popViewController(animated: true)
        }else{
            presenter?.openHome()
        }
    }
}
//MARK: - avatarCollectionView Funcs
extension SelectAvatarVC:UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return presenter?.avatarCount() ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = self.avatarCollectionView.dequeueReusableCell(withReuseIdentifier: "AvatarCell", for: indexPath) as! AvatarCell
        presenter?.configuer(cell: cell, index: indexPath.row)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        presenter?.avatarselected(index: indexPath.row)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if UIScreen.main.bounds.width > 500 {
            return CGSize(width: (UIScreen.main.bounds.width-40)/3, height: 130)
        }else{
            return CGSize(width: (UIScreen.main.bounds.width-40)/2, height: 130)

        }
    }
}
