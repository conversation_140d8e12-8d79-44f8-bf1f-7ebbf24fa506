//
//  SelectAvatarProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 28/12/2022.
//

import Foundation

protocol  SelectAvatarViewProtocol{
    // TODO: Declare view methods
    var presenter: SelectAvatarPresenterProtocol? { get set}
    func reloadData()
    var type:TypeAvatar? {get set}
    func successSelectAvatar()
}

protocol  SelectAvatarPresenterProtocol{
    // TODO: Declare presentation methods
    var view : SelectAvatarViewProtocol? {get set}
    func viewDidload()
    func avatarCount()->Int
    func avatarID(index:Int)->Int
    func avatarselected(index:Int)
    func configuer(cell:avatarCellProtocol, index:Int)
    func openHome()
}

protocol  SelectAvatarInputInteractorProtocol{
    // TODO: Declare use case methods
    var presenter:SelectAvatarOutputInteractorProtocol? { get set}
    func avatarList()
    func assignAvatar(model:AssignAvatarRequestModel)
}

protocol  SelectAvatarOutputInteractorProtocol{
    // TODO: Declare interactor output methods
    func avatarList(list:[AvatarData])
    func selectedAvatarSuccessfully()

}

protocol  SelectAvatarRouterProtocol{
    func openHome()
    func popViewController()
}
