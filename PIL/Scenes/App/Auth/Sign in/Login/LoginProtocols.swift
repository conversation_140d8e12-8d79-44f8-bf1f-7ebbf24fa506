//
//  LoginProtocols.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//  
//

import Foundation
import UIKit

protocol LoginViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: LoginPresenterProtocol? { get set }
    func clear()
    func setKey(num: String)
}

protocol LoginPresenterProtocol: AnyObject {
    var view: LoginViewProtocol? { get set }
    func viewDidLoad()
    func openSignUp()
    func openResetPhone()
    func getVBerificationCode(with phone: String)
    func setNumber(codeKey: DialNumbersDataModel)
    func selectDialKey()
    func openAsGuest()
    func setLanguage(lang: String)
    func onTapGoogleSignIn(_ view: UIViewController)
    func onTapAppleSignIn()
    func onTapFBSignIn(_ view: UIViewController)
}

protocol LoginInteractorInputProtocol {
    var presenter: LoginInteractorOutputProtocol? { get set }
    var services: SocialAuthServices? { set get }
    var userWorker: UserWorker? { set get }
    func getCode(for phone: String, code: String)
    func loginAsGuest()
    func signInWithGoogle(_ view: UIViewController)
    func signInWithFB(_ view: UIViewController)
    func signInWithApple()
    func setUserinFirebase(userID:String, model: UserChatModel)
    func setDevice(model: deviceRequestModel)
}

protocol LoginInteractorOutputProtocol: AnyObject {
    func generateCodeSuccessfully(code: String)
    func successLoginAsGuest(model: loginModel)
    func successfullyLoggedIn(_ data: loginModel)
    func setDeviceSuccessfully(model: UserDeviceDataModel?)
}

protocol LoginRouterProtocol {
    func openVerification(phoneKey: String, number: String, code: String)
    func openSignUp()
    func openResetPhone()
    func openPhoneCodeKeys(selected: DialNumbersDataModel?)
    func chooseAvatar()
    func reloadApp()
    func goToHome()
    func goStartSub(_ delegate: StartSubDelegat)
}



