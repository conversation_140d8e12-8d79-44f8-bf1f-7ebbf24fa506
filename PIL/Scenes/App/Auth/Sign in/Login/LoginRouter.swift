//
//  LoginRouter.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//  
//

import Foundation
import UIKit

class LoginRouter: LoginRouterProtocol,  PhoneCodeKeySelectionDelegate{
    
    weak var VC: LoginViewProtocol?
    var presenter: LoginPresenterProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .AuthST, VC: .LoginVC)) as! LoginVC
        let interactor = LoginInteractor()
        let router = LoginRouter()
        let validator = AuthValidator()
        let presenter = LoginPresenter(view: view, router: router, interactor: interactor, validator: validator, error: view)
        let authWorker = AuthWorker()
        let userWorker = UserWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.authWorker = authWorker
        interactor.error = presenter
        interactor.services = SocialAuthServices(authWorker: authWorker)
        router.VC = view
        router.presenter = presenter
        interactor.userWorker = userWorker
        return view
    }
    /// goToHome
    func goToHome() {
        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = TBC
    }
    /// openPhoneCodeKeys
    /// - Parameters :
    /// - selected : DialNumbersDataModel
    func openPhoneCodeKeys(selected: DialNumbersDataModel?){
        let phoneCodeKeys = PhoneCodeKeyRouter.createModule(selectedCodeKey: selected) as! PhoneCodeKeyVC
        phoneCodeKeys.actionDelegate = self
        if let vc = VC as? UIViewController{
            vc.present(phoneCodeKeys, animated: false, completion: nil)
        }
    }
    
    //MARK: - openPhoneCodeKeys delegate
    func select(item: DialNumbersDataModel) {
        if let presenter = presenter as? LoginPresenter{
            presenter.setNumber(codeKey: item)
        }
    }
    //MARK: - openVerification
    /// - Parameters :
    /// - phoneKey : String
    /// - code: String
    /// - number : String
    func openVerification(phoneKey: String, number: String, code: String) {
        let codeCheck = CheckCodeRouter.createModule(phoneKey: phoneKey, phoneNumber: number, code: code) as! CheckCodeVC
        push(view: codeCheck)
    }
    /// openSignUp
    func openSignUp() {
        let signUp = SignUpRouter.createModule() as! SignUpVC
        push(view: signUp)
    }
    /// openResetPhone
    func openResetPhone() {
        let resetModule = EnterEmailRouter.createModule() as! EnterEmailVC
        push(view: resetModule)
    }
    /// push(view: UIViewController)
    func push(view: UIViewController){
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    /// chooseAvatar
    func chooseAvatar() {
//        let controller = SelectAvatarRouter.createModule()
//        let navigationController = UINavigationController(rootViewController: controller)
//        navigationController.setNavigationBarHidden(true, animated: false)
//        let Delegate = UIApplication.shared.delegate as! AppDelegate
//        Delegate.window?.rootViewController = navigationController
        goHome()
    }
    /// goStartSub
    func goStartSub(_ delegate: StartSubDelegat) {
        let startVc = StartSilverSubVC.loadFromNib()
        startVc.delegate = delegate
        startVc.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            vc.navigationController?.present(startVc, animated: false)
        }
    }
    /// goHome
    func goHome(){
        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = TBC
    }
    /// reloadApp
    func reloadApp() {
        let splash = SetStoryBoard.controller(controller: Helper(Story: .MainST, VC: .SplashVC))
        
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.set(rootViewController: splash)
        Delegate.window?.makeKeyAndVisible()
    }
}
