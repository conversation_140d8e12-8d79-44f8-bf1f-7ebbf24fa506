//
//  LoginInteractor.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//  
//

import Foundation
import FirebaseAuth


class LoginInteractor: LoginInteractorInputProtocol, SocialAuthDelegate {

    //MARK: - variables
    weak var presenter: LoginInteractorOutputProtocol?
    weak var error: ErrorProtocol?
    var authWorker: AuthWorkerProtocol?
    var services: SocialAuthServices?
    var userWorker: UserWorker?
    //MARK: - signInWithFB
    func signInWithFB(_ view: UIViewController) {
        services?.delegate = self
        services?.signInWithFacebook(view)
    }
    //MARK: - signInWithGoogle
    func signInWithGoogle(_ view: UIViewController) {
        services?.delegate = self
        services?.signInWithGoogle(view)
    }
    //MARK: - signInWithApple
    func signInWithApple(){
        services?.delegate = self
        services?.signInWithApple()
    }
    //MARK: - setDevice
    /// - Parameters:
    ///   - model: deviceRequestModel
    ///   - completion: setDevice call back completion
    func setDevice(model: deviceRequestModel) {
        authWorker?.setDevice(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.setDeviceSuccessfully(model: model.data)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - getCode
    /// - Parameters:
    ///   - phone: String
    ///   - code: String
    ///   - completion: getCode call back completion
    func getCode(for phone: String, code: String) {
        authWorker?.getPhoneCode(for: phone, code: code, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status == true{
                        self.presenter?.generateCodeSuccessfully(code: model.message ?? "")
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - loginAsGuest
    ///   - completion: loginAsGuest call back completion
    func loginAsGuest(){
        authWorker?.loginAsGuest(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status == true{
                        self.presenter?.successLoginAsGuest(model:model)
                        GoogleAnalyticsHelper.shared.trackLoginEvent(user: model.user ?? UserDataModel())
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - didCompleteSignInWithCredentials
    func didCompleteSignInWithCredentials(_ result: AuthDataResult?,_ error: Error?){
        print("here result from social sign in: ", result?.user.email)
    }
    //MARK: - didCompleteSignInToServers
    func didCompleteSignInToServers(_ response: loginModel?, _ error: Error?) {
        print("result from social login api: ", response)
        guard let response = response,
        200...299 ~= response.code ?? 200
        else {
            self.error?.featching(error: response?.message ?? "Something went wrong, please try again later")
            return
        }
        guard let user = response.user else { return }
        self.presenter?.successfullyLoggedIn(response)
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        GoogleAnalyticsHelper.shared.Login_success(login_datetime: stringDate, login_success: "true")
        GoogleAnalyticsHelper.shared.trackLoginEvent(user: user)
        UserModel.shared.loginAsGuest(status: false)

    }
    //MARK: - setUserinFirebase
    /// - Parameters:
    ///   - userID: String
    ///   - model: UserChatModel
    ///   - completion: setUserinFirebase call back completion
    func setUserinFirebase(userID: String, model: UserChatModel) {
        userWorker?.uploadChatUser(userID: userID, model: model, compilition: { (Status, Error) in
            if let error = Error{
                print(error)
            }else{
                print("Success")
            }
        })
    }
    
}
