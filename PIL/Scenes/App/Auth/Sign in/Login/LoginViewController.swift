//
//  LoginViewController.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//  
//

import Foundation
import UIKit

class LoginVC: UIViewController, LoginViewProtocol, ErrorProtocol{
    
    //MARK: - variables
    var presenter: LoginPresenterProtocol?
    
    
    //MARK: - outlets
    @IBOutlet weak var codeNumber: UILabel!
    @IBOutlet weak var numberTF: UITextField!
    @IBOutlet weak var numberTFView: UIView!
    @IBOutlet weak var confirmBtn: UIButton!
    @IBOutlet weak var languageLable: UILabel!
    @IBOutlet weak var resetBu: UIButton!
    
    
    //MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        endTap()
        resetBu.underline(textColor: UIColor(named: "Black-White"))
        if app_lang == "ar"{
            languageLable.text = "اللغة الانجليزية"
        }else{
            languageLable.text = "Arabic"
        }
    }
    //MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
        
    }
    
    //MARK: - functions
    func setupUI(){
        numberTFView.shadow(color: shadowColor)
        numberTF.setDirection()
        numberTF.keyboardType = .asciiCapableNumberPad
    }
    
    /// languageOperation
    func languageOperation() {
        let alert = UIAlertController(title: "ChooseLang".localized, message: "", preferredStyle: .actionSheet)
        
        let Arabic = UIAlertAction(title: "Arabic".localized, style: .destructive) { [weak self] (action) in
            self?.presenter?.setLanguage(lang: "ar")

        }
        
        let English = UIAlertAction(title: "English".localized, style: .destructive) { [weak self] (action) in
            self?.presenter?.setLanguage(lang: "en")

        }
        
        let Cancle = UIAlertAction(title: "Cancel".localized, style: .cancel)
        
        if let popoverPresentationController = alert.popoverPresentationController {

                popoverPresentationController.sourceRect = view.frame
                popoverPresentationController.sourceView = self.view

        }
        alert.addAction(Arabic)
        alert.addAction(English)
        alert.addAction(Cancle)
        DispatchQueue.main.async {
            self.present(alert, animated: true, completion: nil)
        }
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    
    //MARK: - delegate functions
    func clear() {
        //TODO: empty number field or not
//        numberTF.text = ""
//        confirmBtn.inValid()
    }
    
    func setKey(num: String) {
        codeNumber.text = num
    }
    
    
    //MARK: - actions
    @IBAction func editingChanged(_ sender: UITextField) {
//        if sender.text!.isEmpty{
//            confirmBtn.inValid()
//        }else{
//            confirmBtn.valid()
//        }
    }
    
    @IBAction func chooseCodeNumberAction(_ sender: UIButton) {
        presenter?.selectDialKey()
    }
    
    @IBAction func signUpActions(_ sender: UIButton) {
        presenter?.openSignUp()
    }
    
    @IBAction func resetPfoneAction(_ sender: UIButton) {
        presenter?.openResetPhone()
    }
    
    @IBAction func getverificationCodeAction(_ sender: UIButton) {
        endEditing()
        presenter?.getVBerificationCode(with: numberTF.text!)
    }
    
    
    @IBAction func loginGuestBTN(_ sender: Any) {
        presenter?.openAsGuest()
    }
    
    
    @IBAction func changeLanguageBTN(_ sender: Any) {
//        languageOperation()
        if let url: URL = .init(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url)
        }
    }
    
    @IBAction func googleSignInAction(){
        presenter?.onTapGoogleSignIn(self)
    }
    
    @IBAction func appleSignInAction(){
        presenter?.onTapAppleSignIn()
    }
    
    @IBAction func fbSignInAction(){
        presenter?.onTapFBSignIn(self)
    }
    
}
