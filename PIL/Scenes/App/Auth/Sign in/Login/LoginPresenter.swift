//
//  LoginPresenter.swift
//  PIL
//
//  Created by <PERSON> on 8/25/21.
//  
//

import Foundation
import UIKit

class LoginPresenter: LoginPresenterProtocol, LoginInteractorOutputProtocol, ErrorProtocol {

    //MARK: - variables
    weak var view: LoginViewProtocol?
    weak var error: ErrorProtocol?
    var router: LoginRouterProtocol?
    var interactor: LoginInteractorInputProtocol?
    var validator: AuthValidatorProtocol?
    var numberCodeKey: DialNumbersDataModel?
    var phone: String?
    
    
    //MARK: - init
    init(view: LoginViewProtocol,
         router: LoginRouterProtocol,
         interactor: LoginInteractorInputProtocol,
         validator: AuthValidatorProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.validator = validator
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad(){
        setNumber(codeKey: DialNumbersDataModel(_id: "623d0d277d34e60012e40a2b",
                                                name: "Egypt",
                                                alph_code: "EG",
                                                num_code: "+20"))
        UserModel.shared.SetCountryCode(countryName: "Egypt", DialCode:  "+20", countryCode: "EG",name_ar: "مصر", name_en: "Egypt")
    }
    
    func openSignUp() {
        router?.openSignUp()
    }
    
    func openResetPhone() {
        router?.openResetPhone()
    }
    
    func setNumber(codeKey: DialNumbersDataModel) {
        numberCodeKey = codeKey
        let keyNum = codeKey.num_code ?? ""
        view?.setKey(num: "\(keyNum)")
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    
    //MARK: - interactor delegate
    func generateCodeSuccessfully(code: String) {
        view?.clear()
        router?.openVerification(phoneKey: numberCodeKey?.num_code ?? "",
                                 number: phone ?? "",
                                 code: code)
    }
    
    //MARK: - actions
    func selectDialKey() {
        router?.openPhoneCodeKeys(selected: numberCodeKey)
    }
    
    func getVBerificationCode(with phone: String) {
        
        let phoneValidator = validator?.validate(number: phone)
        
//        if phoneValidator?.error ?? false{
//            error?.featching(error: phoneValidator?.msg ?? "")
//            return
//        }
        
        self.phone = phone
        
        let keyNum = numberCodeKey?.num_code ?? ""
//        var phoneFinal = phone
//        if phone.first == "0"{
//            phoneFinal = "\(phone.dropFirst())"
//        }
//        let filnalFormOfPhone = "\(keyNum)\(phoneFinal)"
        interactor?.getCode(for: phone, code: keyNum)
    }
    
    //MARK: - open As Guest
    func openAsGuest(){
        interactor?.loginAsGuest()
    }
    //MARK: - successLoginAsGuest
    /// - Parameters :
    /// - model: loginModel
    func successLoginAsGuest(model: loginModel) {
        if let user = model.data{
            UserModel.shared.loginAsGuest(status: true)
            UserModel.shared.setData(model: user)
            UserModel.shared.setLogin(value: false)
            UserModel.shared.setFirstTimeV(value: false)
            router?.chooseAvatar()
        }

    }
    
    func setLanguage(lang: String) {
        UserModel.shared.setLang(lang: lang)
        router?.reloadApp()
    }
    
    func onTapGoogleSignIn(_ view: UIViewController) {
        interactor?.signInWithGoogle(view)
    }
    
    func onTapAppleSignIn(){
        interactor?.signInWithApple()
    }
    
    func onTapFBSignIn(_ view: UIViewController) {
        interactor?.signInWithFB(view)
    }
    
    //MARK: - successfullyLoggedIn
    /// - Parameters :
    /// - data: loginModel
    /// - Fetch SetDevice
    func successfullyLoggedIn(_ data: loginModel) {
        UserModel.shared.loginAsGuest(status: false)
        UserModel.shared.setData(model: data.user!)
        UserModel.shared.setLogin(value: true)
        UserModel.shared.setFirstTimeV(value: true)
        setFireBaseUser()
        if data.user?.first_login ?? "0" == "0" {
            router?.goStartSub(self)
        }else{
            router?.goToHome()
        }
        interactor?.setDevice(model: getDeviceData(userId: (data.user?.id?.getValue)!))
    }
    //MARK: - getDeviceData
    /// - Parameters :
    /// - userId: String
    ///  - compelition : deviceRequestModel
    func getDeviceData(userId: String) -> deviceRequestModel{
        print(UserModel.shared.get_FCMToken())
        let obj = deviceRequestModel(model: UIDevice.current.model,
                                     release: "12.2",
                                     brand: UIDevice.current.model,
                                     broad: UIDevice.current.name,
                                     base: "",
                                     manufacturer: "Apple",
                                     platform: "IOS",
                                     userId: userId,
                                     sdk: UIDevice.current.systemVersion,
                                     type: "user")
        return obj
    }
    
    func setDeviceSuccessfully(model: UserDeviceDataModel?) {
//        guard let user = self.user else { return }
//
//        if let device = model{
//            user.device = device
//        }
//        UserModel.shared.loginAsGuest(status: false)
//        UserModel.shared.setData(model: user)
//        UserModel.shared.setLogin(value: true)
//        setFireBaseUser()
//        router?.goToHome()
    }
    //MARK: - setFireBaseUser
    func setFireBaseUser(){
        DispatchQueue.global(qos: .background).async{ [weak self] in
            guard let self = self else { return }
            let userModel = UserChatModel(name: UserModel.shared.get_username(),
                                          image: UserModel.shared.get_image(),
                                          FCM: UserModel.shared.get_FCMToken(),
                                          online: true)
            self.interactor?.setUserinFirebase(userID: UserModel.shared.get_id() ,model: userModel)
        }
    }
}

extension LoginPresenter: StartSubDelegat{
    func onStart() {
        router?.chooseAvatar()
    }
}
