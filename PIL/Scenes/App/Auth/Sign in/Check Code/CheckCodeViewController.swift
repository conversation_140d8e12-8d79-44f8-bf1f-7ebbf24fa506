//
//  CheckCodeViewController.swift
//  PIL
//
//  Created by <PERSON> on 8/29/21.
//  
//

import Foundation
import UIKit

class CheckCodeVC: UIViewController, CheckCodeViewProtocol, ErrorProtocol, UITextFieldDelegate{

    //MARK: - variables
    var presenter: CheckCodePresenterProtocol?
    var timerObj : Timer!

    var min: Int = 1
    var sec: Int = 60
    
    var inActiveColor = UIColor(named: "Black-White") ?? .clear
    var errorColor = UIColor(named: "Red") ?? .clear
    var successColor = UIColor(named: "Green") ?? .clear
    
    var canClear = false
    
    //MARK: - outlets

    @IBOutlet weak var timer: UILabel!
    @IBOutlet weak var phoneTxt: UILabel!
    @IBOutlet weak var verifyBtn: UIButton!
    @IBOutlet weak var resendBtn: UIButton!
    @IBOutlet weak var typingDigitsTFView: UIView!
    @IBOutlet weak var typingDigitsTF: UITextField!
    @IBOutlet var codeDigits: [UILabel]!
    
    @IBOutlet weak var codeExample: UILabel!
    @IBOutlet weak var backBtn: UIButton!
    @IBOutlet weak var textVerificationLable: UILabel!
    @IBOutlet weak var stackFileds: UIStackView!
    
    //MARK: - viewDidLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
    }
    //MARK: - viewWillAppear
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
        startTimer()
        codeExample.isHidden = true
        stackFileds.semanticContentAttribute = .forceLeftToRight
        typingDigitsTFView.semanticContentAttribute = .forceLeftToRight
        UI()
    }
    //MARK: - viewWillDisappear
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        timerObj.invalidate()
    }
    
    //MARK: - functions
    func UI(){
        typingDigitsTFView.shadow(color: shadowColor)
        resendBtn.underline(textColor: UIColor(named: "Orange Primary Color")!)
        resendBtn.isHidden = true
        typingDigitsTF.keyboardType = .numberPad
        verifyBtn.inValid()
        typingDigitsTF.becomeFirstResponder()
        for item in codeDigits{
            item.textColor = inActiveColor
        }
        backBtn.setBackArrow()
    }
    /// startTimer
    func startTimer(){
        
        timer.text = "02:00"
        min = 1
        sec = 60
        timer.isHidden = false
        resendBtn.isHidden = true
        timerObj = Timer()
        timerObj = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { [weak self] (timer) in
            guard let self = self else { return }
            if self.sec == 0 && self.min != 0{
                self.sec = 60
                self.min-=1
            }else if self.sec == 0 && self.min == 0{
                timer.invalidate()
                self.timer.isHidden = true
                self.resendBtn.isHidden = false
            }
            self.sec-=1
            
            var secTxt = ""
            
            self.sec<10 ? (secTxt = "0\(self.sec)") : (secTxt = "\(self.sec)")
            
            self.timer.text = "0\(self.min):\(secTxt)"
        })
    }
    /// setCode
    func setCode(code: String){
        let digits = code.map{String($0)}
        //when text equal 3 digit add - to string
        for index in 0..<6{
            if index <= (digits.count-1){
                codeDigits[index].text = digits[index]
            }else{
                codeDigits[index].textColor = inActiveColor
                codeDigits[index].text = ""
            }
        }
        
        if digits.count == 6{
            endEditing()
//            typingDigitsTF.isUserInteractionEnabled = false
            presenter?.check(code: code)
        }
    }
    /// initView
    func initView(){
//        typingDigitsTF.isUserInteractionEnabled = true
        typingDigitsTF.text = ""
        setCode(code: "")
        canClear = false
        for item in codeDigits{
            item.textColor = inActiveColor
        }
        typingDigitsTF.becomeFirstResponder()
    }
    
    //MARK: - delegate functions
    func setCode(txt: String) {
        codeExample.text = txt
        codeExample.isHidden = true
        let characters = Array(txt)
        print("characters",characters.count)
        var code = ""
        for item in 0..<characters.count{
            self.codeDigits[item].text = "\(characters[item])"
            code+="\(characters[item])"
            self.typingDigitsTF.text = code
            
        }
        verifyBtn.valid()

    }
    func setData(phone: String) {
        setStyle(from: .login)
    }
    func setData(email: String) {
        setStyle(from: .reset)
    }
//    func isNumMob(value: Bool) {
//        if value{
//            SMSlabel.text = "code Sent to email"
//        }else{
//            SMSlabel.text = "SMS Sent to"
//        }
//    }
    func setStyle(from type: CheckCodeSource){
//        changeNumberBtn.isHidden = type == .reset
        if type == .reset{
            textVerificationLable.text = "We Sent A 6-Digital Code To Mail".localized
        }else{
            textVerificationLable.text = "We Sent A 6-Digital Code To Your Phone".localized

//            if let value = isNumMobinil{
//                phoneNumber.isHidden = value
//                if value{
//                    phoneTxt.text = "email".localized
//                    SMSlabel.text = "code Sent to".localized
//                }else{
//                    phoneTxt.text = "phone number".localized
//                    SMSlabel.text = "SMS Sent to".localized
//                }
//            }

        }
//        SMSlabel.text = type == .reset ? "Email Sent to".localized : "SMS Sent to".localized
        verifyBtn.setTitle(type == .reset ? "VERIFY".localized : "VERIFY".localized, for: .normal)
    }
    func reInitiateScreen() {
        startTimer()
        initView()
        verifyBtn.inValid()
        
    }
    func codeCheck(result: Bool) {
        canClear = !result
        if result{
            verifyBtn.valid()
        }else{
            verifyBtn.inValid()
        }
        for lbl in codeDigits{
            lbl.textColor = result ? successColor : errorColor
        }
    }
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }

        }
    }
    //MARK: - actions
    @IBAction func typeDigits(_ sender: UITextField) {
        setCode(code: sender.text!)
    }
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField == typingDigitsTF{
            if textField.text!.count > 6{
                endEditing()
            }
        }
    }
    @IBAction func resendCodeAction(_ sender: UIButton) {
        presenter?.sendAnewCode()
    }
    @IBAction func verifyAction(_ sender: UIButton) {
        if typingDigitsTF.text?.count ?? 0 == 6 {
            presenter?.verifyCode()
        }
    }
    @IBAction func backAction(_ sender: UIButton) {
        navigationController?.popViewController(animated: true)
    }
}
