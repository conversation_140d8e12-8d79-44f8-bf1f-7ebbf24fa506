//
//  CheckCodeRouter.swift
//  PIL
//
//  Created by <PERSON> on 8/29/21.
//  
//

import Foundation
import UIKit

class CheckCodeRouter: CheckCodeRouterProtocol {

    weak var VC: CheckCodeViewProtocol?
    weak var viewController: UIViewController?

    static func createModule(phoneKey: String, phoneNumber: String, code: String) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .AuthST, VC: .CheckCodeVC)) as! CheckCodeVC
        let interactor = CheckCodeInteractor()
        let router = CheckCodeRouter()
        let presenter = CheckCodePresenter(view: view, router: router, interactor: interactor, error: view, type: .login, phone: phoneNumber, phoneKey: phoneKey, code: code)
        let authWorker = AuthWorker()
        let userWorker = UserWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.authWorker = authWorker
        interactor.error = presenter
        interactor.userWorker = userWorker
        router.VC = view
        return view
    }
    
    static func createModule(email: String, code: String , userID:String) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .AuthST, VC: .CheckCodeVC)) as! CheckCodeVC
        let interactor = CheckCodeInteractor()
        let router = CheckCodeRouter()
        let presenter = CheckCodePresenter(view: view, router: router, interactor: interactor, error: view, type: .reset, email: email, code: code)
        let authWorker = AuthWorker()
        presenter.userID = userID
        let userWorker = UserWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.authWorker = authWorker
        interactor.error = presenter
        interactor.userWorker = userWorker
        router.VC = view
        return view
    }
    
    func goStartSub(_ delegate: StartSubDelegat) {
        let startVc = StartSilverSubVC.loadFromNib()
        startVc.delegate = delegate
        startVc.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController{
            vc.navigationController?.present(startVc, animated: false)
        }
    }
    
    
    func chooseAvatar(){
//        let controller = SelectAvatarRouter.createModule(type: .none)
//        let navigationController = UINavigationController(rootViewController: controller)
//        navigationController.setNavigationBarHidden(true, animated: false)
//        let Delegate = UIApplication.shared.delegate as! AppDelegate
//        Delegate.window?.rootViewController = navigationController
        
        let avatar = CreateAvatarWebViewRouter.createModule(type: .signUp) as! CreateAvatarWebViewVC
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = avatar
        
    }
    
    func goToHome() {
        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = TBC
    }
    
    func goToChangeNumber(userID:String) {
        let enterNewNumber = EnterPhoneRouter.createModule(ueserID: userID) as! EnterPhoneVC
        push(view: enterNewNumber)
    }
    
    
    func push(view: UIViewController){
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(view, animated: true)
        }
    }
    
    func goToLogin() {
        if let vc = VC as? UIViewController{
            for controller in vc.navigationController!.viewControllers as Array{
                if controller.isKind(of: LoginVC.self){
                    _ = vc.navigationController?.popToViewController(controller, animated: true)
                }
            }
        }
    }
}
