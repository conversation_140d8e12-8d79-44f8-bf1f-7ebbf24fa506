//
//  CheckCodeInteractor.swift
//  PIL
//
//  Created by <PERSON> on 8/29/21.
//  
//

import Foundation

class CheckCodeInteractor: CheckCodeInteractorInputProtocol {
    
    //MARK: - variables
    weak var presenter: CheckCodeInteractorOutputProtocol?
    var authWorker: AuthWorkerProtocol?
    weak var error: ErrorProtocol?
    var userWorker: UserWorkerProtocol?
    //MARK: - getPhoneCode
    /// - Parameters:
    ///   - phone: String
    ///   - code: String
    ///   - completion: getPhoneCode call back completion
    func getPhoneCode(for phone: String, code: String) {
        authWorker?.getPhoneCode(for: phone, code: code, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.code == 200{
                    self.presenter?.generateCodeSuccessfully(code: model.message ?? "")
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - login
    /// - Parameters:
    ///   - model: loginRequestModel
    ///   - completion: login call back completion
    func login(model: loginRequestModel){
        authWorker?.Login(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.loginSuccessfully(model: model)
                    let currentDate = Date()
                    let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
                    GoogleAnalyticsHelper.shared.Login_success(login_datetime: stringDate, login_success: "true")
                    GoogleAnalyticsHelper.shared.trackLoginEvent(user: model.user ?? UserDataModel())

                   
                    
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - setDevice
    /// - Parameters:
    ///   - model: deviceRequestModel
    ///   - completion: setDevice call back completion
    func setDevice(model: deviceRequestModel) {
        authWorker?.setDevice(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    self.presenter?.setDeviceSuccessfully(model: model.data)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    //MARK: - resetPassword
    /// - Parameters:
    ///   - model: ResetPasswordRequestModel
    ///   - completion: resetPassword call back completion
    func resetPassword(model:ResetPasswordRequestModel){
        authWorker?.resetPassword(model: model, compiliation: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.GetresetPassword(userID: "\(model.data?.dataID ?? 0)")
                        let data = UserDataModel()
                        data.token = "Bearer \(model.data?.token ?? "")"
                        UserModel.shared.setData(model:data)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    //MARK: - setUserinFirebase
    /// - Parameters:
    ///   - userID: String
    ///   - model: UserChatModel
    ///   - completion: setUserinFirebase call back completion
    func setUserinFirebase(userID: String, model: UserChatModel) {
        userWorker?.uploadChatUser(userID: userID, model: model, compilition: { (Status, Error) in
            if let error = Error{
                print(error)
            }else{
                print("Success")
            }
        })
    }
}
