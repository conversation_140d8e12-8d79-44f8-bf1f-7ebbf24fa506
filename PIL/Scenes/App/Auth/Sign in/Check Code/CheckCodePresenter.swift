//
//  CheckCodePresenter.swift
//  PIL
//
//  Created by <PERSON> on 8/29/21.
//  
//

import Foundation
import BranchSDK

class CheckCodePresenter: CheckCodePresenterProtocol, CheckCodeInteractorOutputProtocol, ErrorProtocol {

    //MARK: - variables
    weak var view: CheckCodeViewProtocol?
    var error: ErrorProtocol?
    var router: CheckCodeRouterProtocol?
    var interactor: CheckCodeInteractorInputProtocol?
    var type: CheckCodeSource?
    var phone: String?
    var email: String?
    var phoneKey: String?
    var code: String?
    var user: UserDataModel?
    var userID = String()
    
    //MARK: - init
    init(view: CheckCodeViewProtocol,
         router: CheckCodeRouterProtocol,
         interactor: CheckCodeInteractorInputProtocol,
         error: ErrorProtocol,
         type: CheckCodeSource,
         phone: String,
         phoneKey: String,
         code: String){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
        self.phone = phone
        self.phoneKey = phone<PERSON>ey
        self.code = code
        self.type = type
    }
    
    init(view: CheckCodeViewProtocol,
         router: CheckCodeRouterProtocol,
         interactor: CheckCodeInteractorInputProtocol,
         error: ErrorProtocol,
         type: CheckCodeSource,
         email: String,
         code: String){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
        self.email = email
        self.code = code
        self.type = type
    }
    
    //MARK: - functions
    func viewDidLoad() {
        if type == .reset{
            view?.setData(email: email?.encrypt ?? "")
            view?.setCode(txt: "")
        }else{
            var phoneFinal = phone ?? ""
//            let key = phoneKey?.dropFirst() ?? "20"
            if phone?.first == "0"{
                phoneFinal = "\(phone?.dropFirst() ?? "")"
            }
//            phone = "\(key)\(phoneFinal)"
//            print(phoneFinal)
            
            if phoneFinal == "1008812862" ||  phoneFinal == "1062907963" ||  phoneFinal == "1115187658"  ||  phoneFinal == "10001234566"  ||  phoneFinal == "10001234567"  ||  phoneFinal == "10001234568"  ||  phoneFinal == "10001234569"  ||  phoneFinal == "1125905008"  {
                view?.setCode(txt: code ?? "")
            }else{
//                view?.setCode(txt: "")
            }
            view?.setData(phone: phone ?? "")
        }
    }
    
    func check(code: String){
        view?.codeCheck(result: self.code == code)
    }
    
    func sendAnewCode(){
        interactor?.getPhoneCode(for: phone ?? "", code: phoneKey ?? "")
    }
    
    func getDeviceData() -> deviceRequestModel{
        print(UserModel.shared.get_FCMToken())
        let obj = deviceRequestModel(model: UIDevice.current.model,
                                     release: "12.2",
                                     brand: UIDevice.current.model,
                                     broad: UIDevice.current.name,
                                     base: "",
                                     manufacturer: "Apple",
                                     platform: "IOS",
                                     userId: user?.id?.getValue,
                                     sdk: UIDevice.current.systemVersion,
                                     type: "user")
        return obj
    }
    
    func setFireBaseUser(){
        DispatchQueue.global(qos: .background).async{ [weak self] in
            guard let self = self else { return }
            let userModel = UserChatModel(name: UserModel.shared.get_username(),
                                          image: UserModel.shared.get_image(),
                                          FCM: UserModel.shared.get_FCMToken(),
                                          online: true)
            self.interactor?.setUserinFirebase(userID: UserModel.shared.get_id() ,model: userModel)
        }
    }
    
    //MARK: - interactor delegater functions
    func generateCodeSuccessfully(code: String) {        self.code = code
        view?.reInitiateScreen()
//        view?.setCode(txt: code)
    }
    
    func loginSuccessfully(model: loginModel) {
        if type == .reset{
            router?.goToChangeNumber(userID: "")
        }else{
            if let user = model.user{
                self.user = user
                UserModel.shared.setReferralCode(code: user.referralCode ?? "")
                UserModel.shared.loginAsGuest(status: false)
                UserModel.shared.setData(model: user)
                UserModel.shared.setLogin(value: true)
                setFireBaseUser()
                if model.user?.first_login ?? "0" == "0" {
                    router?.goStartSub(self)
                    let event = BranchEvent.standardEvent(.completeRegistration)
                    event.customData = [
                        "email": user.email ?? "",
                        "phone": user.phone ?? ""
                    ]
                    event.logEvent()
                }else{
                    router?.goToHome()
                    let event = BranchEvent.standardEvent(.login)
                    event.customData = [
                        "email": user.email ?? "",
                        "phone": user.phone ?? ""
                    ]
//                    event.logEvent()
                }
                interactor?.setDevice(model: getDeviceData())
                
            }
        }
    }
    
    func setDeviceSuccessfully(model: UserDeviceDataModel?) {
        guard let user = self.user else { return }
        
        if let device = model{
            user.device = device
        }
        UserModel.shared.loginAsGuest(status: false)
        UserModel.shared.setData(model: user)
        UserModel.shared.setLogin(value: true)
        setFireBaseUser()
        router?.goToHome()
    }
    
    func signUpSuccessfully() {
        router?.goToLogin()
    }
    
    //MARK: - error delegate function
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    //MARK: - action delegate functions
    func verifyCode() {
        if type == .reset{
            goToNewNumber()
        }else if type == .login{
            login()
        }
    }
    
    func login(){
        interactor?.login(model: loginRequestModel(phone: phone ?? "", verification: code ?? "", country_code: phoneKey ?? "" , language: UserModel.shared.getLanguage()))
    }
    
    func goToNewNumber(){
        // send request
        let request = ResetPasswordRequestModel(username: email, verification: Int(code ?? "") ?? 0, language: UserModel.shared.getLanguage())
        self.interactor?.resetPassword(model: request)
    }
    
    
    func GetresetPassword(userID: String) {
        router?.goToChangeNumber(userID: userID)

    }
}

extension CheckCodePresenter: StartSubDelegat{
    func onStart() {
        router?.chooseAvatar()
    }
}
