//
//  CheckCodeProtocols.swift
//  PIL
//
//  Created by <PERSON> on 8/29/21.
//  
//

import Foundation

protocol CheckCodeViewProtocol: AnyObject {
    var presenter: CheckCodePresenterProtocol? { get set }
    func setData(phone: String)
    func setData(email: String)
    func reInitiateScreen()
    func codeCheck(result: Bool)
    func setCode(txt: String)
//    func isNumMob(value: Bool)
}

protocol CheckCodePresenterProtocol: AnyObject {
    var view: CheckCodeViewProtocol? { get set }
    func viewDidLoad()
    func check(code: String)
    func sendAnewCode()
    func verifyCode()
}

protocol CheckCodeInteractorInputProtocol {
    var presenter: CheckCodeInteractorOutputProtocol? { get set }
    func getPhoneCode(for phone: String, code: String)
    func login(model: loginRequestModel)
    func setDevice(model: deviceRequestModel)
    func resetPassword(model:ResetPasswordRequestModel)
    func setUserinFirebase(userID:String, model: UserChatModel)
}

protocol CheckCodeInteractorOutputProtocol: AnyObject {
    func generateCodeSuccessfully(code: String)
    func loginSuccessfully(model: loginModel)
    func setDeviceSuccessfully(model: UserDeviceDataModel?)
    func GetresetPassword(userID:String)
}

protocol CheckCodeRouterProtocol {
    func goToHome()
    func chooseAvatar()
    func goToChangeNumber(userID:String)
    func goToLogin()
    func goStartSub(_ delegate: StartSubDelegat)
}

