//
//  Auth Validator.swift
//  PIL
//
//  Created by <PERSON> on 8/27/21.
//

import Foundation


protocol AuthValidatorProtocol{
    
    /// validate phone number
    /// - Parameter number: response objectwith error and message
    func validate(number: String) -> ValidatorResponse
    
    /// validate email
    /// - Parameter number: response objectwith error and message
    func validate(email: String) -> ValidatorResponse
}


class AuthValidator: AuthValidatorProtocol{
    
    func validate(number: String) -> ValidatorResponse {
        if number.isEmpty{
            return ValidatorResponse(error: true, msg: "Please Enter non Empty number".localized)
        }
        
        let phoneNumberRegex = "(^[0-9]{11})|(^[0-9]{10})$"
        let phoneTest = NSPredicate(format:"SELF MATCHES %@", phoneNumberRegex)
        let result = phoneTest.evaluate(with: number)
        if !result{
            return ValidatorResponse(error: true, msg: "Please Enter a valid phone number".localized)
        }
        
        
        return ValidatorResponse(error: false, msg: nil)
    }
    
    
    func validate(email: String) -> ValidatorResponse {
        if email.isEmpty{
            return ValidatorResponse(error: true, msg: "Please Enter non Empty Email".localized)
        }
        
        if !(email.checkEmail){
            return ValidatorResponse(error: true, msg: "Please Enter a valid Email".localized)
        }
        
        return ValidatorResponse(error: false, msg: nil)
    }
}
