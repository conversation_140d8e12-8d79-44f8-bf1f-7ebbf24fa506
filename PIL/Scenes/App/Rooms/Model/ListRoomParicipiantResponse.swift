//
//  ListRoomParicipiantResponse.swift
//  PIL
//
//  Created by admin on 30/09/2023.
//

import Foundation

struct ListRoomParicipiantResponse: Codable {
    
    let roomParicipiantRecords: [RoomParicipiantRecords]?
    let message: String?
    let success: Bool?
    let statusCode, totalCount: Int?

    
}

struct RoomParicipiantRecords: Codable {
    
    let id, peerID: Int?
    let isPeerFilter: Bool?
    let createdBy: Int?
    let creationDateStr: String?
    let isDeleted: Bool?
    let modificationDateStr: String?
//    let modifiedBy: JSONNull?
    let roomID: Int?
    let createdName: String?
    let createdImageURL: String?
    let blocked: Bool?
//    let administrator: JSONNull?
    let isFollowers: Bool?
//    let isFavourite: JSONNull?
    let isOwner: Bool?
//    let bio: JSONNull?
    
    
    enum CodingKeys: String, CodingKey {
        case id, peerID, isPeerFilter, createdBy, creationDateStr, isDeleted, modificationDateStr
//        case modifiedBy
        case roomID, createdName
        case createdImageURL = "createdImageUrl"
        case blocked
        //        case  administrator
        case isFollowers
//        case isFavourite
        case isOwner
//        case bio
    }
    
}
