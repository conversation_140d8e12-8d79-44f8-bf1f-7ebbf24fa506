//
//  SocialRoomsRouter.swift
//  PIL
//
//  Created by admin on 27/09/2023.
//

import Foundation

class SocialRoomsRouter: SocialRoomsRouterProtocol {

    var view: SocialRoomsViewProtocol?
    
    static func createModule() -> UIViewController{
        
        var story : UIStoryboard
        story = UIStoryboard(name: "Rooms", bundle: nil)
        let vc = story.instantiateViewController(withIdentifier: "SocialRoomsVC") as! SocialRoomsVC
        var workerRooms = RoomsWorker()
        let preseenter = SocialRoomsPresenter()
        let interactor = SocialRoomsInteractor()
        let router = SocialRoomsRouter()
        
        preseenter.view = vc
        preseenter.interactor = interactor
        preseenter.router = router
        vc.presenter = preseenter
        interactor.presenter = preseenter
        interactor.workerRooms = workerRooms
        router.view = vc
        interactor.error = preseenter
        
        return vc
    }
    
    /// view all members room navigate to it
    func viewMembersRooms(roomID:Int) {
        let vc = AllMemberRoomsRouter.createModule(roomID: roomID) as! AllMemberRoomsVC
        if let view = view as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
    
    // create new room
    func viewCreateRoom() {
        let vc = CreateRoomRouter.createModule() as! CreateRoomVC
        vc.modalPresentationStyle = .fullScreen
        
        if let view = view as? UIViewController {
            view.navigationController?.pushViewController(vc, animated: true)
//            view.navigationController?.present(vc, animated: true)
        }
    }
    
    /// navigate to room
    func toRoom(roomID:Int , isMyRoom:Bool){
        let vc = SocialChatRotur.createModule(roomID:roomID, isMyRoom:isMyRoom) as! SocialChatVC
        vc.modalPresentationStyle = .fullScreen
        if let view = view as? UIViewController {
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
}
