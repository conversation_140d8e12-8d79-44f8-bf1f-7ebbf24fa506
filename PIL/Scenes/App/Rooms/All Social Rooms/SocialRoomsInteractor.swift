//
//  SocialRoomsInteractor.swift
//  PIL
//
//  Created by admin on 27/09/2023.
//

import Foundation

class SocialRoomsInteractor: SocialRoomsInputInteractorProtocol {    
    
    // MARK: - Properties
    
    var presenter: SocialRoomsOutputInteractorProtocol?
    var error: ErrorProtocol?
    var workerRooms:RoomsWorker?

    /// get topics room
    func getRoomListTopic() {
        SocialWorker.shared.getListRoomTopic { [weak self] (result, statusCode) in
            switch result {
            case .success(let response):
                //                if 200...299 ~= ( response.statusCode ?? 0 ) {
                print("response.emojiRecord ===>",response.emojiRecords)
                self?.presenter?.didCompleteGetRoomTopic(response.emojiRecords ?? [], response.message)
                //                }else {
                //                    self?.presenter?.didCompleteGetRoomTopic([], response.message)
                //                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self?.error?.featching(error: localizedError)
                } sessionExpired: {
                    self?.error?.sessionExpired?()
                } noInternet: {
                    self?.error?.noInternet?()
                }
            }
        }
    }
    
    
    
    /// get list of rooms
    func getRoomList(
        topicId: Int?,
        _ userId: String?,
        _ pageIndex: Int,
        _ pageSize: Int,
        isMyRoom: Bool?
    ) {
        var payload = Payloads.ListRoomPayload()
        
        payload.createdBy = Int(UserModel.shared.get_id())!
        payload.pageIndex = pageIndex
        payload.pageSize = pageSize
        payload.isDesc = true
//        payload.isMyRoom = isMyRoom
        if !userId.isNilOrEmpty{
            payload.createdBy = Int(userId!) ?? 0
        }else{
            payload.createdBy = Int(UserModel.shared.get_id()) ?? 0
        }
        payload.roomRecord = Payloads.RoomRecord(roomType: 2,topicId: topicId,isMyRoom: isMyRoom)
        
        SocialWorker.shared.getRoomList(payload) { [weak self] (result, statusCode) in
            switch result {
            case .success(let response):
                if 200...299 ~= ( response.statusCode ?? 0 ) {
                    isMyRoom == true ? self?.presenter?.didCompleteLoadMyRoom(response.roomRecords ?? [], nil) :  self?.presenter?.didCompleteGetRoomList(response.totalCount ,response.roomRecords ?? [], nil)
                }else {
                    self?.presenter?.didCompleteGetRoomTopic([], response.message)
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self?.error?.featching(error: localizedError)
                } sessionExpired: {
                    self?.error?.sessionExpired?()
                } noInternet: {
                    self?.error?.noInternet?()
                }
            }
        }
        
    }
    
    /// delete room
    func deleteRoom(roomId: Int?) {
        var payLoad = Payloads.DeleteRoomPayLoad()
        payLoad.createdBy = Int(UserModel.shared.get_id())!
        payLoad.roomRecord = Payloads.DeleteRoomRecord(id: roomId ?? 0)
        SocialWorker.shared.deleteRoom(payLoad) { [weak self] (result, statusCode) in
            switch result {
            case .success(let response):
                if 200...299 ~= ( response.statusCode ?? 0 ) {
                    self?.presenter?.didCompleteDeleteRoom(nil)
                }else {
                    self?.presenter?.didCompleteDeleteRoom(response.message)
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self?.error?.featching(error: localizedError)
                } sessionExpired: {
                    self?.error?.sessionExpired?()
                } noInternet: {
                    self?.error?.noInternet?()
                }
            }
            
        }
    }
    
}

//MARK: - join Room
extension SocialRoomsInteractor {
    func joinRoom(model: RoomJoinLeaveRM){
        workerRooms?.joinRoom(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success join Room
                    self.presenter?.joinRoomSuccessfully()
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
