//
//  SocialRoomsPresenter.swift
//  PIL
//
//  Created by admin on 26/09/2023.
//


import Foundation

class SocialRoomsPresenter: SocialRoomsPresenterProtocol, SocialRoomsOutputInteractorProtocol, ErrorProtocol {
    
    // MARK: - Properties
    
    var router: SocialRoomsRouterProtocol?
    var view: SocialRoomsViewProtocol?
    var interactor: SocialRoomsInputInteractorProtocol?
    var roomsTopic : [RoomTopicEmojiRecords] = []
    var listRoom: [SocialRoomRecord] = []
    var userMyRoom: SocialRoomRecord?
    var selectedRoom:SocialRoomRecord?
    var isMyRoom = false
    var isPaginating = true
    var topicId: Int? = nil
    var total_Count : Int = 0
    private var pageSize: Int = 5
    private var pageIndex: Int = 0
    private let group = DispatchGroup()
    var header = ZVRefreshNormalHeader()
    var footer = ZVRefreshBackAnimationFooter()
    var firstPage = true

    // MARK: - Private
    
    private func resetRoomView() {
        self.listRoom.removeAll()
        topicId = nil
        pageIndex = 0
        view?.reloadRoomListData()
    }

    // MARK: - SocialRoomsPresenterProtocol Methods
    
    /// get my room and other room when screen load a frist time and categories
    func viewDidLoad() {
        interactor?.getRoomListTopic()
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: true)
    }
    
    /// reload my room and other room when back from room to all rooms and categories
    func viewWillAppear() {
        resetRoomView()
        interactor?.getRoomListTopic()
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: true)
    }
    
    func initObjects(){
        firstPage = true
        listRoom.removeAll()
    }
    /// refresh screen data
    func addDataHandler() {
        header.refreshHandler = { [self] in
            initObjects()
            footer.isNoMoreData = false
            viewWillAppear()
        }
        view?.updateRoomHeader()
        footer.refreshHandler = { [self] in
            firstPage = false
            if listRoom.count < total_Count {
                self.pageIndex += 1
                interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
            }else{
                footer.isNoMoreData = true
            }
        }
        view?.updateRoomFooter()
    }
    
    /// update list rooms
    func updateList(data : [SocialRoomRecord]){
        for group in data {
            if !(listRoom.contains(where: { $0.id == group.id })) {
                listRoom.append(group)
            }
        }
        view?.updateRoomData()
    }
    
    /// pagination
    func willDisplayCell(_ indexPath: IndexPath) {
        print("PAGE ROOM IS",indexPath.row , "---",listRoom.count-1)
        if (indexPath.row) < (listRoom.count-1){
            view?.showTableViewActivityIndicator()
            self.pageIndex += 1
            interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
        }
    }
    
    /// when select topic
    func loadRoomWithTopicId() {
        self.listRoom.removeAll()
        self.view?.reloadRoomListData()
        pageIndex = 0
        isPaginating = true
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
    }

    /// delete room
    func deleteRoom() {
        interactor?.deleteRoom(roomId: userMyRoom?.id)
        self.listRoom.removeAll()
        pageIndex = 0
        interactor?.getRoomList(topicId: topicId, nil, pageIndex, pageSize, isMyRoom: false)
    }
    
    /// open all members room
    func openAllmembers() {
        router?.viewMembersRooms(roomID: userMyRoom?.id ?? 0)
    }
    
    /// create room if i not have one
    func openCreateRoom() {
        router?.viewCreateRoom()
    }
    
    /// when press join button send api join roon and navigate to room
    func joinRoom(index:Int){
        self.isMyRoom = false
        let roomID = self.listRoom[index].id ?? 0
        self.selectedRoom = self.listRoom[index]
        interactor?.joinRoom(model: RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID:roomID),
                                                    createdBy:Int(UserModel.shared.get_id()) ?? 0))
    }
    
    
    /// join my room because i am owner
    func JoinMyRoom(){
        self.isMyRoom = true
        self.selectedRoom = self.userMyRoom
        interactor?.joinRoom(model: RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID:self.userMyRoom?.id),
                                                    createdBy:Int(UserModel.shared.get_id()) ?? 0))
    }
    
    //MARK: - SocialRoomsOutputInteractorProtocol Methods
    
    /// respose get topic
    func didCompleteGetRoomTopic(_ data: [RoomTopicEmojiRecords], _ error: String?) {
        roomsTopic = data
        view?.reloadRoomTopicData()
    }
    
    /// respose get room list
    func didCompleteGetRoomList(_ totalCount : Int?,_ data: [SocialRoomRecord], _ error: String?) {
        guard error.isNilOrEmpty else{
            view?.onError(error!)
            return
        }
        guard !data.isEmpty else{
            isPaginating = false
            view?.hideTableViewActivityIndicator()
            return
        }

        print("listRoom =>>>>", listRoom)
        print("data =>>>>", data)
        total_Count = totalCount ?? 0
//        listRoom += data
        updateList(data: data)
//        view?.endRefresh()
        listRoom.isEmpty ? view?.reloadEmptyRoomView() : view?.reloadRoomListData()
    }
    
    /// response get my room data if i have
    func didCompleteLoadMyRoom(_ data: [SocialRoomRecord], _ error: String?) {
        print("My Room ===>>>>", data)
        self.userMyRoom = data.first
        view?.reloadMyRoomView()
    }

    /// respose when delete room
    func didCompleteDeleteRoom(_ error: String?) {
        guard error.isNilOrEmpty else {
            view?.onError(error!)
            return
        }
        self.userMyRoom = nil
        self.view?.reloadMyRoomView()
    }
    
    
    /// when join room successfully navigate to room
    func joinRoomSuccessfully() {
        KeyCenter.Token = self.selectedRoom?.token ?? ""
        KeyCenter.ChannelID = self.selectedRoom?.channelName ?? ""
        KeyCenter.AppId = self.selectedRoom?.appID
        self.router?.toRoom(roomID: self.selectedRoom?.id ?? 0 , isMyRoom:self.isMyRoom)
    }
    
    // MARK: - ErrorProtocol Methods
    
    func sessionExpired() {
        view?.onSessionExpiration()
    }
    
    func featching(error: String) {
        view?.onError(error)
    }
    
}



