//
//  RoomsProtocols.swift
//  PIL
//
//  Created by admin on 26/09/2023.
//


import Foundation

protocol SocialRoomsViewProtocol {
    var presenter: SocialRoomsPresenterProtocol? { get }
    func onError(_ msg: String)
    func onSessionExpiration()
    func reloadRoomTopicData()
    func reloadRoomListData()
    func reloadMyRoomView()
    func reloadEmptyRoomView()
    func endRefresh()
    func showTableViewActivityIndicator()
    func hideTableViewActivityIndicator()
    func updateRoomData()
    func updateRoomFooter()
    func updateRoomHeader()
}


protocol SocialRoomsRouterProtocol {
    var view: SocialRoomsViewProtocol? { get }
    func viewMembersRooms(roomID:Int)
    func viewCreateRoom()
    func toRoom(roomID:Int , isMyRoom:Bool)
}


protocol SocialRoomsPresenterProtocol {
    var router: SocialRoomsRouterProtocol? { get }
    var view: SocialRoomsViewProtocol? { get }
    var interactor: SocialRoomsInputInteractorProtocol? { get }
    var roomsTopic: [RoomTopicEmojiRecords] {get}
    var listRoom: [SocialRoomRecord] {get set}
    var userMyRoom: SocialRoomRecord? { get set }
    var topicId: Int? {set get}
    var total_Count : Int {set get}
    var header : ZVRefreshNormalHeader {get set}
    var footer : ZVRefreshBackAnimationFooter {get set}
    func viewDidLoad()
    func viewWillAppear()
    func willDisplayCell(_ indexPath: IndexPath)
    func initObjects()
    func addDataHandler()
    func updateList(data : [SocialRoomRecord])
    func loadRoomWithTopicId()
    func deleteRoom()
    func openAllmembers()
    func openCreateRoom()
    func joinRoom(index:Int)
    func JoinMyRoom()
}

protocol SocialRoomsInputInteractorProtocol{
    var presenter: SocialRoomsOutputInteractorProtocol? { get }
    func getRoomListTopic()
    func getRoomList(topicId: Int?, _ userId: String?,_ pageIndex: Int,_ pageSize: Int, isMyRoom: Bool?)
    func deleteRoom(roomId: Int?)
    func joinRoom(model: RoomJoinLeaveRM)
}

protocol SocialRoomsOutputInteractorProtocol{
    func didCompleteGetRoomTopic(_ data: [RoomTopicEmojiRecords],_ error: String?)
    func didCompleteGetRoomList(_ totalCount : Int?,_ data: [SocialRoomRecord], _ error: String?)
    func didCompleteDeleteRoom(_ error: String?)
    func didCompleteLoadMyRoom(_ data: [SocialRoomRecord], _ error: String?)
    func joinRoomSuccessfully()
}
