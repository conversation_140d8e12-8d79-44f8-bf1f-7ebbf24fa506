//
//  RoomsVC.swift
//  PIL
//
//  Created by admin on 26/09/2023.
//


import UIKit
import DropDown
import AnimatableReload

// ListUsersChatVC

enum TypeFilterRoom: Int {
    case delete = 1
    case members
}

class SocialRoomsVC: UIViewController, SocialRoomsViewProtocol {
    
    // MARK: - @IBOutlets
    @IBOutlet weak var roomsCollectionView: UICollectionView!
    @IBOutlet weak var roomOwnerNameLabel: UILabel!
//    @IBOutlet weak var roomsTableView: UITableView!
    @IBOutlet weak var filtersCollectionView: UICollectionView!
    @IBOutlet weak var createRoomButton: UIButton!
    @IBOutlet weak var createRoomView: UIView!
    @IBOutlet weak var userRoomView: UIView!
//    @IBOutlet weak var lineView: UIView!
    @IBOutlet weak var menuContainer: UIView!
    @IBOutlet weak var arMenuContainer: UIView!
    @IBOutlet weak var roomNameLabel: UILabel!
    @IBOutlet weak var iconImageUrl: UIImageView!
    @IBOutlet weak var memberCountLabel: UILabel!
    @IBOutlet weak var noRoomAvalibaleLabel: UILabel!
    @IBOutlet weak var enterButton: UIButton!
    @IBOutlet weak var scrollUpBtn: UIButton!

    // MARK: - Properties
    static let identifier = "roomsCollectionViewCell"
    static let nib = UINib(nibName: "roomsCollectionViewCell", bundle: nil)
    var action: checkUserLoginProtocol?
    var presenter: SocialRoomsPresenterProtocol?
    var type:TypeFilterRoom?
    let menu: DropDown = {
        let dropDown = DropDown()
        dropDown.backgroundColor = .init(named: "brown")
        dropDown.cornerRadius = 15
        dropDown.textColor = .init(named: "Black-White")!
        dropDown.selectedTextColor = .init(named: "Black-White")!
        dropDown.textFont = UIFont(name: "Poppins-Light" , size: 14)!
        dropDown.selectionBackgroundColor = .clear
        return dropDown
    }()
//    var refreshControl: UIRefreshControl?

    // MARK: - Life Cycel: -
    override func viewDidLoad() {
        super.viewDidLoad()
        
        /// ui cell room
        setupCell()
        // menu dorpdown
        setupMenu()
        setupRefreshControl()
        /// titles buttons
        enterButton.setTitle("enter".localized, for: .normal)
        createRoomButton.setTitle("create.room".localized, for: .normal)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // start with frist page in api's
        presenter?.initObjects()
        // get all room screen  data 
        presenter?.addDataHandler()
        presenter?.viewWillAppear()
    }
        
    // MARK: - SocialRoomsViewProtocol Method
    
    /// reload rooms with animation
    func reloadRoomTopicData() {
        AnimatableReload.reload(collectionView: filtersCollectionView, animationDirection: "left")
    }
    
    /// reload rooms list
    func reloadRoomListData() {
        self.roomsCollectionView.isHidden = false
        self.noRoomAvalibaleLabel.isHidden = true
        self.roomsCollectionView.reloadData()
    }
    
    /// if no room
    func reloadEmptyRoomView() {
        self.noRoomAvalibaleLabel.text = "no.rooms.message".localized
        self.roomsCollectionView.isHidden = true
        self.noRoomAvalibaleLabel.isHidden = false
        self.roomsCollectionView.reloadData()
    }

    /*
     two case
     1 - show my created room view info i have
     2 - show create room if don't have one
     */
    
    func reloadMyRoomView() {
        if presenter?.userMyRoom != nil {
//            lineView.isHidden = true
            createRoomView.isHidden = true
            userRoomView.isHidden = false
            self.setUserMyRoomData()
        }else {
//            lineView.isHidden = false
            createRoomView.isHidden = false
            userRoomView.isHidden = true
        }
    }
    
    /// error handlert
    func onError(_ msg: String) {
        showAlert(withTitle: true, msg: msg, compilition: nil)
    }
    
    /// sesstion expire
    func onSessionExpiration() {
        loginAlert(compilition: {
            UserModel.shared.logOut()
        })
    }
    /// reload data
    func showTableViewActivityIndicator() {
//        roomsCollectionView.tableFooterView?.isHidden = false
        roomsCollectionView.reloadData()
    }

    /// end refresh
    func hideTableViewActivityIndicator() {
//        roomsCollectionView.tableFooterView?.isHidden = true
        roomsCollectionView.header?.endRefreshing()
        roomsCollectionView.footer?.endRefreshing()
        roomsCollectionView.reloadData()
    }
    
    func endRefresh() {
//        refreshControl?.endRefreshing()
    }
    
    // MARK: - Paganition
    
    /*
     update room heder if room created
     hidden indicator refresh
     when finish pagination hide footer indicator
     */
    
    func updateRoomHeader() {
        roomsCollectionView.header = presenter?.header
    }
    
    func updateRoomFooter() {
        roomsCollectionView.footer = presenter?.footer
    }
    
    func updateRoomData() {
        roomsCollectionView.header?.endRefreshing()
        roomsCollectionView.footer?.endRefreshing()
        roomsCollectionView.reloadData()
    }
    

    // MARK: - Privates
    
    /// UI Screen
    private func setupCell() {
        roomsCollectionView.delegate = self
        roomsCollectionView.dataSource = self
        filtersCollectionView.register(FilterCollectionViewCell.nib, forCellWithReuseIdentifier: FilterCollectionViewCell.identifier)
        roomsCollectionView.register(RoomCollectionViewCell.nib, forCellWithReuseIdentifier: RoomCollectionViewCell.identifier)
    }
    
    
    /// display my room info
    private func setUserMyRoomData() {
        let userMyRoom = presenter?.userMyRoom
        self.roomNameLabel.text = userMyRoom?.roomName
        self.roomOwnerNameLabel.text = userMyRoom?.createdName
        self.iconImageUrl.sd_setImage(with: .init(string: userMyRoom?.iconURL ?? ""))
        self.memberCountLabel.text = "\(userMyRoom?.viewerCount ?? 0) "
    }
    
    /// menu UI with actions
    private func setupMenu() {
        
        menu.anchorView = app_lang == "ar" ? arMenuContainer : menuContainer
        menu.dataSource = [ "delete".localized, "members".localized]
        menu.selectionAction = { [self] (index, _) in
            switch index {
            case 0:
                type = .delete
                presenter?.deleteRoom()
            case 1:
                type = .members
                presenter?.openAllmembers()
            default: break
            }
        }
        menu.cancelAction = { [unowned self] in
            print("Drop down dismissed")
            if app_lang == "ar" { arMenuContainer.isHidden = true }
            else { menuContainer.isHidden = true }
            menu.hide()
        }
    }
    
    private func setupRefreshControl() {
//        refreshControl = UIRefreshControl()
//        refreshControl?.addTarget(self, action: #selector(self.refresh(_:)), for: .valueChanged)
//        roomsCollectionView.addSubview(refreshControl!)
    }
    
    /// animation
    private func showScrollUpBtn(){
        scrollUpBtn.isHidden = false
        UIView.animate(withDuration: 0.2) {
            self.scrollUpBtn.alpha = 1
        }
    }
    
    private func hideScrollUpBtn(){
        UIView.animate(withDuration: 0.2) {
            self.scrollUpBtn.alpha = 0
        } completion: { _ in
            self.scrollUpBtn.isHidden = true
        }
    }

    /// when refrsh screen
    @objc func refresh(_ sender: AnyObject) {
        presenter?.viewWillAppear()
        self.roomsCollectionView.reloadData()
    }

    // MARK: - @IBActions
    
    @IBAction func menuAction(_ sender: UIButton) {
        switch sender.tag{
        case 0:
            sender.tag = 1
            if app_lang == "ar" { arMenuContainer.isHidden = false }
            else { menuContainer.isHidden = false }
            menu.show()
        case 1:
            sender.tag = 0
            if app_lang == "ar" { arMenuContainer.isHidden = true }
            else { menuContainer.isHidden = true }
            menu.hide()
        default: break
        }
        
    }
    
    /// when join my room
    @IBAction func enterAction(_ sender: Any) {
        self.presenter?.JoinMyRoom()
    }
    
    /// navigate to create room screen
    @IBAction func createAction() {
        
        if UserModel.shared.get_loginAsGuest() == true{
            self.action?.isLogin()
        }else{
            presenter?.openCreateRoom()
        }
    }
    
    @IBAction func scrollToTopAction(_ sender: Any) {
//        roomsCollectionView.scrollToRow(at: .init(row: 0, section: 0), at: .top, animated: true)
        var index = IndexPath(item: 0, section: 0)
        roomsCollectionView.scrollToItem(at: index , at: .top , animated: true)
        self.hideScrollUpBtn()
    }

}


// MARK: - CollectionViewDataSource & CollectionViewDelegate

extension SocialRoomsVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == filtersCollectionView {
            return presenter?.roomsTopic.count ?? 0
        }else{
            return presenter?.listRoom.count ?? 0
        }
        
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == filtersCollectionView {
            /// topics
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FilterCollectionViewCell.identifier, for: indexPath) as! FilterCollectionViewCell
            let model = presenter?.roomsTopic[indexPath.row]
            cell.loadData(for: model)
//            let name = app_lang == "en" ? model?.nameEn : model?.nameAr
//            cell.load(from: SelectableFilter(name: name ?? "" , selected: true))
//            if presenter?.roomsTopic[indexPath.row].id == presenter?.topicId{
//                cell.name.font = UIFont(name: "Poppins-Medium", size: 14)
//                cell.containerView.layer.borderColor = UIColor(named: "Black-White")?.cgColor
//            }else{
//                cell.name.font = UIFont(name: "Poppins-Light", size: 14)
//                cell.containerView.layer.borderColor = UIColor(named: "border")?.cgColor
//            }
            return cell
        }else{
            //rroms
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: RoomCollectionViewCell.identifier, for: indexPath) as! RoomCollectionViewCell
            cell.loadData(presenter?.listRoom[indexPath.row])
            cell.joinBu.tag = indexPath.row
            cell.joinBu.addTarget(self, action: #selector(joinRoom(_ :)), for: .touchUpInside)
            return cell
        }
    }
    
    
    /// when select topic get  rooms in this topic
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == filtersCollectionView {
            guard let roomTopic = self.presenter?.roomsTopic[indexPath.row].id, roomTopic != presenter?.topicId else {return}
            presenter?.topicId = roomTopic
            presenter?.loadRoomWithTopicId()
        }else{
            
        }
    }
    
    /// Size Cell in (Iphone - Ipad )
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == filtersCollectionView {
            return .init(width: collectionView.frame.width/4, height: collectionView.frame.height)
        }else{
            if UIScreen.main.bounds.width > 500{
                return .init(width: collectionView.frame.width/4, height: 260)
            }
            return .init(width: collectionView.frame.width/2, height: 260)
        }
        
    }
    
    func collectionView(_ collectionView: UICollectionView, willDisplay cell: UICollectionViewCell, forItemAt indexPath: IndexPath) {
        if collectionView == roomsCollectionView {
//            presenter?.willDisplayCell(indexPath)
            indexPath.row > 6 ? self.showScrollUpBtn() : self.hideScrollUpBtn()
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
}

// MARK: - UITableViewDelegate & UITableViewDataSource

extension SocialRoomsVC: UITableViewDelegate, UITableViewDataSource{
    
    /// numbers Row
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if presenter?.listRoom.count == 0 {
            /// if no rooms
            self.roomsCollectionView.setEmptyMessage(bigTitle: "No Rooms Avaliable".localized, smallTitle: "")
        }else{
            self.roomsCollectionView.restore()
        }
        return presenter?.listRoom.count ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: RoomTableViewCell.identifier, for: indexPath) as! RoomTableViewCell
        print("---->",indexPath.row , presenter?.listRoom.count)
        cell.loadData(presenter?.listRoom[indexPath.row])
        cell.joinBu.tag = indexPath.row
        cell.joinBu.addTarget(self, action: #selector(joinRoom(_ :)), for: .touchUpInside)
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        presenter?.willDisplayCell(indexPath)
        indexPath.row > 6 ? self.showScrollUpBtn() : self.hideScrollUpBtn()
    }
    
    @objc private func joinRoom(_ sender :UIButton){
        self.presenter?.joinRoom(index: sender.tag)
    }
    
}
