//
//  SocialChat+TB.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 05/10/2023.
//

import Foundation
import SignalRClient
import AVFoundation
import IQKeyboardManager
import DropDown


extension SocialChatVC:UITableViewDelegate , UITableViewDataSource , ActionsCaht{
    
    // number rows messages and users room
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        var number = 0
        if tableView == chatTableView{
            number = presenter?.messageCount() ?? 0
        }else{
            number = presenter?.usersRoomCount() ?? 0
        }
        return number
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == chatTableView{
            /// chat row display message info and user image
            let cell = self.chatTableView.dequeueReusableCell(withIdentifier: "ChatRoomCell", for: indexPath) as! ChatRoomCell
            presenter?.configuer(cell: cell, index: indexPath.row)
            cell.MessageSide(mySide: presenter?.isMyMessage(index: indexPath.row) ?? false)
            cell.userProfileGuestBu.tag = indexPath.row
            cell.userProfileGuestBu.addTarget(self, action: #selector(SelectUser(_:)), for: .touchUpInside)
            cell.actions = self
            return cell

        }else {
            /// members room in popup
            /// row have two action mic status and kick out for admin and he can change status or kickout any user
            /// for normal user he can show the user and status mic for each user
            let cell = self.membersTableView.dequeueReusableCell(withIdentifier: MemberRoomsCell.identifier , for: indexPath) as! MemberRoomsCell
            if let member =  self.presenter?.usersRoom?[indexPath.row]{
                cell.loadUI(member: member)
            }
        
            
            if presenter?.isMyRoom == true{
                cell.kickoutBu.isHidden = false
                cell.onMic = {
                    print("MIC From Admin")
                    self.sendMicRequsetOrCancelFromAdmin(index: indexPath.row)
                }
                
                cell.onKickout = {
                    print("On KickOut From Admin")
                    self.showConfirmActionsAlert(msg: "Are you sure from kick out this user from room?") {
                        let toUserId = self.presenter?.getuserRoomID(index: indexPath.row) ?? 0
                        self.sendKickoutUserFromRoom(toUserId: toUserId)
                    }                   
                }
                
                /// if admin show kickout
                if "\(self.presenter?.usersRoom?[indexPath.row].createdBy ?? 0 )" == UserModel.shared.get_id(){
                    cell.kickoutBu.isHidden = true
                }else{
                    cell.kickoutBu.isHidden = false
                }
                
                if indexPath.row > 0 {
                    cell.onUserProfile = {
                        self.presenter?.onTapUser(indexPath)
                    }
                }
            }else{
                cell.kickoutBu.isHidden = true
            }
            
            return cell

        }
    }
    
    /// if tab aother user in view all member
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if tableView == membersTableView{
            if presenter?.userIsOwner(index: indexPath.row) == false && presenter?.isMyRoom == true{ // open popup Permission
                self.openUserPopup(index: indexPath.row)
            }else{ // open user profile
                self.presenter?.onTapUser(indexPath)
            }
        }
    }
    
    /// if admin tap any user form view all members show popup with two option kickoit or( mute , unmute) user
    func openUserPopup(index:Int){
        if presenter?.userIsOwner(index: index) == false && presenter?.isMyRoom == true{
            let member = self.storyboard?.instantiateViewController(withIdentifier: "PopupMemberVC") as! PopupMemberVC
            member.indexUser = index
            member.actions = self
            if let user = self.presenter?.usersRoom?[index]{
                member.user = user
            }
            self.present(member, animated: false)
        }

    }
    func tableView(_ tableView: UITableView, contextMenuConfigurationForRowAt indexPath: IndexPath, point: CGPoint) -> UIContextMenuConfiguration? {
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil , actionProvider: nil)
    }

    @objc func SelectUser(_ sender:UIButton){
//        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.presenter?.getUserID(index: sender.tag) ?? 0)")) as! MainProfileVC
//        self.navigationController?.pushViewController(vc, animated: true)
    }
    
    func SelectCell(index:Int){
//        print("--->",presenter?.getTypeMessage(index: index) ?? 0)
//        if presenter?.getTypeMessage(index: index) ?? 0 == 1{
//            presenter?.selectRoom(index: index)
//        }
    }
    
    func openFile(url: String) {
 
    }
    
    
    /// when start scroll down in chat room  hidden keyboard
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        var lastVelocityYSign = 0
        let currentVelocityY =  scrollView.panGestureRecognizer.velocity(in: scrollView.superview).y
        let currentVelocityYSign = Int(currentVelocityY).signum()
        if currentVelocityYSign != lastVelocityYSign &&
            currentVelocityYSign != 0 {
            lastVelocityYSign = currentVelocityYSign
        }
        if lastVelocityYSign < 0 {
            print("SCROLLING DOWN")
        } else if lastVelocityYSign > 0 {
            print("SCOLLING UP")
            view.endEditing(true)
        }
    }
    
    
}


//MARK: - select member from members list home
extension SocialChatVC:popupMemberProtocol{

    ///Option selected from popup in all members
    ///mute or unmute send request to sockit
    ///kickout  send request to sockit

    func muteMember(index: Int) {
        self.sendMicRequsetOrCancelFromAdmin(index:index)
    }
    
    func kickoutMember(index: Int) {
        let toUserId = self.presenter?.getuserRoomID(index: index) ?? 0
        self.sendKickoutUserFromRoom(toUserId: toUserId)
    }
    
    func userProfile(indexPath: Int) {
        let index = IndexPath(item:indexPath , section: 0)
        self.presenter?.onTapUser(index)

    }
}




extension SocialChatVC:UICollectionViewDelegate , UICollectionViewDataSource , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        print("Users count is",self.presenter?.usersRoomCount() ?? 0)
        return self.presenter?.usersRoomCount() ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        print("Users count is 2",self.presenter?.usersRoomCount() ?? 0 , indexPath.row)
        let cell = self.usersCollectoinView.dequeueReusableCell(withReuseIdentifier: "UsersScoialRoomCell", for: indexPath) as! UsersScoialRoomCell
        self.presenter?.configuerUserRoom(cell: cell, index: indexPath.row)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        print("Index user is",indexPath.row)
        if self.presenter?.isMyRoom == true && self.presenter?.userIsOwner(index: indexPath.row) ?? false == false{
//            self.sendMicRequsetOrCancelFromAdmin(index: indexPath.row)
            self.openUserPopup(index: indexPath.row)

            
        }else if self.presenter?.userIsOwner(index: indexPath.row) ?? false == true{
            //if i am not admin room  send an invitation to admin to give me permission to speak

            if self.presenter?.OwnerID ?? 0 != Int(UserModel.shared.get_id()) ?? 0{
//                self.sendUserRequestMic(toUserId: self.presenter?.OwnerID ?? 0, isAdmin: false)
                print("-----> i am onwer room")
            }
        }
        
   

    }
    /// size users in top section in room screen
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 60, height: 60)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return -20
    }
    
    
    
    /// - Parameter index: to get user form list users
    func sendMicRequsetOrCancelFromAdmin(index:Int){
        ///if i am admin room send invitation to user to speak in room
        let toUserId = self.presenter?.getuserRoomID(index: index) ?? 0
        if presenter?.getuserRoomID(index: index) ?? 0 != Int(UserModel.shared.get_id()) ?? 0 && presenter?.checkUserInMic(index: index) == false{
            self.sendUserRequestMic(toUserId: toUserId,isAdmin: true)
            print("permission mic speack in room")

            /// if i am admin room cancel assess mic user to speak in room
        }else if presenter?.getuserRoomID(index: index) ?? 0 != Int(UserModel.shared.get_id()) ?? 0 && presenter?.checkUserInMic(index: index) == true{
            print("permission mic  Remove speack in room")
            self.SendMicStatus(fromUserId: Int( UserModel.shared.get_id()) ?? 0,
                               toRoomId: self.presenter?.roomID ?? 0 ,
                               on: false,
                               touserID: toUserId,
                               isAdmin:  self.presenter?.isMyRoom ?? false)
            
        }
    }
}


///Test filed delegate 
extension SocialChatVC: UITextFieldDelegate{
    func textFieldDidEndEditing(_ textField: UITextField) {
        UIView.animate(withDuration: 0.4) {
            self.buttomConstraint.constant = 0
            self.view.layoutIfNeeded()
        }
    }
}
