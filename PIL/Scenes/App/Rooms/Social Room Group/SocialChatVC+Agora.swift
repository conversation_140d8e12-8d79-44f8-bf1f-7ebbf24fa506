
//
//  LiveOneToOne+Agora.swift
//  PIL
//
//  Created by Diaa on 16/08/2023.
//

import Foundation
import AgoraRtcKit
import SignalRClient
extension SocialChatVC{
    
    
    func initializeAgoraEngine() {
        /// init AgoraRtcEngineKit
        agoraKit = AgoraRtcEngineKit.sharedEngine(withAppId:  KeyCenter.AppId ?? "" , delegate: self)
    }
    
    
    func setupAudio() {
        /// make myself a broadcaster
        agoraKit.setClientRole(.broadcaster)
        /// disable video module
        agoraKit.disableVideo()
        /// enable audio module
        agoraKit.enableAudio()
        /// Set audio route to speaker
        agoraKit.setDefaultAudioRouteToSpeakerphone(true)
        /// enable volume indicator
    }
    
    /// when join room must be have Token , ChannelID get it from backend in api join room
    func joinChannel() {
        agoraKit.setDefaultAudioRouteToSpeakerphone(true)
        agoraKit.joinChannel(byToken:  KeyCenter.Token, channelId: KeyCenter.ChannelID ?? "", info: nil, uid: UInt(Int(UserModel.shared.get_id()) ?? 0)) { [unowned self] (channel, uid, elapsed) -> Void in
            
            print("---Guest")
            agoraKit.setChannelProfile(.communication)
            let x = AgoraClientRoleOptions()
            x.audienceLatencyLevel = .lowLatency
            agoraKit.setClientRole(.broadcaster, options: x)
            agoraKit.muteLocalAudioStream(true)

            self.logVC?.log(type: .info, content: "did join channel")
        }
        
    }
    
    
    
}



//MARK: - functions agora
@available(iOS 13.0, *)
extension SocialChatVC: AgoraRtcEngineDelegate {
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinChannel channel: String, withUid uid: UInt, elapsed: Int) {
        print("Join \(channel) with uid \(uid) elapsed \(elapsed)ms")
    }
    
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didLeaveChannelWith stats: AgoraChannelStats) {
        print("Leave From Channel")
      
    }
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinedOfUid uid: UInt, elapsed: Int) {
        print("remote user join: \(uid) \(elapsed)ms")
        
    }
    
    /// Occurs when a remote user (Communication)/host (Live Broadcast) leaves a channel.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - uid: ID of the user or host who leaves a channel or goes offline.
    ///   - reason: Reason why the user goes offline
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOfflineOfUid uid:UInt, reason:AgoraUserOfflineReason) {
        print("Leave channel",reason)
       
    }
    
    /// Occurs when a remote user’s video stream playback pauses/resumes.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - muted: YES for paused, NO for resumed.
    ///   - byUid: User ID of the remote user.
    func rtcEngine(_ engine: AgoraRtcEngineKit, didVideoMuted muted:Bool, byUid:UInt) {
        //        isRemoteVideoRender = !muted
    }
    
    /// Reports a warning during SDK runtime.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - warningCode: Warning code
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurWarning warningCode: AgoraWarningCode) {
        logVC?.log(type: .warning, content: "did occur warning, code: \(warningCode.rawValue)")
        print( "did occur warning, code: \(warningCode.rawValue)")
    }
    
    /// Reports an error during SDK runtime.
    /// - Parameters:
    ///   - engine: RTC engine instance
    ///   - errorCode: Error code
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurError errorCode: AgoraErrorCode) {
        logVC?.log(type: .error, content: "did occur error, code: \(errorCode.rawValue)")
        print( "did occur warning, code: \(errorCode.rawValue)")
        
    }
}


