//
//  SocialChatPresenter.swift
//  PIL
//
//  Created by sameh mohammed on 31/05/2023.
//

import Foundation
import UIKit
import SignalRClient
import CoreData

class SocialChatPresenter:SocialChatPresenterProtocol , SocialChatInteractorOutputProtocol{

    
    var view: SocialChatViewProtocol?
    var error:ErrorProtocol?
    var interactor:SocialChatInteractorInputProtocol?
    var router:SocialChatRouterProtocol?
    var userChatRecords = [UserChatRecord]()
    var roomID:Int?
    var usersRoom:[RoomParicipiantRecordModel]?
    var isMyRoom:Bool?
    var OwnerID:Int?
    var skillsPlayer = 0
    var tournament: Tournament?
    var localGamesArray = [LocalGames]()
    var localGames = [NSManagedObject]()
    var isUpdate = false
    
    
    init(view: SocialChatViewProtocol,
         error:ErrorProtocol,
          interactor:SocialChatInteractorInputProtocol,
          router:SocialChatRouterProtocol) {
        self.view = view
        self.error = error
        self.interactor = interactor
        self.router = router
    }
    
    func viewDidLoad(){ 
        /// load all games from local
        getLocalGames()
        /// remove list in users
        usersRoom?.removeAll()
        /// get list user in room
        getUserRoomRequest()
        /// get room game if game created/
        interactor?.getPrivateRoomTournament(roomID!)
        /// get user skills to konw runk
        interactor?.getSkills(playerID: UserModel.shared.get_id())
    }
    
    /// navigate to screen ctrate tournament
    func onTapCreate() {
        router?.toCreateTournament(roomID!)
    }
    /// navigate to screen ctrate tournament
    func onTapPlayAction() {
        router?.toCreateTournament(roomID!)
    }
    
    //MARK: - chat and users Chat
    
    /// respons get users rooms
    func getListChat(data: listUserChatModel) {
        self.userChatRecords.append(contentsOf: data.userChatRecords ?? [])
    }
    
    /// get user id
    func getUserID(index:Int)->Int{ // when click user in chat
        return self.userChatRecords[index].createdBy ?? 0
    }
       
    /// get number of messages in chat
    func messageCount()->Int{
        return self.userChatRecords.count
    }
    
    /// configuer cell caht
    func configuer(cell:ChatCellProtocol, index:Int){
        cell.index(index: index)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEE, MMM d, yyyy - h:mm a"
        let date = dateFormatter.date(from: userChatRecords[index].creationDate ?? "")
        dateFormatter.dateFormat = "h:mm a"
        cell.dateMessage(date: dateFormatter.string(from: date ?? Date()))
        cell.textMessage(txt: userChatRecords[index].message ?? "")
        cell.typeMessage(type: userChatRecords[index].typeID ?? 0 , url: userChatRecords[index].message ?? "", voice: userChatRecords[index].voiceData, localMediaUrl: userChatRecords[index].localMediaUrl)
        if userChatRecords[index].createdBy ?? 0 == Int(UserModel.shared.get_id()) {
            cell.userData(image: userChatRecords[index].createdImageURL ?? "" , myImage: true)
        }else{
            cell.userData(image: userChatRecords[index].createdImageURL ?? "" , myImage: false)
        }
    }

    /// get user image caht
    func getImagegURL(index:Int)->String{
        return self.userChatRecords[index].message ?? ""
    }
    
    /// to know this message from me or other users
    func isMyMessage(index:Int)->Bool{
        if userChatRecords[index].createdBy ?? 0 == Int(UserModel.shared.get_id()) {
            return true
        }else{
            return false
        }
    }
    
    /// get tyep of message ( image - text - video - record - ...... )
    func getTypeMessage(index:Int)->Int{
        return self.userChatRecords[index].typeID ?? 0
    }
    
    /// get message text
    func getMessage(index:Int)->String{
        return self.userChatRecords[index].message ?? ""
    }
    
    /// add new message in caht list
    func setNewMessage(obj:UserChatRecord){
        self.userChatRecords.insert(obj, at: 0)
    }
    
    ///  retuen get all messages
    func getAllMessage()-> [UserChatRecord]{
        return self.userChatRecords
    }
    
   //MARK: - leave Room

    /// requset  leave from room
    func LeaveRoomRequest(){
        let request = RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: self.roomID),
                                     createdBy:  Int(UserModel.shared.get_id()) ?? 0)
        
        interactor?.leaveRoom(model: request)
    }
    
    
    /// response leave from room clear all room data
    func successLeaveRoom() {
        KeyCenter.ChannelID = ""
        KeyCenter.Token = ""
        self.view?.leaveRoom()
    }
    
    
    //MARK: - get user Rooms
    /// requset get users in room
    func getUserRoomRequest(){
        interactor?.GetUsersRoom(model: RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID: self.roomID),
                                                        createdBy:  Int(UserModel.shared.get_id()) ?? 0))
    }
    ///response get users room
    func getUsersList(user: [RoomParicipiantRecordModel]) {
        self.usersRoom =  user
        self.view?.ReloadCollectionView()
        self.getListRooms(RoomID: self.roomID ?? 0)

    }
    

    /// return user id in room
    func getuserRoomID(index:Int)->Int{// when click user in list roms
        return self.usersRoom?[index].createdBy ?? 0
    }
    
    /// configuer list of users join room
    func configuerUserRoom(cell:UsersScoialRoomCellProtocol, index:Int){
        cell.userInfo(image: usersRoom?[index].createdImageURL ?? "",
                      isMute: usersRoom?[index].isMute ?? false)
    }
    
    /// any user mic status is changed update in list UI
    func changeStatusMute(id:Int,ststus:Bool){
        for i in 0..<(self.usersRoom?.count ?? 0){
            if self.usersRoom?[i].createdBy ?? 0 == id{
                self.usersRoom?[i].isMute = ststus
                self.view?.ReloadCollectionView()
                return
            }
        }
    }
    
    
    /// when new user is join in room check it is exixt or not , if not add in list of user
    func addUser(image:String , name:String , id:Int){
        let isExsit = usersRoom?.contains{ $0.id == id}
        if isExsit == false{
            let user = RoomParicipiantRecordModel(id: id, createdBy: id, createdName: name, createdImageURL: image)
            self.usersRoom?.append(user)
            self.view?.ReloadCollectionView()
        }
       
    }

    func usersRoomCount()->Int{
        return self.usersRoom?.count ?? 0
    }
    
    /// when user leave from room remove it from list and update ui
    func removeUser(id:Int){
        for i in 0..<(self.usersRoom?.count ?? 0) {
            if usersRoom?[i].createdBy  ?? 0 == id{
                self.usersRoom?.remove(at: i)
                self.view?.ReloadCollectionView()
                return
            }
        }
    }

    
    /// request get user list
    func getListRooms(RoomID:Int) {
        interactor?.getListRoom(model: CreateRoomRequstModel(createdBy:Int(UserModel.shared.get_id()) ?? 0 ,RoomRecord: CreateRoomRoomRecordRM(id:RoomID)))
    }
    
    /// get user list and add isMute variable in list and the default value is false
    func successfullyCreatedRoom(data: RoomModelRecord) {
        self.OwnerID = data.createdBy ?? 0
        for i in 0..<(self.usersRoom?.count ?? 0){
//            if usersRoom?[i].createdBy ?? 0 == OwnerID{
//                self.usersRoom?[i].isMute = true
//            }else{
                self.usersRoom?[i].isMute = false
//            }
        }
        self.view?.RoomInfo(roomName: data.roomName ?? "")
        self.view?.ReloadCollectionView()
    }
    
    /// check user is mute or not
    func checkUserInMic(index:Int)->Bool{
        return self.usersRoom?[index].isMute ?? false
    }
    
    /// check this user is owner of room or not
    func userIsOwner(index:Int)->Bool{
        if self.usersRoom?[index].createdBy ?? 0 == self.OwnerID{
            return true
        }else{
            return false
        }
    }
    
    /// respose get tournament info
    func fetchPrivateRoomTournament(_ data: Tournament?) {
        self.tournament = data
        view?.onFetchTournament(data)
    }
    
    /// when click join tournament
    func didTapJoinBtn() {
        self.interactor?.askForJoiningTournament(roomID!)
    }
    
    
    func didCompleteWithJoinPermission(_ reason: String?) {
        guard reason.isNilOrEmpty else{ return }
        self.view?.joinedState()
    }
    
}



//MARK:- not use for now
extension SocialChatPresenter{
    func joinRoom() {
        interactor?.joinRoom(model: RoomJoinLeaveRM(roomParicipiantRecord: RoomParicipiantRecordRM(roomID:self.roomID),
                                                    createdBy:Int(UserModel.shared.get_id()) ?? 0))
    }
    
    func joinRoomSuccessfully() {
        
    }
    
 
    func onTapUser(_ indexPath: IndexPath) {
        router?.toUser(self.usersRoom?[indexPath.row].createdBy ?? 0)
    }
    
}
