//
//  leaderboardPlayerTableViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 18/03/2024.
//

import UIKit

class leaderboardPlayerTableViewCell: UITableViewCell {

    static let identifier = "leaderboardPlayerTableViewCell"
    static let nib = UINib(nibName: "leaderboardPlayerTableViewCell", bundle: nil)
    
    @IBOutlet weak var index: UILabel!
    @IBOutlet weak var avatar: UIImageView!
    @IBOutlet weak var username: UILabel!
    @IBOutlet weak var score: UILabel!
    
    var data: LeaderBoardContestDataModel?{
        didSet{
            username.text = data?.name
            score.text = "\(data?.score ?? 0)"
            avatar.sd_setImage(with: .init(string: data?.image ?? ""))
        }
    }
    
    
}
