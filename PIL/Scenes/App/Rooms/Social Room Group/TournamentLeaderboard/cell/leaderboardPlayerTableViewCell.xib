<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="dark"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="132" id="KGk-i7-Jjw" customClass="leaderboardPlayerTableViewCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="428" height="132"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" ambiguous="YES" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="428" height="132"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nFk-bO-i9N">
                        <rect key="frame" x="0.0" y="10" width="428" height="60"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="vH9-mr-bEb">
                                <rect key="frame" x="35" y="0.0" width="70" height="60"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="70" id="bjh-31-dcp"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar" translatesAutoresizingMaskIntoConstraints="NO" id="IPc-BK-wPr">
                                <rect key="frame" x="45" y="5" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="Hlh-0f-v6a"/>
                                    <constraint firstAttribute="height" constant="50" id="fWF-pN-2xh"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="25"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Username" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vbp-Ay-UqR">
                                <rect key="frame" x="115" y="19.666666666666668" width="263" height="21.000000000000004"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" name="Black-White"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NR2-1g-FDu">
                                <rect key="frame" x="0.0" y="70" width="428" height="2"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="IHd-mk-kXd">
                                        <rect key="frame" x="0.0" y="0.0" width="428" height="2"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="2" id="4UM-mR-MLN"/>
                                    <constraint firstItem="IHd-mk-kXd" firstAttribute="top" secondItem="NR2-1g-FDu" secondAttribute="top" id="FqR-Jn-2by"/>
                                    <constraint firstItem="IHd-mk-kXd" firstAttribute="leading" secondItem="NR2-1g-FDu" secondAttribute="leading" id="Jh5-Pf-XCu"/>
                                    <constraint firstAttribute="bottom" secondItem="IHd-mk-kXd" secondAttribute="bottom" id="Lv5-74-Av8"/>
                                    <constraint firstAttribute="trailing" secondItem="IHd-mk-kXd" secondAttribute="trailing" id="ZZn-Ck-aPO"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="R77-el-YCv">
                                <rect key="frame" x="10" y="19.666666666666668" width="25" height="21.000000000000004"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" name="Black-White"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="price" translatesAutoresizingMaskIntoConstraints="NO" id="dSk-1s-d9T">
                                <rect key="frame" x="348" y="15" width="60" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="60" id="7ZV-pS-Sec"/>
                                    <constraint firstAttribute="height" constant="30" id="yQs-TT-1ZO"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Wt0-7M-Bor">
                                <rect key="frame" x="348" y="23" width="60" height="14.333333333333336"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="60" id="Pf3-Pt-bEW"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" name="Black-White"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="dSk-1s-d9T" secondAttribute="trailing" constant="20" id="13U-r2-T7K"/>
                            <constraint firstItem="Vbp-Ay-UqR" firstAttribute="centerY" secondItem="IPc-BK-wPr" secondAttribute="centerY" id="6fZ-nB-Le0"/>
                            <constraint firstAttribute="trailing" secondItem="Vbp-Ay-UqR" secondAttribute="trailing" constant="50" id="8mC-c7-5GK"/>
                            <constraint firstItem="IPc-BK-wPr" firstAttribute="centerY" secondItem="vH9-mr-bEb" secondAttribute="centerY" id="BhO-ui-h4p"/>
                            <constraint firstItem="NR2-1g-FDu" firstAttribute="leading" secondItem="nFk-bO-i9N" secondAttribute="leading" id="JvE-pj-TS7"/>
                            <constraint firstItem="Vbp-Ay-UqR" firstAttribute="leading" secondItem="vH9-mr-bEb" secondAttribute="trailing" constant="10" id="NrI-fK-zBG"/>
                            <constraint firstItem="Wt0-7M-Bor" firstAttribute="centerX" secondItem="dSk-1s-d9T" secondAttribute="centerX" id="Uon-El-ScW"/>
                            <constraint firstAttribute="height" constant="60" id="Wvp-KN-yIJ"/>
                            <constraint firstItem="IPc-BK-wPr" firstAttribute="centerY" secondItem="nFk-bO-i9N" secondAttribute="centerY" id="Znx-SP-QSd"/>
                            <constraint firstItem="R77-el-YCv" firstAttribute="leading" secondItem="nFk-bO-i9N" secondAttribute="leading" constant="10" id="dHT-i0-xeE"/>
                            <constraint firstAttribute="trailing" secondItem="NR2-1g-FDu" secondAttribute="trailing" id="ep6-y4-qXy"/>
                            <constraint firstItem="dSk-1s-d9T" firstAttribute="centerY" secondItem="Vbp-Ay-UqR" secondAttribute="centerY" id="gPE-M2-1Yv"/>
                            <constraint firstItem="IPc-BK-wPr" firstAttribute="centerX" secondItem="vH9-mr-bEb" secondAttribute="centerX" id="ivZ-NA-xc6"/>
                            <constraint firstItem="vH9-mr-bEb" firstAttribute="leading" secondItem="nFk-bO-i9N" secondAttribute="leading" constant="35" id="joL-pN-pSD"/>
                            <constraint firstItem="IPc-BK-wPr" firstAttribute="leading" secondItem="R77-el-YCv" secondAttribute="trailing" constant="10" id="m8X-km-BCQ"/>
                            <constraint firstAttribute="bottom" secondItem="vH9-mr-bEb" secondAttribute="bottom" id="mh9-DD-R9n"/>
                            <constraint firstItem="Wt0-7M-Bor" firstAttribute="centerY" secondItem="dSk-1s-d9T" secondAttribute="centerY" id="qpM-dH-7ra"/>
                            <constraint firstItem="vH9-mr-bEb" firstAttribute="top" secondItem="nFk-bO-i9N" secondAttribute="top" id="tSB-A7-owc"/>
                            <constraint firstItem="NR2-1g-FDu" firstAttribute="top" secondItem="IPc-BK-wPr" secondAttribute="bottom" constant="15" id="uyB-s5-pDf"/>
                            <constraint firstItem="R77-el-YCv" firstAttribute="centerY" secondItem="nFk-bO-i9N" secondAttribute="centerY" id="yKu-fx-oA5"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="nFk-bO-i9N" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="UDk-CO-1uL"/>
                    <constraint firstAttribute="bottom" secondItem="nFk-bO-i9N" secondAttribute="bottom" constant="10" id="bDv-zG-awq"/>
                    <constraint firstAttribute="trailing" secondItem="nFk-bO-i9N" secondAttribute="trailing" id="eKY-aS-ewQ"/>
                    <constraint firstItem="nFk-bO-i9N" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="kGS-Ai-fEh"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="avatar" destination="IPc-BK-wPr" id="Ir3-yx-sFX"/>
                <outlet property="index" destination="R77-el-YCv" id="GKB-eB-9aV"/>
                <outlet property="score" destination="Wt0-7M-Bor" id="g1I-Kw-PtG"/>
                <outlet property="username" destination="Vbp-Ay-UqR" id="kRE-zk-09v"/>
            </connections>
            <point key="canvasLocation" x="116.03053435114504" y="50.70422535211268"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="A_Divider" width="324" height="1"/>
        <image name="avatar" width="86" height="91"/>
        <image name="price" width="94" height="34"/>
        <image name="user_placeholder" width="96" height="96"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
