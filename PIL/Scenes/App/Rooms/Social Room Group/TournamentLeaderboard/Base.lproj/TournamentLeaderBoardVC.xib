<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="dark"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Light.ttf">
            <string>Poppins-Light</string>
        </array>
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
        <array key="Poppins-SemiBold.ttf">
            <string>Poppins-SemiBold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="TournamentLeaderBoardVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="hoursLabel" destination="wXc-22-mKE" id="2WZ-0X-Umb"/>
                <outlet property="leaderboardTableView" destination="UCz-2c-z13" id="wNz-B4-KI1"/>
                <outlet property="minutesLabel" destination="LCS-H5-Lcy" id="Sv6-5B-SVq"/>
                <outlet property="myPointsLabrl" destination="JcK-Yw-EKS" id="23s-mp-6Wm"/>
                <outlet property="myRankLabrl" destination="Cnv-3o-c88" id="Cm6-et-U8E"/>
                <outlet property="myUserImg" destination="B1F-4t-Jve" id="TwI-Wt-v4b"/>
                <outlet property="myUserNameLabel" destination="jRX-U7-zW1" id="1Nj-sV-IIl"/>
                <outlet property="myUserView" destination="FJR-mc-UOd" id="DgN-bH-b9h"/>
                <outlet property="prize1" destination="yo5-q2-c68" id="qdw-WM-r9C"/>
                <outlet property="prize2" destination="fBb-u6-IxE" id="GOj-ET-4Yp"/>
                <outlet property="prize3" destination="Ue3-0z-rET" id="Nkg-ak-P7X"/>
                <outlet property="prizeRank1" destination="6hT-Vm-sdg" id="qat-1K-3py"/>
                <outlet property="prizeRank2" destination="gi4-Y2-jue" id="CKc-Uk-0QE"/>
                <outlet property="prizeRank3" destination="nw7-Gr-bN6" id="8xv-Dr-N8W"/>
                <outlet property="rank1" destination="0U2-v6-5hI" id="gGK-9a-vnD"/>
                <outlet property="rank2" destination="lFu-7K-7A8" id="yY8-XP-Hjf"/>
                <outlet property="rank3" destination="UVb-KO-8AC" id="6oN-dw-S3f"/>
                <outlet property="secondsLabel" destination="PIl-va-1Nf" id="ksK-Wd-BKm"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iv1-br-dQM">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="rFL-oL-kCo">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                    <blurEffect style="regular"/>
                </visualEffectView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hJD-gk-xaf">
                    <rect key="frame" x="0.0" y="59" width="393" height="150"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="150" id="qxb-Lu-4tA"/>
                    </constraints>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <connections>
                        <action selector="back:" destination="-1" eventType="touchUpInside" id="kfp-Ny-kLz"/>
                    </connections>
                </button>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AAQ-k7-E2s" customClass="ViewCorners" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="209" width="393" height="643"/>
                    <subviews>
                        <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="lba-LS-rDp">
                            <rect key="frame" x="34" y="75" width="142" height="44"/>
                            <subviews>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fjf-Pc-MbG">
                                    <rect key="frame" x="0.0" y="0.0" width="44" height="44"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0h" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="wXc-22-mKE">
                                            <rect key="frame" x="13.333333333333337" y="12.333333333333316" width="17.666666666666671" height="19.666666666666671"/>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" name="Gray-Black"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="44" id="hmI-7L-zZw"/>
                                        <constraint firstItem="wXc-22-mKE" firstAttribute="centerX" secondItem="fjf-Pc-MbG" secondAttribute="centerX" id="ruL-J1-XqY"/>
                                        <constraint firstAttribute="width" constant="44" id="x9F-Nd-gHw"/>
                                        <constraint firstItem="wXc-22-mKE" firstAttribute="centerY" secondItem="fjf-Pc-MbG" secondAttribute="centerY" id="z0z-qw-Ivl"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                            <color key="value" name="border"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="8"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2LI-LJ-2ec">
                                    <rect key="frame" x="49" y="0.0" width="44" height="44"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0m" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LCS-H5-Lcy">
                                            <rect key="frame" x="10.666666666666671" y="12.333333333333316" width="23" height="19.666666666666671"/>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" name="Gray-Black"/>
                                    <constraints>
                                        <constraint firstItem="LCS-H5-Lcy" firstAttribute="centerY" secondItem="2LI-LJ-2ec" secondAttribute="centerY" id="R76-31-Sei"/>
                                        <constraint firstItem="LCS-H5-Lcy" firstAttribute="centerX" secondItem="2LI-LJ-2ec" secondAttribute="centerX" id="RCi-2X-gDJ"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                            <color key="value" name="border"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="8"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="d1d-Rr-MVJ">
                                    <rect key="frame" x="98" y="0.0" width="44" height="44"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0s" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PIl-va-1Nf">
                                            <rect key="frame" x="14" y="12.333333333333316" width="16" height="19.666666666666671"/>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" name="Gray-Black"/>
                                    <constraints>
                                        <constraint firstItem="PIl-va-1Nf" firstAttribute="centerX" secondItem="d1d-Rr-MVJ" secondAttribute="centerX" id="9au-7Z-nce"/>
                                        <constraint firstItem="PIl-va-1Nf" firstAttribute="centerY" secondItem="d1d-Rr-MVJ" secondAttribute="centerY" id="tXr-wG-9Tg"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                            <color key="value" name="border"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="8"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                            </subviews>
                        </stackView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OKQ-fC-V5T">
                            <rect key="frame" x="30" y="25" width="150" height="30"/>
                            <subviews>
                                <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="End Time" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jMq-pS-k3s">
                                    <rect key="frame" x="0.0" y="0.0" width="150" height="30"/>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="150" id="3J3-YE-nDD"/>
                                        <constraint firstAttribute="height" constant="30" id="AOL-Mg-PSU"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <nil key="highlightedColor"/>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="15"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </label>
                            </subviews>
                            <color key="backgroundColor" name="Orange Primary Color"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="30" id="O8Y-3i-hKJ"/>
                                <constraint firstItem="jMq-pS-k3s" firstAttribute="leading" secondItem="OKQ-fC-V5T" secondAttribute="leading" id="UCR-0Y-LwB"/>
                                <constraint firstItem="jMq-pS-k3s" firstAttribute="top" secondItem="OKQ-fC-V5T" secondAttribute="top" id="c9Z-q7-DOe"/>
                                <constraint firstAttribute="trailing" secondItem="jMq-pS-k3s" secondAttribute="trailing" id="tSo-ET-dvX"/>
                                <constraint firstAttribute="bottom" secondItem="jMq-pS-k3s" secondAttribute="bottom" id="wjN-jN-GsT"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="15"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="kEx-ay-pIk">
                            <rect key="frame" x="288.33333333333331" y="25" width="79.666666666666686" height="20"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="bnk-h8-5xw">
                                    <rect key="frame" x="0.0" y="0.0" width="36" height="20"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rank" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hdo-rt-xhb">
                                            <rect key="frame" x="0.0" y="0.0" width="36" height="20"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="20" id="fZQ-ir-BR3"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Poppins-SemiBold" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Orange Primary Color"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0U2-v6-5hI">
                                            <rect key="frame" x="16" y="0.0" width="4.3333333333333321" height="30"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="30" id="5Wf-co-fPw"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lFu-7K-7A8">
                                            <rect key="frame" x="14" y="0.0" width="8.3333333333333357" height="30"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="30" id="Nyn-aw-f1s"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UVb-KO-8AC">
                                            <rect key="frame" x="14" y="0.0" width="8.3333333333333357" height="30"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="30" id="9ih-yf-zHM"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </stackView>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="7" translatesAutoresizingMaskIntoConstraints="NO" id="RqZ-2T-gse">
                                    <rect key="frame" x="46" y="0.0" width="33.666666666666657" height="20"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Prize" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8vT-e0-SpS">
                                            <rect key="frame" x="0.0" y="0.0" width="33.666666666666664" height="20"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="20" id="Hvz-vA-Hfn"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Poppins-SemiBold" family="Poppins" pointSize="14"/>
                                            <color key="textColor" name="Orange Primary Color"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yo5-q2-c68">
                                            <rect key="frame" x="-15.666666666666629" y="0.0" width="65" height="30"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="price" translatesAutoresizingMaskIntoConstraints="NO" id="78V-xX-jtU">
                                                    <rect key="frame" x="0.0" y="0.0" width="65" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="8OL-9L-6a8"/>
                                                        <constraint firstAttribute="width" constant="65" id="A9z-8j-ljC"/>
                                                    </constraints>
                                                </imageView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="eaM-tq-pLF">
                                                    <rect key="frame" x="17.666666666666629" y="0.0" width="30" height="30"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pi-coins" translatesAutoresizingMaskIntoConstraints="NO" id="biJ-Ab-1k6">
                                                            <rect key="frame" x="0.0" y="0.0" width="20" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="20" id="RFH-66-oXL"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="6hT-Vm-sdg">
                                                            <rect key="frame" x="23" y="0.0" width="7" height="30"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                                            <color key="textColor" name="Black-White"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isShadowOnText" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="Ddo-Ce-MmT"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="78V-xX-jtU" secondAttribute="bottom" id="dRA-0C-GWG"/>
                                                <constraint firstItem="eaM-tq-pLF" firstAttribute="centerX" secondItem="78V-xX-jtU" secondAttribute="centerX" id="jLA-ML-Ofa"/>
                                                <constraint firstAttribute="trailing" secondItem="78V-xX-jtU" secondAttribute="trailing" id="lfX-oL-h53"/>
                                                <constraint firstItem="78V-xX-jtU" firstAttribute="top" secondItem="yo5-q2-c68" secondAttribute="top" id="nQE-Ce-ebK"/>
                                                <constraint firstItem="78V-xX-jtU" firstAttribute="leading" secondItem="yo5-q2-c68" secondAttribute="leading" id="ueS-dV-ga0"/>
                                                <constraint firstItem="eaM-tq-pLF" firstAttribute="centerY" secondItem="78V-xX-jtU" secondAttribute="centerY" id="ybL-Po-D3n"/>
                                            </constraints>
                                        </view>
                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fBb-u6-IxE">
                                            <rect key="frame" x="-15.666666666666629" y="0.0" width="65" height="30"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="price" translatesAutoresizingMaskIntoConstraints="NO" id="Gtl-po-8Z2">
                                                    <rect key="frame" x="0.0" y="0.0" width="65" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="65" id="Kd1-0H-bwg"/>
                                                        <constraint firstAttribute="height" constant="30" id="R9z-Gi-Tib"/>
                                                    </constraints>
                                                </imageView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="gcK-Wl-J1o">
                                                    <rect key="frame" x="17.666666666666629" y="0.0" width="30" height="30"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pi-coins" translatesAutoresizingMaskIntoConstraints="NO" id="uzJ-0U-OrK">
                                                            <rect key="frame" x="0.0" y="0.0" width="20" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="20" id="dwL-Ur-4pU"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="gi4-Y2-jue">
                                                            <rect key="frame" x="23" y="0.0" width="7" height="30"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                                            <color key="textColor" name="Black-White"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isShadowOnText" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="qZu-IC-PVc"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="gcK-Wl-J1o" firstAttribute="centerX" secondItem="Gtl-po-8Z2" secondAttribute="centerX" id="8iI-83-TPa"/>
                                                <constraint firstAttribute="bottom" secondItem="Gtl-po-8Z2" secondAttribute="bottom" id="o8o-h8-L00"/>
                                                <constraint firstAttribute="trailing" secondItem="Gtl-po-8Z2" secondAttribute="trailing" id="qBr-cV-pWN"/>
                                                <constraint firstItem="Gtl-po-8Z2" firstAttribute="leading" secondItem="fBb-u6-IxE" secondAttribute="leading" id="sLN-hV-VAW"/>
                                                <constraint firstItem="gcK-Wl-J1o" firstAttribute="centerY" secondItem="Gtl-po-8Z2" secondAttribute="centerY" id="sih-bd-oC2"/>
                                                <constraint firstItem="Gtl-po-8Z2" firstAttribute="top" secondItem="fBb-u6-IxE" secondAttribute="top" id="t8k-a2-enI"/>
                                            </constraints>
                                        </view>
                                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ue3-0z-rET">
                                            <rect key="frame" x="-15.666666666666629" y="0.0" width="65" height="30"/>
                                            <subviews>
                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="price" translatesAutoresizingMaskIntoConstraints="NO" id="bF2-eA-WPH">
                                                    <rect key="frame" x="0.0" y="0.0" width="65" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="65" id="2ac-qL-hgn"/>
                                                        <constraint firstAttribute="height" constant="30" id="kfk-tK-bcT"/>
                                                    </constraints>
                                                </imageView>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="hCy-2D-W3g">
                                                    <rect key="frame" x="17.666666666666629" y="0.0" width="30" height="30"/>
                                                    <subviews>
                                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pi-coins" translatesAutoresizingMaskIntoConstraints="NO" id="hd2-N5-rbx">
                                                            <rect key="frame" x="0.0" y="0.0" width="20" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="20" id="3Z5-0i-VWS"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="nw7-Gr-bN6">
                                                            <rect key="frame" x="23" y="0.0" width="7" height="30"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                                            <color key="textColor" name="Black-White"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="boolean" keyPath="isShadowOnText" value="YES"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="m3T-ha-gnw"/>
                                                    </constraints>
                                                </stackView>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="bF2-eA-WPH" secondAttribute="bottom" id="119-dO-kHU"/>
                                                <constraint firstItem="hCy-2D-W3g" firstAttribute="centerX" secondItem="bF2-eA-WPH" secondAttribute="centerX" id="4SG-TY-pFl"/>
                                                <constraint firstAttribute="trailing" secondItem="bF2-eA-WPH" secondAttribute="trailing" id="If1-tr-iJ6"/>
                                                <constraint firstItem="hCy-2D-W3g" firstAttribute="centerY" secondItem="bF2-eA-WPH" secondAttribute="centerY" id="PAm-NJ-2Bj"/>
                                                <constraint firstItem="bF2-eA-WPH" firstAttribute="leading" secondItem="Ue3-0z-rET" secondAttribute="leading" id="a7j-jf-6VB"/>
                                                <constraint firstItem="bF2-eA-WPH" firstAttribute="top" secondItem="Ue3-0z-rET" secondAttribute="top" id="mU9-mV-wCZ"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                </stackView>
                            </subviews>
                        </stackView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Leaderboard" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tuN-KP-RnK">
                            <rect key="frame" x="141.66666666666666" y="115" width="109.66666666666666" height="24"/>
                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="17"/>
                            <color key="textColor" name="Black-White"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="UCz-2c-z13">
                            <rect key="frame" x="10" y="159" width="373" height="474"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </tableView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FJR-mc-UOd">
                            <rect key="frame" x="20" y="500" width="353" height="80"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="4" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cnv-3o-c88">
                                    <rect key="frame" x="10" y="29.666666666666629" width="30" height="21"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="30" id="gtE-Fq-jjL"/>
                                    </constraints>
                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="15"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jZn-WH-xT8">
                                    <rect key="frame" x="249" y="24" width="94" height="32"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="price" translatesAutoresizingMaskIntoConstraints="NO" id="mLa-BK-CHm">
                                            <rect key="frame" x="0.0" y="0.0" width="94" height="32"/>
                                        </imageView>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" text="123" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JcK-Yw-EKS">
                                            <rect key="frame" x="3" y="9" width="88" height="14"/>
                                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="10"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="mLa-BK-CHm" firstAttribute="top" secondItem="jZn-WH-xT8" secondAttribute="top" id="Tdj-2W-Dva"/>
                                        <constraint firstAttribute="width" constant="94" id="aDa-Op-uiq"/>
                                        <constraint firstAttribute="trailing" secondItem="mLa-BK-CHm" secondAttribute="trailing" id="fxn-w9-E5D"/>
                                        <constraint firstItem="JcK-Yw-EKS" firstAttribute="centerX" secondItem="jZn-WH-xT8" secondAttribute="centerX" id="lVH-I2-pUA"/>
                                        <constraint firstItem="JcK-Yw-EKS" firstAttribute="centerY" secondItem="jZn-WH-xT8" secondAttribute="centerY" id="mL1-Md-nbU"/>
                                        <constraint firstAttribute="trailing" secondItem="JcK-Yw-EKS" secondAttribute="trailing" constant="3" id="rCm-Nr-h9Q"/>
                                        <constraint firstItem="JcK-Yw-EKS" firstAttribute="leading" secondItem="jZn-WH-xT8" secondAttribute="leading" constant="3" id="vC3-SU-QhN"/>
                                        <constraint firstAttribute="height" constant="32" id="xAu-c5-Oa7"/>
                                        <constraint firstAttribute="bottom" secondItem="mLa-BK-CHm" secondAttribute="bottom" id="z84-1b-WC9"/>
                                        <constraint firstItem="mLa-BK-CHm" firstAttribute="leading" secondItem="jZn-WH-xT8" secondAttribute="leading" id="zNh-G9-RQM"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="4"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oH7-Kc-aHu">
                                    <rect key="frame" x="50" y="10" width="60" height="60"/>
                                    <subviews>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar" translatesAutoresizingMaskIntoConstraints="NO" id="B1F-4t-Jve">
                                            <rect key="frame" x="7.6666666666666714" y="7.6666666666666288" width="45" height="45"/>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="45" id="XhL-KD-M56"/>
                                                <constraint firstAttribute="width" constant="45" id="x5T-R7-Ztu"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                                    <real key="value" value="10"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </imageView>
                                    </subviews>
                                    <color key="backgroundColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="60" id="bYs-e7-7Ib"/>
                                        <constraint firstAttribute="width" constant="60" id="jAG-9i-pwW"/>
                                        <constraint firstItem="B1F-4t-Jve" firstAttribute="centerX" secondItem="oH7-Kc-aHu" secondAttribute="centerX" id="nBm-vV-33S"/>
                                        <constraint firstItem="B1F-4t-Jve" firstAttribute="centerY" secondItem="oH7-Kc-aHu" secondAttribute="centerY" id="riS-IJ-d58"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="30"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="UserName" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jRX-U7-zW1">
                                    <rect key="frame" x="120" y="28.666666666666629" width="119" height="22.666666666666671"/>
                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" name="White-Black"/>
                            <constraints>
                                <constraint firstItem="oH7-Kc-aHu" firstAttribute="centerY" secondItem="FJR-mc-UOd" secondAttribute="centerY" id="0Qo-lC-koq"/>
                                <constraint firstItem="jRX-U7-zW1" firstAttribute="centerY" secondItem="FJR-mc-UOd" secondAttribute="centerY" id="1b7-Ia-0ac"/>
                                <constraint firstItem="oH7-Kc-aHu" firstAttribute="leading" secondItem="Cnv-3o-c88" secondAttribute="trailing" constant="10" id="L9L-Ay-qpE"/>
                                <constraint firstItem="jZn-WH-xT8" firstAttribute="centerY" secondItem="FJR-mc-UOd" secondAttribute="centerY" id="LEM-24-fB9"/>
                                <constraint firstItem="jZn-WH-xT8" firstAttribute="leading" secondItem="jRX-U7-zW1" secondAttribute="trailing" constant="10" id="OWe-V5-zXH"/>
                                <constraint firstItem="Cnv-3o-c88" firstAttribute="leading" secondItem="FJR-mc-UOd" secondAttribute="leading" constant="10" id="V0l-Im-axn"/>
                                <constraint firstItem="Cnv-3o-c88" firstAttribute="centerY" secondItem="FJR-mc-UOd" secondAttribute="centerY" id="f3d-H6-mMJ"/>
                                <constraint firstAttribute="height" constant="80" id="fqG-He-OeL"/>
                                <constraint firstItem="jRX-U7-zW1" firstAttribute="leading" secondItem="oH7-Kc-aHu" secondAttribute="trailing" constant="10" id="hbI-dE-KRJ"/>
                                <constraint firstAttribute="trailing" secondItem="jZn-WH-xT8" secondAttribute="trailing" constant="10" id="mwx-EC-i4H"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="16"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                    <color key="value" name="Black-Orange"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" name="Dialog"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="FJR-mc-UOd" secondAttribute="bottom" constant="20" id="36z-r8-miq"/>
                        <constraint firstAttribute="trailing" secondItem="kEx-ay-pIk" secondAttribute="trailing" constant="25" id="5KJ-Zp-50v"/>
                        <constraint firstAttribute="bottom" secondItem="UCz-2c-z13" secondAttribute="bottom" constant="10" id="KXE-xr-B8K"/>
                        <constraint firstItem="UCz-2c-z13" firstAttribute="top" secondItem="tuN-KP-RnK" secondAttribute="bottom" constant="20" id="Lg9-KS-orB"/>
                        <constraint firstItem="tuN-KP-RnK" firstAttribute="centerX" secondItem="AAQ-k7-E2s" secondAttribute="centerX" id="VhI-S9-DO8"/>
                        <constraint firstAttribute="trailing" secondItem="UCz-2c-z13" secondAttribute="trailing" constant="10" id="c07-5f-Z8I"/>
                        <constraint firstAttribute="trailing" secondItem="FJR-mc-UOd" secondAttribute="trailing" constant="20" id="dWV-gg-mqT"/>
                        <constraint firstItem="lba-LS-rDp" firstAttribute="top" secondItem="OKQ-fC-V5T" secondAttribute="bottom" constant="20" id="exF-og-Sqc"/>
                        <constraint firstItem="kEx-ay-pIk" firstAttribute="top" secondItem="AAQ-k7-E2s" secondAttribute="top" constant="25" id="gdf-Yr-KIo"/>
                        <constraint firstItem="UCz-2c-z13" firstAttribute="leading" secondItem="AAQ-k7-E2s" secondAttribute="leading" constant="10" id="hcZ-Q0-aT6"/>
                        <constraint firstItem="OKQ-fC-V5T" firstAttribute="leading" secondItem="AAQ-k7-E2s" secondAttribute="leading" constant="30" id="jvG-Ue-2hr"/>
                        <constraint firstItem="tuN-KP-RnK" firstAttribute="top" secondItem="kEx-ay-pIk" secondAttribute="bottom" constant="70" id="mme-s6-rrm"/>
                        <constraint firstItem="OKQ-fC-V5T" firstAttribute="top" secondItem="AAQ-k7-E2s" secondAttribute="top" constant="25" id="orh-Hn-qNy"/>
                        <constraint firstItem="lba-LS-rDp" firstAttribute="centerX" secondItem="OKQ-fC-V5T" secondAttribute="centerX" id="sdx-b4-6tD"/>
                        <constraint firstItem="FJR-mc-UOd" firstAttribute="leading" secondItem="AAQ-k7-E2s" secondAttribute="leading" constant="20" id="u7U-GQ-3S5"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                            <color key="value" name="border"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                            <real key="value" value="0.5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedTopLeft" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedTopRight" value="YES"/>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRaduis">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                    <variation key="default">
                        <mask key="subviews">
                            <exclude reference="FJR-mc-UOd"/>
                        </mask>
                    </variation>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="iv1-br-dQM" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="4eA-WG-BMf"/>
                <constraint firstItem="AAQ-k7-E2s" firstAttribute="top" secondItem="hJD-gk-xaf" secondAttribute="bottom" id="FNm-Y5-D8F"/>
                <constraint firstAttribute="bottom" secondItem="iv1-br-dQM" secondAttribute="bottom" id="HKS-JY-apk"/>
                <constraint firstItem="hJD-gk-xaf" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="Kbj-QL-k2t"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="iv1-br-dQM" secondAttribute="trailing" id="brW-u1-sPm"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="AAQ-k7-E2s" secondAttribute="trailing" id="ed9-XA-ewl"/>
                <constraint firstItem="AAQ-k7-E2s" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="f0q-3Z-5ds"/>
                <constraint firstItem="iv1-br-dQM" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="gbz-fa-RAl"/>
                <constraint firstItem="hJD-gk-xaf" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="irN-gM-5s2"/>
                <constraint firstAttribute="bottom" secondItem="AAQ-k7-E2s" secondAttribute="bottom" id="jh7-7U-HCK"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="hJD-gk-xaf" secondAttribute="trailing" id="ll0-bE-MGY"/>
            </constraints>
            <point key="canvasLocation" x="33" y="20"/>
        </view>
    </objects>
    <resources>
        <image name="avatar" width="86" height="91"/>
        <image name="pi-coins" width="50" height="48"/>
        <image name="price" width="94" height="34"/>
        <namedColor name="Black-Orange">
            <color red="0.071000002324581146" green="0.071000002324581146" blue="0.071000002324581146" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Gray-Black">
            <color red="0.63499999046325684" green="0.63499999046325684" blue="0.63499999046325684" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="White-Black">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
