//
//  TournamentLeaderBoardVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 18/03/2024.
//

import UIKit

class TournamentLeaderBoardVC: UIViewController {
    
    @IBOutlet weak var myPointsLabrl: UILabel!
    @IBOutlet weak var myUserNameLabel: UILabel!
    @IBOutlet weak var myRankLabrl: UILabel!
    @IBOutlet weak var myUserImg: UIImageView!
    @IBOutlet weak var myUserView: UIView!
    
    @IBOutlet weak var hoursLabel: UILabel!
    @IBOutlet weak var minutesLabel: UILabel!
    @IBOutlet weak var secondsLabel: UILabel!
    @IBOutlet weak var prizeRank1: UILabel!
    @IBOutlet weak var prizeRank2: UILabel!
    @IBOutlet weak var prizeRank3: UILabel!
    @IBOutlet weak var leaderboardTableView: UITableView!
    @IBOutlet weak var rank1: UILabel!
    @IBOutlet weak var rank2: UILabel!
    @IBOutlet weak var rank3: UILabel!
    @IBOutlet weak var prize1: UIView!
    @IBOutlet weak var prize2: UIView!
    @IBOutlet weak var prize3: UIView!
    
    var tournament: Tournament?
    var leaderboard: [LeaderBoardContestDataModel]?
    var timer: Timer?

    override func viewDidLoad() {
        super.viewDidLoad()
        getLeaderBoard()
        tournament?.setCountDownInSeconds()
        timer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(fire), userInfo: nil, repeats: true)
        if (tournament?.prizesDistribution?.count ?? 0) > 0{
            prizeRank1.text = "\(tournament?.prizesDistribution?[0].prize ?? 0)"
            rank1.isHidden = false
            prize1.isHidden = false
        }
        if (tournament?.prizesDistribution?.count ?? 0) > 1{
            prizeRank2.text = "\(tournament?.prizesDistribution?[1].prize ?? 0)"
            rank2.isHidden = false
            prize2.isHidden = false
        }
        if (tournament?.prizesDistribution?.count ?? 0) > 2{
            prizeRank3.text = "\(tournament?.prizesDistribution?[2].prize ?? 0)"
            rank3.isHidden = false
            prize3.isHidden = false
        }
        leaderboardTableView.register(leaderboardPlayerTableViewCell.nib, forCellReuseIdentifier: leaderboardPlayerTableViewCell.identifier)
    }
    
    
    func setupMyUserView() {
        let myId = leaderboard?.first(where: {$0.id == Int(UserModel.shared.get_id())})
        if myId != nil {
            myUserView.isHidden = false
            myUserNameLabel.text = UserModel.shared.get_name()
            myUserImg.sd_setImage(with: .init(string: (UserModel.shared.get_image())))
            myPointsLabrl.text = "\(myId?.score ?? 0)"
        }else{
            myUserView.isHidden = true
        }
    }
    
    @IBAction func back(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
    @objc func fire(){
        guard tournament!.timeInterval! > 0 else{
            timer?.invalidate()
            calculateCountDown((tournament!.timeInterval)!)
            return
        }
        tournament!.timeInterval! -= 1
        calculateCountDown((tournament!.timeInterval)!)
    }
    
    func getLeaderBoard(){
        ChallengesGameWorker.init().getLeaderBoard(contestID: "\(tournament?.id ?? 0)", compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200 || model.status ?? 0 == 201{
                    if let model = model.data{
                        self.leaderboard = model
                        self.leaderboardTableView.delegate = self
                        self.leaderboardTableView.dataSource = self
                        self.leaderboardTableView.reloadLeftToRight()
                        self.setupMyUserView()
                    }
                }
                break
            default: break
            }
        })
    }
    
    private func calculateCountDown(_ seconds: Int){
        let counter = seconds.secondsToHoursMinutesSeconds()
        if counter.0 > 24{
            let days = Int(counter.0 / 24)
            let hours = counter.0 - (Int(counter.0 / 24) * 24)
            let minutes = counter.1
            hoursLabel.text = "\(days)" + "d".localized
            minutesLabel.text = "\(hours)" + "h".localized
            secondsLabel.text = "\(minutes)" + "m".localized
        }else{
            hoursLabel.text = "\(counter.0)" + "h".localized
            minutesLabel.text = "\(counter.1)" + "m".localized
            secondsLabel.text = "\(counter.2)" + "s".localized
        }
    }

}

extension TournamentLeaderBoardVC: UITableViewDelegate, UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return leaderboard?.count ?? 0
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: leaderboardPlayerTableViewCell.identifier, for: indexPath) as! leaderboardPlayerTableViewCell
        self.myRankLabrl.text = "\(indexPath.row+1)"
        cell.index.text = "\(indexPath.row+1)"
        cell.data = leaderboard![indexPath.row]
        return cell
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if "\(leaderboard?[indexPath.row].id ?? 0)" == UserModel.shared.get_id() {
            self.myUserView.isHidden = true
        }
    }
    
    func tableView(_ tableView: UITableView, didEndDisplaying cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        if "\(leaderboard?[indexPath.row].id ?? 0)" == UserModel.shared.get_id() {
            self.myUserView.isHidden = false
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 90
    }
}
