//
//  ChatNewVC.swift
//  PIL
//
//  Created by sameh mohammed on 31/05/2023.
//

import UIKit
import SignalRClient
import AVFoundation
import IQKeyboardManager
import DropDown
import AgoraRtcKit

class SocialChatVC: UIViewController ,SocialChatViewProtocol ,ErrorProtocol , AVAudioRecorderDelegate{
    

    //MARK: - outlet
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var chatTableView: UITableView!
    @IBOutlet weak var backBu: UIButton!
    @IBOutlet weak var messageTF: UITextField!
    @IBOutlet weak var TitleLable: UILabel!

    @IBOutlet weak var onlineStatus: UILabel!
    @IBOutlet weak var buttomConstraint: NSLayoutConstraint!
    @IBOutlet weak var stackSendMeasseg: UIStackView!
    @IBOutlet weak var messageViewHeightCnst: NSLayoutConstraint!
    @IBOutlet weak var usersCollectoinView: UICollectionView!
    @IBOutlet weak var micButton: UIButton!
    @IBOutlet weak var listenBu: UIButton!
    
    @IBOutlet weak var gameImage: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet weak var createGameBtn: UIButton!
    @IBOutlet weak var gameView: UIView!
    @IBOutlet weak var requestMicBu: UIButton!
    @IBOutlet weak var fees: UILabel!
    @IBOutlet weak var joinView: UIView!
    @IBOutlet weak var playBtn: UIButton!
    @IBOutlet weak var viewMembers: UIView!
    @IBOutlet weak var membersTableView: UITableView!
    @IBOutlet weak var HeightViewMember: NSLayoutConstraint!
    
    var imagereceiver = String()
    var namereceiver = String()
    var presenter: SocialChatPresenterProtocol?
 
    
    let bottomPadding = UIApplication.shared.windows.first?.safeAreaInsets.bottom
    var agoraKit: AgoraRtcEngineKit! /// agora
    weak var logVC: LogViewController? /// agora
    var isListen = true
    var isSpeack = false
    var CanSpeack = false
    
    //MARK: - life cycle
    override func viewDidLoad() {
        super.viewDidLoad()

        SetNavigation()
        setXIB()
        InitCall()
        
        ///when tap tableview hidden keyboard
        chatTableView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.tableViewTap)))
        
        ///keyboard setting
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        IQKeyboardManager.shared().disabledDistanceHandlingClasses.add(SocialChatVC.self)
        IQKeyboardManager.shared().disabledToolbarClasses.add(SocialChatVC.self)
        chatTableView.delegate = self

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        ///hidden tabbar
        self.tabBarController?.tabBar.isHidden = true
        self.tabBarController?.tabBar.isTranslucent = true
        self.buttomConstraint.constant = 0 /// the default value
        presenter?.viewDidLoad() /// call api
        ///check if i'm the owner or not
        /// show create tournament button
        if presenter?.isMyRoom == true{
            createGameBtn.isHidden = false
        }else{
            createGameBtn.isHidden = true
        }
        
    }
    
    
    /// hidden keyboard
    @objc func tableViewTap(){
        view.endEditing(true)
    }

    ///Configration navigation bar
    func SetNavigation(){
        /// create view
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        /// actioins in navigation
        self.navigationView.selectedAction(actions: [.leaveRoom, .roomTournament , .roomMembers])
        self.navigationView.onTapLeaveRoom = {
            ///send request leave room
            self.presenter?.LeaveRoomRequest()
        }
       
        /// to show all members room the popup in  the same screen
        self.navigationView.onTapRoomMembers = {
            self.membersTableView.reloadData()
            
            UIView.animate(withDuration: 0.3, animations: { () -> Void in
                self.HeightViewMember.constant = 476
                self.view.layoutIfNeeded()
            })
            self.viewMembers.isHidden = false

            self.view.endEditing(true)
        }
    }
    
    
    /// get tournament room info
    /// - Parameter tournamemt: get tournament info
    /// if i'm owner show and tournament is created show  play button  , if it not created show create tournament
    /// if user show join and play button if not exist no thing show
    func onFetchTournament(_ tournamemt: Tournament?) {
        if let tournamemt = tournamemt, tournamemt.id != nil{
            self.navigationView.selectedAction(actions: [.leaveRoom, .roomMembers, .roomTournament])
            gameView.isHidden = false
            /// fee to join torunament
            let entryFees = Int(tournamemt.entryfeeDeposit?.getValue ?? "0")! + (tournamemt.entryfeeBouns ?? 0)
            fees.text = "\(entryFees)"
            createGameBtn.isHidden = true
            gameName.text = tournamemt.name ?? ""
            gameImage.sd_setImage(with: .init(string: tournamemt.game?.icons ?? ""))
            if tournamemt.isJoined == true || presenter?.isMyRoom == true{
                joinView.isHidden = true
                playBtn.isHidden = false
            }else{
                joinView.isHidden = false
                playBtn.isHidden = true
            }
            /// navigate to create toutnament
            self.navigationView.onTapRoomTournament = {
                let vc = TournamentLeaderBoardVC.loadFromNib()
                vc.tournament = tournamemt
                vc.modalPresentationStyle = .overCurrentContext
                self.present(vc, animated: true)
            }
        }else{
            self.navigationView.selectedAction(actions: [.leaveRoom, .roomMembers])
            gameView.isHidden = true
            joinView.isHidden = true
    
            if presenter?.isMyRoom == true{
                createGameBtn.isHidden = false
            }else{
                createGameBtn.isHidden = true
            }
        }
    }
    /// first step join button and pay fee
    /// second step play button
    func joinedState() {
        playBtn.isHidden = false
        joinView.isHidden = true
    }
    
    /// get height keyboard to change buttom viwe constraint
    @objc func keyboardWillShow(notification: NSNotification) {
        if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            let height = keyboardSize.height
            UIView.animate(withDuration: 0.5) {
                self.buttomConstraint.constant = height - 30
                self.view.layoutIfNeeded()
            }
        }
    }
    
    
    //MARK: - function
    
    /// UI Cell
    func setXIB(){
        chatTableView.transform = CGAffineTransform(rotationAngle: (-.pi))
        self.chatTableView.register(UINib(nibName: "ChatRoomCell", bundle: nil), forCellReuseIdentifier: "ChatRoomCell")
        self.usersCollectoinView.register(UINib(nibName: "UsersScoialRoomCell", bundle: nil), forCellWithReuseIdentifier: "UsersScoialRoomCell")
        self.membersTableView.register(MemberRoomsCell.nib, forCellReuseIdentifier: MemberRoomsCell.identifier)
        membersTableView.rowHeight = 90 /// Default height to row
    }
    
    ///Configuration Agora and sockit
    private func InitCall() {
        initializeAgoraEngine()
        setupAudio() // Configuration Audio Agora
        joinChannel() // join channel Agora ( Audio )
        configuerSocket()
        ///if i am owner can open mic
        /// if not user send request to admin to accept after that the mic open automatic
        if presenter?.isMyRoom == false{
            self.micButton.isUserInteractionEnabled = false
        }else{
            self.requestMicBu.isHidden = true
            self.micButton.isHidden = false
            self.micButton.isUserInteractionEnabled = true
        }
    }
    
    
    //MARK: - action
    
    ///When press back button send request leave room
    @IBAction func backBTN(_ sender: Any) {
        self.presenter?.LeaveRoomRequest()
    }
    
    /// show popup send gift to other users
    @IBAction func giftBTN(_ sender: Any) {
        let vc = RoomMembersViewController.loadFromNib()
        vc.modalPresentationStyle = .overCurrentContext
        vc.members = presenter?.usersRoom?.filter{ return "\($0.createdBy ?? 0)" != UserModel.shared.get_id() } ?? []
        vc.tempMembers = presenter?.usersRoom?.filter{ return "\($0.createdBy ?? 0)" != UserModel.shared.get_id() } ?? []
        vc.isSendable = true
        self.present(vc, animated: true)
    }
    
    /// show all members room
    @IBAction func seeAllAction(_ sender: Any) {
        let vc = RoomMembersViewController.loadFromNib()
        vc.modalPresentationStyle = .overCurrentContext
        vc.members = presenter?.usersRoom?.filter{ return "\($0.createdBy ?? 0)" != UserModel.shared.get_id() } ?? []
        vc.tempMembers = presenter?.usersRoom?.filter{ return "\($0.createdBy ?? 0)" != UserModel.shared.get_id() } ?? []
        vc.isSendable = false
        self.present(vc, animated: true)
    }
    
    ///Send meesage in chat Room Sockit
    @IBAction func sendMessageBTN(_ sender: Any) {
            if messageTF.text ?? "" != ""{
                self.sendMessage(fromUserId: Int(UserModel.shared.get_id()) ?? 0,
                                 message: messageTF.text ?? "",
                                 typeID: .message,
                                 toUserId: 0,
                                 toImageUrl: self.imagereceiver,
                                 ImageUrl: UserModel.shared.get_image(),
                                 toname: self.namereceiver,
                                 toRoomId: presenter?.roomID ?? 0)
        }
    }
    
    /// Send Request To Admin To open my mic and adimn can accept or cancel request
    @IBAction func requestMicBTN(_ sender: Any) {
        self.sendUserRequestMic(toUserId: self.presenter?.OwnerID ?? 0,isAdmin: false)
        self.showToast(message: "Mic permission Request sent".localized, font: .systemFont(ofSize: 12.0))
    }
    
    //MARK: - actions Agora
    
    @IBAction func listenBTN(_ sender: Any) {
        ///the user have two option to listen the voice  open Speaker on close speaker
        if isListen == true{
            isListen = false
            agoraKit.setDefaultAudioRouteToSpeakerphone(false)
            agoraKit.setEnableSpeakerphone(false)
            listenBu.setImage(UIImage(named: "unSpeaker"), for: .normal)
        }else{
            isListen = true
            agoraKit.setDefaultAudioRouteToSpeakerphone(true)
            agoraKit.setEnableSpeakerphone(true)
            listenBu.setImage(UIImage(named: "Speaker"), for: .normal)
        }
        print("Check speacker is enble",agoraKit.isSpeakerphoneEnabled())
        
    }
    
    /// open mic
    @IBAction func didClickMuteButton(_ sender: UIButton) {
        agoraKit.muteLocalAudioStream(isSpeack)
        /// if mic opened and user click mic the is closed
        /// if mic closed send request to open mic if he is admin the mic open automatic,  if user the admin must be accept request
        if isSpeack == false{
            self.isSpeack = true
            self.micButton.setImage(#imageLiteral(resourceName: "microphone"), for: .normal)
        }else{
            self.isSpeack = false
            self.micButton.setImage(#imageLiteral(resourceName: "mute-microphone"), for: .normal)
        }
        
        /// sockit requesrt and Update status mic to all users
        ///
        SendMicStatus(fromUserId: Int(UserModel.shared.get_id()) ?? 0,
                      toRoomId: self.presenter?.roomID ?? 0,
                      on: isSpeack,
                      touserID: Int(UserModel.shared.get_id()) ?? 0,
                      isAdmin:  self.presenter?.isMyRoom ?? false)

    }

    /// hidden view view all members room with animation
    @IBAction func hiddenMembersViewAction(_ sender: Any) {
        UIView.animate(withDuration: 0.3, animations: { () -> Void in
            self.HeightViewMember.constant = 0
            self.view.layoutIfNeeded()
            Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { timer in
                self.viewMembers.isHidden = true
            }
        })
   }
    

    /// show message  as alert if has Error from API
    func featching(error: String) {
        self.showAlert(withTitle: false, msg: error, compilition: nil)
    }
    
    /// Reload chat tableview after add new meesage
    func ReloadTableView() {
        self.chatTableView.reloadData()
    }
    
    /// reload users if user leave or join from room
    func ReloadCollectionView() {
        usersCollectoinView.reloadData()
        membersTableView.reloadData()
    }

    /// after send request leave room or kickout from room user automatic back to list rooms
    func leaveRoom() {
//        chatHubConnection?.stop()
        agoraKit.leaveChannel(nil)
        self.navigationController?.popViewController(animated: true)
    }
    
    
    /// get room info
    func RoomInfo(roomName: String) {
//        self.TitleLable.text = roomName
        self.navigationView.setNavigationTitle(title: roomName)
        print("OWNER IS",  self.presenter?.OwnerID , Int(UserModel.shared.get_id()))
        if self.presenter?.OwnerID == Int(UserModel.shared.get_id()){
            self.requestMicBu.isHidden = true
            self.micButton.isHidden = false
        }
    }
    
    /// when admin clcik create trournament
    @IBAction func toCreateTournament(){
        presenter?.onTapCreate()
    }
    
    
    @IBAction func joinAction(_ sender: Any) {
        self.presenter?.didTapJoinBtn()
    }
    
    ///play tournament
    @IBAction func playAction(_ sender: Any) {
        self.presenter?.playTournament()
    }
    
    /// when press play tournament and i has old version of this game show alert to update game first to continue play
    func updateGameVersion(message: String) {
        self.showAlertUpdateGame(title: "Alert".localized, msg: message) {
            print("Update")
            self.presenter?.DownloadsFIle(isUpdate: true)
        }
    }
    
    /// If I don't have the assests before show view download game
    func progressGameDownload(name: String, file: String, image: String) {
        let progress = UIStoryboard.init(name: "DownloadGame", bundle: nil).instantiateViewController(withIdentifier: "DownloadGameVC") as! DownloadGameVC
        print("fileURL --->",file)
        progress.nameGame = name
        progress.imageURL = image
        progress.fileURL = file
        progress.action = self
        self.present(progress, animated: false, completion: nil)
    }
    
}

extension SocialChatVC: DownloadGamFinished{
    
    /// after user download the game and progress completed get the file path downloaded
    /// - Parameter Path: file path downloaded
    func DownloadGamFinished(Path: String) {
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "--->",Path)
        self.presenter?.FileDownloaded(pathFile: Path, destination: newFolderURL.path)
 
    }
    
    
}



extension SocialChatVC{
    
    /// show  message when user send request to admin to open mic if accept or canceled show meesage to user to know status
    /// - Parameters:
    ///   - message: message will be show to user
    ///   - font: font style 
    func showToast(message : String, font: UIFont) {

        let toastLabel = UILabel(frame: CGRect(x: self.view.frame.size.width/2 - 75, y: self.view.frame.size.height-140, width: 170, height: 35))
        toastLabel.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        toastLabel.textColor = UIColor.white
        toastLabel.font = font
        toastLabel.textAlignment = .center;
        toastLabel.text = message
        toastLabel.alpha = 1.0
        toastLabel.layer.cornerRadius = 10;
        toastLabel.clipsToBounds  =  true
        self.view.addSubview(toastLabel)
        UIView.animate(withDuration: 4.0, delay: 0.1, options: .curveEaseOut, animations: {
             toastLabel.alpha = 0.0
        }, completion: {(isCompleted) in
            toastLabel.removeFromSuperview()
        })
    } 
}
