//
//  SocialChatVC+Game.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 30/10/2023.
//

import Foundation
import CoreData
import Zip

extension SocialChatPresenter: UnityFrameworkDelegate{
    /// prossess game
    /// when press play button
    /// step 1 : checl in local this game is downloaded before or not ( if exist oepn or update version and open)
    ///              before update veriosn we must remove old version
    /// step 2: if assest not exist download assets and show view with progress bar
    /// step 3:  have to option  1- after download asstes uncompress  file and  open game
    ///                  2- if file is  exist open game
    /// step 4: save game in local
    func playTournament(){
        checkVersion()
    }
    
    /// step 1
    func checkVersion(){
        for i in self.localGamesArray{
            if i.sceneName ?? "" == self.tournament?.game?.sceneName ?? "" {
                if i.version ?? "" !=  self.tournament?.game?.version ?? ""  {
                    self.view?.updateGameVersion(message: "New version".localized+"\(i.version ?? "")")
                    return

                }else{
                    self.Downloads<PERSON><PERSON>(isUpdate: false)
                    return
                }
            }
        }
        self.DownloadsFIle(isUpdate: false)
    }
  
    /// step 1
    func removeFile(filePath:URL){
        let fileManager = FileManager.default
        do{
            try  fileManager.removeItem(at: filePath)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }

    
    /// step 2
    func DownloadsFIle(isUpdate:Bool){
        self.isUpdate = isUpdate
        if isUpdate == true{
            self.removeOldVersion()
        }
        self.interactor?.createFolderInDownloadsDirectory(name: self.tournament?.game?.sceneName ?? "" , files:tournament?.game?.assetsbundle ?? "" )
    }
    
    /// step 3
    func FileDownloaded(pathFile:String , destination:String){
        print("File Path is",pathFile , destination)
        let sourceURL = URL(string:pathFile)!
        let destinationURL = URL(string: "\(destination)")!
        do {
                
            try Zip.unzipFile(sourceURL, destination: destinationURL, overwrite: true, password: nil, progress: { (progress) -> () in
                print("--->",progress)
                if progress == 1{
                    self.removeFile(filePath: sourceURL)
                    //send request model to open game
                    
                    self.save(name: self.tournament?.game?.gameName ?? "" ,
                              sceneName: self.tournament?.game?.sceneName ?? "",
                              image: self.tournament?.game?.icons ?? "",
                              version: self.tournament?.game?.version ?? "",
                              filePath: "\(destination)/\(self.tournament?.game?.sceneName ?? "")",
                              gameID: "\(self.tournament?.game?.id ?? 0 )")
                    
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            self.OpenGame( path: "\(destination)/\(self.tournament?.game?.sceneName ?? "")")
                        }
                }
            })
            
        } catch let error {
            print("Extraction of ZIP archive failed with error:\(error)")
        }
    }
    ///step 3
    func FilesIFExist(path:String){
        print("File Path is",path)
        DispatchQueue.main.async {
            self.OpenGame(path:"\(path)")
        }
    }
    
    ///step 3
    func OpenGame(path:String){

        (UIApplication.shared.delegate as! AppDelegate).orientationMask = .all
        
        let request = [
            "GameId": "\(self.tournament?.game?.id ?? 0 )",
            "GameName": self.tournament?.game?.sceneName ?? "",
            "SceneName": self.tournament?.game?.sceneName ?? "",
            "EntryFee": 0,// total deposit and bouns
            "DepositEntryFee":0 ,// deposit
            "BonusEntryFee": 0 ,//bouns
            "MaximimAttempt": self.tournament?.maximumAttempt ?? 0,
            "NumberOfWinners": tournament?.noOfWinners ?? 0,
            "BattleScoreSyncInSec":" 1.0",
            "PlayerSkill":self.skillsPlayer,
            "PlayerSkillRange":self.tournament?.skillRange ?? 0,
            "MaximumPlayer":tournament?.maximumPlayers ?? 0,
            "Landscape":  getLandscape() ,
            "MaxMatchMakingRetries": tournament?.knockoutTries ?? 0,
            "PrizeTable": getDicPrize(),
            "AuthToken": "Bearer \(UserModel.shared.get_token())",
            "MaxPauseDuration": 30,
            "ConnectionRetryTimeout": 30,
            "IsInDebugMode": false,
            "PingInterval": 10,
            "MaxPongDelay": 20,
            "AppVersionCode": "1",//Build number APP
            "AppVersionName": "1.0.20",//version number APP
            "Profile": [
                "id": UserModel.shared.get_id(),
                "phone": UserModel.shared.get_phone(),
                "name": UserModel.shared.get_username(),
                "username": UserModel.shared.get_username(),
                "image": UserModel.shared.get_image(),
                "token": "Bearer \(UserModel.shared.get_token())",
                "email":UserModel.shared.get_email(),
                "bio":UserModel.shared.get_bio(),
                "birthdate":UserModel.shared.get_birthdate(),
                "cashWin":UserModel.shared.get_cashWin(),
                "followers":UserModel.shared.get_followers(),
                "following":UserModel.shared.get_following(),
                "gender":UserModel.shared.get_gender(),
                "helpdeskId":UserModel.shared.get_HelpdeskId(),
                "level": Int(UserModel.shared.get_level()) ?? 0 ,
                "operator":UserModel.shared.getOperator(),
                "type":UserModel.shared.get_loginAsGuestTypeGame(),
            ] as [String : Any],
            "Host": URls().SmartFoxHost,
            "Zone": "PIL",
            "LobbyId": "",
            "ContestId": "\(tournament?.id ?? 0)",
            "ContestName": tournament?.contestType ?? "",
            "ContestType": tournament?.contestType ?? "",
            "EndDateTime":EndTimeContest(),
            "StartDateTime":StartTimeContest(),
            "IsTournament": istournament(),
            "EnableLog": true,
            "DeveloperConsoleURL": URls().BASE_URL_INDIA,
            "UserURL": URls().BASE_URL,
            "AssetBundleURL": "\(path)",
            "Language": UserModel.shared.getLanguage(),
            "GameSoundOn":UserModel.shared.getSound(),
            "IsKnockout": self.tournament?.isKnockout ?? false
        ] as [String : Any]
        
        let jsonData = try? JSONSerialization.data(withJSONObject: request, options: [])
        let jsonString = String(data: jsonData!, encoding: .utf8)
        
        Unity.shared.delegate = self
        Unity.shared.show()
        Unity.shared.sendMessage("PILNativeInterface","SetGameConfig", "\( jsonString ?? "")")
        Indicator.shared.hideProgressView()
        
        print("Game Config " , request)
        
        
        let currentDate = Date()
        let stringDate = currentDate.toStringForrmater(withFormat: "dd-MM-yyyy HH:mm a")
        
        GoogleAnalyticsHelper.shared.start_game(start_game_time: stringDate,
                                                game_name: self.tournament?.game?.sceneName ?? "",
                                                event_id: "",
                                                game_entree_fee: "0",
                                                game_event_type: self.tournament?.type ?? "",
                                                game_event_name: "")
    }
    
    
    //step 4
    func save(name: String , sceneName:String , image:String , version:String , filePath:String , gameID:String) {
      print("Game ID Local", gameID)
        guard let appDelegate =
                UIApplication.shared.delegate as? AppDelegate else {
            return
        }
      
      // 1
        let managedContext =   appDelegate.persistentContainer.viewContext
      
      // 2
      let entity =  NSEntityDescription.entity(forEntityName: "Games",
                                   in: managedContext)!
      
      let person = NSManagedObject(entity: entity,
                                   insertInto: managedContext)
      
      // 3
        person.setValue(name, forKeyPath: "name")
        person.setValue(sceneName, forKeyPath: "sceneName")
        person.setValue(image, forKeyPath: "image")
        person.setValue(version, forKeyPath: "version")
        person.setValue(filePath, forKey: "filePath")
        person.setValue(gameID, forKey: "gameID")
      // 4
      do {
        try managedContext.save()
          print("Game save successfully in local storage")
      } catch let error as NSError {
        print("Could not save. \(error), \(error.userInfo)")
      }
    }
  
    
    func getLocalGames(){
        self.localGamesArray.removeAll()
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {  return }
        let managedContext = appDelegate.persistentContainer.viewContext
        let fetchRequest =    NSFetchRequest<NSManagedObject>(entityName: "Games")
        
        do {
            localGames = try managedContext.fetch(fetchRequest)
            
            for i in localGames{
                let gameName = i.value(forKey: "name") as? String
                let sceneName = i.value(forKey: "sceneName") as? String
                let image = i.value(forKey: "image") as? String
                let version = i.value(forKey: "version") as? String
                let filePath = i.value(forKey: "filePath") as? String
                let gameID = i.value(forKey: "gameID") as? String
                var size = "10"
                let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let path = "\(documentsUrl)\(sceneName ?? "")".replacingOccurrences(of: "file://",with: "")
                
                //                print("Size file is" , filePath?.sizeto)
                let url = NSURL.fileURL(withPath: path)
                print("the url = \(url)")
                do {
                    
                    if let sizeOnDisk = try url.sizeOnDisk() {
                        size = sizeOnDisk
                        print("Size:", sizeOnDisk) // Size: 3.15 GB on disk
                    }
                } catch {
                    print(error)
                }
                
                let game = LocalGames(name: gameName, sceneName: sceneName, image: image, version: version , filePath:filePath,gameID: gameID,sizeGame: size)
                localGamesArray.append(game)
            }
//                checkVersion()
            
        } catch let error as NSError {
            print("Could not fetch. \(error), \(error.userInfo)")
        }
    }
    
    func removeOldVersion(){
        //remove from core data (Local)
        let index = localGamesArray.firstIndex{ $0.sceneName == self.tournament?.game?.sceneName ?? "" } ?? 0
        let managedContext = (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext
        let note = localGames[index]
        managedContext.delete(note)
        do {
            try managedContext.save()
        } catch let error as NSError {
            print("Error While Deleting Note: \(error.userInfo)")
        }
        
        //Delete Game Folder
        let sceneName = localGamesArray[index].sceneName ?? ""
        let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let path = "\(documentsUrl)\(sceneName)".replacingOccurrences(of: "file://",with: "")
        
        let fileManager = FileManager()
        do{
            try  fileManager.removeItem(atPath: path)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    
    func unityDidUnload(){
        
    }
    
    
    /// get  iLandscape from game info
    func getLandscape()->Bool{
        var Landscape = false
        if self.tournament?.game?.gameOrientation ?? "" == "LandScape"{
            Landscape = true
        }else{
            Landscape = false
        }
        return Landscape
    }
    
    /// get  start time  from tournament  game info nd convert it to time stamp
    func StartTimeContest()->Int{
        let endTimeBattle = Int((tournament?.startDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
     }
    
    /// get  end time  from tournament  game info and convert it to time stamp
    func EndTimeContest()->Int{
        let endTimeBattle = Int((tournament?.endDateTime ?? "").getDateUTC(currentFormate: "yyyy-MM-dd'T'HH:mm:ss" , from: "").TotimeStamp())
        return endTimeBattle
     }
    
    /// get  prize  from tournament  game info and convert it to dictionary
    func getDicPrize()->[[String:Any]]{
        var DicPrize = [[String:Any]]()
        for i in self.tournament?.prizesDistribution ?? []{
            var dic =  [String:Any]()
            dic["prize"] = i.prize ?? 0
            dic["currency"] = i.currency ?? ""
            dic["rank"] = i.rank ?? 0
            DicPrize.append(dic)
        }
        return DicPrize
    }
    
    /// check the game is tournmanet or battel
    func istournament()->Bool{
        if tournament?.type ?? "" == "tournament" {
            return true
        }
        return false
    }
    
    /// get skill user
    func getSkillsPlayer(skills: Int) {
        self.skillsPlayer = skills
    }
    
    
    func progressGameDownload(name: String, file: String) {
        self.view?.progressGameDownload(name: name, file: file, image: self.tournament?.game?.icons ?? "" )
    }
}
