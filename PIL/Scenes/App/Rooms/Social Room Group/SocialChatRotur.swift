//
//  ChatNewRotur.swift
//  PIL
//
//  Created by same<PERSON> mohammed on 31/05/2023.
//

import Foundation
import UIKit


class SocialChatRotur: SocialChatRouterProtocol{
 
    var vc: SocialChatViewProtocol?
    
    static func createModule(roomID:Int , isMyRoom:Bool)-> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .rooms, VC: .SocialChatVC)) as! SocialChatVC
        let interator  =  SocialChatInteractor()
        let router = SocialChatRotur()
        let worker = ChatWorker()
        let workerRooms = RoomsWorker()
        let presenter = SocialChatPresenter(view: view, error: view, interactor: interator, router: router)
        presenter.interactor = interator
        presenter.view = view
        presenter.router = router
        presenter.error = view
        presenter.roomID = roomID
        presenter.isMyRoom = isMyRoom
        view.presenter = presenter
        interator.presenter = presenter
        interator.worker = worker
        interator.workerRooms = workerRooms
        router.vc = view
        return view
    }
    
    func toCreateTournament(_ roomId: Int) {
        let view = SetStoryBoard.controller(controller: .init(Story: .Branded, VC: .Create)) as! CreateTournamentsVC
        
        let presenter = CreateTournamentsVC.Presenter()
        let interactor = CreateTournamentsVC.Interactor()
        let router = CreateTournamentsVC.Router()
        
        router.vc = view
        view.presenter = presenter
        presenter.interactor = interactor
        interactor.presenter = presenter
        presenter.view = view
        presenter.router = router
        presenter.error = view
        interactor.error = presenter
        presenter.isRooms = true
        presenter.roomId = roomId
        
        guard let vc = self.vc as? UIViewController else { return }
        vc.navigationController?.pushViewController(view, animated: true)
    }
    
    
    
    func toRommGroup(userID:Int , roomID:Int , groupID:Int){
        //GroupRoomChatRouter
        let view = GroupRoomChatRouter.createModule(userID: userID, roomID: roomID , groupID: groupID , incomingCall: false) as! GroupRoomChatVC
        
        guard let vc = self.vc as? UIViewController else { return }
        vc.navigationController?.pushViewController(view, animated: true)
    }
    
    
    func openRequestMicPopUP(){
        
    }
    
    /// navigate to user profile
    func toUser(_ id: Int) {
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(id)"))
        if let view = self.vc as? UIViewController{
            view.navigationController?.pushViewController(vc, animated: true)
        }
    }
}
