//
//  ChatNewVC+Sockit.swift
//  PIL
//
//  Created by sameh mohammed on 04/06/2023.
//

import Foundation
import UIKit
import SignalRClient

extension SocialChatVC:confirmationRequestMicProtocol{
    
    /// if admin accept or cacnecl request any user
    /// - Parameters:
    ///   - status: Bool status mic is true or false
    ///   - userID:String
    ///   - toUserId: Int other use id
    func statusRequest(status: Bool, userID:String,toUserId:Int) {
        print("Status request",status)
        self.SendMicStatus(fromUserId: Int(UserModel.shared.get_id()) ?? 0,
                               toRoomId: self.presenter?.roomID ?? 0,
                               on: status,
                               touserID: toUserId,
                               isAdmin:  self.presenter?.isMyRoom ?? false)
    }
}

extension SocialChatVC{
    
    /// Sockit configuertion
    /// lestion some event when join rom
    /// leave or join room , recive request to open
    func configuerSocket(){
        CheckLeaveORJoinUSer()
        ReceiveMicRequest()
        ReceiveMicStatus()
        /// when recive new message in chat room add it in list of messages and reload tableview
        AppDelegate.notificationsHubConnection?.on(method: "NewLiveMessage", callback: { (messageOBJ:MessageOBJ?)  in
            if messageOBJ?.toRoomId ?? 0 == self.presenter?.roomID ?? 0  && messageOBJ?.fromUserId ?? 0 != Int(UserModel.shared.get_id()){
                    let messageOBJ = UserChatRecord(id: messageOBJ?.fromUserId,
                                                    createdBy: messageOBJ?.fromUserId,
                                                    creationDate: "",
                                                    creationDateStr: "",
                                                    userID: messageOBJ?.toUserId,
                                                    connectionID: "",
                                                    message:  messageOBJ?.message,
                                                    isSeen: false,
                                                    isOnline: false,
                                                    createdName: messageOBJ?.toname ?? "",
                                                    createdImageURL: messageOBJ?.image ?? "",
                                                    toUserID:  messageOBJ?.toUserId,
                                                    itemID: 0,
                                                    typeID:  messageOBJ?.typeID ?? 0 ,
                                                    itemCount: 0,
                                                    groupName: "",
                                                    groupID: 0,
                                                    isGroupChat: false,
                                                    toGroupId:messageOBJ?.toGroupId ?? 0)
                    
                    self.presenter?.setNewMessage(obj: messageOBJ)
                }
                 self.chatTableView.reloadData()
            })

    }
    
    //MARK: - Receive Leave or join Room
    /// when new user joined --  case true -> add user to list of user in top view  and list of all member view
    ///                 -- case false -> remove it form list of user in top view and list of all member view
    func CheckLeaveORJoinUSer(){
        AppDelegate.notificationsHubConnection?.on(method: "LeaveOrJoinRoom", callback: { (messageOBJ:SendMessageRoomModel?)  in
            print("user name",messageOBJ?.isJoin ?? ""  , messageOBJ?.name ?? "")
            if self.self.presenter?.roomID ?? 0 == messageOBJ?.toRoomId {
                if messageOBJ?.isJoin == true  {
                    if messageOBJ?.fromUserId ?? 0 != Int(UserModel.shared.get_id()){
                        self.presenter?.addUser(image: messageOBJ?.image ?? "" ,
                                                 name: messageOBJ?.name ?? "",
                                                 id: messageOBJ?.fromUserId ?? 0)
                        }
                    
                }else if  messageOBJ?.isJoin == false {
                    self.presenter?.removeUser(id: messageOBJ?.fromUserId ?? 0)
                    if messageOBJ?.fromUserId ?? 0 == Int(UserModel.shared.get_id()) ?? 0{
                        self.navigationController?.popViewController(animated: true)
                    }
                }
            }
        })
    }
    
    //MARK: - kickOut From Room
    /// when admin kickout user from room
    /// and received action in leave or join event and user leave automatic
    /// - Parameter toUserId: Int
    func sendKickoutUserFromRoom(toUserId:Int){
        let Message = SendMessageRoomModel(fromUserId:toUserId,
                                           toRoomId: self.presenter?.roomID ?? 0,
                                           toUserId: toUserId)
        
        AppDelegate.notificationsHubConnection?.send(method: "KickOut", Message ) { error in
            DispatchQueue.main.async {
                print("Success sendUserRequestMic",Message)
            }
        }
    }
        
    //MARK: - Sende Mic Action
    /// when user send request to admin to accept or cancel or admin send request to user
    func sendUserRequestMic(toUserId:Int, isAdmin:Bool){ // step 1
        let message = "Request Mic Permission".localized
        let Message = SendMessageRoomModel(fromUserId:Int(UserModel.shared.get_id()) ?? 0,
                                           message: "\(message)",
                                           name: UserModel.shared.get_name(),
                                           image: UserModel.shared.get_image(),
                                           toUserId: toUserId,
                                           isAdmin: isAdmin)
        
        AppDelegate.notificationsHubConnection?.send(method: "OnMicRequest", Message ) { error in
            DispatchQueue.main.async {
                print("Success sendUserRequestMic")
            }
        }
    }
    
    ///when user or admin send  request sendUserRequestMic
    /// the other user receive it in this event show popup to accept or cancel request
    func ReceiveMicRequest(){ // step 2
        AppDelegate.notificationsHubConnection?.on(method: "UserOnMicRequest", callback: { (messageOBJ:SendMessageRoomModel?)  in
            print("Receive UserOnMicRequest", messageOBJ)
            let popUp = self.storyboard?.instantiateViewController(withIdentifier: "confirmationMicVC") as! confirmationMicVC
            popUp.action = self
            
            //if admin send me requst and i not mute change status to mute
            if messageOBJ?.toUserId ?? 0 == Int(UserModel.shared.get_id()) ?? 0 &&
                self.presenter?.isMyRoom == false &&
                self.isSpeack == true{
                
                self.SendMicStatus(fromUserId: Int(UserModel.shared.get_id()) ?? 0,
                                   toRoomId: self.presenter?.roomID ?? 0,
                                   on: false,
                                   touserID:  Int(UserModel.shared.get_id()) ?? 0 ,
                                   isAdmin: self.presenter?.isMyRoom ?? false)
                return
            //if admin recive requst from user
            }else if messageOBJ?.toUserId ?? 0 == self.presenter?.OwnerID ?? 0 {
                popUp.toUserId = messageOBJ?.fromUserId ?? 0
                
            }else{
                //if admin send requst from user
                popUp.toUserId = messageOBJ?.toUserId ?? 0
            }
            popUp.message = "\(messageOBJ?.name ?? "") \( messageOBJ?.message ?? "")"
            self.present(popUp, animated: true)
        })
    }
    
    ///when user change status mic
    func SendMicStatus(fromUserId:Int , toRoomId:Int , on:Bool , touserID:Int , isAdmin:Bool){ // step 3
        let obj = MessageOBJ(fromUserId: fromUserId, toUserId: touserID, toRoomId: toRoomId, on: on,isAdmin: isAdmin)
        AppDelegate.notificationsHubConnection?.send(method: "sendMuteUserMic", obj ) { error in
            DispatchQueue.main.async {
                print("Success SendMessage")
            }
        }
    }
    /// when send request SendMicStatus
    /// receive it in this event  , it  is responsible for open mic or close mic
    /// variable on -> if true is mean the mic is open
    func ReceiveMicStatus(){ // step 4
        AppDelegate.notificationsHubConnection?.on(method: "receiveMuteUserMic", callback: { ( message:MessageOBJ)  in
            if self.self.presenter?.roomID ?? 0 == message.toRoomId {
                print("USER MUTE IS",message.fromUserId ?? 0, message.toUserId ?? 0 , UserModel.shared.get_id())
                self.presenter?.changeStatusMute(id: message.toUserId ?? 0, ststus: message.on ?? false)
                /// in case use my mic
                if message.toUserId ?? 0 == Int(UserModel.shared.get_id()){
                    if message.on ?? false == true{
                        self.isSpeack = true // enable microphone
                        self.micButton.isUserInteractionEnabled = true // this button is clickable
                        self.requestMicBu.isHidden = true
                        self.micButton.isHidden = false
                        self.micButton.setImage(UIImage(named: "microphone"), for: .normal)
                        self.agoraKit.muteLocalAudioStream(false)
                       
                    }else{
                        self.agoraKit.muteLocalAudioStream(true)
                        self.micButton.setImage(UIImage(named: "mute-microphone"), for: .normal)
                        if message.isAdmin == false{
                            self.requestMicBu.isHidden = false
                            self.micButton.isHidden = true
                        }else{
                            self.requestMicBu.isHidden = true
                            self.micButton.isHidden = false
                        }
                    }
                }
                
                if message.on ?? false == true && message.fromUserId ?? 0 != message.toUserId ?? 0 && message.fromUserId ?? 0 == self.presenter?.OwnerID ?? 0 && message.toUserId ?? 0 ==  Int(UserModel.shared.get_id()) {
                    /// if admin accept request user ----- show tost to user and open mic

                    self.showToast(message: "Admin accepted mic Request".localized, font: .systemFont(ofSize: 12))
                    self.requestMicBu.isHidden = true
                    self.micButton.isHidden = false
                    self.isSpeack = true
                    self.micButton.setImage(UIImage(named: "microphone"), for: .normal)
                    self.agoraKit.muteLocalAudioStream(false)

                }else if message.on ?? false == false && message.fromUserId ?? 0 != message.toUserId ?? 0 && message.fromUserId ?? 0 == self.presenter?.OwnerID ?? 0 && message.toUserId ?? 0 ==  Int(UserModel.shared.get_id()){
                    /*
                     two case
                     1- if admin reject requst
                     2- user turn off mic
                     */
                    if self.isSpeack == false{
                        //case one
                        self.showToast(message: "Admin rejected mic Request".localized, font: .systemFont(ofSize: 12))
                    }else{
                        //case two
                        self.showToast(message: "Admin turned off mic".localized, font: .systemFont(ofSize: 12))
                        self.micButton.setImage(UIImage(named: "mute-microphone"), for: .normal)

                    }///The admin turned off the mic
                    print("IS SPEAKE MIC 4")
                    self.requestMicBu.isHidden = false
                    self.micButton.isHidden = true
                    self.isSpeack = false
                    self.agoraKit.muteLocalAudioStream(true)

                }
            }
        })
        
    }
    
    
    //MARK: -  send  message chat  socket
    func sendMessage(fromUserId:Int,
                     message:String,
                     typeID:TypeMessage,
                     toUserId:Int,
                     toImageUrl:String = "",
                     ImageUrl:String,
                     toname:String,
                     toRoomId:Int,
                     toVoiceData: Data? = nil){
        let messageRequest = MessageOBJ(fromUserId: fromUserId ,
                                    message: message,
                                    typeID: typeID.rawValue,
                                    toRoomId: toRoomId ,
                                    toImageUrl: toImageUrl ,
                                    ImageUrl: ImageUrl,
                                    toname: toname)

        print("Request",messageRequest)
        AppDelegate.notificationsHubConnection?.send(method: "SendLiveMessage" ,messageRequest ) { error in
            DispatchQueue.main.async {
                self.messageTF.text = ""
               
            }
            if let e = error {
                print("Error is : \(e)")
            }
        }
        
        guard typeID == .message else { return }
        
        let messageOBJ = UserChatRecord(id: fromUserId,
                                        createdBy: fromUserId,
                                        creationDate: "",
                                        creationDateStr: "",
                                        userID: toUserId,
                                        connectionID: "",
                                        message: message,
                                        isSeen: false,
                                        isOnline: false,
                                        createdName: UserModel.shared.get_name(),
                                        createdImageURL: UserModel.shared.get_image(),
                                        toUserID: toUserId,
                                        itemID: 0,
                                        typeID: typeID.rawValue ,
                                        itemCount: 0,
                                        groupName: "",
                                        groupID: 0,
                                        isGroupChat: false,
                                        toGroupId: 0,
                                        voiceData: toVoiceData)
        
        self.presenter?.setNewMessage(obj: messageOBJ)
    }
}
