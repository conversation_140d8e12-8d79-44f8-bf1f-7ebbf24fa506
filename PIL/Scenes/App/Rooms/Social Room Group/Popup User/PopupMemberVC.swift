//
//  PopupMemberVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 03/04/2024.
//

import UIKit
protocol popupMemberProtocol{
    func muteMember(index:Int)
    func kickoutMember(index:Int)
    func userProfile(indexPath:Int)
}

class PopupMemberVC: UIViewController {

    @IBOutlet weak var userImage: UIImageView!
    @IBOutlet weak var usernameLable: UILabel!
    @IBOutlet weak var mutebu: UIButton!
    
    
    var actions:popupMemberProtocol?
    var user:RoomParicipiantRecordModel?
    var indexUser = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setData()
     }
    
    func setData(){
        self.userImage.sd_setImage(with: URL(string: user?.createdImageURL ?? ""),placeholderImage: placeHolderImage)
        self.usernameLable.text = user?.createdName ?? ""
        if user?.isMute == true{
            mutebu.setTitle("Mute".localized, for: .normal)
        }else{
            mutebu.setTitle("UnMute".localized, for: .normal)
        }
    }
 
    @IBAction func MuteAction(_ sender: Any) {
        self.dismiss(animated: false) {
            self.actions?.muteMember(index: self.indexUser)
        }
    }
    
    @IBAction func kickoutAction(_ sender: Any) {
        
        let optionMenu = UIAlertController(title: nil, message: "Are you sure you want to kick him out of the room?", preferredStyle: .actionSheet)

          let Kickout = UIAlertAction(title: "Kick out", style: .default, handler: {
              (alert: UIAlertAction!) -> Void in
              self.dismiss(animated: false) {
                  self.actions?.kickoutMember(index: self.indexUser)
              }

          })
    
          
          let cancelAction = UIAlertAction(title: "Cancel", style: .cancel, handler: {
              (alert: UIAlertAction!) -> Void in
          })
          optionMenu.addAction(Kickout)
          optionMenu.addAction(cancelAction)
        self.present(optionMenu, animated: true, completion: nil)
    }
    
    @IBAction func cancelBTN(_ sender: Any) {
        self.dismiss(animated: false)
    }
    
    @IBAction func userPofile(_ sender: Any) {
        print("XXXXXX")
        self.dismiss(animated: false) {
            self.actions?.userProfile(indexPath: self.indexUser)
        }
        

    }
}
