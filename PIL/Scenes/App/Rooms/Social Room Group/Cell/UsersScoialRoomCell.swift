//
//  UsersScoialRoomCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 05/10/2023.
//

import UIKit
import SDWebImage
import Lottie

protocol UsersScoialRoomCellProtocol{
    func userInfo(image:String,isMute:Bool)
}

class UsersScoialRoomCell: UICollectionViewCell , UsersScoialRoomCellProtocol {
    @IBOutlet weak var userImage: UIImageView!
    @IBOutlet weak var micUser: UIImageView!
    @IBOutlet weak var loadingView: LottieAnimationView!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    
    func userInfo(image: String,isMute:Bool) {
        self.userImage.sd_setImage(with: URL(string: image),placeholderImage: placeHolderLogoImage)
        if isMute == true{
            self.micUser.image = UIImage(named: "microphone")
            playLottie()
        }else{
            self.micUser.image = UIImage(named: "mute-microphone")
            stopLottie()
        }
    }

    
    func playLottie(){
        loadingView.isHidden = false
        loadingView.loopMode = .loop
        loadingView.play()
        
    }
    
    func stopLottie(){
        loadingView.isHidden = true
        loadingView.stop()
    }
}
