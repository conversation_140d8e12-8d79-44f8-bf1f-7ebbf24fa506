<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Light.ttf">
            <string>Poppins-Light</string>
        </array>
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="ChatRoomCell" rowHeight="235" id="zOJ-9Y-Iy3" customClass="ChatRoomCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="485" height="235"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="zOJ-9Y-Iy3" id="EKT-c3-OGL">
                <rect key="frame" x="0.0" y="0.0" width="485" height="235"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="Igr-ME-xx4">
                        <rect key="frame" x="65" y="10" width="355" height="225"/>
                        <subviews>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Username" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="i3h-1s-trr">
                                <rect key="frame" x="293" y="-16" width="62" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="dl4-Mo-7Ke"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="NzR-WW-xwV">
                                <rect key="frame" x="115" y="-200" width="240" height="200"/>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.4921875" colorSpace="custom" customColorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="200" id="WAs-vv-WCY"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="N7O-r1-m7V">
                                <rect key="frame" x="172.33333333333337" y="0.0" width="182.66666666666663" height="172"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="P43-a8-B3m">
                                        <rect key="frame" x="0.0" y="5" width="182.66666666666666" height="162"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="Fpw-YR-32J">
                                                <rect key="frame" x="0.0" y="48.666666666666664" width="182.66666666666666" height="64.666666666666686"/>
                                                <subviews>
                                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EK7-Zm-6kh">
                                                        <rect key="frame" x="0.0" y="0.0" width="182.66666666666666" height="50.666666666666664"/>
                                                        <subviews>
                                                            <label opaque="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zj0-lC-h0v" customClass="ContextLabel" customModule="ContextLabel">
                                                                <rect key="frame" x="10" y="10.000000000000009" width="137.66666666666666" height="30.666666666666671"/>
                                                                <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="14"/>
                                                                <color key="textColor" name="Black-White"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                        <real key="value" value="0.0"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" name="Black"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="Zj0-lC-h0v" secondAttribute="trailing" constant="35" id="04o-kO-Q2t"/>
                                                            <constraint firstItem="Zj0-lC-h0v" firstAttribute="top" secondItem="EK7-Zm-6kh" secondAttribute="top" constant="10" id="KHs-cT-Pqe"/>
                                                            <constraint firstAttribute="bottom" secondItem="Zj0-lC-h0v" secondAttribute="bottom" constant="10" id="Mqe-0z-8vG"/>
                                                            <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="50" id="UDK-ih-Yjv"/>
                                                            <constraint firstItem="Zj0-lC-h0v" firstAttribute="leading" secondItem="EK7-Zm-6kh" secondAttribute="leading" constant="10" id="gBj-9P-Mcl"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                <real key="value" value="12"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Oct 7 , 12:10 AM" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AzE-gs-IZN">
                                                        <rect key="frame" x="107.66666666666666" y="54.666666666666664" width="75" height="9.9999999999999929"/>
                                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="10" id="FFt-5b-en8"/>
                                                            <constraint firstAttribute="width" constant="75" id="ohe-lI-UC5"/>
                                                        </constraints>
                                                        <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="10"/>
                                                        <color key="textColor" name="Black-White"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="P43-a8-B3m" firstAttribute="top" secondItem="N7O-r1-m7V" secondAttribute="top" constant="5" id="C80-Gl-5Yl"/>
                                    <constraint firstAttribute="trailing" secondItem="P43-a8-B3m" secondAttribute="trailing" id="DpP-Bl-WfX"/>
                                    <constraint firstItem="P43-a8-B3m" firstAttribute="top" secondItem="N7O-r1-m7V" secondAttribute="top" constant="5" id="KQA-6I-Wcs"/>
                                    <constraint firstAttribute="bottom" secondItem="P43-a8-B3m" secondAttribute="bottom" constant="5" id="Qhw-w0-OOr"/>
                                    <constraint firstItem="P43-a8-B3m" firstAttribute="leading" secondItem="N7O-r1-m7V" secondAttribute="leading" id="Qz0-eu-B9h"/>
                                    <constraint firstAttribute="width" relation="lessThanOrEqual" constant="300" id="qGm-Uy-Efg"/>
                                    <constraint firstAttribute="bottom" secondItem="P43-a8-B3m" secondAttribute="bottom" constant="5" id="uQz-eE-DZ1"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lpn-Ru-goh">
                                <rect key="frame" x="75" y="175" width="280" height="50"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wEl-de-gFW">
                                        <rect key="frame" x="5" y="5" width="40" height="40"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JEk-gL-NsR">
                                                <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="HCR-aH-kk9">
                                                        <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                                                <real key="value" value="15"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                                                <color key="value" systemColor="systemGray6Color"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </imageView>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar" translatesAutoresizingMaskIntoConstraints="NO" id="mRE-MW-Hw7">
                                                        <rect key="frame" x="7.6666666666666572" y="7.6666666666666572" width="25" height="25"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="25" id="GcF-ri-T0b"/>
                                                            <constraint firstAttribute="height" constant="25" id="OGe-uB-ElD"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                                                <real key="value" value="15"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                                                <color key="value" systemColor="systemGray6Color"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </imageView>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ezn-xM-NPp">
                                                        <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                        <connections>
                                                            <action selector="voiceMessageUserAction:" destination="zOJ-9Y-Iy3" eventType="touchUpInside" id="AcH-7c-df9"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="mRE-MW-Hw7" firstAttribute="centerX" secondItem="JEk-gL-NsR" secondAttribute="centerX" id="C9g-ub-0ng"/>
                                                    <constraint firstAttribute="trailing" secondItem="ezn-xM-NPp" secondAttribute="trailing" id="HwZ-4u-4De"/>
                                                    <constraint firstItem="ezn-xM-NPp" firstAttribute="leading" secondItem="JEk-gL-NsR" secondAttribute="leading" id="QQ0-RK-wWu"/>
                                                    <constraint firstAttribute="trailing" secondItem="HCR-aH-kk9" secondAttribute="trailing" id="VVd-FN-CkQ"/>
                                                    <constraint firstItem="HCR-aH-kk9" firstAttribute="leading" secondItem="JEk-gL-NsR" secondAttribute="leading" id="W9W-t5-Nyf"/>
                                                    <constraint firstAttribute="bottom" secondItem="ezn-xM-NPp" secondAttribute="bottom" id="WMx-gE-6KY"/>
                                                    <constraint firstAttribute="width" constant="40" id="bQs-8N-Pdf"/>
                                                    <constraint firstAttribute="height" constant="40" id="fst-Qa-zow"/>
                                                    <constraint firstAttribute="bottom" secondItem="HCR-aH-kk9" secondAttribute="bottom" id="hoa-9r-jJP"/>
                                                    <constraint firstItem="ezn-xM-NPp" firstAttribute="top" secondItem="JEk-gL-NsR" secondAttribute="top" id="hzk-1m-H21"/>
                                                    <constraint firstItem="HCR-aH-kk9" firstAttribute="top" secondItem="JEk-gL-NsR" secondAttribute="top" id="ifI-cY-X5i"/>
                                                    <constraint firstItem="mRE-MW-Hw7" firstAttribute="centerY" secondItem="JEk-gL-NsR" secondAttribute="centerY" id="pYv-Bb-v1q"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                        <real key="value" value="25"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="0F0-ik-fjI"/>
                                            <constraint firstAttribute="height" constant="40" id="9mk-oh-sOk"/>
                                            <constraint firstItem="JEk-gL-NsR" firstAttribute="centerY" secondItem="wEl-de-gFW" secondAttribute="centerY" id="9tC-L0-Tag"/>
                                            <constraint firstAttribute="trailing" secondItem="JEk-gL-NsR" secondAttribute="trailing" id="UdW-Xq-Wtk"/>
                                            <constraint firstItem="JEk-gL-NsR" firstAttribute="leading" secondItem="wEl-de-gFW" secondAttribute="leading" id="deh-G2-Sx8"/>
                                        </constraints>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="xyJ-hI-oCk">
                                        <rect key="frame" x="50" y="10" width="225" height="40"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rj5-bE-574">
                                                <rect key="frame" x="0.0" y="0.0" width="25" height="40"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bRY-EP-pgm" customClass="LocalizedButton" customModule="PIL" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="-2.3333333333333428" width="25" height="25"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="25" id="R8b-Jv-gVZ"/>
                                                            <constraint firstAttribute="width" constant="25" id="s1T-ra-DOf"/>
                                                        </constraints>
                                                        <color key="tintColor" name="Black-White"/>
                                                        <state key="normal" image="play.circle" catalog="system">
                                                            <preferredSymbolConfiguration key="preferredSymbolConfiguration" scale="large"/>
                                                        </state>
                                                        <connections>
                                                            <action selector="playAction" destination="zOJ-9Y-Iy3" eventType="touchUpInside" id="soC-vq-aZn"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="25" id="RlF-6J-BBu"/>
                                                    <constraint firstAttribute="trailing" secondItem="bRY-EP-pgm" secondAttribute="trailing" id="h4y-5F-lOD"/>
                                                    <constraint firstItem="bRY-EP-pgm" firstAttribute="leading" secondItem="Rj5-bE-574" secondAttribute="leading" id="u7y-n0-kHW"/>
                                                </constraints>
                                            </view>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="P5M-Ub-gXU">
                                                <rect key="frame" x="25" y="0.0" width="200" height="35"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Eja-or-Pf2">
                                                        <rect key="frame" x="0.0" y="0.0" width="200" height="20"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="20" id="Aut-Gw-alq"/>
                                                            <constraint firstAttribute="width" constant="200" id="NKU-9n-9IC"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Mkh-UG-esB">
                                                        <rect key="frame" x="0.0" y="25" width="200" height="10"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jtp-D2-KGn">
                                                                <rect key="frame" x="0.0" y="0.0" width="26.333333333333332" height="10"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="10" id="GIk-Js-tjM"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                                <color key="textColor" name="Black-White"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2:25 PM" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GDN-9V-sH8">
                                                                <rect key="frame" x="163.33333333333331" y="0.0" width="36.666666666666657" height="10"/>
                                                                <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="10" id="KN5-tW-MNk"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                                <color key="textColor" name="Black-White"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="GDN-9V-sH8" firstAttribute="top" secondItem="Mkh-UG-esB" secondAttribute="top" id="HNp-N5-6Ud"/>
                                                            <constraint firstItem="jtp-D2-KGn" firstAttribute="top" secondItem="Mkh-UG-esB" secondAttribute="top" id="aJJ-8z-yOH"/>
                                                            <constraint firstAttribute="trailing" secondItem="GDN-9V-sH8" secondAttribute="trailing" id="iUN-ny-X9o"/>
                                                            <constraint firstItem="jtp-D2-KGn" firstAttribute="leading" secondItem="Mkh-UG-esB" secondAttribute="leading" id="lga-7d-BlY"/>
                                                            <constraint firstAttribute="bottom" secondItem="jtp-D2-KGn" secondAttribute="bottom" id="pvQ-A2-KZo"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="Eja-or-Pf2" secondAttribute="trailing" id="8cs-ZY-yeG"/>
                                                    <constraint firstItem="Eja-or-Pf2" firstAttribute="leading" secondItem="P5M-Ub-gXU" secondAttribute="leading" id="AsA-hM-Oaq"/>
                                                    <constraint firstItem="Mkh-UG-esB" firstAttribute="leading" secondItem="P5M-Ub-gXU" secondAttribute="leading" id="EM3-jG-Pyg"/>
                                                    <constraint firstAttribute="trailing" secondItem="Mkh-UG-esB" secondAttribute="trailing" id="kdl-Cd-fAc"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Rj5-bE-574" secondAttribute="bottom" id="Bwr-lV-nhf"/>
                                            <constraint firstItem="Rj5-bE-574" firstAttribute="top" secondItem="xyJ-hI-oCk" secondAttribute="top" id="iO9-oe-oUq"/>
                                            <constraint firstItem="bRY-EP-pgm" firstAttribute="centerY" secondItem="Eja-or-Pf2" secondAttribute="centerY" id="mmS-dV-zoe"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" name="Black"/>
                                <constraints>
                                    <constraint firstItem="wEl-de-gFW" firstAttribute="centerY" secondItem="lpn-Ru-goh" secondAttribute="centerY" id="HTP-cg-BC4"/>
                                    <constraint firstAttribute="bottom" secondItem="xyJ-hI-oCk" secondAttribute="bottom" id="XH1-ga-uxK"/>
                                    <constraint firstItem="xyJ-hI-oCk" firstAttribute="leading" secondItem="wEl-de-gFW" secondAttribute="trailing" constant="5" id="dIg-0G-qyT"/>
                                    <constraint firstAttribute="trailing" secondItem="xyJ-hI-oCk" secondAttribute="trailing" constant="5" id="e66-Ll-r1D"/>
                                    <constraint firstAttribute="height" constant="50" id="e9b-rx-2Tk"/>
                                    <constraint firstItem="xyJ-hI-oCk" firstAttribute="top" secondItem="lpn-Ru-goh" secondAttribute="top" constant="10" id="mYL-JB-Uco"/>
                                    <constraint firstItem="wEl-de-gFW" firstAttribute="leading" secondItem="lpn-Ru-goh" secondAttribute="leading" constant="5" id="p7b-Zh-7Hj"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                        <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRaduis">
                                        <real key="value" value="7"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                        <real key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </stackView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="za1-5W-olY">
                        <rect key="frame" x="5" y="5" width="50" height="50"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="fEw-pI-O70">
                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                        <color key="value" systemColor="systemGray6Color"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar" translatesAutoresizingMaskIntoConstraints="NO" id="GsD-px-rGO">
                                <rect key="frame" x="7.6666666666666643" y="7.6666666666666643" width="35" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="35" id="7D1-3Z-hsG"/>
                                    <constraint firstAttribute="width" constant="35" id="s77-jR-nzf"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                        <color key="value" systemColor="systemGray6Color"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uYT-lk-LR0">
                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="uYT-lk-LR0" secondAttribute="bottom" id="842-bG-QsJ"/>
                            <constraint firstAttribute="height" constant="50" id="EDF-aL-BiQ"/>
                            <constraint firstAttribute="width" constant="50" id="Gsa-MI-KMj"/>
                            <constraint firstItem="fEw-pI-O70" firstAttribute="top" secondItem="za1-5W-olY" secondAttribute="top" id="Ig4-zJ-5U9"/>
                            <constraint firstItem="fEw-pI-O70" firstAttribute="leading" secondItem="za1-5W-olY" secondAttribute="leading" id="KGX-h3-7Oq"/>
                            <constraint firstAttribute="trailing" secondItem="uYT-lk-LR0" secondAttribute="trailing" id="N1n-Co-pck"/>
                            <constraint firstAttribute="bottom" secondItem="fEw-pI-O70" secondAttribute="bottom" id="PoC-16-tQz"/>
                            <constraint firstItem="uYT-lk-LR0" firstAttribute="top" secondItem="za1-5W-olY" secondAttribute="top" id="Wfd-9c-hJi"/>
                            <constraint firstItem="GsD-px-rGO" firstAttribute="centerY" secondItem="za1-5W-olY" secondAttribute="centerY" id="aEN-cB-emw"/>
                            <constraint firstAttribute="trailing" secondItem="fEw-pI-O70" secondAttribute="trailing" id="kPm-B7-u6r"/>
                            <constraint firstItem="uYT-lk-LR0" firstAttribute="leading" secondItem="za1-5W-olY" secondAttribute="leading" id="q8a-kg-nWj"/>
                            <constraint firstItem="GsD-px-rGO" firstAttribute="centerX" secondItem="za1-5W-olY" secondAttribute="centerX" id="vBb-Eh-WRA"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="25"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v2g-Sm-dls">
                        <rect key="frame" x="430" y="5" width="50" height="50"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="Ls8-Ca-lzp">
                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                        <color key="value" systemColor="systemGray6Color"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar" translatesAutoresizingMaskIntoConstraints="NO" id="q0f-gJ-dsr">
                                <rect key="frame" x="7.6666666666666856" y="7.6666666666666643" width="35" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="35" id="8sd-Pi-2fC"/>
                                    <constraint firstAttribute="width" constant="35" id="YrG-ne-2kd"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="15"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                        <color key="value" systemColor="systemGray6Color"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xrS-kP-KhH">
                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="xrS-kP-KhH" firstAttribute="leading" secondItem="v2g-Sm-dls" secondAttribute="leading" id="0Bc-LJ-iuY"/>
                            <constraint firstAttribute="height" constant="50" id="0a8-at-a4v"/>
                            <constraint firstItem="q0f-gJ-dsr" firstAttribute="centerY" secondItem="v2g-Sm-dls" secondAttribute="centerY" id="7JT-EZ-SQF"/>
                            <constraint firstAttribute="bottom" secondItem="Ls8-Ca-lzp" secondAttribute="bottom" id="8dl-sj-7JB"/>
                            <constraint firstAttribute="trailing" secondItem="Ls8-Ca-lzp" secondAttribute="trailing" id="Ieb-Jq-g62"/>
                            <constraint firstAttribute="trailing" secondItem="xrS-kP-KhH" secondAttribute="trailing" id="aJ6-TT-BR1"/>
                            <constraint firstAttribute="bottom" secondItem="xrS-kP-KhH" secondAttribute="bottom" id="fcb-qb-aJW"/>
                            <constraint firstAttribute="width" constant="50" id="j81-A6-XBO"/>
                            <constraint firstItem="Ls8-Ca-lzp" firstAttribute="leading" secondItem="v2g-Sm-dls" secondAttribute="leading" id="rJE-1d-hvS"/>
                            <constraint firstItem="xrS-kP-KhH" firstAttribute="top" secondItem="v2g-Sm-dls" secondAttribute="top" id="v8d-7d-5IQ"/>
                            <constraint firstItem="Ls8-Ca-lzp" firstAttribute="top" secondItem="v2g-Sm-dls" secondAttribute="top" id="wzs-BJ-44S"/>
                            <constraint firstItem="q0f-gJ-dsr" firstAttribute="centerX" secondItem="v2g-Sm-dls" secondAttribute="centerX" id="yRa-KD-GGj"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="25"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="tYX-SD-xn0">
                        <rect key="frame" x="260" y="-130" width="80" height="80"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="80" id="DES-hw-A00"/>
                            <constraint firstAttribute="width" constant="80" id="HwO-qs-Pfl"/>
                        </constraints>
                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                        <state key="normal" image="play.circle.fill" catalog="system">
                            <preferredSymbolConfiguration key="preferredSymbolConfiguration" configurationType="pointSize" pointSize="150"/>
                        </state>
                        <connections>
                            <action selector="playVideoAction:" destination="zOJ-9Y-Iy3" eventType="touchUpInside" id="0hD-bU-4cy"/>
                        </connections>
                    </button>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="Igr-ME-xx4" secondAttribute="trailing" constant="65" id="AHe-Z1-16y"/>
                    <constraint firstAttribute="bottom" secondItem="Igr-ME-xx4" secondAttribute="bottom" id="FKe-8E-adX"/>
                    <constraint firstItem="za1-5W-olY" firstAttribute="top" secondItem="EKT-c3-OGL" secondAttribute="top" constant="5" id="Nbm-Iv-dtE"/>
                    <constraint firstItem="tYX-SD-xn0" firstAttribute="centerY" secondItem="NzR-WW-xwV" secondAttribute="centerY" id="PJE-Lc-CME"/>
                    <constraint firstAttribute="trailing" secondItem="v2g-Sm-dls" secondAttribute="trailing" constant="5" id="bhl-Rb-B8U"/>
                    <constraint firstItem="tYX-SD-xn0" firstAttribute="centerX" secondItem="NzR-WW-xwV" secondAttribute="centerX" id="dS6-Ua-ff1"/>
                    <constraint firstItem="Igr-ME-xx4" firstAttribute="top" secondItem="EKT-c3-OGL" secondAttribute="top" constant="10" id="jKa-wl-hHN"/>
                    <constraint firstItem="za1-5W-olY" firstAttribute="leading" secondItem="EKT-c3-OGL" secondAttribute="leading" constant="5" id="mmS-2r-diP"/>
                    <constraint firstItem="Igr-ME-xx4" firstAttribute="leading" secondItem="EKT-c3-OGL" secondAttribute="leading" constant="65" id="u7O-sE-eud"/>
                    <constraint firstItem="v2g-Sm-dls" firstAttribute="top" secondItem="EKT-c3-OGL" secondAttribute="top" constant="5" id="xMc-5I-Jp7"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="cahtStackView" destination="Igr-ME-xx4" id="07S-RM-s0j"/>
                <outlet property="chateMessageTF" destination="Zj0-lC-h0v" id="fmx-xW-jqg"/>
                <outlet property="currentTime" destination="jtp-D2-KGn" id="Sgq-Sn-aCU"/>
                <outlet property="dateMessageLable" destination="AzE-gs-IZN" id="DNB-hL-Rxc"/>
                <outlet property="imageChat" destination="NzR-WW-xwV" id="cb8-Df-Hni"/>
                <outlet property="imageGuest" destination="GsD-px-rGO" id="zqB-rS-iIM"/>
                <outlet property="imageUser" destination="q0f-gJ-dsr" id="vra-wd-JfG"/>
                <outlet property="playBtn" destination="bRY-EP-pgm" id="C5T-mK-hAa"/>
                <outlet property="playVideoBtn" destination="tYX-SD-xn0" id="5YT-U5-Ajo"/>
                <outlet property="superViewMessage" destination="N7O-r1-m7V" id="oeB-LP-4Ln"/>
                <outlet property="userNameLable" destination="i3h-1s-trr" id="elE-na-9he"/>
                <outlet property="userProfileGuestBu" destination="uYT-lk-LR0" id="r7g-w8-dx2"/>
                <outlet property="viewGuest" destination="za1-5W-olY" id="stB-KD-Kom"/>
                <outlet property="viewMessage" destination="EK7-Zm-6kh" id="bTJ-Sm-V6D"/>
                <outlet property="viewUser" destination="v2g-Sm-dls" id="Pqz-Yt-Q1a"/>
                <outlet property="voiceMessageDate" destination="GDN-9V-sH8" id="mVn-qk-lPO"/>
                <outlet property="voiceMessageUserImage" destination="mRE-MW-Hw7" id="Ea1-Sd-zji"/>
                <outlet property="voiceView" destination="lpn-Ru-goh" id="BDU-Fx-jgy"/>
                <outlet property="voiceWaveContainer" destination="Eja-or-Pf2" id="hgn-jW-cOR"/>
            </connections>
            <point key="canvasLocation" x="156.4885496183206" y="217.25352112676057"/>
        </tableViewCell>
    </objects>
    <designables>
        <designable name="Zj0-lC-h0v">
            <size key="intrinsicContentSize" width="37" height="19.666666666666668"/>
        </designable>
        <designable name="bRY-EP-pgm">
            <size key="intrinsicContentSize" width="25.666666666666668" height="25.666666666666668"/>
        </designable>
    </designables>
    <resources>
        <image name="avatar" width="86" height="91"/>
        <image name="play.circle" catalog="system" width="128" height="123"/>
        <image name="play.circle.fill" catalog="system" width="128" height="123"/>
        <image name="user_placeholder" width="96" height="96"/>
        <namedColor name="Black">
            <color red="0.87800002098083496" green="0.87800002098083496" blue="0.87800002098083496" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemGray6Color">
            <color red="0.94901960780000005" green="0.94901960780000005" blue="0.96862745100000003" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
