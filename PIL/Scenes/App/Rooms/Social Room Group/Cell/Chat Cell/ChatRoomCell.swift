//
//  ChatRoomCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 07/12/2023.
//

import UIKit
import UIKit
import AVFoundation
import ActiveLabel
import AVKit
import ContextLabel
import FDWaveformView
class ChatRoomCell: UITableViewCell  ,ChatCellProtocol{
    func checkReply() {
        
    }
    
    func checkSelection() {
        
    }
    
    //MARK: - outlet
    @IBOutlet weak var viewMessage: UIView!
    @IBOutlet weak var userNameLable: UILabel!
    @IBOutlet weak var chateMessageTF: ContextLabel!
    @IBOutlet weak var dateMessageLable: UILabel!
    @IBOutlet weak var cahtStackView: UIStackView!
    @IBOutlet weak var imageUser: UIImageView!
    @IBOutlet weak var viewUser: UIView!
    @IBOutlet weak var imageGuest: UIImageView!
    @IBOutlet weak var viewGuest: UIView!
    @IBOutlet weak var imageChat: UIImageView!
    @IBOutlet weak var superViewMessage: UIView!
    @IBOutlet weak var currentTime: UILabel!
    @IBOutlet weak var playBtn: UIButton!
    @IBOutlet weak var voiceView: UIView!
    @IBOutlet weak var userProfileGuestBu: UIButton!
    @IBOutlet weak var playVideoBtn: UIButton!
    @IBOutlet weak var voiceWaveContainer: UIView!
    @IBOutlet weak var voiceMessageUserImage: UIImageView!
    @IBOutlet weak var voiceMessageDate: UILabel!
    
    var data: UserChatRecord?
    var index = 0
    var actions:ActionsCaht?
    var Call = "Call Now From"
    var player : AVAudioPlayer?
    var currentPlayingTime : Float?
    var minutes : String?
    var seconds : String?
    var timer : Timer?
    var samplesConstantMult = 0
    private var videoUrl: URL?
    private lazy var fullImageVC: ImageFullScreenVC = {
        let vc = UIStoryboard(name: "NewChat", bundle: nil).instantiateViewController(withIdentifier: "ImageFullScreenVC") as! ImageFullScreenVC
        vc.modalPresentationStyle = .fullScreen
        return vc
    }()
    lazy private var waveformView: FDWaveformView = {
        let fdwaveformView = FDWaveformView()
        fdwaveformView.contentMode = .scaleAspectFit
        fdwaveformView.wavesColor = .white
        fdwaveformView.progressColor = .init(named: "Orange Primary Color")!
        fdwaveformView.translatesAutoresizingMaskIntoConstraints = false
        return fdwaveformView
    }()
    
    override func prepareForReuse() {
        super.prepareForReuse()
        currentTime.text = "00:00"
        player = nil
        timer = nil
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
//        self.contentView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.viewMessageGesture(_:))))
//        self.contentView.isUserInteractionEnabled = true
        self.transform = self.transform.rotated(by: .pi)
    }

    
    func setCustomEditView(){
        self.editingAccessoryView = nil
        self.editingAccessoryType = .none
        self.setEditImage(data?.isSelected ?? false)
    }
    
    private func setEditImage(_ selected: Bool){
        for subViewA in self.subviews {
            if subViewA.classForCoder.description() == "UITableViewCellEditControl" {
                if let subViewB = subViewA.subviews.last {
                    if subViewB.isKind(of: UIImageView.self) {
                        let imageView = subViewB as! UIImageView
                        let widthConstraintA = NSLayoutConstraint(item: subViewA, attribute: .width, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: 60)
                           let heightConstraintA = NSLayoutConstraint(item: subViewA, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: 60)
                        imageView.contentMode = .scaleAspectFit
                        imageView.translatesAutoresizingMaskIntoConstraints = false
                        subViewA.translatesAutoresizingMaskIntoConstraints = false
                        let horizontalConstraint = NSLayoutConstraint(item: imageView, attribute: .centerX, relatedBy: .equal, toItem: subViewA, attribute: .centerX, multiplier: 1, constant: 0)
                            let verticalConstraint = NSLayoutConstraint(item: imageView, attribute: .centerY, relatedBy: .equal, toItem: subViewA, attribute: .centerY, multiplier: 1, constant: 0)
                        let widthConstraint = NSLayoutConstraint(item: imageView, attribute: .width, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: 25)
                           let heightConstraint = NSLayoutConstraint(item: imageView, attribute: .height, relatedBy: .equal, toItem: nil, attribute: .notAnAttribute, multiplier: 1, constant: 25)
                        NSLayoutConstraint.activate([widthConstraint, heightConstraint, horizontalConstraint, verticalConstraint])
                        NSLayoutConstraint.activate([widthConstraintA, heightConstraintA])
                        if selected{
                            imageView.image = .init(named: "select_message")
                        }else{
                            imageView.image = .init(named: "unselect_message")
                        }
                    }
                }
            }
        }
    }
        
    func index(index:Int){
        self.index = index
    }
    
    func setData(_ data: UserChatRecord){
        self.data = data
    }
    
    func MessageSide(mySide:Bool){
//        self.viewMessage.backgroundColor = #colorLiteral(red: 0.1960784314, green: 0.1176470588, blue: 0.09411764706, alpha: 1)
        chateMessageTF.textColor = UIColor(named: "Black-White")

        if mySide == false {
            dateMessageLable.textAlignment = .left
            cahtStackView.alignment = .leading

            if app_lang == "en"{
                self.chateMessageTF.textAlignment = .left
            }else{
                self.chateMessageTF.textAlignment = .left
            }
 
            imageGuest.isHidden = false
            viewGuest.isHidden = false

            imageUser.isHidden = true
            viewUser.isHidden = true

            cahtStackView.alignment = .leading
        }else{
            cahtStackView.alignment = .trailing
            dateMessageLable.textAlignment = .right
             
            if app_lang == "ar"{
                self.chateMessageTF.textAlignment = .right
            }else{
                self.chateMessageTF.textAlignment = .left
            }
            
            imageGuest.isHidden = true
            viewGuest.isHidden = true

            imageUser.isHidden = false
            viewUser.isHidden = false

            cahtStackView.alignment = .trailing
        }
    }
    
        
    //MARK: - delegate
    
     func textMessage(txt:String){
//        self.chateMessageTF.text = txt
    }
 
    func dateMessage(date:String){
        [dateMessageLable, voiceMessageDate].forEach{ $0.text = date }
        [dateMessageLable, voiceMessageDate].forEach{ $0.textAlignment = app_lang == "en" ? .right : .left }
    }
    
    func userData(image:String , myImage:Bool){
        if myImage == true{
            imageUser.sd_setImage(with: URL(string: image), placeholderImage: placeHolderImage, options: .continueInBackground, completed: nil)
         }else{
             imageGuest.sd_setImage(with: URL(string: image), placeholderImage: placeHolderImage, options: .continueInBackground, completed: nil)
        }
        voiceMessageUserImage.sd_setImage(with: URL(string: image), placeholderImage: placeHolderImage, options: .continueInBackground, completed: nil)
    }
    
    func typeMessage(type: Int, url: String, voice: Data?, localMediaUrl: URL?){
        if type == 1{
            var text = ""
            if url.contains(Call) {
                print("exists")
                text = url.before(first: "$")
            }else{
                text = url
            }
            self.superViewMessage.isHidden = false
            self.imageChat.isHidden = true
            self.playVideoBtn.isHidden = true
            self.voiceView.isHidden = true
            if Language.detectLanguage(for: url) == "Arabic" || Language.detectLanguage(for: url) == "العربية" {
                self.chateMessageTF.text = "\(text)  "  //This line used to fix a bug in ActiveLabel package
            }else{
                self.chateMessageTF.text = text
            }
            self.chateMessageTF.foregroundColor = { linkResult in
                switch linkResult.detectionType {
                  case .url, .email:
                    return .systemBlue
                  default:
                    return .white
                  }
            }
            self.chateMessageTF.underlineStyle = { (linkResult) in
                return .single
            }
            self.chateMessageTF.canCopy = true
            self.chateMessageTF.didTouch = { (touchResult) in
                guard let linkResult = touchResult.linkResult else { return }
                switch linkResult.detectionType{
                case .url:
                    print("url --->",url)
                    if let url = URL(string: url){
                        UIApplication.shared.open(url)
                    }
                default: break
                }
            }
        }else if type == 2{
            self.playVideoBtn.isHidden = true
            self.superViewMessage.isHidden = true
            self.imageChat.isHidden = false
            self.voiceView.isHidden = true
            if let localMediaUrl = localMediaUrl, let data = try? Data(contentsOf: localMediaUrl){
                let image = UIImage(data: data)
                self.imageChat.image = image
                self.fullImageVC.image = image
            }else{
                self.imageChat.sd_setImage(with: URL(string: url))
                self.fullImageVC.imageURL = url
            }
            self.imageChat.addGestureRecognizer(UITapGestureRecognizer.init(target: self, action: #selector(showImageInFullScreen)))
            self.imageChat.isUserInteractionEnabled = true
        }else if type == 3{
            if let localMediaUrl = localMediaUrl{
                videoUrl = localMediaUrl
            }else if !url.isEmpty{
                videoUrl = .init(string: url)
            }else{ return }
            self.imageChat.image = Thumbnail.generateThumbnail(from: videoUrl!)
            self.superViewMessage.isHidden = true
            self.imageChat.isHidden = false
            self.voiceView.isHidden = true
            self.playVideoBtn.isHidden = false
            self.imageChat.isUserInteractionEnabled = false
        }else if type == 5{
            self.playVideoBtn.isHidden = true
            self.superViewMessage.isHidden = true
            self.imageChat.isHidden = true
            self.voiceView.isHidden = false
            self.viewUser.isHidden = true
            self.viewGuest.isHidden = true
            if !url.isEmpty{
                FileDownloader.loadFileAsync(url: URL(string: url)!) { fileUrl, error in
                    guard error == nil else { return }
                    guard let fileUrl = fileUrl else { return }
                    guard let data = try? Data(contentsOf: URL(string: fileUrl)!) else { return }
                    self.player = try? AVAudioPlayer(data: data)
                    DispatchQueue.main.async {
                        self.waveformView.frame = .init(x: 0,
                                                        y: 0,
                                                        width: self.voiceWaveContainer.bounds.width,
                                                        height: self.voiceWaveContainer.bounds.height)
                        self.waveformView.audioURL = URL(string: fileUrl)!
                        self.voiceWaveContainer.addSubview(self.waveformView)
                        self.setWaveFormConstraints()
                        self.initPlayer()
                    }
                }
            }else if let data = voice{
                self.player = try? AVAudioPlayer(data: data)
                DispatchQueue.main.async {
                    self.initPlayer()
                }
            }
        }
    }
    
    private func setWaveFormConstraints(){
        NSLayoutConstraint.activate([
            .init(item: waveformView, attribute: .height, relatedBy: .equal, toItem: voiceWaveContainer, attribute: .height, multiplier: 1, constant: 0),
            .init(item: waveformView, attribute: .width, relatedBy: .equal, toItem: voiceWaveContainer, attribute: .width, multiplier: 1, constant: 0)
        ])
    }
    
    @IBAction func playVideoAction(_ sender: Any) {
        let player = AVPlayer(url: videoUrl!)
        let playerController = AVPlayerViewController()
        playerController.player = player
        UIApplication.topViewController?.present(playerController, animated: true) {
            player.play()
        }
    }
    
    
    @IBAction func playAction(){
        if self.player?.isPlaying == false{
            self.playBtn.setImage(UIImage(systemName: "play.circle"), for: .normal)
            self.player?.play()
        }else{
            self.playBtn.setImage(UIImage(systemName: "pause.circle"), for: .normal)
            self.player?.pause()
        }
    }
    
    func initPlayer(){
        guard player != nil else { return }
        player!.prepareToPlay()
        player?.delegate = self
        player!.volume = 1.0
        timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updatePlayer), userInfo: nil, repeats: true)
    }
    
    @objc func updatePlayer(){
        guard player != nil else { return }
        if player?.isPlaying == false {
            playBtn.setImage(UIImage(systemName: "play.circle"), for: .normal)
        }else{
            playBtn.setImage(UIImage(systemName: "pause.circle"), for: .normal)
            currentPlayingTime = Float((player?.currentTime)!)
            updateTime()
            samplesConstantMult += 1
            UIView.animate(withDuration: 1) { [self] in
                waveformView.highlightedSamples = 0..<(waveformView.totalSamples / Int(player!.duration) * samplesConstantMult)
            }
        }
    }
    
    func updateTime(){
        guard player != nil else{ return }
        let audioLength = Int(player!.duration)
        let currentTime = Int(player!.currentTime)
        let minutes = (audioLength - currentTime)/60
        var seconds = (audioLength - currentTime) - minutes / 60
        if minutes > 0 {
            seconds = seconds - 60 * minutes
        }
        self.currentTime.text = NSString(format: "%02d:%02d", minutes,seconds) as String
    }
    
    @objc func showImageInFullScreen(){
        UIApplication.topViewController?.present(fullImageVC, animated: true)
    }
    
    func setDateHidden(_ isHidden: Bool) {
        UIView.animate(withDuration: 0.3) {
            self.dateMessageLable.isHidden = isHidden
            self.layoutIfNeeded()
        }
    }
    
//    @objc func viewMessageGesture(_ sender:UITapGestureRecognizer /*UILongPressGestureRecognizer*/){
//        actions?.SelectCell(index: index)
//    }
    
    @IBAction func voiceMessageUserAction(_ sender: Any) {
        let vc = MainProfileRouter.createModule(profile: .publicProfile("\(self.data?.createdBy ?? 0)")) as! MainProfileVC
        UIApplication.topViewController?.navigationController?.pushViewController(vc, animated: true)
    }
    
}


extension ChatRoomCell: ActiveLabelDelegate{
    func didSelect(_ text: String, type: ActiveType) {
        print("Active label did select")
    }
    
}


extension ChatRoomCell: AVAudioPlayerDelegate{
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        samplesConstantMult = 0
    }
}
