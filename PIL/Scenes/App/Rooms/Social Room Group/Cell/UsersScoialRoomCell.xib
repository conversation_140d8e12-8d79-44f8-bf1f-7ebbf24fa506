<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="collection view cell content view" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="UsersScoialRoomCell" id="eep-jV-JXY" customClass="UsersScoialRoomCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="80" height="80"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <collectionViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="t6D-Ns-PVB">
                <rect key="frame" x="0.0" y="0.0" width="80" height="80"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8sg-76-RmT" customClass="LottieAnimationView" customModule="Lottie">
                        <rect key="frame" x="0.0" y="0.0" width="80" height="80"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="string" keyPath="animationName" value="sound_anim"/>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="dsR-OY-YzR">
                        <rect key="frame" x="0.0" y="5" width="80" height="75"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="75" id="6vk-Fn-ies"/>
                            <constraint firstAttribute="width" constant="80" id="I2T-aP-WFQ"/>
                        </constraints>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar2" translatesAutoresizingMaskIntoConstraints="NO" id="1kB-xr-bnI">
                        <rect key="frame" x="17" y="17" width="46" height="46"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="46" id="31N-Li-bBc"/>
                            <constraint firstAttribute="height" constant="46" id="5ev-7W-ATF"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                <real key="value" value="24"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mute-microphone" translatesAutoresizingMaskIntoConstraints="NO" id="gWb-0z-bgK">
                        <rect key="frame" x="57" y="4" width="15" height="15"/>
                        <color key="tintColor" name="Orange Primary Color"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="15" id="5oG-7g-m2b"/>
                            <constraint firstAttribute="height" constant="15" id="p8L-Sj-nKi"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="1kB-xr-bnI" firstAttribute="centerX" secondItem="t6D-Ns-PVB" secondAttribute="centerX" id="0bA-XH-2a9"/>
                    <constraint firstItem="8sg-76-RmT" firstAttribute="top" secondItem="t6D-Ns-PVB" secondAttribute="top" id="1fj-t0-ptU"/>
                    <constraint firstAttribute="trailing" secondItem="8sg-76-RmT" secondAttribute="trailing" id="7d2-tp-GSy"/>
                    <constraint firstItem="dsR-OY-YzR" firstAttribute="leading" secondItem="t6D-Ns-PVB" secondAttribute="leading" id="AYG-JM-fUE"/>
                    <constraint firstItem="8sg-76-RmT" firstAttribute="leading" secondItem="t6D-Ns-PVB" secondAttribute="leading" id="Oal-5Q-53s"/>
                    <constraint firstAttribute="trailing" secondItem="gWb-0z-bgK" secondAttribute="trailing" constant="8" id="Swz-IW-97i"/>
                    <constraint firstItem="dsR-OY-YzR" firstAttribute="top" secondItem="t6D-Ns-PVB" secondAttribute="top" constant="5" id="ZvZ-47-cQp"/>
                    <constraint firstAttribute="trailing" secondItem="dsR-OY-YzR" secondAttribute="trailing" id="cGf-cU-CUE"/>
                    <constraint firstItem="1kB-xr-bnI" firstAttribute="centerY" secondItem="t6D-Ns-PVB" secondAttribute="centerY" id="n0N-uc-NC7"/>
                    <constraint firstAttribute="bottom" secondItem="8sg-76-RmT" secondAttribute="bottom" id="nKW-yQ-XR9"/>
                    <constraint firstAttribute="bottom" secondItem="dsR-OY-YzR" secondAttribute="bottom" id="ntk-ie-NrG"/>
                    <constraint firstItem="gWb-0z-bgK" firstAttribute="top" secondItem="t6D-Ns-PVB" secondAttribute="top" constant="4" id="oPm-F6-0OP"/>
                </constraints>
            </collectionViewCellContentView>
            <size key="customSize" width="50" height="108"/>
            <connections>
                <outlet property="loadingView" destination="8sg-76-RmT" id="Mrk-1D-SJ0"/>
                <outlet property="micUser" destination="gWb-0z-bgK" id="scU-yQ-aze"/>
                <outlet property="userImage" destination="1kB-xr-bnI" id="vE8-nO-Zje"/>
            </connections>
            <point key="canvasLocation" x="-670.2290076335878" y="402.11267605633805"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="avatar2" width="800" height="800"/>
        <image name="mute-microphone" width="64" height="64"/>
        <image name="user_placeholder" width="96" height="96"/>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
