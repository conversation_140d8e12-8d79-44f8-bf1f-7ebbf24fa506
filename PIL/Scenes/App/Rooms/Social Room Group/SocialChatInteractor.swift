//
//  SocialChatInteractor.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 31/05/2023.
//

import Foundation
import Alamofire

class SocialChatInteractor: SocialChatInteractorInputProtocol{

    
    var presenter: SocialChatInteractorOutputProtocol?
    var error:ErrorProtocol?
    var worker:ChatWorker?
    var workerRooms:RoomsWorker?
    
    /// Get user Skills
    func getSkills(playerID:String){
        GamesWorker.init().getSKills(model: PlayerIDModelRequest(playerId: playerID,language: UserModel.shared.getLanguage()), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 ==  201 ||  model.status ?? 0 ==  200{
                    self.presenter?.getSkillsPlayer(skills: model.data?.skill ?? 5)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }

    
    /// create folder when download game if user not download it before
    func createFolderInDownloadsDirectory(name:String, files:String) {
        
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path , "Name is",name , files)
        
        if !manager.fileExists(atPath: url.appendingPathComponent(name).path) {
            do {
                
                
                // should be go new progress VC
                self.presenter?.progressGameDownload(name: name, file: files)
                print("progressGameDownload",name , files)
                
            }catch let error{
                // file is created before open folder
                print("--->",error)
                
            }
        }else{
            print("Folder is created Before","\(newFolderURL.path)/\(name)")
            self.presenter?.FilesIFExist(path: "\(newFolderURL.path)/\(name)")
            
        }
        
    }
    
    ///check if tournament is not free and i have tokens to join
    func askForJoiningTournament(_ id: Int) {
        RoomsWorker.shared.canJoinContest(Int(UserModel.shared.get_id())!,id){ [weak self] result, statusCode in
            guard let self = self else{ return }
            switch result{
            case .success(let response):
                if 200...299 ~= response.status,
                   response.data?.reason.isNilOrEmpty == true{
                    self.presenter?.didCompleteWithJoinPermission(nil)
                }else{
                    self.error?.featching(error: response.data?.reason ?? "")
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
            }
        }
    }
    
    /// get tournament info
    func getPrivateRoomTournament(_ roomId: Int) {
        self.workerRooms?.getPrivateRoomTournamet(Int(UserModel.shared.get_id())!,
                                                  roomId,
                                                  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                self.presenter?.fetchPrivateRoomTournament(model.data)
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
  
    
    //MARK: - Get List Chat Message
    func getListChat(model:UserChatRecordRM){
        self.worker?.getChat(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
            
                if model.success ?? false == true{
                    self.presenter?.getListChat(data: model)
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    /// send request to get users in room
    func GetUsersRoom(model: RoomJoinLeaveRM){
        workerRooms?.listMembersRoom(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode == 200 || model.statusCode == 201{
                    self.presenter?.getUsersList(user: model.roomParicipiantRecords ?? [] )
                }else{
                    self.error?.featching(error: model.message ?? "" )
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    /// request leave room
    func leaveRoom(model: RoomJoinLeaveRM){
        workerRooms?.LeaveRoom(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success leave Room
                    self.presenter?.successLeaveRoom()
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    /// request get my room info
    func getListRoom(model:CreateRoomRequstModel){
        workerRooms?.listRooms(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success join Room
                    if let data = model.roomRecords?[0]{
                        
                        self.presenter?.successfullyCreatedRoom(data: data)
                        
                    }
                 
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
        
        
    }
}


extension SocialChatInteractor {
    func joinRoom(model: RoomJoinLeaveRM){
        workerRooms?.joinRoom(model: model, compilition:{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.statusCode ?? 0 == 200 || model.statusCode ?? 0 == 201{
                    //success join Room
                    self.presenter?.joinRoomSuccessfully()
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
