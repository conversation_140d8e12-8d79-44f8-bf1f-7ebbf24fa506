//
//  MemberRoomsCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 20/03/2024.
//

import UIKit

class MemberRoomsCell: UITableViewCell {

    static let identifier = "MemberRoomsCell"
    static let nib = UINib(nibName: "MemberRoomsCell", bundle: nil)
    
    @IBOutlet weak var userimage: UIImageView!
    @IBOutlet weak var username: U<PERSON>abel!
    @IBOutlet weak var micBu: UIButton!
    @IBOutlet weak var kickoutBu: UIButton!
    @IBOutlet weak var userProfileBu: UIButton!

//    var member: RoomParicipiantRecordModel?
    var onMic: (()->Void)? = {}
    var onKickout: (()->Void)? = {}
    var onUserProfile : (()->Void)? = {}
    
    func loadUI( member: RoomParicipiantRecordModel){
        userimage.sd_setImage(with: .init(string: member.createdImageURL ?? ""))
        username.text = member.createdName ?? ""
        if member.isMute == false{
            self.micBu.setImage(UIImage(named: "mute-microphone"), for: .normal)
        }else{
            self.micBu.setImage(UIImage(named: "microphone"), for: .normal)
        }
        
    }
    
 
    @IBAction func micAction(_ sender: Any) {
        self.onMic?()
    }
    
    @IBAction func kickoutAction(_ sender: Any) {
        self.onKickout?()
    }
    
    
    @IBAction func UserProfileAction(_ sender: Any) {
        self.onUserProfile?()
    }
}
