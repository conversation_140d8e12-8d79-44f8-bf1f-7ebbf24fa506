<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="MemberRoomsCell" rowHeight="113" id="54m-nH-FNj" customClass="MemberRoomsCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="326" height="113"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="54m-nH-FNj" id="agr-VV-vbo">
                <rect key="frame" x="0.0" y="0.0" width="326" height="113"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gDp-mv-KHd">
                        <rect key="frame" x="10" y="106" width="306" height="2"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="PjK-Vd-Mlt">
                                <rect key="frame" x="0.0" y="0.0" width="306" height="2"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="PjK-Vd-Mlt" firstAttribute="leading" secondItem="gDp-mv-KHd" secondAttribute="leading" id="7II-Bt-1pT"/>
                            <constraint firstAttribute="bottom" secondItem="PjK-Vd-Mlt" secondAttribute="bottom" id="LIu-RY-LHR"/>
                            <constraint firstAttribute="trailing" secondItem="PjK-Vd-Mlt" secondAttribute="trailing" id="gxY-is-5Tu"/>
                            <constraint firstAttribute="height" constant="2" id="hOR-dW-tK5"/>
                            <constraint firstItem="PjK-Vd-Mlt" firstAttribute="top" secondItem="gDp-mv-KHd" secondAttribute="top" id="vJH-IR-HiZ"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="0.5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8nT-Ne-Moy">
                        <rect key="frame" x="65" y="46.666666666666664" width="0.0" height="19.999999999999993"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="oXi-Am-YBW"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                        <color key="textColor" name="Black-White"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Mlu-7W-eaw">
                        <rect key="frame" x="10" y="34" width="45" height="45"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="j4y-09-lJg">
                                <rect key="frame" x="0.0" y="0.0" width="45" height="45"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="TNM-aE-znz">
                                <rect key="frame" x="0.0" y="0.0" width="45" height="45"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="37.5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MLc-F1-o1b">
                                <rect key="frame" x="0.0" y="0.0" width="45" height="45"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <connections>
                                    <action selector="UserProfileAction:" destination="54m-nH-FNj" eventType="touchUpInside" id="XR1-yS-x2G"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="MLc-F1-o1b" secondAttribute="trailing" id="2r1-Aa-6E2"/>
                            <constraint firstAttribute="bottom" secondItem="MLc-F1-o1b" secondAttribute="bottom" id="8cs-UB-Xj5"/>
                            <constraint firstAttribute="height" constant="45" id="Frp-D2-2N3"/>
                            <constraint firstAttribute="trailing" secondItem="TNM-aE-znz" secondAttribute="trailing" id="JR3-H3-2nr"/>
                            <constraint firstAttribute="trailing" secondItem="j4y-09-lJg" secondAttribute="trailing" id="KGD-dx-cIQ"/>
                            <constraint firstAttribute="bottom" secondItem="TNM-aE-znz" secondAttribute="bottom" id="KcY-Rn-z74"/>
                            <constraint firstAttribute="width" constant="45" id="QUf-9q-BfK"/>
                            <constraint firstItem="TNM-aE-znz" firstAttribute="leading" secondItem="Mlu-7W-eaw" secondAttribute="leading" id="RWP-ki-uVh"/>
                            <constraint firstAttribute="bottom" secondItem="j4y-09-lJg" secondAttribute="bottom" id="arJ-PM-gpO"/>
                            <constraint firstItem="j4y-09-lJg" firstAttribute="leading" secondItem="Mlu-7W-eaw" secondAttribute="leading" id="q2D-1d-889"/>
                            <constraint firstItem="j4y-09-lJg" firstAttribute="top" secondItem="Mlu-7W-eaw" secondAttribute="top" id="tcD-Le-4YG"/>
                            <constraint firstItem="MLc-F1-o1b" firstAttribute="top" secondItem="Mlu-7W-eaw" secondAttribute="top" id="trL-z5-EZx"/>
                            <constraint firstItem="MLc-F1-o1b" firstAttribute="leading" secondItem="Mlu-7W-eaw" secondAttribute="leading" id="uPj-Bg-mb4"/>
                            <constraint firstItem="TNM-aE-znz" firstAttribute="top" secondItem="Mlu-7W-eaw" secondAttribute="top" id="zCT-eL-eff"/>
                        </constraints>
                    </view>
                    <stackView opaque="NO" contentMode="scaleToFill" alignment="top" translatesAutoresizingMaskIntoConstraints="NO" id="ytZ-Jt-eF1">
                        <rect key="frame" x="236" y="36.666666666666664" width="80" height="39.999999999999993"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZdJ-g5-x8i">
                                <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="GDo-R9-Zt2"/>
                                    <constraint firstAttribute="width" constant="40" id="nju-R5-GLe"/>
                                </constraints>
                                <color key="tintColor" name="Orange Primary Color"/>
                                <inset key="imageEdgeInsets" minX="10" minY="10" maxX="10" maxY="10"/>
                                <state key="normal" image="mute-microphone">
                                    <preferredSymbolConfiguration key="preferredSymbolConfiguration" configurationType="pointSize" pointSize="25"/>
                                </state>
                                <connections>
                                    <action selector="micAction:" destination="54m-nH-FNj" eventType="touchUpInside" id="m00-UG-6Pb"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="seB-p0-hbO">
                                <rect key="frame" x="40" y="0.0" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="Gvk-pL-5cF"/>
                                    <constraint firstAttribute="height" constant="40" id="WIx-L3-NeC"/>
                                </constraints>
                                <color key="tintColor" name="Orange Primary Color"/>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="Kickout">
                                    <preferredSymbolConfiguration key="preferredSymbolConfiguration" configurationType="pointSize" pointSize="25"/>
                                </state>
                                <connections>
                                    <action selector="kickoutAction:" destination="54m-nH-FNj" eventType="touchUpInside" id="4Xw-oA-n75"/>
                                </connections>
                            </button>
                        </subviews>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="8nT-Ne-Moy" firstAttribute="centerY" secondItem="agr-VV-vbo" secondAttribute="centerY" id="1C6-SH-laH"/>
                    <constraint firstItem="Mlu-7W-eaw" firstAttribute="centerY" secondItem="agr-VV-vbo" secondAttribute="centerY" id="8jN-LU-vCL"/>
                    <constraint firstItem="gDp-mv-KHd" firstAttribute="leading" secondItem="agr-VV-vbo" secondAttribute="leading" constant="10" id="MJn-eK-OKM"/>
                    <constraint firstItem="8nT-Ne-Moy" firstAttribute="leading" secondItem="Mlu-7W-eaw" secondAttribute="trailing" constant="10" id="REv-Zs-a2q"/>
                    <constraint firstAttribute="trailing" secondItem="ytZ-Jt-eF1" secondAttribute="trailing" constant="10" id="Uit-97-NpD"/>
                    <constraint firstItem="ytZ-Jt-eF1" firstAttribute="centerY" secondItem="agr-VV-vbo" secondAttribute="centerY" id="fca-a2-zSd"/>
                    <constraint firstItem="Mlu-7W-eaw" firstAttribute="leading" secondItem="agr-VV-vbo" secondAttribute="leading" constant="10" id="nOA-p5-1xv"/>
                    <constraint firstAttribute="trailing" secondItem="gDp-mv-KHd" secondAttribute="trailing" constant="10" id="qgw-q8-3du"/>
                    <constraint firstAttribute="bottom" secondItem="gDp-mv-KHd" secondAttribute="bottom" constant="5" id="xpP-ld-5ja"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="ZL4-sj-0Yg"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="kickoutBu" destination="seB-p0-hbO" id="48O-m2-6Xw"/>
                <outlet property="micBu" destination="ZdJ-g5-x8i" id="RXX-0O-4BB"/>
                <outlet property="userProfileBu" destination="MLc-F1-o1b" id="rcj-Hd-z18"/>
                <outlet property="userimage" destination="TNM-aE-znz" id="7Dq-xG-NMN"/>
                <outlet property="username" destination="8nT-Ne-Moy" id="HGY-h3-RjB"/>
            </connections>
            <point key="canvasLocation" x="131.29770992366412" y="44.014084507042256"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="A_Divider" width="324" height="1"/>
        <image name="Kickout" width="26" height="25"/>
        <image name="mute-microphone" width="64" height="64"/>
        <image name="user_placeholder" width="96" height="96"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
