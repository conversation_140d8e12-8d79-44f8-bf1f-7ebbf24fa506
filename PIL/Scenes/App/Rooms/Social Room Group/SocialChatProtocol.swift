//
//  SocialChatProtocol.swift
//  PIL
//
//  Created by sameh mohammed on 31/05/2023.
//

import Foundation

protocol SocialChatViewProtocol {
    var presenter:SocialChatPresenterProtocol? {get set}
    func ReloadTableView()
    func ReloadCollectionView()
    func leaveRoom()
    func RoomInfo(roomName:String)
    func onFetchTournament(_ tournamemt: Tournament?)
    func joinedState()
    func updateGameVersion(message:String)
    func progressGameDownload(name:String,file:String, image:String)
}

protocol SocialChatPresenterProtocol{
    var view:SocialChatViewProtocol? {get set}
    var roomID:Int? {get set}
    var usersRoom:[RoomParicipiantRecordModel]? { get set}
    var isMyRoom:Bool? {get set}
    var OwnerID:Int?{ get set}
    var tournament: Tournament? { set get }
    func viewDidLoad()
    func messageCount()->Int
    func configuer(cell:ChatCellProtocol, index:Int)
    func isMyMessage(index:Int)->Bool
    func getTypeMessage(index:Int)->Int
    func getMessage(index:Int)->String
    func getImagegURL(index:Int)->String
    func setNewMessage(obj:UserChatRecord)
    func getAllMessage()-> [UserChatRecord]
    func getUserID(index:Int)->Int
    func getuserRoomID(index:Int)->Int
    func configuerUserRoom(cell:UsersScoialRoomCellProtocol, index:Int)
    func addUser(image:String , name:String , id:Int)
    func usersRoomCount()->Int
    func removeUser(id:Int)
    func LeaveRoomRequest()
    func userIsOwner(index:Int)->Bool
    func changeStatusMute(id:Int,ststus:Bool)
    func onTapCreate()
    func didTapJoinBtn()
    func playTournament()
    func DownloadsFIle(isUpdate:Bool)
    func FileDownloaded(pathFile:String , destination:String)
    func checkUserInMic(index:Int)->Bool
    func joinRoom()
    func onTapUser(_ indexPath: IndexPath)
}


extension SocialChatPresenterProtocol{
    
}
protocol SocialChatInteractorInputProtocol{
    var presenter:SocialChatInteractorOutputProtocol? {get set}
    func getListChat(model:UserChatRecordRM)
    func GetUsersRoom(model: RoomJoinLeaveRM)
    func leaveRoom(model: RoomJoinLeaveRM)
    func getListRoom(model:CreateRoomRequstModel)
    func getPrivateRoomTournament(_ roomId: Int)
    func askForJoiningTournament(_ id: Int)
    func createFolderInDownloadsDirectory(name:String, files:String)
    func getSkills(playerID:String)
    func joinRoom(model: RoomJoinLeaveRM)
}


protocol SocialChatInteractorOutputProtocol{
    func getListChat(data:listUserChatModel)
    func getUsersList(user:[RoomParicipiantRecordModel])
    func successLeaveRoom()
    func successfullyCreatedRoom(data:RoomModelRecord)
    func fetchPrivateRoomTournament(_ data: Tournament?)
    func didCompleteWithJoinPermission(_ reason: String?)
    func FileDownloaded(pathFile:String , destination:String)
    func progressGameDownload(name:String,file:String)//new design
    func FilesIFExist(path:String)
    func getSkillsPlayer(skills:Int)
    func joinRoomSuccessfully()

}

protocol SocialChatRouterProtocol{ 
    func toCreateTournament(_ roomId: Int)
    func toRommGroup(userID:Int , roomID:Int , groupID:Int)
    func toUser(_ id: Int) 
}
