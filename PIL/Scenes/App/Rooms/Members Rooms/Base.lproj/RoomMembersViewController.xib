<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Light.ttf">
            <string>Poppins-Light</string>
        </array>
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="RoomMembersViewController" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="membersTableView" destination="xVu-KF-zae" id="yv6-gk-BJZ"/>
                <outlet property="view" destination="5le-sN-NbO" id="OGA-f7-mCr"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="5le-sN-NbO">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lei-Yl-2y6" customClass="ViewCorners" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="255.66666666666669" width="393" height="596.33333333333326"/>
                    <subviews>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eJj-95-5Uv">
                            <rect key="frame" x="156.66666666666666" y="10.000000000000028" width="80" height="5"/>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="80" id="CcD-fr-I5P"/>
                                <constraint firstAttribute="height" constant="5" id="kQj-MK-7wK"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="2.5"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="xVu-KF-zae">
                            <rect key="frame" x="10" y="70.000000000000028" width="373" height="501.33333333333337"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <connections>
                                <outlet property="dataSource" destination="-1" id="xCg-L9-hNl"/>
                                <outlet property="delegate" destination="-1" id="6nP-GI-mb8"/>
                            </connections>
                        </tableView>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="P1C-17-vLl">
                            <rect key="frame" x="10" y="15.000000000000028" width="373" height="40"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="3PU-5h-05g">
                                    <rect key="frame" x="10" y="5" width="353" height="30"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QYD-Vi-kL1">
                                            <rect key="frame" x="0.0" y="0.0" width="20.333333333333332" height="30"/>
                                            <color key="tintColor" name="Orange Primary Color"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="magnifyingglass" catalog="system"/>
                                        </button>
                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="Find user" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="9c0-we-Wds">
                                            <rect key="frame" x="30.333333333333343" y="0.0" width="322.66666666666663" height="30"/>
                                            <color key="textColor" name="White"/>
                                            <fontDescription key="fontDescription" name="Poppins-Light" family="Poppins" pointSize="13"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <connections>
                                                <action selector="searchTFAction:" destination="-1" eventType="editingChanged" id="dfU-sv-pMl"/>
                                            </connections>
                                        </textField>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" name="Dark Grey"/>
                            <constraints>
                                <constraint firstItem="3PU-5h-05g" firstAttribute="leading" secondItem="P1C-17-vLl" secondAttribute="leading" constant="10" id="2CS-dm-Oad"/>
                                <constraint firstAttribute="bottom" secondItem="3PU-5h-05g" secondAttribute="bottom" constant="5" id="44o-tK-c4y"/>
                                <constraint firstAttribute="height" constant="40" id="9nm-H0-cro"/>
                                <constraint firstAttribute="trailing" secondItem="3PU-5h-05g" secondAttribute="trailing" constant="10" id="KR7-kC-K1f"/>
                                <constraint firstItem="3PU-5h-05g" firstAttribute="top" secondItem="P1C-17-vLl" secondAttribute="top" constant="5" id="exo-Pi-eHZ"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="10"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                    <color key="value" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Members" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oam-0f-nOm">
                            <rect key="frame" x="156.66666666666666" y="20.000000000000028" width="80" height="24"/>
                            <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="17"/>
                            <color key="textColor" name="Black-White"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" name="Dialog"/>
                    <constraints>
                        <constraint firstItem="P1C-17-vLl" firstAttribute="leading" secondItem="lei-Yl-2y6" secondAttribute="leading" constant="10" id="DCP-NJ-ewJ"/>
                        <constraint firstItem="eJj-95-5Uv" firstAttribute="centerX" secondItem="lei-Yl-2y6" secondAttribute="centerX" id="Dbi-ge-ejP"/>
                        <constraint firstAttribute="trailing" secondItem="xVu-KF-zae" secondAttribute="trailing" constant="10" id="Dz1-HY-qyw"/>
                        <constraint firstItem="oam-0f-nOm" firstAttribute="top" secondItem="lei-Yl-2y6" secondAttribute="top" constant="20" id="IRE-kw-w19"/>
                        <constraint firstItem="P1C-17-vLl" firstAttribute="top" secondItem="eJj-95-5Uv" secondAttribute="bottom" id="R1h-RX-Bh8"/>
                        <constraint firstItem="xVu-KF-zae" firstAttribute="top" secondItem="P1C-17-vLl" secondAttribute="bottom" constant="15" id="Upv-3d-qb9"/>
                        <constraint firstItem="oam-0f-nOm" firstAttribute="centerX" secondItem="lei-Yl-2y6" secondAttribute="centerX" id="WEe-Ka-L85"/>
                        <constraint firstItem="eJj-95-5Uv" firstAttribute="top" secondItem="lei-Yl-2y6" secondAttribute="top" constant="10" id="f7T-Uf-azb"/>
                        <constraint firstAttribute="bottom" secondItem="xVu-KF-zae" secondAttribute="bottom" constant="25" id="fXh-DR-uf9"/>
                        <constraint firstAttribute="trailing" secondItem="P1C-17-vLl" secondAttribute="trailing" constant="10" id="fgl-E8-PoP"/>
                        <constraint firstItem="xVu-KF-zae" firstAttribute="leading" secondItem="lei-Yl-2y6" secondAttribute="leading" constant="10" id="wDr-KN-tqF"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="singleCornerRaduis">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedTopLeft" value="YES"/>
                        <userDefinedRuntimeAttribute type="boolean" keyPath="roundedTopRight" value="YES"/>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                            <color key="value" name="border"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                            <real key="value" value="0.5"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3tA-Wh-hFV">
                    <rect key="frame" x="0.0" y="58.999999999999986" width="393" height="196.66666666666663"/>
                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                    <connections>
                        <action selector="dismissAction" destination="-1" eventType="touchUpInside" id="NFf-pC-WoE"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="gI3-SE-lMp"/>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.2467998985" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="gI3-SE-lMp" firstAttribute="trailing" secondItem="3tA-Wh-hFV" secondAttribute="trailing" id="0zJ-X4-Pzi"/>
                <constraint firstItem="lei-Yl-2y6" firstAttribute="height" secondItem="5le-sN-NbO" secondAttribute="height" multiplier="0.7" id="4Qg-KO-2su"/>
                <constraint firstItem="lei-Yl-2y6" firstAttribute="top" secondItem="3tA-Wh-hFV" secondAttribute="bottom" id="aLQ-fb-eCV"/>
                <constraint firstItem="gI3-SE-lMp" firstAttribute="trailing" secondItem="lei-Yl-2y6" secondAttribute="trailing" id="iLH-9T-L2b"/>
                <constraint firstItem="3tA-Wh-hFV" firstAttribute="top" secondItem="gI3-SE-lMp" secondAttribute="top" id="jp0-86-4Lp"/>
                <constraint firstItem="lei-Yl-2y6" firstAttribute="leading" secondItem="gI3-SE-lMp" secondAttribute="leading" id="khm-mB-T1q"/>
                <constraint firstAttribute="bottom" secondItem="lei-Yl-2y6" secondAttribute="bottom" id="maz-7v-hkQ"/>
                <constraint firstItem="3tA-Wh-hFV" firstAttribute="leading" secondItem="gI3-SE-lMp" secondAttribute="leading" id="tV7-m3-XMR"/>
            </constraints>
            <point key="canvasLocation" x="68" y="20"/>
        </view>
    </objects>
    <resources>
        <image name="magnifyingglass" catalog="system" width="128" height="117"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dark Grey">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="White">
            <color red="0.93699997663497925" green="0.92500001192092896" blue="0.95300000905990601" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
