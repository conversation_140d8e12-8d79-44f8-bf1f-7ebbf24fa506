//
//  RoomMembersViewController.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 12/11/2023.
//

import UIKit

/*
    display all users in room
    can serch
 */
class RoomMembersViewController: UIViewController {
    
    @IBOutlet weak var membersTableView: UITableView!
    
    var members: [RoomParicipiantRecordModel] = [] // all members
    var tempMembers: [RoomParicipiantRecordModel] = [] // filter users
    var isSendable: Bool = true

    override func viewDidLoad() {
        super.viewDidLoad()
        membersTableView.register(GroupMemberTableViewCell.nib, forCellReuseIdentifier: GroupMemberTableViewCell.identifier)
        membersTableView.reloadData()
    }
    
        
    /// send gift to user show popup
    func onSendAction(_ id: Int){
        self.dismiss(animated: true) {
            let vc = GiftsVC.loadFromNib()
            vc.toUserId = id
            vc.modalPresentationStyle = .overCurrentContext
            UIApplication.topViewController?.present(vc, animated: true)
        }
    }
    
    /// search by name or id
    @IBAction func searchTFAction(_ sender: UITextField) {
        guard sender.text != "Find user".localized else{
            endSearch()
            return
        }
        guard !sender.text.isNilOrEmpty else{
            endSearch()
            return
        }
        members = members.filter({ return $0.name?.lowercased().contains((sender.text?.lowercased() ?? "")) ?? false || "\($0.id ?? 0)".contains((sender.text ?? "")) })
        membersTableView.reloadData()
    }
    
    
    func endSearch(){
        self.members = tempMembers
        self.membersTableView.reloadData()
    }
    
    @IBAction func dismissAction(){
        self.dismiss(animated: true)
    }

}

//MARK: - text field delegate
extension RoomMembersViewController: UITextFieldDelegate{
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField.text == "Find user".localized{
            textField.text = ""
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.text.isNilOrEmpty{
            textField.text = "Find user".localized
            endSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField.text.isNilOrEmpty{
            textField.text = "Find user".localized
            endSearch()
        }
        view.endEditing(true)
        return true
    }
}

/*
  display list of users
 */
extension RoomMembersViewController: UITableViewDelegate, UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return members.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: GroupMemberTableViewCell.identifier, for: indexPath) as! GroupMemberTableViewCell
        let roomMember = members[indexPath.row]
        cell.userimage.sd_setImage(with: .init(string: roomMember.createdImageURL ?? ""))
        cell.username.text = roomMember.createdName
        if isSendable{
            cell.sendBtn.isHidden = "\(roomMember.id ?? 0)" == UserModel.shared.get_id()
        }else{
            cell.sendBtn.isHidden = true
        }
        cell.onSend = { self.onSendAction(self.members[indexPath.row].id!) }
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 100
    }
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        cell.animateFromBothSides(tableView, indexPath)
    }
}
