//
//  AllMemberRoomsRouter.swift
//  PIL
//
//  Created by admin on 01/10/2023.
//

import Foundation

class AllMemberRoomsRouter: AllMembersRoomsRouterProtocol {
    
    static func createModule(roomID: Int)-> UIViewController {
        
        let vc = SetStoryBoard.controller(controller: Helper(Story: .rooms, VC: .AllMemberRoomsVC)) as! AllMemberRoomsVC
        
//        let vc = UIStoryboard(name: "Rooms", bundle: nil).instantiateViewController(withIdentifier: "AllMemberRoomsVC") as! AllMemberRoomsVC
        let interactor = AllMemberRoomsInteractor()
        let worker = ChatWorker()
        let router = AllMemberRoomsRouter()
        let presenter = AllMemberRoomsPresenter(interactor: interactor, router: router, view: vc )
        
        vc.presenter = presenter
        interactor.presenter = presenter
        presenter.view = vc
        presenter.router = router
        presenter.interactor = interactor
        presenter.roomID = roomID
        return vc
    }
    
}
