//
//  AllMemberRoomsPresenter.swift
//  PIL
//
//  Created by admin on 01/10/2023.
//

import Foundation

class AllMemberRoomsPresenter: AllMembersRoomsPresenterProtocol, AllMembersRoomsInteractorOutputProtocol {
    
    // MARK: - Properties
    
    var presenter  : AllMembersRoomsInteractorInputProtocol?
    var interactor : AllMembersRoomsInteractorInputProtocol?
    var router     : AllMembersRoomsRouterProtocol?
    var view       : AllMembersRoomsViewProtocol?
    var roomID     = 0
    var members    : [RoomParicipiantRecords] = []
    var tempMembers: [RoomParicipiantRecords] = []
    
    // MARK: - Init
    init(
        interactor: AllMembersRoomsInteractorInputProtocol,
        router: AllMembersRoomsRouterProtocol,
        view: AllMembersRoomsViewProtocol
    ) {
        self.interactor = interactor
        self.view = view
        self.router = router
    }
    
    // MARK: -  AllMembersRoomsPresenterProtocol
    
    func viewDidload(){
        interactor?.getMembersRooms(roomID: roomID)
    }
    
    func membersCount()->Int {
        return self.members.count
    }
    
    func configuer(
        cell: MemberGroupCellProtocol,
        index: Int
    ) {
        cell.info(image: members[index].createdImageURL ?? "",
                  name: members[index].createdName ?? "")
    }
    
    func didStartSearch(_ query: String) {
        self.members = members.filter({ return $0.createdName!.lowercased().contains(query.lowercased()) || "\($0.id ?? 0)".contains(query.lowercased()) })
        view?.onFetchData()
    }
    
    func didEndSearch() {
        self.members = tempMembers
        view?.onFetchData()
    }
    
    // MARK: - AllMembersRoomsInteractorOutputProtocol
    
    func didCompleteGetRoomsMembers(data:[RoomParicipiantRecords]) {
        self.members = data
        self.tempMembers = data
        self.view?.onFetchData()
        print("---> members",data.count)
    }
    
}
