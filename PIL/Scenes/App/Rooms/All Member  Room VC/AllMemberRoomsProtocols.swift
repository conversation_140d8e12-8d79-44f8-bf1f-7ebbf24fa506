//
//  AllMemberRoomsProtocols.swift
//  PIL
//
//  Created by admin on 01/10/2023.
//

import Foundation

protocol AllMembersRoomsViewProtocol {
    
    var presenter:AllMembersRoomsPresenterProtocol? {get set}
    func onFetchData()
}

protocol AllMembersRoomsPresenterProtocol {
    
    var members: [RoomParicipiantRecords] {get set}
    var tempMembers: [RoomParicipiantRecords] {get set}
    var view: AllMembersRoomsViewProtocol? {get set}
    func viewDidload()
    func membersCount()->Int
    func configuer(cell: MemberGroupCellProtocol , index: Int)
    func didStartSearch(_ query: String)
    func didEndSearch()
}

protocol AllMembersRoomsInteractorInputProtocol {
    
    var presenter: AllMembersRoomsInteractorOutputProtocol? {get set}
    func getMembersRooms(roomID: Int)
 }

protocol AllMembersRoomsInteractorOutputProtocol {
    
    func didCompleteGetRoomsMembers(data:[RoomParicipiantRecords])
}

protocol AllMembersRoomsRouterProtocol {

}
