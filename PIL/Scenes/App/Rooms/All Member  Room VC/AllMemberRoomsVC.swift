//
//  AllMemberRoomsVC.swift
//  PIL
//
//  Created by admin on 01/10/2023.
//

import UIKit

class AllMemberRoomsVC: UIViewController, AllMembersRoomsViewProtocol {
    
    // MARK: - @IBOutlets
    
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var userTableView: UITableView!
    @IBOutlet weak var back: UIButton!
    @IBOutlet weak var noMemberLabel: UILabel!
    
    // MARK: - Propertries
    
    var presenter: AllMembersRoomsPresenterProtocol?

    // MARK: - Life Cycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.backgroundColor = .clear
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back])
        self.navigationView.setNavigationTitle(title: "All Members".localized)
        setXIB()
        presenter?.viewDidload()
    }
   
   private func setXIB(){
        self.userTableView.register(UINib(nibName: "MemberGroupCell", bundle: nil), forCellReuseIdentifier: "MemberGroupCell")
    }
 
    func onFetchData() {
        self.userTableView.reloadData()
    }
    
    //MARK: - Actions
    
    @IBAction func backBTN(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    /// Search
    @IBAction func searchTFAction(_ sender: UITextField) {
        guard sender.text != "Find member".localized else{
            presenter?.didEndSearch()
            return
        }
        guard !sender.text.isNilOrEmpty else{
            presenter?.didEndSearch()
            return
        }
        presenter?.didStartSearch(sender.text!)
    }
    
}

// MARK: - TableView DataSource

extension AllMemberRoomsVC:  UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.membersCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = self.userTableView.dequeueReusableCell(withIdentifier: "MemberGroupCell", for: indexPath) as! MemberGroupCell
        presenter?.configuer(cell: cell, index: indexPath.row)
        return cell
    }
    
}

// MARK: - TextField Delegate

extension AllMemberRoomsVC: UITextFieldDelegate {
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField.text == "Find member".localized{
            textField.text = ""
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.text.isNilOrEmpty{
            textField.text = "Find member".localized
            presenter?.didEndSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField.text.isNilOrEmpty{
            textField.text = "Find member".localized
            presenter?.didEndSearch()
        }
        view.endEditing(true)
        return true
    }
}
