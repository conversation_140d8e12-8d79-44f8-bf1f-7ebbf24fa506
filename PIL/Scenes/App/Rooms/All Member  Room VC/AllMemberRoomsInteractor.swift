//
//  AllMemberRoomsInteractor.swift
//  PIL
//
//  Created by admin on 01/10/2023.
//

import Foundation

class AllMemberRoomsInteractor: AllMembersRoomsInteractorInputProtocol {
    
    // MARK: - Properties
    
    var presenter: AllMembersRoomsInteractorOutputProtocol?
    var error: ErrorProtocol?
    
    func getMembersRooms(roomID: Int) {
        
        var payload = ListRoomParicipiantParamter()
        payload.createdBy = Int(UserModel.shared.get_id()) ?? 0
        payload.roomParicipiantRecord = RoomParicipiantRecord(roomID: roomID)
        
        SocialWorker.shared.loadListRoomParicipiant(payload) { [weak self] (result, statusCode) in
            guard let self = self  else {return}
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                
                if model.success ?? false == true{
                    self.presenter?.didCompleteGetRoomsMembers(data: model.roomParicipiantRecords ?? [])
                }else{
                    self.error?.featching(error: model.message ?? "")
                }
                
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
            
        }
    }
    
}
