//
//  confirmationMicVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 15/10/2023.
//

import UIKit

/*
    popup with two options : -
    1 - if admin need to send requst mute or unmnute
    2 - and if admin need to kickout user from rom
 
    and back with data to send it to sockit
    user confirmationRequestMicProtocol
 */


protocol confirmationRequestMicProtocol{
    func statusRequest(status:Bool, userID:String ,toUserId:Int)
}

class confirmationMicVC: UIViewController {
    
    @IBOutlet weak var titleLable: UILabel!
    var action:confirmationRequestMicProtocol?
    var toUserId = 0
    var message = String()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        let txt =  "Request Mic Permission".localized
        titleLable.text = "\(message)"
    }
    
    
    @IBAction func acceptRequestAction(_ sender: Any) {
        self.dismiss(animated: false) {
            self.action?.statusRequest(status: true ,
                                       userID: UserModel.shared.get_id(),
                                       toUserId: self.toUserId)
        }
    }
    
    
    @IBAction func cancelRequestAction(_ sender: Any) {
        self.dismiss(animated: false) {
            self.action?.statusRequest(status: false,
                                       userID: UserModel.shared.get_id(),
                                       toUserId: self.toUserId)
        }
    }
}
