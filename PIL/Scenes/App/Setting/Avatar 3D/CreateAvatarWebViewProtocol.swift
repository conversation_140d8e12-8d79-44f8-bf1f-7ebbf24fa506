//
//  CreateAvatarWebViewProtocol.swift
//  PIL
//
//  Created by Diaa on 18/07/2023.
//

import Foundation

protocol CreateAvatarWebViewProtocol{
    var presenter:CreateAvatarWebViewPresenter? {get set}
}

protocol CreateAvatarWebViewPresenter{
    var view:CreateAvatarWebViewProtocol? { get set}
    func openHome()
}

protocol CreateAvatarWebViewInputInteractor{
    var presentner:CreateAvatarWebViewOutPutInteractor? {get set}
}

protocol CreateAvatarWebViewOutPutInteractor{
    
    
}

protocol CreateAvatarWebViewRouterProtocl{
    func openHome()
}
