//
//  CreateAvatarWebViewVC.swift
//  PIL
//
//  Created by Diaa on 18/07/2023.
//

import UIKit
import WebKit
import CryptoKit
import CommonCrypto

protocol CreatedAvatarSuccessfullyProtocol{
    func ReloadUserInfo()
}

class CreateAvatarWebViewVC: UIViewController ,CreateAvatarWebViewProtocol, WKScriptMessageHandler{
    var presenter: CreateAvatarWebViewPresenter?

    @IBOutlet weak var webView: WKWebView!
    
    
    var USERNAME = "playitAvatarSecret"
    var PASSWORD = "Fdyh5@kfjt%44*&#fg4@jfjk44y#hdg"
    var userId = UserModel.shared.get_id()
    let currentTimestamp = Int(Date().TotimeStamp())
    var action:CreatedAvatarSuccessfullyProtocol? = nil
    var type:typeAvatar?
    var url = ""
    override func viewDidLoad() {
        super.viewDidLoad()
        
        WKWebsiteDataStore.default().httpCookieStore.getAllCookies { (cookies) in
            for cookie in cookies {
                print(cookie)
                self.webView.configuration.websiteDataStore.httpCookieStore.setCookie(cookie)

            }
        }
        
        if type == .profile || type == .signUp {
             url = "https://avatar.pil.live/?entity=\(userId)&timestamp=\(currentTimestamp)&dcode=\(self.Getmd5())"
        }
        print("URL IS",url)
         if let url = URL (string: url) {
             var requestObj = URLRequest(url: url)
             requestObj.httpShouldHandleCookies = true
            self.webView.load(requestObj)
        }

        let source = "function captureLog(msg) { window.webkit.messageHandlers.logHandler.postMessage(msg); } window.console.log = captureLog;"
        let script = WKUserScript(source: source, injectionTime: .atDocumentEnd, forMainFrameOnly: false)
        webView.configuration.userContentController.addUserScript(script)
        webView.configuration.userContentController.add(self, name: "logHandler")
    }
    
 
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.tabBarController?.tabBar.isHidden = true
    }
    
    @objc(userContentController:didReceiveScriptMessage:) func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        
        if message.name == "logHandler" {
            print("LOG message is : \(message.body)")
            if message.body as! String == "avatar_created" {
                if self.type == .profile{
                    self.action?.ReloadUserInfo()
                    self.navigationController?.popViewController(animated: true)
                }else if self.type == .view{
                    self.navigationController?.popViewController(animated: true)
                }else{
                    self.presenter?.openHome()
                }
            }
        }
    }
    
    func Getmd5()->String{
        let FinalString = "\(USERNAME):\(PASSWORD)\(userId)\(currentTimestamp)"
        let md5 = FinalString.md5
        return md5
    }

 

}


extension String {
    var md5: String {
        let data = Data(self.utf8)
        let hash = data.withUnsafeBytes { (bytes: UnsafeRawBufferPointer) -> [UInt8] in
            var hash = [UInt8](repeating: 0, count: Int(CC_MD5_DIGEST_LENGTH))
            CC_MD5(bytes.baseAddress, CC_LONG(data.count), &hash)
            return hash
        }
        return hash.map { String(format: "%02x", $0) }.joined()
    }
}
