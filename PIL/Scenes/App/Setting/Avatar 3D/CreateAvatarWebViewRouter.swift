//
//  CreateAvatarWebViewRouter.swift
//  PIL
//
//  Created by Di<PERSON> on 18/07/2023.
//

import Foundation

enum typeAvatar{
    case signUp
    case profile
    case view
}
class CreateAvatarWebViewRouter:CreateAvatarWebViewRouterProtocl{
    
    weak var viewController: UIViewController?
    static func createModule(type:typeAvatar) -> UIViewController {
        let interactor = CreateAvatarWebViewInteractor()
        let router = CreateAvatarWebViewRouter()
        let view = SetStoryBoard.controller(controller: Helper(Story: .NewProfileST, VC: .CreateAvatarWebViewVC)) as! CreateAvatarWebViewVC
        let presenter = CreateAvatarWebViewPresnenter(view: view, router: router, interactor: interactor)
        view.presenter = presenter
        view.type = type
        presenter.view = view
        presenter.interactor = interactor
        presenter.router = router
        interactor.presentner = presenter
        
        return view
    }
    
    func openHome(){
        let controller = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC)) as! BaseTabbarController
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = controller
    }
    
}
