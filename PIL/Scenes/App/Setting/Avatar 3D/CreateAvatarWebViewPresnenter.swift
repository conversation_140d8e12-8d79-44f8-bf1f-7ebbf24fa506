//
//  CreateAvatarWebViewPresnenter.swift
//  PIL
//
//  Created by Diaa on 18/07/2023.
//

import Foundation

class CreateAvatarWebViewPresnenter:CreateAvatarWebViewPresenter , CreateAvatarWebViewOutPutInteractor{
    var view:CreateAvatarWebViewProtocol?
    var router:CreateAvatarWebViewRouterProtocl?
    var interactor:CreateAvatarWebViewInputInteractor?
    
    init(view:CreateAvatarWebViewProtocol,
         router:CreateAvatarWebViewRouterProtocl,
         interactor:CreateAvatarWebViewInputInteractor
    ) {
        self.view = view
        self.router = router
        self.interactor = interactor
    }
    
    func openHome() {
        self.router?.openHome()
    }
}
