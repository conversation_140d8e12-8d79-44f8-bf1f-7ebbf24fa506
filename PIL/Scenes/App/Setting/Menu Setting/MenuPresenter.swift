//
//  MenuPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 17/03/2024.
//

import Foundation

class MenuPresenter: MenuPresenterProtocol , ErrorProtocol {
    
    
    //MARK: - variables
    weak var view: MenuViewProtocol?
    var router: MenuRouterProtocol?
    var numberCodeKey: DialNumbersDataModel?
    var error: ErrorProtocol?
    
    var contentList = [settingItem]()
    var listRange = 0..<3
    let currentCoubtry = "Current Country".localized
    
    //MARK: - init
    init(view: MenuViewProtocol,
         router: MenuRouterProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        contentList.removeAll()
        
        /// this list form local all with same name (menuTitle) but add number row in string 
        for index in listRange{
            let elementName = "menuTitle\(index)"
            contentList.append(settingItem(title: elementName.localized,
                                               icon: UIImage(named: elementName) ?? UIImage()))
        }
    }
    
    func getContentCount() -> Int {
        return contentList.count
    }
    
    func configure(cell: SettingCellViewDelegate, at index: Int) {
        let obj = contentList[index]
        cell.setRow(icon: obj.icon, title: obj.title)
    }
    
    
    
    
    //MARK: - actions
    
    /// Navigate to screens
    func tapOn(index: Int) {
        switch index{
        case 0:
            router?.openSetting()
            break
        case 1:
            router?.toReferAndEarn()
            break
            
        case 2:
            router?.openHelp()
            break

        default: break
        }
    }
    
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    
   
}


