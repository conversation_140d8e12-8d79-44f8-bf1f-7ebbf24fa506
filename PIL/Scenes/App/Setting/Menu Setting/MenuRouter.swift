//
//  MenuRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 17/03/2024.
//

import Foundation
import UIKit

class MenuRouter: MenuRouterProtocol {
    
    
    weak var VC: MenuViewProtocol?
    var presenter: MenuPresenterProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .SettingST, VC: .MenuVC)) as! MenuVC
        let router = MenuRouter()
        let presenter = MenuPresenter(view: view, router: router, error: view)
        view.presenter = presenter 
        router.VC = view
        router.presenter = presenter
        presenter.error = view
        return view
    }
    
    
    func openSetting(){
        let setting = SettingsRouter.createModule() as! SettingsVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(setting, animated: true)
        }
    }
  
    func openHelp(){
        print("HELP ----- router")
        let help = HelpRouter.createModule() as! HelpPILVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(help, animated: true)
        }
    }
    
    func toReferAndEarn() {
        let referVC = NewReferAndEarnRouter.createModule()
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(referVC, animated: true)
        }
    }
    
    
}
