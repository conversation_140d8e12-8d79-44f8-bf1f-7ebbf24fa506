//
//  MenuVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 17/03/2024.
//

import UIKit

class MenuVC: UI<PERSON>iewController , MenuViewProtocol, ErrorProtocol{
    
    
    //MARK: - variables
    var presenter: MenuPresenterProtocol?

    //MARK: - outlets
    @IBOutlet weak var contentTableList: UITableView!
    @IBOutlet weak var heightTableView: NSLayoutConstraint!
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        setXIBs()
        presenter?.viewDidLoad() /// get list menu
        contentTableList.reloadData()

        
        /// animation
        Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { [self] (_) in
            UIView.animate(withDuration: 0.3) {
                self.heightTableView.constant = 250
                self.view.layoutIfNeeded()
            }
        }
       
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
        Style(navigation: false)/// configuration navigation

    }
    
    //MARK: - functions

/// Cell UI
    func setXIBs(){
        contentTableList.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        contentTableList.register(UINib(nibName: "SettingCell", bundle: Bundle.main), forCellReuseIdentifier: "SettingCell")
    }
    
    
    @IBAction func backBu(_ sender: Any) {
//        let controller = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC)) as! BaseTabbarController
//        let Delegate = UIApplication.shared.delegate as! AppDelegate
//        Delegate.window?.rootViewController = controller
        
        /// Hidden View with  Animation
        UIView.animate(withDuration: 0.3, animations: { () -> Void in
            self.heightTableView.constant = 0
            self.view.layoutIfNeeded()
            Timer.scheduledTimer(withTimeInterval: 0.3, repeats: false) { timer in
                self.navigationController?.popViewController(animated: false)
            }
        })
        
    }
    
    
    //MARK: - delegate functions
    func reloadScreenContent() {
        contentTableList.reloadData()
    }
     
    //MARK: - error deleagte functions
    func featching(error: String) {
        showAlert(withTitle: false, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
//        loginAlert(compilition: { [weak self] in
//            guard let self = self else { return }
//            self.presenter?.logout()
//        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }

    
}
