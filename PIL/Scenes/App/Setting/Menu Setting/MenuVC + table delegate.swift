//
//  MenuVC + table delegate.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 17/03/2024.
//

import Foundation

extension MenuVC : UITableViewDelegate, UITableViewDataSource{
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("Cas<PERSON>",presenter?.getContentCount() ?? 0)
        return presenter?.getContentCount() ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath) as! SettingCell
   
        presenter?.configure(cell: cell, at: indexPath.row)
        return cell
    }
    
    
  
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        presenter?.tapOn(index: indexPath.row)
    }
    
    
    
}
