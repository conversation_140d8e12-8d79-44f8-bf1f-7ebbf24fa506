//
//  MenuProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 17/03/2024.
//

import Foundation

protocol MenuViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: MenuPresenterProtocol? { get set }
    func reloadScreenContent()
}

protocol MenuPresenterProtocol: AnyObject {
    // TODO: Declare presentation methods
    var view: MenuViewProtocol? { get set }
    func viewDidLoad()
    func getContentCount() -> Int
    func configure(cell: SettingCellViewDelegate, at index: Int)
    func tapOn(index: Int)

}

protocol MenuRouterProtocol {
    func openHelp()
    func openSetting()
    func toReferAndEarn()
}

