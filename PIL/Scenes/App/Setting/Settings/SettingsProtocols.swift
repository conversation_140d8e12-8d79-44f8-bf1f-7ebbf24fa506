//
//  SettingsProtocols.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//  
//

import Foundation

protocol SettingsViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: SettingsPresenterProtocol? { get set }
    func reloadScreenContent()
    func languageOperation()
    func changeDarkMode()
    func logoutConfirmation()
    func DeleteAccountComfirmation()
    func AccountDeletedSuccessfully()
    func setHome(lives: Int)
    func shareApp(index:Int)
    func updateCoins(with coins: Int)
    func restorePurchases()
    func showError(_ error: String)
}

protocol SettingsPresenterProtocol: AnyObject {
    // TODO: Declare presentation methods
    var view: SettingsViewProtocol? { get set }
    func viewDidLoad()
    func getContentCount() -> Int
    func configure(cell: SettingCellViewDelegate, at index: Int)
    func tapOn(index: Int)
    func setLanguage(lang: String)
    func logout()
    func setNumber(codeKey: DialNumbersDataModel)
    func sendRequestDeleteAccount()
    func getWalletInfo(obj: WalletWorkerData)

}

protocol SettingsInteractorInputProtocol {
    // TODO: Declare use case methods
    var presenter: SettingsInteractorOutputProtocol? { get set }
    func getLink(model: PILHelpReuestModel)
    func deleteAccount(model:DeleteAccountReuestModel)
    func sendWalletRequest()
    func logout()
}

protocol SettingsInteractorOutputProtocol: AnyObject {
    // TODO: Declare interactor output methods
    func getLinkSuccessfully(model: HelpModel)
    func getStatusDeleteAccount(status:Bool)
    func getWalletInfo(obj:WalletWorkerData)
    func didCompleteLogout()
}

protocol SettingsRouterProtocol {
    func reloadApp()
    func logOut()
    func openRate()
    func openHelpCenter()
    func openPayment()
    func openPhoneCodeKeys(selected: DialNumbersDataModel?)
    func openWebView(title: String, link: String)
    func openGameMangement()
    func openSubscription()
    func openHelp()
    func openBlockList()
    func toReferAndEarn()
    func toAccountRecovery()
}

