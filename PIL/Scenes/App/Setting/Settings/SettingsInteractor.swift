//
//  SettingsInteractor.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//  
//

import Foundation

class SettingsInteractor: SettingsInteractorInputProtocol {

    //MARK: - variables
    weak var presenter: SettingsInteractorOutputProtocol?
    var error: ErrorProtocol?
    var helpWorker: HelpWorkerProtocol?
    var walletWorker:WalletWorker?
    var authWorker: AuthWorker?

    /// get link web view like ( terms - privacy)
    func getLink(model: PILHelpReuestModel) {
        helpWorker?.getLink(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getLinkSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    
    
    func deleteAccount(model:DeleteAccountReuestModel){
        helpWorker?.deleteAccount(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getStatusDeleteAccount(status: model.status ?? false)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    
    //MARK: - get Wallet conins info
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.getWalletInfo(obj: model)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func logout() {
        authWorker?.Logout(completion: { result, statusCode in
            self.presenter?.didCompleteLogout()
        })
    }
}
