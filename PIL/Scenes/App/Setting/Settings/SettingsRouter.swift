//
//  SettingsRouter.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//  
//

import Foundation
import UIKit

class SettingsRouter: SettingsRouterProtocol , PhoneCodeKeySelectionDelegate{
    
    
    weak var VC: SettingsViewProtocol?
    var presenter: SettingsPresenterProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .SettingST, VC: .SettingVC)) as! SettingsVC
        let interactor = SettingsInteractor()
        let router = SettingsRouter()
        let presenter = SettingsPresenter(view: view, router: router, interactor: interactor, error: view)
        let helpWorker = HelpWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.helpWorker = helpWorker
        interactor.error = presenter
        interactor.walletWorker = .init()
        interactor.authWorker = .init()
        router.VC = view
        router.presenter = presenter
        presenter.error = view
        return view
    }
    
    func openBlockList() {
        let blockListVC = BlockListVC.loadFromNib()
        if let vc = VC as? UIViewController {
            vc.navigationController?.pushViewController(blockListVC, animated: true)
        }
    }
    
    func openSubscription() {
        let sub = SetStoryBoard.controller(controller: Helper(Story: .SettingST, VC: .SubscriptionVC)) as! SubscriptionVC
        if let vc = VC as? UIViewController {
            vc.navigationController?.pushViewController(sub, animated: true)
        }
    }
    
    
    func openRate(){
        let rateVC = RateVC.loadFromNib()
        rateVC.modalPresentationStyle = .overCurrentContext
        if let vc = VC as? UIViewController {
            vc.present(rateVC, animated: false)
        }
    }
    
    func openHelpCenter(){
        let help =  HelpRouter.createModule() as! HelpPILVC
        if let vc = VC as? UIViewController {
            vc.navigationController?.pushViewController(help, animated: true)
        }
    }
    
    func openPayment(){
     
    }
    
    func logOut(){
        let controller = LoginRouter.createModule()
        let navigationController = UINavigationController(rootViewController: controller)
        navigationController.setNavigationBarHidden(true, animated: false)
        
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = navigationController
    }
    
    func reloadApp() {
        let splash = SetStoryBoard.controller(controller: Helper(Story: .MainST, VC: .SplashVC))
        
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.set(rootViewController: splash)
        Delegate.window?.makeKeyAndVisible()
    }
    
    
    
    func openPhoneCodeKeys(selected: DialNumbersDataModel?){
        let phoneCodeKeys = PhoneCodeKeyRouter.createModule(selectedCodeKey: selected) as! PhoneCodeKeyVC
        phoneCodeKeys.actionDelegate = self
        if let vc = VC as? UIViewController{
            vc.present(phoneCodeKeys, animated: false, completion: nil)
        }
    }
    
    func select(item: DialNumbersDataModel) {
        if let presenter = presenter as? SettingsPresenter{
            presenter.setNumber(codeKey: item)
        }
    }
    
    func openWebView(title: String, link: String) {
        SetStoryBoard.controller(controller: Helper(Story: .Help, VC: .WebViewControllerVC)) { (controller) in
            let viewController = controller as! WebViewController
            viewController.type = .custom(title, link)
            viewController.hidesBottomBarWhenPushed = true
            if let vc = VC as? UIViewController{
                vc.navigationController?.pushViewController(viewController, animated: true)
            }
        }
    }
    
    func openGameMangement(){
        let game = GameManagemnetRouter.createModule() as! GameManagemnetVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(game, animated: true)
        }
    }
    
    func openHelp(){
        let help = HelpRouter.createModule() as! HelpPILVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(help, animated: true)
        }
    }
    
    func toReferAndEarn() {
        let referVC = NewReferAndEarnRouter.createModule()
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(referVC, animated: true)
        }
    }
    
    func toAccountRecovery() {
        let recoverAccountVC = RecoverAccountVC.loadFromNib()
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(recoverAccountVC, animated: true)
        }
    }
}
