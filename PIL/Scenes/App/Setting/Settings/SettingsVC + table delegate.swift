//
//  SettingsVC + table delegate.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//

import UIKit

extension SettingsVC: UITableViewDelegate, UITableViewDataSource{
    
    /// row height
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == 7 {
            return 0
        }else{
            return 85
        }
        
    }
    
    /// number rows
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("Casdas",presenter?.getContentCount() ?? 0)
        return presenter?.getContentCount() ?? 0
    }
    
    /// rows
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath) as! SettingCell
        
        /// get the sound game is on of off from local
        cell.soundSwitch.isOn = UserModel.shared.getSound()
        if cell.soundSwitch.isOn == true{
            cell.statusSwitchLable.text = "on".localized
        }else{
            cell.statusSwitchLable.text = "off".localized
        }
        if indexPath.row == 4{
            cell.stackSwitch.isHidden = false
        }else{
            cell.stackSwitch.isHidden = true
        }
        /// change switch sound
        cell.soundSwitch.tag = indexPath.row
        cell.soundSwitch.addTarget(self, action: #selector(switchTriggered(sender:)), for: .valueChanged)
        presenter?.configure(cell: cell, at: indexPath.row)
        return cell
    }
    
    
    /// change switch sound game on or off and save new value in local storage
    @objc func switchTriggered(sender: AnyObject) {
        let action = sender as! UISwitch
        print("Sender is ",action.isOn)
        UserModel.shared.setSound(status: action.isOn)
        self.contentTableList.reloadData()
        
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        presenter?.tapOn(index: indexPath.row)
    }
    
    
    /// chage application on other application  
    func shareApp(index:Int){
        let firstActivityItem = "shareApp".localized
        let secondActivityItem : NSURL = NSURL(string: "https://apps.apple.com/us/app/playit-pi/id1555945981")!
         
         // If you want to use an image
         let image : UIImage = UIImage(named: "Logo")!
         let activityViewController : UIActivityViewController = UIActivityViewController(
             activityItems: [firstActivityItem, secondActivityItem, image], applicationActivities: nil)
         
        if UIDevice.current.userInterfaceIdiom == .pad {
            // This lines is for the popover you need to show in iPad
            activityViewController.popoverPresentationController?.sourceView = self.view
            
            // This line remove the arrow of the popover to show in iPad
            activityViewController.popoverPresentationController?.permittedArrowDirections = UIPopoverArrowDirection.down
            activityViewController.popoverPresentationController?.sourceRect = CGRect(x: self.view.bounds.midX, y: self.view.bounds.midY,width: 0,height: 0)
        }
         // Pre-configuring activity items
         activityViewController.activityItemsConfiguration = [
         UIActivity.ActivityType.message
         ] as? UIActivityItemsConfigurationReading
         
         // Anything you want to exclude
         activityViewController.excludedActivityTypes = [
             UIActivity.ActivityType.postToWeibo,
             UIActivity.ActivityType.print,
             UIActivity.ActivityType.assignToContact,
             UIActivity.ActivityType.saveToCameraRoll,
             UIActivity.ActivityType.addToReadingList,
             UIActivity.ActivityType.postToFlickr,
             UIActivity.ActivityType.postToVimeo,
             UIActivity.ActivityType.postToTencentWeibo,
             UIActivity.ActivityType.postToFacebook
         ]
         
         activityViewController.isModalInPresentation = true
         self.present(activityViewController, animated: true, completion: nil)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        cell.animateFromBothSides(tableView, indexPath)
    }
}

@IBDesignable

class UISwitchCustom: UISwitch {
    @IBInspectable var OffTint: UIColor? {
        didSet {
            self.tintColor = OffTint
            self.layer.cornerRadius = 16
            self.backgroundColor = OffTint
        }
    }
}
