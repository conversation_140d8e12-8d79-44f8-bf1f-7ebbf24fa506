//
//  SettingsPresenter.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//  
//

import Foundation
import MOLH
import Alamofire

class SettingsPresenter: SettingsPresenterProtocol, SettingsInteractorOutputProtocol, ErrorProtocol {
    
    //MARK: - variables
    weak var view: SettingsViewProtocol?
    var router: SettingsRouterProtocol?
    var interactor: SettingsInteractorInputProtocol?
    var numberCodeKey: DialNumbersDataModel?
    var error: ErrorProtocol?
    
    var contentList = [settingItem]()
    var listRange = 0..<15
    let currentCoubtry = "Current Country".localized
    
    //MARK: - init
    init(view: SettingsViewProtocol,
         router: SettingsRouterProtocol,
         interactor: SettingsInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        contentList.removeAll()
        interactor?.sendWalletRequest()
        /// list rows
        for index in listRange{
            var elementName = "settingTitle\(index)"
            if index == listRange.count-2{
                if UserModel.shared.get_DarkMode() == "light"{
                    elementName = "Dark Mode".localized
                }else{
                    elementName = "Light Mode".localized
                }
                contentList.append(settingItem(title: elementName.localized,
                                               icon: UIImage(named: "settingTitle13") ?? UIImage()))
            }else{
                contentList.append(settingItem(title: elementName.localized,
                                               icon: UIImage(named: elementName) ?? UIImage()))
            }
        }

//        var countryName = ""
//        if app_lang == "ar"{
//            countryName = "\(UserModel.shared.get_countryName_AR())"
//        }else{
//            countryName = "\(UserModel.shared.get_countryName_EN())"
//        }
//        let countryCode = "\(UserModel.shared.get_countryCode())"
//        let countryDialCode = "\(UserModel.shared.get_DialCode())"
        
//        contentList.insert(settingItem(title:"\(currentCoubtry) - \(countryName)",
//                                       icon: #imageLiteral(resourceName: "globe")), at: 3)
        
//        setNumber(codeKey: DialNumbersDataModel(name: countryName,
//                                                alph_code: countryCode,
//                                                num_code: countryDialCode ,
//                                                name_ar: countryName,
//                                                name_en: countryName))
        
    }
    
    func getContentCount() -> Int {
        return contentList.count
    }
    
    func configure(cell: SettingCellViewDelegate, at index: Int) {
        let obj = contentList[index]
        cell.setRow(icon: obj.icon, title: obj.title)
    }
    
    
    func setLanguage(lang: String) {
        UserModel.shared.setLang(lang: lang)
        router?.reloadApp()
    }
    
    /// request delete account
    func sendRequestDeleteAccount(){ // send requst delete account
        let request = DeleteAccountReuestModel(user_id: UserModel.shared.get_id())
        print("userid",UserModel.shared.get_ID())
        self.interactor?.deleteAccount(model: request)
    }
    /// response delete account
    func getStatusDeleteAccount(status: Bool) { // response
        if status == true{
//            UserModel.shared.logOut()
//            self.view?.AccountDeletedSuccessfully()
            self.logout()
        }
    }
    
    // get user lives count
    func getWalletInfo(obj: WalletWorkerData) {
        self.view?.setHome(lives: obj.lives ?? 0)
        self.view?.updateCoins(with: obj.playerToken ?? 0)
    }
    
    //MARK: - actions
    /// navigagte when select row
    func tapOn(index: Int) {
        switch index{
        case 0:
            print("help")
            router?.openHelp()
            break
        case 1:
            print("Share")
            self.view?.shareApp(index: 1)
            break
            
        case 2:
            router?.openGameMangement()
            break
            
//        case 3:
//            self.selectDialKey()
//            break
            
        case 3:
//            view?.languageOperation()
            if let url: URL = .init(string: UIApplication.openSettingsURLString), UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
            }
            break
            
        case 4:
            print("Sound ")
            break
            
        case 5:
            router?.toReferAndEarn()
            break
      
        case 6:
            interactor?.getLink(model: PILHelpTopics.policy.requestModel)
            break
            
        case 7:
            interactor?.getLink(model: PILHelpTopics.terms.requestModel)
            break
            
        case 8:
            router?.openRate()
            break

        case 9:
            guard UserModel.shared.get_userSubscription()?.subscribed ?? false else{
                error?.featching(error: "You have no subscription".localized)
                return
            }
            router?.openSubscription()
            break
            
        case 10:
            router?.openBlockList()
            break
            
//        case 12:
//            view?.logoutConfirmation()
//            break
            
        case 11:
            view?.DeleteAccountComfirmation()
            break
            
        case 12:
            view?.restorePurchases()
            
        case 13:
            print("Dark Mode")
            view?.changeDarkMode()
            
        case 14:
            self.checkRecover()
            
        default: break
        }
    }
    /// recover my account
    func checkRecover(){
        AuthWorker().canRecoverAccount { result, status in
            switch result {
            case .success(let response):
                if response.data?.status == true{
                    self.router?.toAccountRecovery()
                }else{
                    self.view?.showError("An account has been recovered to this device within the past 30 days. For more information, <NAME_EMAIL> or raise a ticket from app help section.".localized)
                }
            case .failure(let failure):
                break
            }
        }
    }
    
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    /// get like trerms and codations or privacy policy
    func getLinkSuccessfully(model: HelpModel) {
        if let obj = model.data?.body?.first{
            router?.openWebView(title: obj.title ?? "", link: obj.content?.iframeSrc ?? "")
        }
    }
    
    func logout() {
        interactor?.logout()
    }
    
    
    /// tap logout
    func didCompleteLogout() {
        SocialSignOutService.shared.signoutFromAllProviders()
        DispatchQueue.main.async{ [self] in
            UserModel.shared.logOut()
            UserModel.shared.loginAsGuest(status: false)
            router?.logOut()
        }
    }
    
    /// countries
    func selectDialKey() {
        router?.openPhoneCodeKeys(selected: numberCodeKey)
    }
    
    
    func setNumber(codeKey: DialNumbersDataModel) {
        if app_lang == "ar"{
            self.contentList[3].title = "\(currentCoubtry) - \(codeKey.name_ar ?? "")"
        }else{
            self.contentList[3].title = "\(currentCoubtry) - \(codeKey.name_en ?? "")"
        }
        self.view?.reloadScreenContent()
    }
}


