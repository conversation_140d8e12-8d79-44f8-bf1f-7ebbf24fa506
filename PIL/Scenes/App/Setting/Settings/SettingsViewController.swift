//
//  Settings View Controller.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed 
//  
//

import Foundation
import UIKit
import StoreKit

class SettingsVC: UIViewController, SettingsViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: SettingsPresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var contentTableList: UITableView!
    @IBOutlet weak var versionLabel: UILabel!
    @IBOutlet weak var navigationView: NavigationView!
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        // UI Cell
        setXIBs()
        /// navigation view
        setUpNavigation()
        /// load Row Data
        presenter?.viewDidLoad()
        contentTableList.reloadData()
        /// add current version
        versionLabel.text = "Version".localized + " " + ((Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String)!)
    }
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        /// load Row Data
        presenter?.viewDidLoad()
        Style(navigation: false)  /// hidden navigation
    }
    
    //MARK: - functions

    /// Navigation View UI
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Settings".localized)
    }
    
    /// Cell UI
    func setXIBs(){
        contentTableList.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 30, right: 0)
        contentTableList.register(UINib(nibName: "SettingCell", bundle: Bundle.main), forCellReuseIdentifier: "SettingCell")
    }
    
    //MARK: - delegate functions
    func reloadScreenContent() {
        contentTableList.reloadData()
    }
    
    func updateCoins(with coins: Int) {
//        tokensCount.text = "\(coins)"
    }
    
    
    /// Change language and add new value to local
    func languageOperation() {
        let alert = UIAlertController(title: "ChooseLang".localized, message: "", preferredStyle: .actionSheet)
        
        let Arabic = UIAlertAction(title: "العربية", style: .destructive) { [weak self] (action) in
            self?.presenter?.setLanguage(lang: "ar")
        }
        
        let English = UIAlertAction(title: "English".localized, style: .destructive) { [weak self] (action) in
            self?.presenter?.setLanguage(lang: "en")
        }
        
        let Cancle = UIAlertAction(title: "Cancel".localized, style: .cancel)
        
        if let popoverPresentationController = alert.popoverPresentationController {

                popoverPresentationController.sourceRect = view.frame
                popoverPresentationController.sourceView = self.view

        }
        alert.addAction(Arabic)
        alert.addAction(English)
        alert.addAction(Cancle)
        DispatchQueue.main.async {
            self.present(alert, animated: true, completion: nil)
        }
    }
    
    
/// change dark mode and close app automatic and  save new value in local strage and the defualt is dark
    func changeDarkMode() {
        let alert = UIAlertController(title: "The application will be closed, please open it again".localized, message: "", preferredStyle: .actionSheet)
     
        
        let comfirmation = UIAlertAction(title: "OK".localized  ,style: .destructive) { [weak self] (action) in
          if UserModel.shared.get_DarkMode() == "light"{
                
                self?.contentTableList.reloadData()
                UserModel.shared.darMode(status: "dark")
                exit(0)
            }else{
                self?.contentTableList.reloadData()
                UserModel.shared.darMode(status: "light")
                exit(0)
            }
        }
        
        let Cancle = UIAlertAction(title: "Cancel".localized, style: .cancel)
        
        if let popoverPresentationController = alert.popoverPresentationController {

                popoverPresentationController.sourceRect = view.frame
                popoverPresentationController.sourceView = self.view

        }
        alert.addAction(comfirmation)
//        alert.addAction(English)
        alert.addAction(Cancle)
        DispatchQueue.main.async {
            self.present(alert, animated: true, completion: nil)
        }
         
    }
    
    func showError(_ error: String) {
        showActionAlert(msg: error)
    }
    
    func logoutConfirmation() {
        logoutAlert {
            self.presenter?.logout()
        }
    }
     
    /// delete account
    func DeleteAccountComfirmation(){
//        let vc = SetStoryBoard.controller(controller: Helper(Story: .termsDialog , VC: .DeleteAccountViewController )) as! DeleteAccountViewController
        let storyBoard = UIStoryboard(name: "TermsDialog", bundle: Bundle.main)
        let vc = storyBoard.instantiateViewController(withIdentifier: "DeleteAccountViewController") as! DeleteAccountViewController
        vc.modalPresentationStyle = .overFullScreen
        navigationController?.pushViewController(vc, animated: true)
//        present(vc, animated: false, completion: nil)
        
//        self.showConfirmActionsAlert(msg: "Are you sure to Delete your account".localized) {
//            self.presenter?.sendRequestDeleteAccount()
//        }
    }
    
    /// response dele account remove all data in local and logout
    func AccountDeletedSuccessfully(){ // success deleted account
        let login = LoginRouter.createModule() as! LoginVC
        let authNav = UINavigationController(rootViewController: login)
        authNav.setNavigationBarHidden(true, animated: false)
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = authNav
    }
    
    
    //MARK: - error deleagte functions
    func featching(error: String) {
        showAlert(withTitle: false, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }

    func setHome(lives: Int){
//        if UserModel.shared.get_userSubscription()?.subscribed == true{
//            let imageAttachment = NSTextAttachment()
//            imageAttachment.image = UIImage(named: "Infinite")
//            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
//            let imageString = NSAttributedString(attachment: imageAttachment)
//            livesCount.attributedText = imageString
//        }else{
//            livesCount.text = "\(lives)"
//        }
    }

    //MARK: - actions
    @objc func backAction(_ sender: UIButton){
        navigationController?.popViewController(animated: true)
    }
    
  
    
    /// check if user have perchased or made any transactions before
    func restorePurchases() {
        guard SKPaymentQueue.canMakePayments() else { return }
        Indicator.shared.showProgressView()
        SKPaymentQueue.default().add(self)
        if (SKPaymentQueue.canMakePayments()) {
          SKPaymentQueue.default().restoreCompletedTransactions()
        }
    }
    
    
}

extension SettingsVC: SKPaymentTransactionObserver{
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        
    }
    
    func paymentQueueRestoreCompletedTransactionsFinished(_ queue: SKPaymentQueue) {
        print("paymentQueueRestoreCompletedTransactionsFinished")
        Indicator.shared.hideProgressView()
        showAlert(withTitle: false, msg: "Your purchases has been successfully restored".localized, compilition: {})
        
    }
    func paymentQueue(_ queue: SKPaymentQueue, restoreCompletedTransactionsFailedWithError error: Error) {
        
    }
}
