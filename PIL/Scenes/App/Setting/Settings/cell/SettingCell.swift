//
//  SettingCell.swift
//  PIL
//
//  Created by <PERSON> on 9/13/21.
//

import UIKit

protocol SettingCellViewDelegate {
    func setRow(icon: UIImage, title: String)
}

class SettingCell: UITableViewCell, SettingCellViewDelegate {

    //MARK: - outlets
    @IBOutlet weak var rowIcon: UIImageView!
    @IBOutlet weak var rowTitle: UILabel!
    @IBOutlet weak var rowArrow: UIImageView!
    @IBOutlet weak var soundSwitch: UISwitch!
    @IBOutlet weak var stackSwitch: UIStackView!
    @IBOutlet weak var statusSwitchLable: UILabel!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
     
    }
    
    

    //MARK: - functions
    func setRow(icon: UIImage, title: String) {
        rowIcon.image = icon
        rowTitle.text = title
    }
    
}
