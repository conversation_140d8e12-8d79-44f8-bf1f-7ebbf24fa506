//
//  ReportProtocols.swift
//  PIL
//
//  Created by mac on 10/01/2022.
//

import Foundation

protocol ReportViewProtocol: AnyObject{
    
}

protocol ReportPresenterProtocol{
    func reportPlayer(with description: String)
}

protocol ReportInteractorInputProtocol{
    func report(model: ReportRequestModel)
}

protocol ReportInteractoroOutputProtocol: AnyObject{
    func reportedScessfully()
}

protocol ReportRouterProtocol{
    func goBack()
}

