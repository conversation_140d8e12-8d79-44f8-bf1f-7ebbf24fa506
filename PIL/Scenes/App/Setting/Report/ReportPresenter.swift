//
//  ReportPresenter.swift
//  PIL
//
//  Created by mac on 10/01/2022.
//

import Foundation

class ReportPresenter: ReportPresenterProtocol, ReportInteractoroOutputProtocol, ErrorProtocol{
    
    //MARK: - variables
    weak var view: ReportViewProtocol?
    var interactor: ReportInteractorInputProtocol?
    var router: ReportRouterProtocol?
    var error: ErrorProtocol?
    var assigneeUserID: String?
    
    //MARK: - didLoad
    init(view: ReportViewProtocol,
         interactor: ReportInteractorInputProtocol,
         router: ReportRouterProtocol,
         error: ErrorProtocol,
         assigneeUserID: String){
        
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
        self.assigneeUserID = assigneeUserID
    }
    
    //MARK: - funcions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    func reportedScessfully() {
        router?.goBack()
    }
    
    //MARK: - actions
    func logout(){
        UserModel.shared.logOut()
    }
    
    func reportPlayer(with description: String) {
        
        if description.isEmpty{
            error?.featching(error: errors.empty.rawValue.localized)
            return
        }
       
        let requestModel = ReportRequestModel(message: description,
                                              reporter_id: UserModel.shared.get_id(),
                                              reporting_id: assigneeUserID ?? "")
        
        interactor?.report(model: requestModel)
    }
}
