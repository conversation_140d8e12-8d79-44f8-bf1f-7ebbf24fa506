//
//  ReportRouter.swift
//  PIL
//
//  Created by mac on 10/01/2022.
//

import Foundation

class ReportRouter: ReportRouterProtocol{
    
    weak var VC: ReportViewProtocol?
    
    static func createModel(blockedUserID: String) -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .Block, VC: .reportVC)) as! ReportVC
        let router = ReportRouter()
        let interactor = ReportInteractor()
        let presenter = ReportPresenter(view: view, interactor: interactor, router: router, error: view, assigneeUserID: blockedUserID)
        let userWorker = UserWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.userWorker = userWorker
        router.VC = view
        return view
    }
    
    func goBack(){
        if let vc = VC as? UIViewController{
            vc.dismiss(animated: true, completion: nil)
        }
    }
    
}
