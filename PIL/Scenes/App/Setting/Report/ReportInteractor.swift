//
//  ReportInteractor.swift
//  PIL
//
//  Created by mac on 10/01/2022.
//

import Foundation

class ReportInteractor: ReportInteractorInputProtocol{
    weak var presenter: ReportInteractoroOutputProtocol?
    var error: ErrorProtocol?
    var userWorker: UserWorkerProtocol?
    
    func report(model: ReportRequestModel){
        userWorker?.report(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()

                }else if statusCode == 200 {
                    if model.status ?? false{
                        self.presenter?.reportedScessfully()
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                break

            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
