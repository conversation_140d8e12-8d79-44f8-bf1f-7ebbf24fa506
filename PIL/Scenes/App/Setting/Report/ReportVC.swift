//
//  ReportVC.swift
//  PIL
//
//  Created by mac on 10/01/2022.
//

import UIKit
import KMPlaceholderTextView


class ReportVC: UIViewController, ReportViewProtocol, ErrorProtocol {

    //MARK: - variables
    var presenter: ReportPresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var descriptionTV: KMPlaceholderTextView!
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        
    }
    
    //MARK: - funcions
    func featching(error: String){
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }

        }
    }

    
    //MARK: - actions
    @IBAction func cancleAction(_ sender: UIButton){
        dismiss(animated: true, completion: nil)
    }
    
    @IBAction func submitAction(_ sender: UIButton){
        presenter?.reportPlayer(with: descriptionTV.text!)
    }
    
    
}
