//
//  NewProfilePresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import Foundation
import GoogleSignIn
import CryptoKit
import FirebaseAuth
import Firebase
import AuthenticationServices
import CryptoKit

class NewProfilePresenter: NSObject {
    var view: NewProfileViewProtocol
    private let interactor: NewProfileInteractorInPutProtocol
    private let router: NewProfileRouterProtocol
    var error: ErrorProtocol?
    private let info = [InfoEntity(infoLabel: "Username".localized, info: UserModel.shared.get_username()), InfoEntity(infoLabel: "Email".localized, info: UserModel.shared.get_email()), InfoEntity(infoLabel: "Password".localized, info: "********"), InfoEntity(infoLabel: "Phone".localized, info: UserModel.shared.get_phone())]
    
    private var gameHistoryData = [GameHistoryData]()
    var totalGames: Int?
    var startNewPageeGame: Bool?
    var gamePage: Int = 1
    
    private var referralsData = [Referral]()
    var totalReferrals: Int?
    var startNewPageeReferral: Bool?
    var ReferralPage: Int = 1
    
    init(view: NewProfileViewProtocol, interactor: NewProfileInteractorInPutProtocol, router: NewProfileRouterProtocol, error: ErrorProtocol) {
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    
    func linkWithApple() {
        let provider = ASAuthorizationAppleIDProvider()
        let request = provider.createRequest()
        request.requestedScopes = [.fullName, .email]
        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = self
        controller.performRequests()
    }
    
    func linkWithGoogle() {
        guard let clientID = FirebaseApp.app()?.options.clientID else { return }
        
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config
        
        GIDSignIn.sharedInstance.signIn(withPresenting: (view as? UIViewController)!) { [unowned self] result, error in
            guard error == nil else { return }
            guard let googleUser = result?.user,
                  let idToken = googleUser.idToken?.tokenString
            else { return }
            
//            user = SocialLoginModel(token: idToken,
//                                    email: googleUser.profile?.email,
//                                    vendor: .google,
//                                    name: (googleUser.profile?.givenName ?? "") + " " + (googleUser.profile?.familyName ?? ""))
           
        }
    }
}

extension NewProfilePresenter: NewProfilePresenterProtocol {
  
    func viewDidLoad() {
        view.reloadCollectionData()
        interactor.getProfile()
        interactor.sendWalletRequest()

    }
    
    
    func initiateTabs(){
        referralsData.removeAll()
        ReferralPage = 1
        startNewPageeReferral = false
        totalReferrals = 0
        
        gameHistoryData.removeAll()
        gamePage = 1
        startNewPageeGame = false
        totalGames = 0
        
    }
    
    func popViewController() {
        router.popViewController()
    }
    
    func getInfoCount() -> Int {
        info.count
    }
    
    func configure(cell: InfoCollectionViewCellProtocol, of index: Int) {
        cell.setUserData(user: info[index])
        if index == 3 {
            cell.EditBTN(hidden: true)
        }
    }
    
    // ge user lives count
    func getWalletInfo(obj: WalletWorkerData) {
        UserModel.shared.setCountLives(count: obj.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: obj.playerToken ?? 0)
        self.view.setHome(lives: obj.lives ?? 0)
    }
    
    func getGameHistoryData() {
        interactor.getGameHistory()
    }
    
    func getGameHistoryCount() -> Int {
        gameHistoryData.count
    }
    
    func configure(cell: GameHistoryCollectionViewCellProtocol, of index: Int) {
        cell.setGameHistoryData(game: gameHistoryData[index])
    }
    
    func getReferralsData() {
        interactor.getReferrals(page: "\(self.ReferralPage)")
        view.reloadCollectionData()
    }
    
    func getReferralsCount() -> Int {
        referralsData.count
    }
    
    func getNextpageReferral(){
        if !(referralsData.isEmpty) && (referralsData.count < (totalReferrals ?? 0)){
            startNewPageeReferral = true
            ReferralPage+=1
            getReferralsData()
        }
    }
    
    
    func configure(cell: ReferralsCollectionViewCellProtocol, of index: Int) {
        cell.setReferalData(referal: referralsData[index])
    }
    
    func saveNewUserData(image:String ,name: String, userName: String, email: String, birthDate: String, gender: String, bio: String , referral : String, phone : String , externalIdSnap: String) {
        
        var updatedUserModel = ProfileRequestModel()
        if image == nil || image == ""{
            updatedUserModel = ProfileRequestModel(bio: bio, email: email, username: userName, name: name, gender: gender, birthdate: birthDate, referral: referral, phone: phone)
        }else{
            updatedUserModel = ProfileRequestModel(bio: bio, email: email, username: userName, name: name, gender: gender, birthdate: birthDate, referral: referral, phone: phone,image: image,snap_chat_id: externalIdSnap)
        }
        
        
        GoogleAnalyticsHelper.shared.update_user_info(name: updatedUserModel.username ?? "", email: updatedUserModel.email  ?? "", birthdate: updatedUserModel.birthdate  ?? "", phone: UserModel.shared.get_phone(), gender: updatedUserModel.gender  ?? "")
        
        interactor.update(id: UserModel.shared.get_id(), user: updatedUserModel)
    }
    
    func setFireBaseUser(){
        DispatchQueue.global(qos: .background).async{ [weak self] in
            guard let self = self else { return }
            let userModel = UserChatModel(name: UserModel.shared.get_username(),
                                          image: UserModel.shared.get_image(),
                                          FCM: UserModel.shared.get_FCMToken(),
                                          online: true)
            self.interactor.setUserinFirebase(userID: UserModel.shared.get_id() ,model: userModel)
        }
    }
    
    
    func openListAvatar() {
        self.router.openListAvatar()
    }
}

extension NewProfilePresenter: NewProfileInteractorOutPutProtocol {
    
    func FeatchUserDataSuccessfully(model: UserProfileModel){
        guard let userModel = model.data else { return }
        UserModel.shared.update(model: userModel)
        view.setUserData()
        setFireBaseUser()
    }
    
    func getUserSuccessfully(model: UserProfileModel) {
        guard let userModel = model.data else { return }
//        SDImageCache.shared.clearMemory()
//        SDImageCache.shared.clearDisk()
        UserModel.shared.update(model: userModel)
        setFireBaseUser()
        view.updatedUserDataSuccessfully()
    }
    
    func fetchReferralSuccessfully(model: Referrals) {
        self.totalReferrals = model.data?.total ?? 0
        if let referralsData = model.data?.data {
            self.referralsData.append(contentsOf: referralsData)
            self.startNewPageeReferral = false
        }
        view.reloadCollectionData()
    }
    
    func fetchGameHistorySuccessfully(model: GameHistory) {
        if let gameHistoryData = model.data {
            self.gameHistoryData = gameHistoryData
        }
        view.reloadCollectionData()
    }
}

extension NewProfilePresenter: ErrorProtocol {
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
}

extension NewProfilePresenter: ASAuthorizationControllerDelegate{
    func authorizationController(controller: ASAuthorizationController,
                                 didCompleteWithAuthorization authorization: ASAuthorization) {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else{
            return
        }
        guard let appleIDToken = appleIDCredential.identityToken else {
            print("Unable to fetch identity token")
            return
        }
        guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
            print("Unable to serialize token string from data: \(appleIDToken.debugDescription)")
            return
        }
        if !appleIDCredential.email.isNilOrEmpty{
            KeychainWrapper.standard.set(appleIDCredential.email!, forKey: "appleId")
        }
        
//        user = SocialLoginModel(token: idTokenString,
//                                email: savedAppleID,
//                                vendor: .apple,
//                                name: (appleIDCredential.fullName?.givenName ?? "") + " " + (appleIDCredential.fullName?.familyName ?? ""))
//        self.signInToServers(user!)
//        UserModel.shared.loginAsGuest(status: false)
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("Sign in with Apple errored: \(error)")
        
     }
}
