//
//  InfoCollectionCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 25/01/2023.
//

import UIKit
enum GenderType: String{
    case male = "Male"
    case female = "Female"
    case other = "private"
}

class InfoCollectionCell: UICollectionViewCell {
    
    var gender: GenderType?
    let toolbar = UIToolbar()
    var textview_flag = true
    
    @IBOutlet weak var nameTF: UITextField!
    @IBOutlet weak var usernameTF: UITextField!
    @IBOutlet weak var emailTF: UITextField!
    @IBOutlet weak var birthdateTF: UITextField!
    @IBOutlet weak var phoneTF: UITextField!
    @IBOutlet weak var maleBTN: UIButton!
    @IBOutlet weak var femaleBTN: UIButton!
    @IBOutlet weak var privateBTN: UIButton!
    @IBOutlet weak var bioTV: UITextView!
    @IBOutlet var StackCell: [UIStackView]!
    @IBOutlet weak var datePicker: UIView!
    @IBOutlet weak var referralTF: UITextField!
    
    var onTapApple: ()-> Void = {}
    var onTapGoogle: ()-> Void = {}
    var onTapSnap: ()-> Void = {}

    override func awakeFromNib() {
        super.awakeFromNib()
        bioTV.delegate = self
    
        maleBTN.setTitle("", for: .normal)
        femaleBTN.setTitle("", for: .normal)
        privateBTN.setTitle("", for: .normal)
        nameTF.text = UserModel.shared.get_name()
        usernameTF.text = UserModel.shared.get_name()
        emailTF.text = UserModel.shared.get_email()
        phoneTF.text = UserModel.shared.get_phone()
        if UserModel.shared.get_referralCode().isEmpty {
            referralTF.isEnabled = true
        }else{
            referralTF.text = UserModel.shared.get_referralCode()
            referralTF.isEnabled = false
        }
        birthdateTF.text = String(UserModel.shared.get_birthdate().dropLast(9))
        if UserModel.shared.get_bio().isEmpty{
            bioTV.text = "bio".localized
        }else{
            bioTV.text = UserModel.shared.get_bio()
        }
        switch UserModel.shared.get_gender() {
        case "Male":
            maleBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
            gender = .male
        case "Female":
            femaleBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
            gender = .female
        case "private":
            privateBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
            gender = .other
        default:
            return
        }
        
        if app_lang == "ar"{
            for i in StackCell{
                i.alignment = .leading
            }
        }
        
       // self.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(self.cancelDatePicker)))
        

    }
    
    @IBAction func googleAction(_ sender: Any) {
        onTapGoogle()
    }
    
    @IBAction func appleAction(_ sender: Any) {
        onTapApple()
    }
    
    @IBAction func snapchatAction(_ sender: Any) {
        onTapSnap()
    }
    
    @objc func cancelDatePicker() {
        datePicker.isHidden = true
    }
    
    @IBAction func showDatePicker(_ sender: Any) {
        datePicker.isHidden = false
    }
    
    @IBAction func didChangeDate(_ sender: UIDatePicker) {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        birthdateTF.text = dateFormatter.string(from: sender.date)
        datePicker.isHidden = true
    }
    
    @IBAction func maleAction(_ sender: UIButton) {
        maleBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
        femaleBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        privateBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        gender = .male
    }
    
    @IBAction func femaleAction(_ sender: UIButton) {
        maleBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        femaleBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
        privateBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        gender = .female
    }
    
    @IBAction func privateAction(_ sender: UIButton) {
        maleBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        femaleBTN.setImage(UIImage(systemName: "circle"), for: .normal)
        privateBTN.setImage(UIImage(systemName: "circle.fill"), for: .normal)
        gender = .other
    }
    
    var onTapSave: ((_ name: String, _ userName: String, _ email: String, _ birthDate: String, _ gender: String, _ bio: String, _ referral : String, _ phone : String) -> Void)?
    
    @IBAction func saveAction(_ sender: UIButton) {
        onTapSave?(usernameTF.text ?? "", usernameTF.text ?? "", emailTF.text ?? "", birthdateTF.text ?? "", gender?.rawValue ?? "", bioTV.text ?? "" , referralTF.text ?? "", phoneTF.text ?? "")
    }
}

extension InfoCollectionCell: UITextViewDelegate {
    func textViewShouldBeginEditing(_ textView: UITextView) -> Bool {
        if textView.text == "bio".localized {
            textView.text = ""
        }
        textView.textColor = .white
        return true
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text! == "" {
            textView.textColor = .white
            textView.text = "bio".localized
            var position: UITextPosition!
            if app_lang == "ar" {
                position = textView.endOfDocument
            } else {
                position = textView.beginningOfDocument
            }
            textView.selectedTextRange = textView.textRange(from: position, to: position)
        }
    }
}
