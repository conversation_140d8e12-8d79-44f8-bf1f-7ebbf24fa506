//
//  ReferralsCollectionViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 12/01/2023.
//

import UIKit

class ReferralsCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var referralName: UILabel!
    @IBOutlet weak var referralHistory: UILabel!
    @IBOutlet weak var referralCode: UILabel!
    @IBOutlet weak var copyImage: UIImageView!
    @IBOutlet weak var referralLives: UILabel!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        copyImage.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTappedCopyImage)))
    }
    
    @objc func didTappedCopyImage() {
        guard let referralCode = referralCode.text else {
            return
        }
        UIPasteboard.general.string = referralCode
    }
}

extension ReferralsCollectionViewCell: ReferralsCollectionViewCellProtocol {
    func setReferalData(referal: Referral) {
        referralName.text = referal.name
        referralHistory.text = referal.createdAt
        referralCode.text = referal.referral
//        referralLives.text = "Lives".localized
    }
}
