//
//  GameHistoryCollectionViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 11/01/2023.
//

import UIKit
import SDWebImage

class GameHistoryCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var gameImage: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet weak var userAvatar: UIImageView!
    @IBOutlet weak var userName: UILabel!
    @IBOutlet weak var opponentAvatar: UIImageView!
    @IBOutlet weak var opponentName: UILabel!
    @IBOutlet weak var battleStatus: UILabel!
    @IBOutlet weak var battleHistory: UILabel!
    
    
}

extension GameHistoryCollectionViewCell: GameHistoryCollectionViewCellProtocol {
    func setGameHistoryData(game: GameHistoryData) {
        if let gameImage = game.gameImage {
            if let url = URL(string: gameImage[0]) {
                self.gameImage.sd_setImage(with: url, placeholderImage: #imageLiteral(resourceName: "avatar"), options: .continueInBackground, context: nil)
            }
        }
        
        self.gameName.text = game.gameName

        if let userAvatar = game.playerOneImage {
            if let url = URL(string: userAvatar) {
                self.userAvatar.sd_setImage(with: url, placeholderImage: #imageLiteral(resourceName: "avatar"), options: .continueInBackground, context: nil)
            }
        }
        
        self.userName.text = game.playerOneName
        
        if let opponentAvatar = game.playerTwoImage {
            if let url = URL(string: opponentAvatar) {
                self.opponentAvatar.sd_setImage(with: url, placeholderImage: #imageLiteral(resourceName: "avatar"), options: .continueInBackground, context: nil)
            }
        }

        self.opponentName.text = game.playerTwoName
        
        if game.winner ?? false {
            self.battleStatus.text = "You won".localized
        } else {
            self.battleStatus.text = "You lost".localized
        }
        
        self.battleHistory.text = game.createdAt?.getDateString(currentFormate:"yyyy-MM-dd'T'HH:mm:ss.SSSZ", expectedFormate: "yyy-MM-dd")
        
//        let history = game.createdAt?.getDate(currentFormate: "yyyy-MM-ddTHH:MM:SSZ", from: "en") ?? Date()
//
//        let date = Date()
//
//        let ss = history - date
        
    }
}
