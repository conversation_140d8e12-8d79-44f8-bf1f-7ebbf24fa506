//
//  NewProfileInteractor.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import Foundation

class NewProfileInteractor {
    var presenter: NewProfileInteractorOutPutProtocol?
    
    var userWorker: UserWorkerProtocol?
    var walletWorker:WalletWorker?
    var error: ErrorProtocol?
}

extension NewProfileInteractor: NewProfileInteractorInPutProtocol {
    
    func getProfile() {
        userWorker?.get(ID: UserModel.shared.get_id(), compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()

                }else if statusCode == 200 {
                    if model.status ?? false{
                        self.presenter?.FeatchUserDataSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.error ?? "")
                    }
                }
                break

            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func update(id: String, user: ProfileRequestModel) {
        userWorker?.update(ID: id, model: user, compilition: { [weak self] result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()

                }else if statusCode == 200 {
                    if model.status ?? false{
                        self.presenter?.getUserSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.error ?? "")
                    }
                }else{
                    self.error?.featching(error: model.error ?? "")
                }
                break

            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func setUserinFirebase(userID: String, model: UserChatModel) {
        userWorker?.uploadChatUser(userID: userID, model: model, compilition: { (Status, Error) in
            if let error = Error{
                print(error)
            }else{
                print("Success")
            }
        })
    }
    
    func getReferrals(page:String) {
        userWorker?.getReferrals(page: page , compelition: { [weak self] result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()
                    
                }else if statusCode == 200 {
                    if let data = model.data?.data {
                        if data.isEmpty {
                            self.error?.featching(error: "There're no data".localized)
                        } else {
                            if model.status ?? false{
                                self.presenter?.fetchReferralSuccessfully(model: model)
                            }else{
                                self.error?.featching(error: model.message ?? "")
                            }

                        }
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func getGameHistory() {
        userWorker?.getGameHistory(compelition: { [weak self] result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()
                    
                }else if statusCode == 200 {
                    if model.status == 200 {
                        if ((model.data?.isEmpty) != nil) {
                            self.presenter?.fetchGameHistorySuccessfully(model: model)
                        } else {
                            self.error?.featching(error: model.message ?? "")
                        }
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    //MARK: - get Wallet conins info
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.getWalletInfo(obj: model)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
}
