//
//  NewProfileRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import Foundation

class NewProfileRouter: NewProfileRouterProtocol ,CreatedAvatarSuccessfullyProtocol{
    weak var viewController: UIViewController?
    var vc:NewProfileViewProtocol?
    
    static func createModule(status:StatusProfile) -> UIViewController {
        let interactor = NewProfileInteractor()
        let router = NewProfileRouter()
        let view = SetStoryBoard.controller(controller: Helper(Story: .NewProfileST, VC: .NewProfileVC)) as! NewProfileVC
        let presenter = NewProfilePresenter(view: view, interactor: interactor, router: router, error: view)
        let userWorker = UserWorker()
        let workerWallet = WalletWorker()
        view.presenter = presenter
        view.status = status
        interactor.presenter = presenter
        interactor.userWorker = userWorker
        interactor.error = presenter
        interactor.walletWorker = workerWallet
        router.viewController = view
        return view
    }

    func popViewController() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                return
            }
            self.viewController?.navigationController?.popViewController(animated: true)
        }
    }
    
    func openListAvatar(){
//        let avatar = SelectAvatarRouter.createModule(type: .profile) as! SelectAvatarVC
//        if let vc = viewController{
//            vc.navigationController?.pushViewController(avatar, animated: true)
//        }
        
        let avatar = CreateAvatarWebViewRouter.createModule(type: .profile) as! CreateAvatarWebViewVC
        if let vc = viewController{
            avatar.action = self
            vc.navigationController?.pushViewController(avatar, animated: true)
        }
    }
    
    func ReloadUserInfo() {
        self.vc?.updateUInfo()
    }
}
