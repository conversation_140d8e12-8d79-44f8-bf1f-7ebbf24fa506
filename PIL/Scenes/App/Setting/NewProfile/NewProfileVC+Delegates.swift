//
//  NewProfileVC+Delegates.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import Foundation
import UIKit
import SCSDKLoginKit

extension NewProfileVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        switch flag {
        case 0:
//            return presenter.getInfoCount()
            return 1
        case 1:
            return presenter.getGameHistoryCount()
        case 2:
            return presenter.getReferralsCount()
        default:
            return 0
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        switch flag {
        case 0:
//            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "InfoCollectionViewCell", for: indexPath) as? InfoCollectionViewCell else {
//                return UICollectionViewCell()
//            }
//            presenter.configure(cell: cell, of: indexPath.row)
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "InfoCollectionCell", for: indexPath) as? InfoCollectionCell else {
                return UICollectionViewCell()
            }
//            cell.showDatePicker()
            cell.onTapSave = { [weak self] name, userName, email, birthdate, gender, bio , referral , phone in
                guard let self = self else { return }
                if userName != "" && email != "" /*&& birthdate != "" && gender != "" */{
                    self.presenter.saveNewUserData(image: self.imageSnap , name: name, userName: userName, email: email, birthDate: birthdate, gender: gender, bio: bio, referral: referral, phone: phone,externalIdSnap: self.externalIDSnap)
                }
            }
            
            cell.onTapApple = {
                self.presenter.linkWithApple()
            }
            
            cell.onTapGoogle = {
                self.presenter.linkWithGoogle()
            }
            
            cell.onTapSnap = {
                self.onTapSnap()
            }
            
            return cell
        case 1:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GameHistoryCollectionViewCell", for: indexPath) as? GameHistoryCollectionViewCell else {
                return UICollectionViewCell()
            }
            presenter.configure(cell: cell, of: indexPath.row)
            return cell
        case 2:
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ReferralsCollectionViewCell", for: indexPath) as? ReferralsCollectionViewCell else {
                return UICollectionViewCell()
            }
            presenter.configure(cell: cell, of: indexPath.row)
            return cell
        default:
            return UICollectionViewCell()
        }
        
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        switch flag {
        case 0:
            return CGSize(width: collectionView.frame.width, height: 800)
        case 1:
            return CGSize(width: collectionView.frame.width, height: 120)
        case 2:
            return CGSize(width: collectionView.frame.width, height: 131)
        default:
            return CGSize()
        }
    }
    
    
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
       let height = scrollView.frame.size.height
       let contentYoffset = scrollView.contentOffset.y
       let distanceFromBottom = scrollView.contentSize.height - contentYoffset
        
        switch flag {
        case 1:
            if distanceFromBottom < height && !(presenter?.startNewPageeReferral ?? false) {
                presenter?.getNextpageReferral()
           }
            break
        case 2:
            if distanceFromBottom < height && !(presenter?.startNewPageeReferral ?? false) {
                presenter?.getNextpageReferral()
           }
            break
        default:
            print("CCCC")
            break
            
        }
       
   }
    
    
    func onTapSnap(){
        SCSDKLoginClient.login(from: self, completion:  { success, error in
//            print("_____> Error 1",error.localizedDescription)
            
            guard success , error == nil else {return}
            let query = SCSDKUserDataQueryBuilder().withExternalId().withDisplayName().withBitmojiTwoDAvatarUrl().build()
            
            SCSDKLoginClient.fetchUserData(with: query) { userdata , error in
                let displayName = userdata?.displayName ?? ""
                let bitmojiAvatarURL = userdata?.bitmojiTwoDAvatarUrl ?? ""
                let externalID = userdata?.externalID ?? ""

                print("XXXXXX",displayName , bitmojiAvatarURL , externalID)
                DispatchQueue.main.async {
//                    self.username.text = displayName
                    self.avatarImage.sd_setImage(with: URL(string: bitmojiAvatarURL))
                    self.imageSnap = bitmojiAvatarURL
                    self.externalIDSnap = externalID
                    
                    self.linkBitmojiIMG.image = UIImage(named: "P_unlink")
                    self.linkBitmojiLable.text = "Unlink Bitmoji"
                }
              
            } failure:{ error, _ in
                // Handle error
                print("_____> Error 2",error?.localizedDescription)
            }
            //
        })
    }
}
