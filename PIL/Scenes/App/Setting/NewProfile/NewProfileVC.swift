//
//  NewProfileVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import UIKit
import SDWebImage
import SCSDKLoginKit

enum StatusProfile{
    case home
    case menu
}
class NewProfileVC: UIViewController {
    
    var presenter: NewProfilePresenterProtocol!
    
    let yourAttributes: [NSAttributedString.Key: Any] = [
        .font: UIFont.boldSystemFont(ofSize: 14),
        .foregroundColor: UIColor.white,
        .underlineStyle: NSUnderlineStyle.single.rawValue
    ]
    var flag = 0
    var status:StatusProfile?
    var imageSnap = String()
    var externalIDSnap = String()
    @IBOutlet weak var avatarImage: UIImageView!
    @IBOutlet weak var userName: UILabel!
    @IBOutlet weak var userId: UILabel!
    @IBOutlet weak var infoBTN: UIButton!
    @IBOutlet weak var gameHistoryBTN: UIButton!
    @IBOutlet weak var referBTN: UIButton!
    @IBOutlet weak var profileCollection: UICollectionView!
    @IBOutlet weak var saveView: UIView!
    @IBOutlet weak var navigation: NavigationView!
    @IBOutlet weak var stackTaps: UIStackView!
    @IBOutlet weak var snapBu: UIButton!
    @IBOutlet weak var linkBitmojiLable: UILabel!
    @IBOutlet weak var linkBitmojiIMG: UIImageView!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        navigation = navigation.loadNib() as? NavigationView
        navigation.vc = self
        navigation.setNavigationTitle(title: "Edit Profile".localized)
        navigation.selectedAction(actions: [.back])
        
        setUpCollection()
        UI()
        
        
        if SCSDKLoginClient.isUserLoggedIn {
            self.linkBitmojiIMG.image = UIImage(named: "P_unlink")
            self.linkBitmojiLable.text = "Unlink Bitmoji"
        }else{
            self.linkBitmojiIMG.image = UIImage(named: "P_link")
            self.linkBitmojiLable.text = "Link Bitmoji"
        }
        
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter.viewDidLoad()
        setUserData()
        self.tabBarController?.tabBar.isHidden = false
        
    }
    
    func setUpCollection(){
        profileCollection.register(UINib(nibName: "InfoCollectionCell", bundle: Bundle.main), forCellWithReuseIdentifier: "InfoCollectionCell")
    }
    
    func UI() {
        
        avatarImage.backgroundColor = #colorLiteral(red: 1, green: 1, blue: 1, alpha: 0.2)
        
        let attributeString = NSMutableAttributedString(
            string: "Info".localized,
            attributes: yourAttributes
        )
        infoBTN.setAttributedTitle(attributeString, for: .normal)
    }
    
    func setUserData() {
        print("url image",UserModel.shared.get_avatar())
        self.avatarImage.sd_setImage(with: URL(string: UserModel.shared.get_avatar()))
        userName.text = UserModel.shared.get_name()
        userId.text = "ID: \(UserModel.shared.get_id())"
    }
    
    func removeAttributedLine(string: String) -> NSMutableAttributedString {
        let attributeString: NSMutableAttributedString =  NSMutableAttributedString(string: string)
        attributeString.removeAttribute(NSAttributedString.Key.strikethroughStyle, range: NSMakeRange(0, attributeString.length))
        return attributeString
    }
    
    @IBAction func linkSnapStatusButton(_ sender: Any) {
        if SCSDKLoginClient.isUserLoggedIn {
            SCSDKLoginClient.clearToken()
            self.linkBitmojiIMG.image = UIImage(named: "P_link")
            self.linkBitmojiLable.text = "Link Bitmoji"
            
            print("123444unlink is clearToken")

        }else{
            print("123444Link with snap")
            self.onTapSnap()
        }
        
    }
    
    @IBAction func cameraAction(_ sender: UIButton) {
        
    }
    
    @IBAction func snapAction(_ sender: Any) {

    }
    
    
    @IBAction func infoBTN(_ sender: UIButton) {
        flag = 0
        saveView.isHidden = false
        gameHistoryBTN.setAttributedTitle(removeAttributedLine(string: gameHistoryBTN.titleLabel?.text ?? ""), for: .normal)
        referBTN.setAttributedTitle(removeAttributedLine(string: referBTN.titleLabel?.text ?? ""), for: .normal)
        
        let attributeString = NSMutableAttributedString(
            string: "Info".localized,
            attributes: yourAttributes
        )
        infoBTN.setAttributedTitle(attributeString, for: .normal)
        
        profileCollection.register(UINib(nibName: "InfoCollectionCell", bundle: Bundle.main), forCellWithReuseIdentifier: "InfoCollectionCell")

        profileCollection.isHidden = false
        profileCollection.reloadData()
    }
    @IBAction func gameHistoryBTN(_ sender: UIButton) {
        flag = 1
        saveView.isHidden = true
        referBTN.setAttributedTitle(removeAttributedLine(string: referBTN.titleLabel?.text ?? ""), for: .normal)
        infoBTN.setAttributedTitle(removeAttributedLine(string: infoBTN.titleLabel?.text ?? ""), for: .normal)
        
        let attributeString = NSMutableAttributedString(
            string: "Game History".localized,
            attributes: yourAttributes
        )
        gameHistoryBTN.setAttributedTitle(attributeString, for: .normal)
        profileCollection.register(UINib(nibName: "GameHistoryCollectionViewCell", bundle: Bundle.main), forCellWithReuseIdentifier: "GameHistoryCollectionViewCell")
        self.presenter.initiateTabs()
        presenter.getGameHistoryData()
    }
    
    @IBAction func referBTN(_ sender: UIButton) {
        flag = 2
        saveView.isHidden = true
        infoBTN.setAttributedTitle(removeAttributedLine(string: infoBTN.titleLabel?.text ?? ""), for: .normal)
        gameHistoryBTN.setAttributedTitle(removeAttributedLine(string: gameHistoryBTN.titleLabel?.text ?? ""), for: .normal)
        
        let attributeString = NSMutableAttributedString(
            string: "Referrals".localized,
            attributes: yourAttributes
        )
        referBTN.setAttributedTitle(attributeString, for: .normal)
        profileCollection.register(UINib(nibName: "ReferralsCollectionViewCell", bundle: Bundle.main), forCellWithReuseIdentifier: "ReferralsCollectionViewCell")
        self.presenter.initiateTabs()
        presenter.getReferralsData()
    }
    
    
    @IBAction func openListAvatarBTN(_ sender: Any) {
        if SCSDKLoginClient.isUserLoggedIn {
            let vc = UIStoryboard(name: "NewChat", bundle: nil).instantiateViewController(withIdentifier: "ImageFullScreenVC") as! ImageFullScreenVC
            vc.modalPresentationStyle = .fullScreen
            vc.isSnap = true
            vc.imageURL = UserModel.shared.get_image()
            self.present(vc, animated: true)
        }else{
            self.presenter.openListAvatar()

        }
    }
    
    @IBAction func saveAction(_ sender: UIButton) {

    }
}

extension NewProfileVC: NewProfileViewProtocol {
    func updateUInfo(){
        self.presenter.viewDidLoad()
    }
    
    func reloadCollectionData() {
        profileCollection.isHidden = false
        profileCollection.reloadData()
    }
    
    func updatedUserDataSuccessfully() {
        userName.text = UserModel.shared.get_username()
        
        showAlert(withTitle: false, msg: "your profile updated Successfully".localized){
            DispatchQueue.main.asyncAfter(deadline: .now()+1.5, execute: {
                self.navigationController?.popViewController(animated: true)
            })
        }
    }
    
    func setHome(lives: Int){
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
        }else{

        }
    }
 
}

extension NewProfileVC: ErrorProtocol {
    func featching(error: String) {
//        profileCollection.isHidden = true
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
//            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
}
