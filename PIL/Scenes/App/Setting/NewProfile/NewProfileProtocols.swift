//
//  NewProfileProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 10/01/2023.
//

import Foundation

protocol NewProfileViewProtocol: AnyObject {
    var presenter: NewProfilePresenterProtocol! { get set }

    func reloadCollectionData()
    func updatedUserDataSuccessfully()
    func setHome(lives: Int)
    func updateUInfo()
    func setUserData()
}

protocol NewProfilePresenterProtocol {
    var view: NewProfileViewProtocol { get set }

    func viewDidLoad()
    func popViewController()
    func getInfoCount() -> Int
    func configure(cell: InfoCollectionViewCellProtocol, of index: Int)
    func getGameHistoryData()
    func getGameHistoryCount() -> Int
    func configure(cell: GameHistoryCollectionViewCellProtocol, of index: Int)
    func getReferralsData()
    func getReferralsCount() -> Int
    func configure(cell: ReferralsCollectionViewCellProtocol, of index: Int)
    func saveNewUserData(image:String ,name: String, userName: String, email: String, birthDate: String, gender: String, bio: String , referral : String, phone : String , externalIdSnap:String)
    func openListAvatar()
    func getWalletInfo(obj: WalletWorkerData)

    func getNextpageReferral()
    var startNewPageeReferral: Bool? {get set}
    var startNewPageeGame: Bool?{ get set}

    func initiateTabs()
    func linkWithGoogle()
    func linkWithApple()
    
}

protocol NewProfileRouterProtocol {
    func popViewController()
    func openListAvatar()
}

protocol NewProfileInteractorInPutProtocol {
    var presenter: NewProfileInteractorOutPutProtocol? { get set }

    func getProfile()
    func update(id: String, user: ProfileRequestModel)
    func setUserinFirebase(userID: String, model: UserChatModel)
    func getReferrals(page:String)
    func getGameHistory()
    func sendWalletRequest()
}

protocol NewProfileInteractorOutPutProtocol: AnyObject {
    func getUserSuccessfully(model: UserProfileModel)
    func FeatchUserDataSuccessfully(model: UserProfileModel)
    func fetchReferralSuccessfully(model: Referrals)
    func fetchGameHistorySuccessfully(model: GameHistory)
    func getWalletInfo(obj:WalletWorkerData)

}

protocol InfoCollectionViewCellProtocol {
    func setUserData(user: InfoEntity)
    func EditBTN(hidden: Bool)
}

protocol GameHistoryCollectionViewCellProtocol {
    func setGameHistoryData(game: GameHistoryData)
}

protocol ReferralsCollectionViewCellProtocol {
    func setReferalData(referal: Referral)
}
