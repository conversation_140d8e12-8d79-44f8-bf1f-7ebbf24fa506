//
//  NewProfileEntity.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 11/01/2023.
//

import Foundation

struct InfoEntity {
    let infoLabel: String
    let info: String
}

struct Referrals: Codable {
    let status: Bool?
    let message: String?
    let data: ReferalsData?
}

// MARK: - DataClass
struct ReferalsData: Codable {
    let currentPage: Int?
    let data: [Referral]?
    let firstPageURL: String?
    let from, lastPage: Int?
    let lastPageURL, nextPageURL, path: String?
    let perPage: Int?
    let prevPageURL: String?
    let to, total: Int?
    
    enum CodingKeys: String, CodingKey {
        case currentPage = "current_page"
        case data
        case firstPageURL = "first_page_url"
        case from
        case lastPage = "last_page"
        case lastPageURL = "last_page_url"
        case nextPageURL = "next_page_url"
        case path
        case perPage = "per_page"
        case prevPageURL = "prev_page_url"
        case to, total
    }
}

// MARK: - Datum
struct Referral: Codable {
    let id: Int?
    let name, email, phone: String?
    let verified: Int?
    let gender: String?
    let image: String?
    let referral: String?
    let bio: String?
    let birthdate: String?
    let bot: Int?
    let deleted: Int?
    let helpdeskID, status, group, datumOperator: String?
    let operatorID: Int?
    let username, pilID, delayed, createdAt: String?
    let updatedAt: String?
    let dailyUpdated: String?
    let avatarID: Int?
    let watchedTutorial, type: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, email, phone, verified, gender, image, referral, bio, birthdate, bot, deleted
        case helpdeskID = "helpdeskId"
        case status, group
        case datumOperator = "operator"
        case operatorID = "operator_id"
        case username
        case pilID = "pil_id"
        case delayed
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case dailyUpdated = "daily_updated"
        case avatarID = "avatar_id"
        case watchedTutorial = "watched_tutorial"
        case type
    }
}

struct GameHistory: Codable {
    
    let status: Int?
    let message: String?
    let data: [GameHistoryData]?
}

// MARK: - Datum
struct GameHistoryData: Codable {
    let id: Int?
    let playerOneID, playerTwoID: String?
    let p1Score, p2Score: Int?
    let winner: Bool?
    let gameName: String?
    let gameImage: [String]?
    let gameResultID: Int?
    let createdAt, updatedAt: String?
    let playerOneName: String?
    let playerOneImage: String?
    let playerTwoName: String?
    let playerTwoImage: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case playerOneID = "playerOneId"
        case playerTwoID = "playerTwoId"
        case p1Score, p2Score, winner, gameName, gameImage
        case gameResultID = "gameResultId"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case playerOneName, playerOneImage, playerTwoName, playerTwoImage
    }
}
