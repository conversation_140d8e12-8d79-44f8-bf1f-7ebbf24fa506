//
//  BlockListVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/08/2023.
//

import UIKit

class BlockListVC: UIViewController {

    @IBOutlet weak var blockedTableView: UITableView!
    @IBOutlet weak var navigationView: NavigationView!

    var blockList: [ActionRecord] = []
    var tempBlockList: [ActionRecord] = []
   
    override func viewDidLoad() {
        super.viewDidLoad()
        blockedTableView.register(BlockedUserTableViewCell.nib, forCellReuseIdentifier: BlockedUserTableViewCell.identifier)
        fetchBlockList()
        setUpNavigation()
    }
    
    
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Block list".localized)
    }
    
    func unblockUser(_ id: Int){
        Indicator.shared.showProgressView()
        let payload: Payloads.UserActionRecordPayload = .init(createdBy: UserModel.shared.get_id(),
                                                                 userActionRecord: .init(objectID: Lookups.Objects.user.rawValue,
                                                                                         targetObjectID: id,
                                                                                         actionID: Lookups.Actions.block.rawValue))
        SocialWorker.shared.addUserAction(payload){ [self] (result, statusCode) in
            Indicator.shared.hideProgressView()
            switch result{
            case .success(let response):
                if 200...299 ~= response.statusCode,
                   response.success{
                    self.fetchBlockList()
                }else{
                    self.showAlert(withTitle: false, msg: response.message ?? "", compilition: nil)
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.showAlert(withTitle: false, msg: localizedError, compilition: nil)
                } sessionExpired: {
                    self.loginAlert(compilition: {
                        UserModel.shared.logOut()
                    })
                } noInternet: {
                   
                }
            }
        }
    }
    
    func fetchBlockList(){
        let payload: Payloads.UserActionRecordPayload = .init(createdBy: UserModel.shared.get_id(),
                                                                 userActionRecord: .init(objectID: Lookups.Objects.user.rawValue,
                                                                                         actionID: Lookups.Actions.block.rawValue))
        SocialWorker.shared.listUserAction(payload){ [self] (result, statusCode) in
            switch result{
            case .success(let response):
                if 200...299 ~= response.statusCode,
                   response.success{
                    self.blockList = response.userActionRecords ?? []
                    self.tempBlockList = self.blockList
                    self.blockedTableView.reloadData()
                }else{
                    self.showAlert(withTitle: false, msg: response.message ?? "", compilition: nil)
                }
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.showAlert(withTitle: false, msg: localizedError, compilition: nil)
                } sessionExpired: {
                    self.loginAlert(compilition: {
                        UserModel.shared.logOut()
                    })
                } noInternet: {
                   
                }
            }
        }
    }
    
    @IBAction func searchTFAction(_ sender: UITextField) {
        guard sender.text != "People".localized else{
            endSearch()
            return
        }
        guard !sender.text.isNilOrEmpty else{
            endSearch()
            return
        }
        self.blockList = blockList.filter({ return $0.targetUserName!.lowercased().contains(sender.text!.lowercased()) || "\($0.targetObjectID ?? 0)".contains(sender.text!.lowercased()) })
        self.blockedTableView.reloadData()
    }
    
    func endSearch(){
        self.blockList = tempBlockList
        self.blockedTableView.reloadData()
    }
}

extension BlockListVC: UITextFieldDelegate{
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField.text == "People".localized{
            textField.text = ""
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.text.isNilOrEmpty{
            textField.text = "People".localized
            endSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField.text.isNilOrEmpty{
            textField.text = "People".localized
            endSearch()
        }
        view.endEditing(true)
        return true
    }
}

extension BlockListVC: UITableViewDelegate, UITableViewDataSource{
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return blockList.count
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: BlockedUserTableViewCell.identifier, for: indexPath) as! BlockedUserTableViewCell
        let record = blockList[indexPath.row]
        cell.record = record
        cell.loadUI()
        cell.unblockBtn.tag = indexPath.row
        cell.unblockBtn.addTarget(self, action: #selector(self.unblockAction(sender:)), for: .touchUpInside)
        return cell
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 100
    }
    
    @objc func unblockAction(sender: UIButton){
        self.unblockUser(blockList[sender.tag].targetObjectID!)
    }
}
