//
//  BlockedUserTableViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 08/08/2023.
//

import UIKit

class BlockedUserTableViewCell: UITableViewCell {
    
    static let identifier = "BlockedUserTableViewCell"
    static let nib = UINib.init(nibName: "BlockedUserTableViewCell", bundle: nil)

    @IBOutlet weak var userImage: UIImageView!
    @IBOutlet weak var username: U<PERSON>abe<PERSON>!
    @IBOutlet weak var unblockBtn: UIButton!
    
    var record: ActionRecord?
    
    func loadUI(){
        userImage.sd_setImage(with: .init(string: record?.targetUserImage ?? ""))
        username.text = record?.targetUserName
    }
    
}
