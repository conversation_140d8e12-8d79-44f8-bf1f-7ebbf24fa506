<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="BlockListVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="blockedTableView" destination="8Fs-Fk-M6G" id="xic-V7-2Oc"/>
                <outlet property="navigationView" destination="3dg-1U-78B" id="f3P-2r-fZv"/>
                <outlet property="view" destination="HkW-bu-GEV" id="XkK-LL-jLa"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="HkW-bu-GEV">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aBy-y2-nFd" customClass="MainBackgroundGradientView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3dg-1U-78B" customClass="NavigationView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="120"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="120" id="j6G-XW-7UE"/>
                    </constraints>
                </view>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="mk1-yr-0Pm">
                    <rect key="frame" x="7" y="127" width="379" height="684"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HOr-Uo-I8N">
                            <rect key="frame" x="0.0" y="0.0" width="379" height="40"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="7M3-Ct-Lmt">
                                    <rect key="frame" x="10" y="5" width="359" height="30"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Bzo-wY-Umw">
                                            <rect key="frame" x="0.0" y="0.0" width="20.333333333333332" height="30"/>
                                            <color key="tintColor" name="Orange Primary Color"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" image="magnifyingglass" catalog="system"/>
                                        </button>
                                        <textField opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="248" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="People" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="s7E-yi-rc1">
                                            <rect key="frame" x="30.333333333333343" y="0.0" width="328.66666666666663" height="30"/>
                                            <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <connections>
                                                <action selector="searchTFAction:" destination="-1" eventType="editingChanged" id="jPZ-aZ-toi"/>
                                                <outlet property="delegate" destination="-1" id="XTo-qZ-dgx"/>
                                            </connections>
                                        </textField>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.5" colorSpace="custom" customColorSpace="sRGB"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="5OQ-GJ-CX3"/>
                                <constraint firstAttribute="bottom" secondItem="7M3-Ct-Lmt" secondAttribute="bottom" constant="5" id="7pu-vH-iII"/>
                                <constraint firstItem="7M3-Ct-Lmt" firstAttribute="top" secondItem="HOr-Uo-I8N" secondAttribute="top" constant="5" id="Ken-NS-hTG"/>
                                <constraint firstItem="7M3-Ct-Lmt" firstAttribute="leading" secondItem="HOr-Uo-I8N" secondAttribute="leading" constant="10" id="MI3-cR-9pH"/>
                                <constraint firstAttribute="trailing" secondItem="7M3-Ct-Lmt" secondAttribute="trailing" constant="10" id="O3o-Ns-bDM"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="20"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                    <color key="value" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                    <real key="value" value="1"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="8Fs-Fk-M6G">
                            <rect key="frame" x="0.0" y="50" width="379" height="634"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <connections>
                                <outlet property="dataSource" destination="-1" id="HxZ-bq-nb3"/>
                                <outlet property="delegate" destination="-1" id="jwS-qK-Bot"/>
                            </connections>
                        </tableView>
                    </subviews>
                    <constraints>
                        <constraint firstItem="HOr-Uo-I8N" firstAttribute="leading" secondItem="mk1-yr-0Pm" secondAttribute="leading" id="Vig-Uh-TJN"/>
                        <constraint firstAttribute="trailing" secondItem="HOr-Uo-I8N" secondAttribute="trailing" id="oRu-eA-1V7"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="RLE-ST-Nko"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="aBy-y2-nFd" firstAttribute="top" secondItem="HkW-bu-GEV" secondAttribute="top" id="1ey-1Q-IIL"/>
                <constraint firstItem="RLE-ST-Nko" firstAttribute="bottom" secondItem="mk1-yr-0Pm" secondAttribute="bottom" constant="7" id="7oQ-Fz-PqQ"/>
                <constraint firstItem="3dg-1U-78B" firstAttribute="top" secondItem="HkW-bu-GEV" secondAttribute="top" id="8Sy-3f-dWt"/>
                <constraint firstItem="aBy-y2-nFd" firstAttribute="leading" secondItem="RLE-ST-Nko" secondAttribute="leading" id="B8u-29-yQt"/>
                <constraint firstItem="RLE-ST-Nko" firstAttribute="trailing" secondItem="mk1-yr-0Pm" secondAttribute="trailing" constant="7" id="Kep-9S-odI"/>
                <constraint firstAttribute="bottom" secondItem="aBy-y2-nFd" secondAttribute="bottom" id="Ote-Ah-0c7"/>
                <constraint firstItem="3dg-1U-78B" firstAttribute="leading" secondItem="RLE-ST-Nko" secondAttribute="leading" id="aep-D4-lPk"/>
                <constraint firstItem="RLE-ST-Nko" firstAttribute="trailing" secondItem="aBy-y2-nFd" secondAttribute="trailing" id="cpV-Xh-w04"/>
                <constraint firstAttribute="trailing" secondItem="3dg-1U-78B" secondAttribute="trailing" id="fcM-W6-0Ic"/>
                <constraint firstItem="mk1-yr-0Pm" firstAttribute="leading" secondItem="RLE-ST-Nko" secondAttribute="leading" constant="7" id="goJ-Fc-mFV"/>
                <constraint firstItem="mk1-yr-0Pm" firstAttribute="top" secondItem="3dg-1U-78B" secondAttribute="bottom" constant="7" id="uTV-Fb-xRz"/>
            </constraints>
            <point key="canvasLocation" x="3.8167938931297707" y="19.718309859154932"/>
        </view>
    </objects>
    <resources>
        <image name="magnifyingglass" catalog="system" width="128" height="117"/>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
