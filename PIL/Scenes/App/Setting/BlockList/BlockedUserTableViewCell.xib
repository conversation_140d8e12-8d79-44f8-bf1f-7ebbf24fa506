<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="BlockedUserTableViewCell" rowHeight="139" id="KGk-i7-Jjw" customClass="BlockedUserTableViewCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="388" height="139"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="388" height="139"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Username" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bdf-r4-rVW">
                        <rect key="frame" x="105" y="59.666666666666657" width="151" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="c7c-vj-yDK"/>
                        </constraints>
                        <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                        <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="R5s-km-8WG">
                        <rect key="frame" x="15" y="29.666666666666671" width="80" height="80"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Circle Leader" translatesAutoresizingMaskIntoConstraints="NO" id="45N-J5-qAr">
                                <rect key="frame" x="0.0" y="0.0" width="80" height="80"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar2" translatesAutoresizingMaskIntoConstraints="NO" id="uEW-PQ-nwm">
                                <rect key="frame" x="10" y="0.0" width="60" height="65"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                        <real key="value" value="37.5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="45N-J5-qAr" secondAttribute="trailing" id="Ekl-1V-Wpv"/>
                            <constraint firstAttribute="bottom" secondItem="uEW-PQ-nwm" secondAttribute="bottom" constant="15" id="RJb-SO-qWk"/>
                            <constraint firstAttribute="height" constant="80" id="SJO-vS-gwO"/>
                            <constraint firstAttribute="bottom" secondItem="45N-J5-qAr" secondAttribute="bottom" id="XsW-qF-xrw"/>
                            <constraint firstItem="45N-J5-qAr" firstAttribute="top" secondItem="R5s-km-8WG" secondAttribute="top" id="dee-Hg-jVD"/>
                            <constraint firstItem="uEW-PQ-nwm" firstAttribute="top" secondItem="R5s-km-8WG" secondAttribute="top" id="hP4-aa-f7m"/>
                            <constraint firstItem="uEW-PQ-nwm" firstAttribute="leading" secondItem="R5s-km-8WG" secondAttribute="leading" constant="10" id="uIE-fY-f3D"/>
                            <constraint firstItem="45N-J5-qAr" firstAttribute="leading" secondItem="R5s-km-8WG" secondAttribute="leading" id="vpU-3U-Fz8"/>
                            <constraint firstAttribute="trailing" secondItem="uEW-PQ-nwm" secondAttribute="trailing" constant="10" id="waK-rU-blq"/>
                            <constraint firstAttribute="width" constant="80" id="yqK-Cz-AYH"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="30"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="OoH-6a-GWH">
                        <rect key="frame" x="263" y="52" width="110" height="35"/>
                        <color key="backgroundColor" name="Orange Primary Color"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="35" id="FaN-GL-DbU"/>
                            <constraint firstAttribute="width" constant="110" id="fZs-EN-kGc"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                        <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                        <state key="normal" title="Unblock">
                            <color key="titleColor" name="White"/>
                        </state>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="buttonRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColorButton">
                                <color key="value" name="Orange"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidthButton">
                                <real key="value" value="1"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </button>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Line setting" translatesAutoresizingMaskIntoConstraints="NO" id="TIK-tD-Lfs">
                        <rect key="frame" x="20" y="135" width="348" height="1"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="01B-AA-6pu"/>
                        </constraints>
                    </imageView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="OoH-6a-GWH" firstAttribute="leading" secondItem="bdf-r4-rVW" secondAttribute="trailing" constant="7" id="3RJ-WE-DxV"/>
                    <constraint firstItem="R5s-km-8WG" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="KUx-yR-EBI"/>
                    <constraint firstItem="OoH-6a-GWH" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="OrG-V5-wTN"/>
                    <constraint firstAttribute="trailing" secondItem="TIK-tD-Lfs" secondAttribute="trailing" constant="20" id="Paw-48-cSw"/>
                    <constraint firstItem="OoH-6a-GWH" firstAttribute="leading" secondItem="bdf-r4-rVW" secondAttribute="trailing" constant="7" id="VFo-mI-E2A"/>
                    <constraint firstItem="TIK-tD-Lfs" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="Xb9-tb-zg7"/>
                    <constraint firstAttribute="trailing" secondItem="OoH-6a-GWH" secondAttribute="trailing" constant="15" id="Yaa-k6-0aM"/>
                    <constraint firstItem="bdf-r4-rVW" firstAttribute="leading" secondItem="R5s-km-8WG" secondAttribute="trailing" constant="10" id="bdb-6y-phu"/>
                    <constraint firstItem="bdf-r4-rVW" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="ebB-ua-fgu"/>
                    <constraint firstItem="R5s-km-8WG" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="15" id="mii-o5-6f1"/>
                    <constraint firstAttribute="bottom" secondItem="TIK-tD-Lfs" secondAttribute="bottom" constant="3" id="omI-rW-cVM"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="unblockBtn" destination="OoH-6a-GWH" id="hqi-bk-SOe"/>
                <outlet property="userImage" destination="uEW-PQ-nwm" id="1k5-rw-i8c"/>
                <outlet property="username" destination="bdf-r4-rVW" id="iac-1u-5I2"/>
            </connections>
            <point key="canvasLocation" x="190.83969465648855" y="53.87323943661972"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="Circle Leader" width="141.66667175292969" height="141.66667175292969"/>
        <image name="Line setting" width="327" height="1"/>
        <image name="avatar2" width="800" height="800"/>
        <namedColor name="Orange">
            <color red="0.90200001001358032" green="0.60000002384185791" blue="0.10999999940395355" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="White">
            <color red="0.93699997663497925" green="0.92500001192092896" blue="0.95300000905990601" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
