//
//  SubscriptionVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 16/07/2023.
//

import UIKit
import  StoreKit

class SubscriptionVC: UIViewController {
    
    @IBOutlet weak var image: UIImageView!
    @IBOutlet weak var renewal: UILabel!
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var desc: UILabel!

    var subscription: SubscriptionResponse?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        subscription = UserModel.shared.get_userSubscription()
        image.sd_setImage(with: .init(string: (subscription?.subscription?.first?.icon ?? "")!))
        desc.text = subscription?.subscription?.first?.description
        setUpNavigation()
    }
    

    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Subscription".localized)
    }

    @IBAction func cancelSub(){
        if let appStoreURL = URL(string: "https://apps.apple.com/account/subscriptions") {
         UIApplication.shared.open(appStoreURL)
        }
    }
}
