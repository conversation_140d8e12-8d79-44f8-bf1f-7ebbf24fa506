//
//  NotificationsVC + table delegate.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//

import UIKit

extension NotificationsVC: UITableViewDelegate, UITableViewDataSource{
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == 0{
            return 4
        }else{
            return self.presenter?.appNotifications.count ?? 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "NotificationCell", for: indexPath) as! NotificationCell
        switch indexPath.section{
        case 0:
            cell.record = presenter?.socialNotifications
            cell.loadUI(indexPath.row)
        case 1:
            let note = presenter?.appNotifications[indexPath.row]
            cell.loadAppNotificationUI(note?.title ?? "", note?.body ?? "", img: note?.icon ?? "")
        default: break
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch indexPath.section{
        case 0:
            switch indexPath.row{
            case 0:
                let friendsVC = FriendsRouter.createModule(type: .followers, userId: UserModel.shared.get_id(), isNotifications: true)
                self.navigationController?.pushViewController(friendsVC, animated: true)
            case 1:
                let notificationList = NotificationsListVC.loadFromNib()
                notificationList.action = .like
                self.navigationController?.pushViewController(notificationList, animated: true)
            case 2:
                let notificationList = NotificationsListVC.loadFromNib()
                notificationList.action = .share
                self.navigationController?.pushViewController(notificationList, animated: true)
            case 3:
                let notificationList = NotificationsListVC.loadFromNib()
                notificationList.action = .comment
                self.navigationController?.pushViewController(notificationList, animated: true)
            default: break
            }
        case 1:
            let note = presenter?.appNotifications[indexPath.row]
            let action = Lookups.Actions.init(rawValue: note?.actionId ?? 0)
            switch action{
            case .level:
                if self.tabBarController?.selectedIndex == 4{
                    self.navigationController?.popToRootViewController(animated: true)
                }else if let tabBarController = self.view.window!.rootViewController as? UITabBarController {
                    tabBarController.selectedIndex = 4
                }
            case .gift:
                UserModel.shared.didReceiveGiftNotification(true)
                if self.tabBarController?.selectedIndex == 4{
                    self.navigationController?.popToRootViewController(animated: true)
                }else if let tabBarController = self.view.window!.rootViewController as? UITabBarController {
                    tabBarController.selectedIndex = 4
                }
            default: break
            }
        default: break
        }
    }
    
//    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
//        return 100
//    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = UIView.init(frame: .init(x: 0, y: 0, width: tableView.frame.width, height: 0.2))
        view.backgroundColor = .black.withAlphaComponent(0.3)
        let label = UILabel.init(frame: .init(x: 0, y: 0, width: 100, height: 25))
        label.font = UIFont(name: "Poppins-Medium", size: 16)
        label.textColor = UIColor(named: "Black-White")
        if section == 0{
            label.text = "Social".localized
        }else{
            label.text = "App".localized
        }
        let stack: UIStackView = .init(arrangedSubviews: [label, view])
        stack.spacing = 7
        stack.axis = .vertical
        if Locale.preferredLanguages[0] == "ar"{
            stack.contentMode = .right
        }else{
            stack.contentMode = .left
        }
        return stack
    }
}
