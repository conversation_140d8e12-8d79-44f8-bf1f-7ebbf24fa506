//
//  NotificationsRouter.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//  
//

import Foundation
import UIKit

class NotificationsRouter: NotificationsRouterProtocol, ReloadGetLivesAfterWatchAdsProtocol {

    weak var VC: NotificationsViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .NotificationST, VC: .NotificationVC)) as! NotificationsVC
        let interactor = NotificationsInteractor()
        let router = NotificationsRouter()
        let workerWallet = WalletWorker()
        let presenter = NotificationsPresenter(view: view, router: router, interactor: interactor, error: view)
        let notificationWorker = NotificationWorker()
        presenter.view = view
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.notifcationWorker = notificationWorker
        interactor.walletWorker = workerWallet
        router.VC = view
        return view
    }
    
    func toLives() {
        let lives = YouWantMoreLivesRouter.createModule(type: .live) as! YouWantMoreLivesViewController
        lives.action = self
        if let vc = self.VC as? UIViewController{
            vc.present(lives, animated: false) {}
        }
    }
    
    func getLiveAfterAds() {
        VC?.reloadLives()
    }
}
