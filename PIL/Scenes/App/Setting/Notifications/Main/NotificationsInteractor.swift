//
//  NotificationsInteractor.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//  
//

import Foundation

class NotificationsInteractor: NotificationsInteractorInputProtocol {

    //MARK: - variables
    weak var presenter: NotificationsInteractorOutputProtocol?
    var error: ErrorProtocol?
    var notifcationWorker: NotificationWorkerProtocol?
    var walletWorker:WalletWorker?
    
    func getLives() {
        WalletWorker.init().getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403 || statusCode == 401{
                    self.error?.sessionExpired?()
                }
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.didFetchLives(model)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
            }
        })
        
    }
    
    func getSocialNotifications() {
        notifcationWorker?.getSocialNotifications{ [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()
                }else if statusCode == 200 {
                    self.presenter?.fetchedSocialNotificationsSuccessfully(model)
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        }
    }

    func getAppNotification() {
        notifcationWorker?.getAll(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()
                    
                }else if statusCode == 200 {
                    if model.success {
                        self.presenter?.fetchedAppNotificationsSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    
    //MARK: - get Wallet conins info
    func sendWalletRequest() {
        self.walletWorker?.getCoinsInfo(userID: UserModel.shared.get_id(), compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? false{
                    if let model = model.data{
                        self.presenter?.getWalletInfo(obj: model)
                    }
                }
                break
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
}
