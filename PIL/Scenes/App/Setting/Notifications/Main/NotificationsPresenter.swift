//
//  NotificationsPresenter.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//  
//

import Foundation

class NotificationsPresenter: NotificationsPresenterProtocol, NotificationsInteractorOutputProtocol, ErrorProtocol {

    //MARK: - variables
    weak var view: NotificationsViewProtocol?
    var error: ErrorProtocol?
    var router: NotificationsRouterProtocol?
    var interactor: NotificationsInteractorInputProtocol?
    var socialNotifications: SocialNotifications?
    var appNotifications: [NotificationDataModel] = []
    
    //MARK: - init
    init(view: NotificationsViewProtocol,
         router: NotificationsRouterProtocol,
         interactor: NotificationsInteractorInputProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.router = router
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        self.interactor?.sendWalletRequest()
        interactor?.getSocialNotifications()
        interactor?.getAppNotification()
    }
    
    
    // ge user lives count
    func getWalletInfo(obj: WalletWorkerData) {
        UserModel.shared.setCountLives(count: obj.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: obj.playerToken ?? 0)
        view?.updateLives(with: obj.lives ?? 0)
        view?.updateCoins(with: obj.playerToken ?? 0)
    }
    
    
    //MARK: - error deleagte functions
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    func logout(){
        UserModel.shared.logOut()
    }
    
    func onTapLives() {
        router?.toLives()
    }
    
    func reloadLives(){
        
    }
    
    func didFetchLives(_ data: WalletWorkerData) {
        UserModel.shared.setCountLives(count: data.lives ?? 0 )
        UserModel.shared.setPlayerTokens(count: data.playerToken ?? 0)
        view?.updateLives(with: data.lives ?? 0)
        view?.updateCoins(with: data.playerToken ?? 0)
    }
    
    func fetchedSocialNotificationsSuccessfully(_ data: SocialNotifications) {
        self.socialNotifications = data
        self.view?.reloadNotifications()
    }
    
    func fetchedAppNotificationsSuccessfully(model: NotificationModel) {
        self.appNotifications = model.notificationRecords ?? []
        self.reloadLives()
        self.view?.reloadNotifications()
    }
       
    
    /// get the date and time between now and notification Date
    /// ```
    ///examples -> { 11 minutes ago
    ///              11:00 AM
    ///              3 days ago }
    /// ```
    /// - Parameter from: notification Date
    /// - Returns: the formatted Date
    func getFormattedDate(from strDate: String) -> String{
        var shownDateStr = ""
        if let notificationDate = strDate.getDate(currentFormate: "yyyy-MM-dd'T'HH:mm:ss", from: "en"){
            let dateComponents = Calendar.current.dateComponents([.year , .month, .day], from: notificationDate, to: Date())
            
            if (dateComponents.year ?? 0) != 0{
                shownDateStr+="\(dateComponents.year ?? 0) years ago"
            }else if (dateComponents.month ?? 0) != 0{
                shownDateStr+="\(dateComponents.month ?? 0) months ago"
            }else if (dateComponents.day ?? 0) != 0{
                shownDateStr+="\(dateComponents.day ?? 0) days ago"
            }else{
                shownDateStr = notificationDate.TotimeStamp().convertDate(formate: "HH:mm a")
            }
        }
        return shownDateStr
    }
}
