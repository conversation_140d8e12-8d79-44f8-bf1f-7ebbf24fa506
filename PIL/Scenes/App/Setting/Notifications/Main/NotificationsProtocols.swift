//
//  NotificationsProtocols.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//  
//

import Foundation

protocol NotificationsViewProtocol: AnyObject {
    // TODO: Declare view methods
    var presenter: NotificationsPresenterProtocol? { get set }
    func reloadNotifications()
    func updateLives(with lives: Int)
    func updateCoins(with coins: Int)
    func reloadLives()
}

protocol NotificationsPresenterProtocol: AnyObject {
    // TODO: Declare presentation methods
    var view: NotificationsViewProtocol? { get set }
    var socialNotifications: SocialNotifications? { get }
    var appNotifications: [NotificationDataModel] { get } 
    func viewDidLoad()
    func getWalletInfo(obj: WalletWorkerData)
    func logout()
    func onTapLives()
    func reloadLives()
}

protocol NotificationsInteractorInputProtocol {
    var presenter: NotificationsInteractorOutputProtocol? { get set }
    func getSocialNotifications()
    func getAppNotification()
    func sendWalletRequest()
    func getLives()
}

protocol NotificationsInteractorOutputProtocol: AnyObject {
    func fetchedSocialNotificationsSuccessfully(_ data: SocialNotifications)
    func fetchedAppNotificationsSuccessfully(model: NotificationModel)
    func getWalletInfo(obj:WalletWorkerData)
    func didFetchLives(_ data: WalletWorkerData)
}

protocol NotificationsRouterProtocol {
    // TODO: Declare wireframe methods
    func toLives()
}

