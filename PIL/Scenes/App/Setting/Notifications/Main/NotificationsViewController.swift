//
//  NotificationsViewController.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//  
//

import Foundation
import UIKit

class NotificationsVC: UIViewController, NotificationsViewProtocol, ErrorProtocol{

    //MARK: - variables
    var presenter: NotificationsPresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var notificationTableList: UITableView!
    @IBOutlet weak var noNotificationsLable: UILabel!
    
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back])
        self.navigationView.setNavigationTitle(title: "Notifications".localized)
        Style(navigation: false)
        setXIBs()
    }
    
    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
    }

    //MARK: - functions

    
    func setXIBs(){
        notificationTableList.register(UINib(nibName: "NotificationCell", bundle: Bundle.main), forCellReuseIdentifier: "NotificationCell")
        notificationTableList.contentInset = UIEdgeInsets(top: 16, left: 0, bottom: 16, right: 0)
    }
    
    @IBAction func openLives(_ sender: UIButton) {
        presenter?.onTapLives()
    }
    
    func updateLives(with lives: Int) {
//        if UserModel.shared.get_userSubscription()?.subscribed == true{
//            let imageAttachment = NSTextAttachment()
//            imageAttachment.image = UIImage(named: "Infinite")
//            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
//            let imageString = NSAttributedString(attachment: imageAttachment)
//            livesCount.attributedText = imageString
//        }else{
//            livesCount.text = "\(lives)"
//        }
    }
    
    func updateCoins(with coins: Int) {
//        coinsCount.text = "\(coins)"
    }
    
    func reloadLives() {
        presenter?.reloadLives()
    }
    
    //MARK: - delegate function
    func reloadNotifications() {
        notificationTableList.reloadData()
    }
    
    //MARK: - error delegate functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
}
