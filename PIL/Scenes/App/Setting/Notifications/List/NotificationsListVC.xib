<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="NotificationsListVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="navigationView" destination="Khq-lf-qKl" id="gab-As-hCl"/>
                <outlet property="notificationsTableView" destination="Xdc-wG-cxb" id="2kX-qN-KPG"/>
                <outlet property="view" destination="Qia-CB-auH" id="jte-In-9XA"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="Qia-CB-auH">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="BackGeound" translatesAutoresizingMaskIntoConstraints="NO" id="7Na-Ow-ndG">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                </imageView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ha5-Np-MR9" customClass="MainBackgroundGradientView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Khq-lf-qKl" customClass="NavigationView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="120"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="120" id="SlF-BD-RHm"/>
                    </constraints>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="Xdc-wG-cxb">
                    <rect key="frame" x="7" y="127" width="379" height="684"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="oTv-a4-VoY"/>
                        <outlet property="delegate" destination="-1" id="UBs-Zc-ZUX"/>
                    </connections>
                </tableView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="wKG-R9-UIq"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="7Na-Ow-ndG" secondAttribute="bottom" id="23R-Zi-ZdY"/>
                <constraint firstItem="Ha5-Np-MR9" firstAttribute="top" secondItem="Qia-CB-auH" secondAttribute="top" id="2gI-Og-a9r"/>
                <constraint firstItem="wKG-R9-UIq" firstAttribute="trailing" secondItem="Ha5-Np-MR9" secondAttribute="trailing" id="6Fk-Yy-nUR"/>
                <constraint firstItem="7Na-Ow-ndG" firstAttribute="top" secondItem="Qia-CB-auH" secondAttribute="top" id="6lv-YZ-2Xf"/>
                <constraint firstItem="wKG-R9-UIq" firstAttribute="trailing" secondItem="Xdc-wG-cxb" secondAttribute="trailing" constant="7" id="9a7-IG-Auj"/>
                <constraint firstItem="Xdc-wG-cxb" firstAttribute="leading" secondItem="wKG-R9-UIq" secondAttribute="leading" constant="7" id="KTf-kk-oyH"/>
                <constraint firstItem="Khq-lf-qKl" firstAttribute="top" secondItem="Qia-CB-auH" secondAttribute="top" id="LaX-VF-vqP"/>
                <constraint firstAttribute="trailing" secondItem="Khq-lf-qKl" secondAttribute="trailing" id="PH6-kJ-pNX"/>
                <constraint firstAttribute="trailing" secondItem="7Na-Ow-ndG" secondAttribute="trailing" id="RGo-er-zQO"/>
                <constraint firstItem="Ha5-Np-MR9" firstAttribute="leading" secondItem="wKG-R9-UIq" secondAttribute="leading" id="RdR-O1-KUJ"/>
                <constraint firstItem="7Na-Ow-ndG" firstAttribute="leading" secondItem="Qia-CB-auH" secondAttribute="leading" id="SAp-pS-cDI"/>
                <constraint firstAttribute="bottom" secondItem="Ha5-Np-MR9" secondAttribute="bottom" id="ZOg-rB-oZp"/>
                <constraint firstItem="Khq-lf-qKl" firstAttribute="leading" secondItem="wKG-R9-UIq" secondAttribute="leading" id="eIh-xv-CMD"/>
                <constraint firstItem="Xdc-wG-cxb" firstAttribute="top" secondItem="Khq-lf-qKl" secondAttribute="bottom" constant="7" id="ykN-C1-ZrC"/>
                <constraint firstItem="wKG-R9-UIq" firstAttribute="bottom" secondItem="Xdc-wG-cxb" secondAttribute="bottom" constant="7" id="yxI-cm-CAO"/>
            </constraints>
            <point key="canvasLocation" x="3.8167938931297707" y="19.718309859154932"/>
        </view>
    </objects>
    <resources>
        <image name="BackGeound" width="375" height="812"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
