//
//  NotificationsListVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/08/2023.
//

import UIKit

class NotificationsListVC: UIViewController, UITableViewDelegate, UITableViewDataSource {
    
    @IBOutlet weak var navigationView: NavigationView!
    @IBOutlet weak var notificationsTableView: UITableView!
//    @IBOutlet weak var viewTitle: UILabel!
    
    var action: Lookups.Actions?
    var notificationWorker: NotificationWorker?
    var pageIndex: Int = 0
    let pageSize: Int = 15
    var parameters: [String: String] = ["userid": UserModel.shared.get_id()]
    var records: [NotificationDataModel] = []

    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back] )
        self.navigationView.setNavigationTitle(title: action?.actionTitle ?? "")
        
        notificationsTableView.register(NotificationCell2.nib, forCellReuseIdentifier: NotificationCell2.identifier)
//        viewTitle.text = action?.actionTitle
        notificationWorker = .init()
        parameters.updateValue("\(action?.rawValue ?? -1)", forKey: "actionId")
        notificationWorker?.getNotificationsActionList(parameters, completion: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.logoutAlert {
                        UserModel.shared.logOut()
                    }
                }else if statusCode == 200 {
                    if model.success {
                        self.records = model.notificationRecords ?? []
                        self.notificationsTableView.reloadData()
                    }else{
                        self.showAlert(withTitle: false, msg: model.message ?? "" , compilition: nil)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.showAlert(withTitle: false, msg: localizedError , compilition: nil)
                } sessionExpired: {
                    self.logoutAlert {
                        UserModel.shared.logOut()
                    }
                } noInternet: {
                    
                }
                break
            }
        })
    
    }
    
    @IBAction func backAction(){
        self.navigationController?.popViewController(animated: true)
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return records.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: NotificationCell2.identifier, for: indexPath) as! NotificationCell2
        cell.loadUI(records[indexPath.row].icon ?? "", records[indexPath.row].body ?? "")
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch action{
        case .comment:
            let commentsVC = CommentsRouter.createModule("\(records[indexPath.row].targetObjectID ?? "")", nil, true)
            commentsVC.modalPresentationStyle = .overCurrentContext
            self.tabBarController?.present(commentsVC, animated: true)
            
//            let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
//                                                             postRecord: .init(id: Int(records[indexPath.row].targetObjectID ?? "0")!))
//            SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
//                switch result{
//                case .success(let response):
//                    if let data = response.postRecords.first{
//                        let vc = UserPostRouter.createModule(data)
//                        self.navigationController?.pushViewController(vc, animated: true)
//                    }
//                default: break
//                }
//            }
        case .like, .share:
            let postPayload = Payloads.PostsListPayload.init(createdBy: Int(UserModel.shared.get_id())!,
                                                             postRecord: .init(id: Int(records[indexPath.row].targetObjectID ?? "0")!))
            SocialWorker.shared.getPostsList(postPayload){ result, statusCode in
                switch result{
                case .success(let response):
                    if let data = response.postRecords.first{
                        let vc = UserPostRouter.createModule(data)
                        self.navigationController?.pushViewController(vc, animated: true)
                    }
                default: break
                }
            }
        default: break
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }

}
