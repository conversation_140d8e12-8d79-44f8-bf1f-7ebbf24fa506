//
//  NotificationCell.swift
//  PIL
//
//  Created by <PERSON> on 10/17/21.
//

import UIKit

class NotificationCell: UITableViewCell {

    
    //MARK: - outlets
    @IBOutlet weak var userImg: UIImageView!
    @IBOutlet weak var notificationMessage: UILabel!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var containerStack: UIStackView!
//    @IBOutlet weak var arrowImage: UIImageView!
    
    var record: SocialNotifications?
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        if Locale.preferredLanguages[0] == "ar"{
            containerStack.contentMode = .right
        }else{
            containerStack.contentMode = .left
        }
    }
    
    func loadAppNotificationUI(_ title: String,_ body: String , img : String){
//        arrowImage.isHidden = true
        titleLabel.text = title
        notificationMessage.text = body
        userImg.sd_setImage(with: URL(string: img))
    }
    
    func loadUI(_ index: Int){
        switch index{
        case 0:
            if record?.totalFollow == 0{
                titleLabel.text = "You have no followers".localized
            }else{
                titleLabel.text = "You Have".localized + " \(record?.totalFollow ?? 0) " + "New Followers".localized
            }
            notificationMessage.text = record?.followMsg
            userImg.sd_setImage(with: URL(string: record?.followIcon ?? ""))
//            userImg.sd_setImage(with: .init(string: record?.followIcon ?? ""))
        case 1:
            if record?.totalLike == 0{
                titleLabel.text = "You have no likes".localized
            }else{
                titleLabel.text = "You Have".localized + " \(record?.totalLike ?? 0) " + "Likes".localized
            }
            notificationMessage.text = record?.likeMsg
            userImg.sd_setImage(with: URL(string: record?.likeIcon ?? ""))
        case 2:
            if record?.totalShare == 0{
                titleLabel.text = "You have no shares".localized
            }else{
                titleLabel.text = "You Have".localized + " \(record?.totalShare ?? 0) " + "Post Shares".localized
            }
            notificationMessage.text = record?.shareMsg
            userImg.sd_setImage(with: URL(string: record?.shareIcon ?? ""))
        case 3:
            if record?.totalComment == 0{
                titleLabel.text = "You have no comments".localized
            }else{
                titleLabel.text = "You Have".localized + " \(record?.totalComment ?? 0) " + "Comments".localized
            }
            notificationMessage.text = record?.commentMsg
            userImg.sd_setImage(with: URL(string: record?.commentIcon ?? ""))
        default: break
        }
        
//        arrowImage.isHidden = false
        titleLabel.isHidden = false
        
//        if !image.isNilOrEmpty{
//            userImg.sd_setImage(with: .init(string: image!))
//        }
        
    }
    
    
}
