//
//  NotificationCell2.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 09/08/2023.
//

import UIKit

class NotificationCell2: UITableViewCell {

    static let identifier = "NotificationCell2"
    static let nib = UINib(nibName: "NotificationCell2", bundle: nil)
    
    @IBOutlet weak var message: UILabel!
    
    func loadUI(_ image: String,_ msg: String){
        message.text = msg
    }
}
