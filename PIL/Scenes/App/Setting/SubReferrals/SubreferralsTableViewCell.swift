//
//  SubreferralsTableViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 05/11/2023.
//

import UIKit

class SubreferralsTableViewCell: UITableViewCell {

    static let identifier = "SubreferralsTableViewCell"
    static let nib = UINib(nibName: "SubreferralsTableViewCell", bundle: nil)
    
    @IBOutlet weak var userimage: UIImageView!
    @IBOutlet weak var name: UILabel!
    @IBOutlet weak var sum: UILabel!
    
    var data: UserDataModel?{
        didSet{
            userimage.sd_setImage(with: .init(string: data?.image ?? ""))
            name.text = data?.name
            sum.text = "\(data?.sum ?? 0)"
        }
    }
    
}
