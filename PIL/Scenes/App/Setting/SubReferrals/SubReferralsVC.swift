//
//  SubReferralsVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 05/11/2023.
//

import UIKit

class SubReferralsVC: UIViewController, UITableViewDelegate, UITableViewDataSource {
    
    @IBOutlet weak var userimage: UIImageView!
    @IBOutlet weak var name: UILabel!
    @IBOutlet weak var sum: UILabel!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var total: UILabel!
    @IBOutlet weak var navigationView: NavigationView!

    var user: UserDataModel?
    var id: Int?
    var subReferrals: PlayersReferrals?

    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.tableView.register(SubreferralsTableViewCell.nib, forCellReuseIdentifier: SubreferralsTableViewCell.identifier)
        self.tableView.delegate = self
        self.tableView.dataSource = self

        userimage.sd_setImage(with: .init(string: user?.image ?? ""))
        name.text = user?.name
        sum.text = "\(user?.sum ?? 0)"
        
        getSubReferrals()
        setUpNavigation()

    }
    
    @IBAction func back(_ sender: Any) {
        self.navigationController?.popViewController(animated: true)
    }
    
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Sub-referrals".localized)
    }
    
    func getSubReferrals(){
        ProfileWorker.shared.getSubReferrals(id: id!) { [weak self]  result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                guard statusCode != 403 || statusCode != 401 else{
                    return
                }
                guard let response = model.data else{
                    return
                }
                self.total.text = "Total Referrals: ".localized + "\(response.players?.count ?? 0)"
                self.subReferrals = response
                self.tableView.reloadData()
            case .failure(_):
                break
            }
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return subReferrals?.players?.count ?? 0
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: SubreferralsTableViewCell.identifier, for: indexPath) as! SubreferralsTableViewCell
        cell.data = subReferrals?.players![indexPath.row]
        return cell
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 90
    }

}
