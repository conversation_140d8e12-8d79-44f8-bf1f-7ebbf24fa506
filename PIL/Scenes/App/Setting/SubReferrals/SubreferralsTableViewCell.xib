<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="SubreferralsTableViewCell" rowHeight="131" id="KGk-i7-Jjw" customClass="SubreferralsTableViewCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="363" height="131"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="363" height="131"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c7p-6s-T8U">
                        <rect key="frame" x="7" y="7" width="349" height="117"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PGH-18-nt1">
                                <rect key="frame" x="15" y="23.666666666666671" width="70" height="70"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Circle Leader" translatesAutoresizingMaskIntoConstraints="NO" id="Uu0-rq-EP7">
                                        <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="avatar2" translatesAutoresizingMaskIntoConstraints="NO" id="104-Ye-Uns">
                                        <rect key="frame" x="10" y="0.0" width="50" height="60"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isProfilePicture" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="104-Ye-Uns" firstAttribute="top" secondItem="PGH-18-nt1" secondAttribute="top" id="5md-EM-GPL"/>
                                    <constraint firstAttribute="trailing" secondItem="Uu0-rq-EP7" secondAttribute="trailing" id="PoN-3m-drR"/>
                                    <constraint firstAttribute="bottom" secondItem="Uu0-rq-EP7" secondAttribute="bottom" id="UcA-4h-o9m"/>
                                    <constraint firstItem="Uu0-rq-EP7" firstAttribute="top" secondItem="PGH-18-nt1" secondAttribute="top" id="a5H-lE-c1Y"/>
                                    <constraint firstItem="Uu0-rq-EP7" firstAttribute="leading" secondItem="PGH-18-nt1" secondAttribute="leading" id="cOi-Zd-jX9"/>
                                    <constraint firstAttribute="width" constant="70" id="d0c-Xh-bz9"/>
                                    <constraint firstAttribute="bottom" secondItem="104-Ye-Uns" secondAttribute="bottom" constant="10" id="dQL-9l-ZI3"/>
                                    <constraint firstAttribute="height" constant="70" id="fSm-Kv-Y2m"/>
                                    <constraint firstAttribute="trailing" secondItem="104-Ye-Uns" secondAttribute="trailing" constant="10" id="i9h-m9-0bb"/>
                                    <constraint firstItem="104-Ye-Uns" firstAttribute="leading" secondItem="PGH-18-nt1" secondAttribute="leading" constant="10" id="qOa-8h-OfW"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="pof-cQ-BiL">
                                <rect key="frame" x="100" y="49.666666666666664" width="39" height="17.999999999999993"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C7R-s2-1RO">
                                        <rect key="frame" x="0.0" y="0.0" width="39" height="18"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7A6-oq-1Yt">
                                <rect key="frame" x="254" y="41" width="80" height="35"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_BU_BG_With_BG&amp;border_Gradient" translatesAutoresizingMaskIntoConstraints="NO" id="dZZ-2R-45b">
                                        <rect key="frame" x="0.0" y="0.0" width="80" height="35"/>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="OVt-Rg-eW7">
                                        <rect key="frame" x="15.666666666666682" y="5" width="48.666666666666657" height="25"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pi-coins" translatesAutoresizingMaskIntoConstraints="NO" id="yjb-zb-GWP">
                                                <rect key="frame" x="0.0" y="2.6666666666666643" width="20" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="20" id="b3b-6c-3J0"/>
                                                    <constraint firstAttribute="height" constant="20" id="dGE-3D-qfW"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="200" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="G5f-Vz-vQG">
                                                <rect key="frame" x="25" y="0.0" width="23.666666666666671" height="25"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="Rh7-ab-tx8"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="12"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                        <real key="value" value="7"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="dZZ-2R-45b" secondAttribute="bottom" id="1FM-y6-GKU"/>
                                    <constraint firstAttribute="height" constant="35" id="KNE-6c-Fow"/>
                                    <constraint firstItem="OVt-Rg-eW7" firstAttribute="centerX" secondItem="7A6-oq-1Yt" secondAttribute="centerX" id="QPA-Li-EIq"/>
                                    <constraint firstAttribute="width" constant="80" id="Ttt-rW-wG3"/>
                                    <constraint firstItem="dZZ-2R-45b" firstAttribute="top" secondItem="7A6-oq-1Yt" secondAttribute="top" id="Xrr-qq-ard"/>
                                    <constraint firstItem="dZZ-2R-45b" firstAttribute="leading" secondItem="7A6-oq-1Yt" secondAttribute="leading" id="Xun-ea-buf"/>
                                    <constraint firstAttribute="trailing" secondItem="dZZ-2R-45b" secondAttribute="trailing" id="hNq-fe-6NU"/>
                                    <constraint firstItem="OVt-Rg-eW7" firstAttribute="centerY" secondItem="7A6-oq-1Yt" secondAttribute="centerY" id="jVO-TY-aNr"/>
                                </constraints>
                            </view>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Line setting" translatesAutoresizingMaskIntoConstraints="NO" id="EGh-hS-SWc">
                                <rect key="frame" x="20" y="116" width="309" height="1"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="vuE-aX-fYF"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="PGH-18-nt1" firstAttribute="leading" secondItem="c7p-6s-T8U" secondAttribute="leading" constant="15" id="5Pv-5J-lft"/>
                            <constraint firstItem="pof-cQ-BiL" firstAttribute="centerY" secondItem="c7p-6s-T8U" secondAttribute="centerY" id="6vV-Ia-rPb"/>
                            <constraint firstItem="pof-cQ-BiL" firstAttribute="leading" secondItem="PGH-18-nt1" secondAttribute="trailing" constant="15" id="8xW-3V-ZHV"/>
                            <constraint firstItem="EGh-hS-SWc" firstAttribute="leading" secondItem="c7p-6s-T8U" secondAttribute="leading" constant="20" id="BY4-Yx-B2M"/>
                            <constraint firstItem="7A6-oq-1Yt" firstAttribute="centerY" secondItem="c7p-6s-T8U" secondAttribute="centerY" id="C5j-7Y-kQ9"/>
                            <constraint firstAttribute="trailing" secondItem="7A6-oq-1Yt" secondAttribute="trailing" constant="15" id="NO7-jQ-Qnn"/>
                            <constraint firstAttribute="trailing" secondItem="EGh-hS-SWc" secondAttribute="trailing" constant="20" id="mfe-9R-cMj"/>
                            <constraint firstAttribute="bottom" secondItem="EGh-hS-SWc" secondAttribute="bottom" id="sCB-FB-oxY"/>
                            <constraint firstItem="PGH-18-nt1" firstAttribute="centerY" secondItem="c7p-6s-T8U" secondAttribute="centerY" id="sg4-jL-EMD"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="15"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                <real key="value" value="1.5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                <color key="value" name="Orange"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstItem="c7p-6s-T8U" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="7" id="COs-d4-RSp"/>
                    <constraint firstItem="c7p-6s-T8U" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="7" id="g2k-Q9-AV1"/>
                    <constraint firstAttribute="bottom" secondItem="c7p-6s-T8U" secondAttribute="bottom" constant="7" id="iz5-wR-aYh"/>
                    <constraint firstAttribute="trailing" secondItem="c7p-6s-T8U" secondAttribute="trailing" constant="7" id="v7y-a5-rRe"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="name" destination="C7R-s2-1RO" id="LLh-fy-1ab"/>
                <outlet property="sum" destination="G5f-Vz-vQG" id="RUD-Ac-xVb"/>
                <outlet property="userimage" destination="104-Ye-Uns" id="Pyn-c9-d1q"/>
            </connections>
            <point key="canvasLocation" x="171.75572519083968" y="50.352112676056343"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="A_BU_BG_With_BG&amp;border_Gradient" width="94" height="32"/>
        <image name="Circle Leader" width="141.66667175292969" height="141.66667175292969"/>
        <image name="Line setting" width="327" height="1"/>
        <image name="avatar2" width="800" height="800"/>
        <image name="pi-coins" width="50" height="48"/>
        <namedColor name="Orange">
            <color red="0.90200001001358032" green="0.60000002384185791" blue="0.10999999940395355" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
