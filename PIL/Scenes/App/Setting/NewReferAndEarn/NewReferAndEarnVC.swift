//
//  NewReferAndEarnVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 24/01/2023.
//

import UIKit

class NewReferAndEarnVC: UIViewController, NewReferAndEarnViewProtocol {
    
    var presenter: NewReferAndEarnPresenterProtocol!
 
    @IBOutlet weak var userCodeView: UIView!
    @IBOutlet weak var userCode: UILabel!
    @IBOutlet weak var moreOptionsView: UIView!
    @IBOutlet weak var whatsappBTN: UIButton!
    @IBOutlet weak var segmentLeadingCnst: NSLayoutConstraint!
    @IBOutlet weak var referredView: UIView!
    @IBOutlet weak var instructionsView: UIView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var sum: UILabel!
    @IBOutlet weak var navigationView: NavigationView!

    @IBOutlet weak var contentStackView: UIStackView!
    var selectedSegmentTag = 0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        presenter?.viewDidLoad()
        
        setUserData() /// get user data
        setGuesters() /// actions
        
        /// Cell UI
        self.tableView.register(ReferralTableViewCell.nib, forCellReuseIdentifier: ReferralTableViewCell.identifier)
        self.tableView.delegate = self
        self.tableView.dataSource = self
        setUpNavigation()
        
        if app_lang == "ar"{
            self.contentStackView.alignment = .trailing
        }
    }
    
    /// Navigation UI
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Refer & Earn".localized)
    }
    
    /// get  refrral Code and
    /// check if user subscribed because he has Infinite lives
    func setUserData() {
        userCode.text = UserModel.shared.get_referralCode()
        if UserModel.shared.get_userSubscription()?.subscribed == true{
            let imageAttachment = NSTextAttachment()
            imageAttachment.image = UIImage(named: "Infinite")
            imageAttachment.bounds = .init(x: 0, y: -3, width: 13, height: 13)
            let imageString = NSAttributedString(attachment: imageAttachment)
        }else{
        }

        print("-->",UserModel.shared.get_referralCode())
    }
    
    func setGuesters() {
        userCodeView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTappedUserCodeView)))
        moreOptionsView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(didTappedMoreOptionsView)))
    }
    
    /// copy text
    @objc func didTappedUserCodeView() {
        UIPasteboard.general.string = userCode.text
    }
    
    /// share with friends
    @objc func didTappedMoreOptionsView() {
        let textShare = [ "\(UserModel.shared.get_referralCode())" ]
        let activityViewController = UIActivityViewController(activityItems: textShare , applicationActivities: nil)
        activityViewController.popoverPresentationController?.sourceView = self.view
        self.present(activityViewController, animated: true, completion: nil)
    }
 
    
    /// copu refrral code
    @IBAction func copyCodeBTN(_ sender: Any) {
        UIPasteboard.general.string = "\(UserModel.shared.get_referralCode())"
        self.showAlert(withTitle: false, msg: "copied".localized, compilition: nil)
        
    }
    
    /// switch between tabs ( instructions - referral )
    @IBAction func tournamentsSegment(_ sender: UIButton) {
        if UserModel.shared.get_loginAsGuest() == true{
            let vc = UIStoryboard(name: "CheckLogin", bundle: nil).instantiateViewController(withIdentifier: "CheckLoginVC") as! CheckLoginVC
            self.present(vc, animated: false, completion: nil)
        }else{
            sender.setTitleColor(UIColor(named: "Black-White"), for: .normal)
            switch sender.tag{
            case 0: showInstruction()
            case 1: showReferred()
            default: break
            }
            selectedSegmentTag = sender.tag
        }
    }
    
    
    /// Instruction Tab
    func showInstruction(){
        instructionsView.isHidden = false
        referredView.isHidden = true
        segmentLeadingCnst.constant = 0
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.view.layoutIfNeeded()
        }
    }
    
    
    /// Referred Tab
    func showReferred(){
        instructionsView.isHidden = true
        referredView.isHidden = false
        segmentLeadingCnst.constant = 185
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut) {
            self.view.layoutIfNeeded()
        }
    }
    

    /// share whats
    @IBAction func whatsappAction(_ sender: UIButton) {
        let message = "\(UserModel.shared.get_referrallink())"
        var queryCharSet = NSCharacterSet.urlQueryAllowed
        
        queryCharSet.remove(charactersIn: "+&")
        
        if let escapedString = message.addingPercentEncoding(withAllowedCharacters: queryCharSet) {
            if let whatsappURL = URL(string: "whatsapp://send?text=\(escapedString)") {
                if UIApplication.shared.canOpenURL(whatsappURL) {
                    UIApplication.shared.open(whatsappURL, options: [: ], completionHandler: nil)
                } else {
                    debugPrint("please install WhatsApp")
                }
            }
        }
    }
    
    /// reload table users referral
    func reloadData() {
        sum.text = "\(presenter.referrals!.sum ?? 0)"
        tableView.reloadData()
    }
    
    /// search in users referral
    @IBAction func searchTFAction(_ sender: UITextField) {
        guard sender.text != "Find user".localized else{
            endSearch()
            return
        }
        guard !sender.text.isNilOrEmpty else{
            endSearch()
            return
        }
        presenter.referrals!.players = presenter.referrals!.players!.filter({ return $0.name!.lowercased().contains((sender.text?.lowercased())!) })
        tableView.reloadData()
    }
    
    func endSearch(){
        presenter.referrals = presenter.tempReferrals
        self.tableView.reloadData()
    }
}


/// List users referral
extension NewReferAndEarnVC: UITableViewDataSource, UITableViewDelegate{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter.referrals?.players?.count ?? 0
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ReferralTableViewCell.identifier, for: indexPath) as! ReferralTableViewCell
        cell.data = presenter.referrals?.players![indexPath.row]
        cell.onSeeAction = { self.presenter.onSelectRow(indexPath.row) }
        return cell
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 90
    }
}

/// Search in users referral
//MARKL :- text field delegate
extension NewReferAndEarnVC: UITextFieldDelegate{
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField.text == "Find user".localized{
            textField.text = ""
        }
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if textField.text.isNilOrEmpty{
            textField.text = "Find user".localized
            endSearch()
        }
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField.text.isNilOrEmpty{
            textField.text = "Find user".localized
            endSearch()
        }
        view.endEditing(true)
        return true
    }
}
