<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="ReferralTableViewCell" rowHeight="141" id="KGk-i7-Jjw" customClass="ReferralTableViewCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="433" height="141"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="433" height="141"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eki-l5-og5">
                        <rect key="frame" x="7" y="7" width="419" height="127"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="c0p-mK-jFC">
                                <rect key="frame" x="15" y="28.666666666666664" width="70" height="70"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="user_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="XaY-Q2-grn">
                                        <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="TAb-nB-iyR">
                                        <rect key="frame" x="5" y="10" width="60" height="50"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="isProfilePicture" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="XaY-Q2-grn" secondAttribute="trailing" id="764-uS-UnJ"/>
                                    <constraint firstAttribute="width" constant="70" id="Au0-1u-TPq"/>
                                    <constraint firstItem="XaY-Q2-grn" firstAttribute="leading" secondItem="c0p-mK-jFC" secondAttribute="leading" id="Hml-9K-h9M"/>
                                    <constraint firstAttribute="height" constant="70" id="KcT-m5-Luo"/>
                                    <constraint firstAttribute="bottom" secondItem="TAb-nB-iyR" secondAttribute="bottom" constant="10" id="NBZ-Jd-cBW"/>
                                    <constraint firstItem="XaY-Q2-grn" firstAttribute="top" secondItem="c0p-mK-jFC" secondAttribute="top" id="PNl-qm-wHR"/>
                                    <constraint firstAttribute="bottom" secondItem="XaY-Q2-grn" secondAttribute="bottom" id="SWI-C8-668"/>
                                    <constraint firstAttribute="trailing" secondItem="TAb-nB-iyR" secondAttribute="trailing" constant="5" id="YPG-gM-eim"/>
                                    <constraint firstItem="TAb-nB-iyR" firstAttribute="leading" secondItem="c0p-mK-jFC" secondAttribute="leading" constant="5" id="hf4-gj-NXT"/>
                                    <constraint firstItem="TAb-nB-iyR" firstAttribute="top" secondItem="c0p-mK-jFC" secondAttribute="top" constant="10" id="ysY-SS-NzF"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="JBI-er-xHZ">
                                <rect key="frame" x="100" y="39.666666666666664" width="214" height="47.999999999999993"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eC2-GQ-exA">
                                        <rect key="frame" x="0.0" y="0.0" width="39" height="18"/>
                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                                        <color key="textColor" name="Black-White"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fF7-jX-w9e">
                                        <rect key="frame" x="0.0" y="23.000000000000007" width="105.66666666666667" height="25"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="QrW-Ga-C0V">
                                                <rect key="frame" x="0.0" y="0.0" width="105.66666666666667" height="25"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="1000" verticalHuggingPriority="251" text="See Subreferrals" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pfb-BQ-UPP">
                                                        <rect key="frame" x="0.0" y="0.0" width="105.66666666666667" height="23.333333333333332"/>
                                                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="13"/>
                                                        <color key="textColor" name="Orange"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GMK-FO-YLl">
                                                        <rect key="frame" x="0.0" y="23.333333333333329" width="105.66666666666667" height="1.6666666666666679"/>
                                                        <color key="backgroundColor" name="Orange"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1.5" id="i3k-un-Rnx"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="TUl-ZU-9fg"/>
                                                </constraints>
                                            </stackView>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9uK-c3-bVt">
                                                <rect key="frame" x="0.0" y="0.0" width="105.66666666666667" height="25"/>
                                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                                <connections>
                                                    <action selector="seeAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Gda-Vn-h75"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="9uK-c3-bVt" firstAttribute="leading" secondItem="fF7-jX-w9e" secondAttribute="leading" id="7zq-Sr-2Wi"/>
                                            <constraint firstAttribute="bottom" secondItem="9uK-c3-bVt" secondAttribute="bottom" id="AOt-IZ-QSy"/>
                                            <constraint firstAttribute="trailing" secondItem="QrW-Ga-C0V" secondAttribute="trailing" id="GJt-uo-zB7"/>
                                            <constraint firstAttribute="bottom" secondItem="QrW-Ga-C0V" secondAttribute="bottom" id="O6c-bX-TS1"/>
                                            <constraint firstItem="QrW-Ga-C0V" firstAttribute="leading" secondItem="fF7-jX-w9e" secondAttribute="leading" id="QfA-vt-m0B"/>
                                            <constraint firstItem="9uK-c3-bVt" firstAttribute="top" secondItem="fF7-jX-w9e" secondAttribute="top" id="VHd-qD-udg"/>
                                            <constraint firstItem="QrW-Ga-C0V" firstAttribute="top" secondItem="fF7-jX-w9e" secondAttribute="top" id="cD9-7C-QLy"/>
                                            <constraint firstItem="QrW-Ga-C0V" firstAttribute="top" secondItem="fF7-jX-w9e" secondAttribute="top" id="evA-ss-Axg"/>
                                            <constraint firstAttribute="trailing" secondItem="QrW-Ga-C0V" secondAttribute="trailing" id="gCI-oL-DrP"/>
                                            <constraint firstItem="QrW-Ga-C0V" firstAttribute="leading" secondItem="fF7-jX-w9e" secondAttribute="leading" id="t0u-UW-w9G"/>
                                            <constraint firstAttribute="bottom" secondItem="QrW-Ga-C0V" secondAttribute="bottom" id="vwK-jb-voX"/>
                                            <constraint firstAttribute="trailing" secondItem="9uK-c3-bVt" secondAttribute="trailing" id="yuG-xk-THX"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pCd-Xx-pKA">
                                <rect key="frame" x="319" y="46" width="90" height="35"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_BU_S_BG_With_border_Gradient" translatesAutoresizingMaskIntoConstraints="NO" id="3ce-DY-cTU">
                                        <rect key="frame" x="0.0" y="0.0" width="90" height="35"/>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="4xI-BQ-TL7">
                                        <rect key="frame" x="5" y="5" width="80" height="25"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="pi-coins" translatesAutoresizingMaskIntoConstraints="NO" id="kWr-oz-JQv">
                                                <rect key="frame" x="0.0" y="2.6666666666666643" width="20" height="20"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="gj5-f7-Hfn"/>
                                                    <constraint firstAttribute="width" constant="20" id="rlC-kO-x3v"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="200" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bbc-qx-Ekw">
                                                <rect key="frame" x="24" y="0.0" width="56" height="25"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="XkC-RC-AC5"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="12"/>
                                                <color key="textColor" name="Black-White"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                        <real key="value" value="7"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="4xI-BQ-TL7" firstAttribute="centerY" secondItem="pCd-Xx-pKA" secondAttribute="centerY" id="5CW-RQ-OgZ"/>
                                    <constraint firstAttribute="trailing" secondItem="3ce-DY-cTU" secondAttribute="trailing" id="8q1-dd-3nC"/>
                                    <constraint firstItem="4xI-BQ-TL7" firstAttribute="leading" secondItem="pCd-Xx-pKA" secondAttribute="leading" constant="5" id="Bri-T2-jTy"/>
                                    <constraint firstAttribute="trailing" secondItem="4xI-BQ-TL7" secondAttribute="trailing" constant="5" id="Mq8-Hd-Sn3"/>
                                    <constraint firstItem="3ce-DY-cTU" firstAttribute="top" secondItem="pCd-Xx-pKA" secondAttribute="top" id="NeV-fC-qwh"/>
                                    <constraint firstAttribute="bottom" secondItem="3ce-DY-cTU" secondAttribute="bottom" id="Yl6-G2-dJy"/>
                                    <constraint firstItem="3ce-DY-cTU" firstAttribute="leading" secondItem="pCd-Xx-pKA" secondAttribute="leading" id="aVG-QC-t8F"/>
                                    <constraint firstAttribute="width" constant="90" id="d6L-Lx-JNG"/>
                                    <constraint firstAttribute="height" constant="35" id="q8s-v4-mtA"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="pCd-Xx-pKA" secondAttribute="trailing" constant="10" id="6s0-gl-yOT"/>
                            <constraint firstItem="c0p-mK-jFC" firstAttribute="leading" secondItem="eki-l5-og5" secondAttribute="leading" constant="15" id="NkW-iy-a59"/>
                            <constraint firstItem="JBI-er-xHZ" firstAttribute="leading" secondItem="c0p-mK-jFC" secondAttribute="trailing" constant="15" id="RqB-Ya-jX3"/>
                            <constraint firstItem="pCd-Xx-pKA" firstAttribute="leading" secondItem="JBI-er-xHZ" secondAttribute="trailing" constant="5" id="dBf-F0-exh"/>
                            <constraint firstItem="pCd-Xx-pKA" firstAttribute="centerY" secondItem="eki-l5-og5" secondAttribute="centerY" id="g5Z-ur-eKg"/>
                            <constraint firstItem="c0p-mK-jFC" firstAttribute="centerY" secondItem="eki-l5-og5" secondAttribute="centerY" id="hmw-GN-VLO"/>
                            <constraint firstItem="JBI-er-xHZ" firstAttribute="centerY" secondItem="eki-l5-og5" secondAttribute="centerY" id="kha-Cd-q0Z"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Line setting" translatesAutoresizingMaskIntoConstraints="NO" id="wsL-0f-y4a">
                        <rect key="frame" x="20" y="140" width="393" height="1"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="Urk-Ss-vCT"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="wsL-0f-y4a" secondAttribute="trailing" constant="20" id="DFj-UC-mHO"/>
                    <constraint firstItem="eki-l5-og5" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="7" id="HHX-yK-AsT"/>
                    <constraint firstItem="wsL-0f-y4a" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="KPC-eT-wx8"/>
                    <constraint firstAttribute="bottom" secondItem="wsL-0f-y4a" secondAttribute="bottom" id="aBq-UB-N2J"/>
                    <constraint firstItem="eki-l5-og5" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="7" id="b84-Ml-eRj"/>
                    <constraint firstAttribute="trailing" secondItem="eki-l5-og5" secondAttribute="trailing" constant="7" id="dRU-kR-iNP"/>
                    <constraint firstAttribute="bottom" secondItem="eki-l5-og5" secondAttribute="bottom" constant="7" id="wfJ-H3-2lE"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="name" destination="eC2-GQ-exA" id="6gP-VR-5aA"/>
                <outlet property="sum" destination="bbc-qx-Ekw" id="QFD-Zh-mEc"/>
                <outlet property="userimage" destination="TAb-nB-iyR" id="ZY0-xA-e3f"/>
            </connections>
            <point key="canvasLocation" x="106.10687022900763" y="53.87323943661972"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="A_BU_S_BG_With_border_Gradient" width="94" height="34"/>
        <image name="Line setting" width="327" height="1"/>
        <image name="pi-coins" width="50" height="48"/>
        <image name="user_placeholder" width="96" height="96"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange">
            <color red="0.90200001001358032" green="0.60000002384185791" blue="0.10999999940395355" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
