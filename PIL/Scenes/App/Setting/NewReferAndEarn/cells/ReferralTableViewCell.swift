//
//  ReferralTableViewCell.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 05/11/2023.
//

import UIKit

class ReferralTableViewCell: UITableViewCell {
    
    static let identifier = "ReferralTableViewCell"
    static let nib = UINib(nibName: "ReferralTableViewCell", bundle: nil)
    
    @IBOutlet weak var userimage: UIImageView!
    @IBOutlet weak var name: UILabel!
    @IBOutlet weak var sum: UILabel!
    
    var onSeeAction: (()->Void) = {}
    var data: UserDataModel?{
        didSet{
            userimage.sd_setImage(with: .init(string: data?.image ?? ""))
            name.text = data?.name
            sum.text = "\(data?.sum ?? 0)"
        }
    }
    
    @IBAction func seeAction(_ sender: Any) {
        self.onSeeAction()
    }
    
}
