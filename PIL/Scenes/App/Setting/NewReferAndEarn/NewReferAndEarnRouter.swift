//
//  NewReferAndEarnRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 24/01/2023.
//

import Foundation

class NewReferAndEarnRouter: NewReferAndEarnRouterProtocol {
    weak var viewController: UIViewController?
    static func createModule() -> UIViewController {
        let router = NewReferAndEarnRouter()
        let view = SetStoryBoard.controller(controller: Helper(Story: .NewReferAndEarnST, VC: .NewReferAndEarnVC)) as! NewReferAndEarnVC
        let presenter = NewReferAndEarnPresenter(view: view, router: router)
        view.presenter = presenter
        router.viewController = view
        return view
    }
    
    func toSubreferrals(id: Int, user: UserDataModel){
        let view = SetStoryBoard.controller(controller: Helper(Story: .NewReferAndEarnST, VC: .SubReferralsVC)) as! SubReferralsVC
        view.id = id
        view.user = user
        self.viewController?.navigationController?.pushViewController(view, animated: true)
    }

    func popViewController() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else {
                return
            }
            self.viewController?.navigationController?.popViewController(animated: true)
        }
    }
}
