//
//  NewReferAndEarnPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 24/01/2023.
//

import Foundation

class NewReferAndEarnPresenter {
    private let router: NewReferAndEarnRouterProtocol
    var view: NewReferAndEarnViewProtocol
    var tempReferrals: PlayersReferrals?
    var referrals: PlayersReferrals?
    
    init(view: NewReferAndEarnViewProtocol, router: NewReferAndEarnRouterProtocol) {
        self.view = view
        self.router = router
    }
    
    /// send request referral api
    func viewDidLoad() {
        ProfileWorker.shared.getReferrals { [weak self]  result, statusCode in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                guard statusCode != 403 || statusCode != 401 else{
                    return
                }
                guard let response = model.data else{
                    return
                }
                self.referrals = response
                self.tempReferrals = self.referrals
                self.view.reloadData()
            case .failure(_):
                break
            }
        }
    }
    
    /// navigate user from referral
    func onSelectRow(_ index: Int) {
        router.toSubreferrals(id: Int((referrals?.players![index].id!.getValue)!)!, user: (referrals?.players![index])!)
    }
}

extension NewReferAndEarnPresenter: NewReferAndEarnPresenterProtocol {
    
    func popViewController() {
        router.popViewController()
    }
}
