//
//  NewReferAndEarnProtocols.swift
//  PIL
//
//  Created by <PERSON><PERSON> on 24/01/2023.
//

import Foundation

protocol NewReferAndEarnViewProtocol: AnyObject {
    var presenter: NewReferAndEarnPresenterProtocol! { get set }
    func reloadData()
}

protocol NewReferAndEarnPresenterProtocol {
    var referrals: PlayersReferrals? { set get }
    var tempReferrals: PlayersReferrals? { set get }
    func popViewController()
    func viewDidLoad()
    func onSelectRow(_ index: Int)
}

protocol NewReferAndEarnRouterProtocol {
    func popViewController()
    func toSubreferrals(id: Int, user: UserDataModel)
}
