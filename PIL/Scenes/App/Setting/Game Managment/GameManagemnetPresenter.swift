//
//  GameManagemnetPresenter.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import Foundation
import  CoreData
import Zip

struct LocalGames{
    var name:String?
    var sceneName:String?
    var image:String?
    var version:String?
//    var gamedID:String?
    var filePath:String?
    var gameID:String?
    var sizeGame:String?
}

class GameManagemnetPresenter: GameManagemnetPresenterProtocol{

    
    //MARK: - variables
    weak var view: GameManagemnetViewProtocol?
    var interactor: GameManagemnetInputInteractorProtocol?
    var router: GameManagemnetRouterProtocol?
    var error: ErrorProtocol?
    var gamesListCount: Int?
    var localGames = [NSManagedObject]()
    var gamesArray = [LocalGames]()
    var filterArray = [LocalGames]() // to remove  Duplicate item
    var gameDetails:GameDetailsDataModel?
    var indexGame = -1
    
    //MARK: - didLoad
    init (view: GameManagemnetViewProtocol,
              interactor: GameManagemnetInputInteractorProtocol,
              router: GameManagemnetRouterProtocol,
              error: ErrorProtocol){
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    
    
    //MARK: - funcions
    func viewDidLoad(){
        fetchData()
    }
    
    
    //MARK: -  cell configure
    func configure(cell: ManageGameCellViewDelegate, at index: Int){
        cell.setGame(icon: gamesArray[index].image ?? "" , name: gamesArray[index].name ?? "")
        cell.setGame(size: gamesArray[index].sizeGame ?? "" , version: gamesArray[index].version ?? "")
        cell.setRow(Index: index)
    }
    
    /// remove game from local
    func uninstall(at index: Int){
        let sceneName = gamesArray[index].sceneName ?? ""
        print("scene name",sceneName)
        remove(index: index)
    }
    
    /// Update Game if have new version
    func update(at index: Int){
        self.indexGame  = index
        let gameID = gamesArray[index].gameID ?? ""

        self.interactor?.getGameDetails(gameId: gameID)
        print("update" ,gameID , index)

    }
    
    
    
    //MARK: - actions
    func logout(){
        UserModel.shared.logOut()
    }
    
    /// get Data from local storage
    func fetchData(){
        self.gamesArray.removeAll()
        self.filterArray.removeAll()
        guard let appDelegate = UIApplication.shared.delegate as? AppDelegate else {  return }
        let managedContext = appDelegate.persistentContainer.viewContext
        let fetchRequest =    NSFetchRequest<NSManagedObject>(entityName: "Games")
         
         do {
            localGames = try managedContext.fetch(fetchRequest)

            for i in localGames{
                let gameName = i.value(forKey: "name") as? String
                let sceneName = i.value(forKey: "sceneName") as? String
                let image = i.value(forKey: "image") as? String
                let version = i.value(forKey: "version") as? String
                let filePath = i.value(forKey: "filePath") as? String
                let gameID = i.value(forKey: "gameID") as? String
                var size = "10"
                let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let path = "\(documentsUrl)\(sceneName ?? "")".replacingOccurrences(of: "file://",with: "")

                print("version:::" , version)
                let url = NSURL.fileURL(withPath: path)
                print("the url = \(url)")
                do {
                 
                    if let sizeOnDisk = try url.sizeOnDisk() {
                        size = sizeOnDisk
                        print("Size:", sizeOnDisk) // Size: 3.15 GB on disk
                    }
                } catch {
                    print(error)
                }
                
                let game = LocalGames(name: gameName, sceneName: sceneName, image: image, version: version , filePath:filePath,gameID: gameID,sizeGame: size)
                gamesArray.append(game)
            }
         } catch let error as NSError {
           print("Could not fetch. \(error), \(error.userInfo)")
         }
        
        for i in gamesArray.indices { // to remove  Duplicate item
            filterArray.append(gamesArray[i])
        }
        gamesArray = filterArray
        self.gamesListCount = gamesArray.count
        self.view?.reloadTablelist()
        
     }
    
    
    /// remove from local storage
    func remove(index:Int){
        //remove from core data (Local)
        filterArray.remove(at: index)
        let managedContext = (UIApplication.shared.delegate as! AppDelegate).persistentContainer.viewContext
        let note = localGames[index]
        managedContext.delete(note)
        do {
            try managedContext.save()
        } catch let error as NSError {
            print("Error While Deleting Note: \(error.userInfo)")
        }
        
        //Delete Game Folder
        let sceneName = gamesArray[index].sceneName ?? ""
        let documentsUrl = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let path = "\(documentsUrl)\(sceneName)".replacingOccurrences(of: "file://",with: "")

        let fileManager = FileManager()
        do{
            try  fileManager.removeItem(atPath: path)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
        self.fetchData()
     }

    
}

//MARK: - error delegate functions
extension GameManagemnetPresenter: ErrorProtocol{
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
}

//MARK: - interactor output delegate functions
extension GameManagemnetPresenter: GameManagemnetOutputInteractorProtocol{
    ///check if game have new version or not and update it
    func getGameData(data: GameDetailsDataModel) {
        
        self.gameDetails = data
 
        let newVersion = gameDetails?.game?.version ?? ""
        let currentVersion =  gamesArray[self.indexGame].version ?? ""
        print("Current",currentVersion , "New",newVersion)
        
        if newVersion != currentVersion{// if have new version
            let sceneName = gamesArray[self.indexGame].sceneName ?? ""
            print("scene name",sceneName)
            remove(index: self.indexGame)
            DownloadsFIle()
            
        }else{
            print("IT's last version")
            self.view?.lastVersion()
        }
    }
    
    
    //MARK: - download game frist step
    func DownloadsFIle(){
        self.view?.startLoading()

        self.interactor?.createFolderInDownloadsDirectory(name: self.gameDetails?.game?.sceneName ?? "" , files: self.gameDetails?.game?.assetsbundle ?? "" )
    }
    
    
    //MARK:- download zip file if not exist folder step 2
    func FileDownloaded(pathFile:String , destination:String){
        print("File Path is",pathFile , destination)
        let sourceURL = URL(string:pathFile)!
        let destinationURL = URL(string: "\(destination)")!
        do {

            try Zip.unzipFile(sourceURL, destination: destinationURL, overwrite: true, password: nil, progress: { (progress) -> () in
                print("--->",progress)
                if progress == 1{
                    self.removeFile(filePath: sourceURL)
                    self.view?.reloadTablelist()
                    self.save(name: self.gameDetails?.game?.gameName ?? "",
                              sceneName:  self.gameDetails?.game?.sceneName ?? "" ,
                              image:  self.gameDetails?.game?.icons ?? "",
                              version:  self.gameDetails?.game?.version ?? "" ,
                              filePath: destination,
                              gameID:  "\(self.gameDetails?.game?.id ?? 0)")
                    self.fetchData()
                }
            }) // Unzip

        } catch let error {
            print("Extraction of ZIP archive failed with error:\(error)")
        }
    }
    
    func progressFileDownload(progress: Double) {
        self.view?.changeProgress(progress: progress)
        if progress == 1{
            self.view?.stopLoading()
        }
    }
    
    
    // if file is Exist
    func FilesIFExist(path:String){
        print("File Path is",path)
    
    }
    
    //MARK:- remove zip file after download
    func removeFile(filePath:URL){
        let fileManager = FileManager.default
        do{
            try  fileManager.removeItem(at: filePath)
        }catch let error{
            print("Erro remove is",error.localizedDescription)
        }
    }
    
    //MARK: - save game in local
    
    func save(name: String , sceneName:String , image:String , version:String , filePath:String , gameID:String) {
      print("Game ID Local", gameID)
        guard let appDelegate =
                UIApplication.shared.delegate as? AppDelegate else {
            return
        }
      
      // 1
        let managedContext =   appDelegate.persistentContainer.viewContext
      
      // 2
      let entity =  NSEntityDescription.entity(forEntityName: "Games",
                                   in: managedContext)!
      
      let person = NSManagedObject(entity: entity,
                                   insertInto: managedContext)
      
      // 3
        person.setValue(name, forKeyPath: "name")
        person.setValue(sceneName, forKeyPath: "sceneName")
        person.setValue(image, forKeyPath: "image")
        person.setValue(version, forKeyPath: "version")
        person.setValue(filePath, forKey: "filePath")
        person.setValue(gameID, forKey: "gameID")
      // 4
      do {
        try managedContext.save()
          print("Game save successfully in local storage")
      } catch let error as NSError {
        print("Could not save. \(error), \(error.userInfo)")
      }
    }
    

}


extension URL {
    /// check if the URL is a directory and if it is reachable
    func isDirectoryAndReachable() throws -> Bool {
        guard try resourceValues(forKeys: [.isDirectoryKey]).isDirectory == true else {
            return false
        }
        return try checkResourceIsReachable()
    }

    /// returns total allocated size of a the directory including its subFolders or not
    func directoryTotalAllocatedSize(includingSubfolders: Bool = false) throws -> Int? {
        guard try isDirectoryAndReachable() else { return nil }
        if includingSubfolders {
            guard
                let urls = FileManager.default.enumerator(at: self, includingPropertiesForKeys: nil)?.allObjects as? [URL] else { return nil }
            return try urls.lazy.reduce(0) {
                    (try $1.resourceValues(forKeys: [.totalFileAllocatedSizeKey]).totalFileAllocatedSize ?? 0) + $0
            }
        }
        return try FileManager.default.contentsOfDirectory(at: self, includingPropertiesForKeys: nil).lazy.reduce(0) {
                 (try $1.resourceValues(forKeys: [.totalFileAllocatedSizeKey])
                    .totalFileAllocatedSize ?? 0) + $0
        }
    }

    /// returns the directory total size on disk
    func sizeOnDisk() throws -> String? {
        guard let size = try directoryTotalAllocatedSize(includingSubfolders: true) else { return nil }
        URL.byteCountFormatter.countStyle = .file
        guard let byteCount = URL.byteCountFormatter.string(for: size) else { return nil}
        return byteCount + ""
    }
    private static let byteCountFormatter = ByteCountFormatter()
}
