//
//  GameManagemnetVC.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import UIKit
import CoreData

class GameManagemnetVC: UIViewController {
    
    //MARK: - variables
    var presenter: GameManagemnetPresenterProtocol?
    var backItemButton = Components().backNavButton
    var customView = UIView()
    let progressView = UIProgressView(progressViewStyle: .bar)


    //MARK: - outlets
    @IBOutlet weak var gamesTableList: UITableView!
    @IBOutlet weak var noDataLable: UILabel!
    @IBOutlet weak var navigationView: NavigationView!

    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        setXIBs()
        setUpNavigation()
        self.noDataLable.text = "No games have been downloaded".localized
    }
    
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.viewDidLoad()
    }
    
    //MARK: - funcions
 
    /// Cell UI
    func setXIBs(){
        gamesTableList.register(UINib(nibName: "ManageGameCell", bundle: Bundle.main), forCellReuseIdentifier: "ManageGameCell")
    }
    /// Navigation view UI
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Game Management".localized)
    }
    
}



//MARK: - error delegate functions
extension GameManagemnetVC: ErrorProtocol{
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
}


//MARK: - view delegate functions
extension GameManagemnetVC: GameManagemnetViewProtocol{
    /// get list from local storage
    func reloadTablelist(){
        gamesTableList.reloadData()
        if presenter?.gamesListCount ?? 0 == 0{
            self.noDataLable.isHidden = false
        }else{
            self.noDataLable.isHidden = true
        }
    }
    
    func reloadTablecell(at index: Int){
        gamesTableList.reloadRows(at: [IndexPath(row: index, section: 0)], with: .automatic)
    }
    
    
    /// loading game update 
    func startLoading(){
        customView.frame = CGRect.init(x: 0, y: 0, width: UIScreen.main.bounds.width , height: UIScreen.main.bounds.height)
        customView.alpha = 0.9
        customView.backgroundColor = UIColor.black     //give color to the view
        customView.center = self.view.center
        self.view.addSubview(customView)
        
         
        progressView.center = view.center
        progressView.trackTintColor = UIColor.lightGray
        progressView.tintColor = UIColor.blue
        view.addSubview(progressView)
        
        let label = UILabel(frame: CGRect(x: (UIScreen.main.bounds.width/2)-50, y: (UIScreen.main.bounds.height/2)+10, width: 100, height: 21))
        label.textColor = .white
        label.textAlignment = .center
        label.text = "Loading..."

        self.view.addSubview(label)
        
     }
    
    func stopLoading(){
        self.customView.isHidden = true
        self.progressView.isHidden = true
        
    }
    
    func changeProgress(progress:Double){
        progressView.setProgress(Float(progress), animated: true)

    }
     
    func lastVersion(){
        self.showAlert(withTitle: false, msg: "Your version up to date".localized, compilition: nil)
    }
    
}
