//
//  GameManagemnetVC + table delegate.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import UIKit

extension GameManagemnetVC: UITableViewDelegate, UITableViewDataSource{
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.gamesListCount ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ManageGameCell", for: indexPath) as! ManageGameCell
        cell.actionDelegate = self
        presenter?.configure(cell: cell, at: indexPath.row)
        return cell
    }
}

/// actions cell
extension GameManagemnetVC: ManageGameCellActionDelegate{
    func updateGame(at index: Int) {
        presenter?.update(at: index)
    }
    
    func uninstallGame(at index: Int) {
        showConfirmActionsAlert(msg: "uninstallWarning".localized) { [weak self] in
            self?.presenter?.uninstall(at: index)
        }
    }
}
