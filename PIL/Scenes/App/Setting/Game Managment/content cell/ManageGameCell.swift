//
//  ManageGameCell.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import UIKit

protocol ManageGameCellActionDelegate{
    func uninstallGame(at index: Int)
    func updateGame(at index: Int)
}

protocol ManageGameCellViewDelegate{
    func setRow(Index: Int)
    func setGame(icon: String, name: String)
    func setGame(size: String, version: String)
    func hasUpdate(value: Bool)
}

class ManageGameCell: UITableViewCell, ManageGameCellViewDelegate {

    //MARK: - variables
    var rowIndex: Int?
    var actionDelegate: ManageGameCellActionDelegate?
    
    //MARK: - outlets
    @IBOutlet weak var gameImage: UIImageView!
    @IBOutlet weak var gameName: UILabel!
    @IBOutlet weak var gameSize: UILabel!
    @IBOutlet weak var gameVersion: UILabel!
    @IBOutlet weak var uninstallView: UIView!
    @IBOutlet weak var updateView: UIView!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        setTapGesters()
        updateView.isHidden = false
    }
    
    //MARK: - funcions
    func setTapGesters(){
        let uninstallTap = UITapGestureRecognizer(target: self, action: #selector(ManageGameCell.uninstallAction))
        
        uninstallView.addGestureRecognizer(uninstallTap)
        
        let updateTap = UITapGestureRecognizer(target: self, action: #selector(ManageGameCell.updateAction))
        
        updateView.addGestureRecognizer(updateTap)
    }
    
    func setRow(Index: Int){
        rowIndex = Index
    }
    
    func setGame(icon: String, name: String){
        gameImage.setIMG(img: icon)
        gameName.text = name
    }
    
    func setGame(size: String, version: String){
        gameSize.text = size
        gameVersion.text =  "Ver \(version)"
    }
    
    func hasUpdate(value: Bool){
        updateView.isHidden = !value
    }
    
    
    //MARK: - actions
    @objc func uninstallAction(){
        actionDelegate?.uninstallGame(at: rowIndex ?? 0)
    }
    
    @objc func updateAction(){
        actionDelegate?.updateGame(at: rowIndex ?? 0)
    }
}
