//
//  GameManagemnetRouter.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import Foundation

class GameManagemnetRouter: GameManagemnetRouterProtocol{
    
    var VC: GameManagemnetViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .GameST, VC: .GameManagemnetVC)) as! GameManagemnetVC
        let interactor = GameManagemnetInteractor()
        let router = GameManagemnetRouter()
        let gameWorker = GameDetailsWorker()
        let presenter = GameManagemnetPresenter(view: view, interactor: interactor, router: router, error: view)
        view.presenter = presenter
        interactor.error = presenter
        interactor.gamesWorker = gameWorker
        interactor.presenter = presenter
        router.VC = view
        return view
    }
}
