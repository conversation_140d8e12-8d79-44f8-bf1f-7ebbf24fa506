//
//  GameManagemnetProtocols.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import Foundation

protocol GameManagemnetViewProtocol: AnyObject{
    func reloadTablelist()
    func reloadTablecell(at index: Int)
    
    
    func startLoading()
    func stopLoading()
    func changeProgress(progress:Double)
    func lastVersion()
}

protocol GameManagemnetPresenterProtocol: AnyObject{
    var gamesListCount: Int? { get set }
    func viewDidLoad()
    func configure(cell: ManageGameCellViewDelegate, at index: Int)
    func uninstall(at index: Int)
    func update(at index: Int)
    
    func DownloadsFIle()
    func logout()

}

protocol GameManagemnetInputInteractorProtocol{
    func getGameDetails(gameId:String)
    func createFolderInDownloadsDirectory(name:String, files:String)

 }

protocol GameManagemnetOutputInteractorProtocol{
    func getGameData(data:GameDetailsDataModel)
    func FileDownloaded(pathFile:String , destination:String)
    func progressFileDownload(progress:Double)
    func FilesIFExist(path:String)
}

protocol GameManagemnetRouterProtocol{
    
}
