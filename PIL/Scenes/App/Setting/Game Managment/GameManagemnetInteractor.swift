//
//  GameManagemnetInteractor.swift
//  PIL
//
//  Created by mac on 02/01/2022.
//

import Foundation

class GameManagemnetInteractor: GameManagemnetInputInteractorProtocol{
 
    var presenter: GameManagemnetOutputInteractorProtocol?
    var error: ErrorProtocol?
    var gamesWorker: GameDetailsWorkerProtocol?
    private var observation: NSKeyValueObservation?

    deinit {
      observation?.invalidate()
    }
    
    //MARK: - game Details
    func getGameDetails(gameId:String){
        Indicator.shared.showProgressView()
        self.gamesWorker?.getGameDetails(gameID: gameId, compilition:  { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if model.status ?? 0 == 200{
                    if let model = model.data{
                        self.presenter?.getGameData(data: model)
                    }
                }
                break
                
            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
 
    
    
    //MARK: - create folder games
    func createFolderInDownloadsDirectory(name:String, files:String) {
        
        let manager = FileManager.default
        guard let url = manager.urls(for: .documentDirectory,  in: .userDomainMask).first
        else {  return  }
        let newFolderURL = url
        print("path folder",newFolderURL.path)

        if !manager.fileExists(atPath: url.appendingPathComponent(name).path) {
            do {
                
                
                
                let url = URL(string: files )!
                 let task = URLSession.shared.dataTask(with: url)

                 observation = task.progress.observe(\.fractionCompleted) { progress, _ in
                   print("progress: ", progress.fractionCompleted)
                    DispatchQueue.main.async {
                        self.presenter?.progressFileDownload(progress:  progress.fractionCompleted)
                    }
                 }

                 task.resume()
                
                let urlFile = URL(string: files)!
                FileDownloader.loadFileSync(url: urlFile) { (path, error) in
                    print("PDF File downloaded to : \(path!)")
                    self.presenter?.FileDownloaded(pathFile: "\(path!)", destination: newFolderURL.path)
                }

            }catch let error{
                // file is created before open folder
                print("--->",error)

            }
        }else{
            print("Folder is created Before","\(newFolderURL.path)/\(name)")
            self.presenter?.FilesIFExist(path: "\(newFolderURL.path)/\(name)")

        }
        
    }
    
    
}
