<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="GuideCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hFB-hO-39i">
                        <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                    </imageView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="hFB-hO-39i" secondAttribute="trailing" id="439-nn-D14"/>
                <constraint firstItem="hFB-hO-39i" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="T32-xX-U6u"/>
                <constraint firstAttribute="bottom" secondItem="hFB-hO-39i" secondAttribute="bottom" id="bF5-f3-kzg"/>
                <constraint firstItem="hFB-hO-39i" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="oF1-U8-utY"/>
            </constraints>
            <connections>
                <outlet property="guideImage" destination="hFB-hO-39i" id="sQj-Ad-Ar6"/>
            </connections>
            <point key="canvasLocation" x="139" y="98"/>
        </collectionViewCell>
    </objects>
</document>
