//
//  UserGuideVC.swift
//  PIL
//
//  Created by mac on 15/01/2022.
//

import UIKit

class UserGuideVC: UIViewController ,UserGuideViewProtocol ,ErrorProtocol {

    //MARK: - variables
    var presenter: UserGuidePresenterProtocol?
    
    //MARK: - outlets
    @IBOutlet weak var guideCollction: UICollectionView!
    @IBOutlet weak var skipBtn: UIButton!
    
    //MARK: - didLoad
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        skipBtn.underline()
        setXIBs()
        presenter?.viewDidLoad()
    }
    
    func setXIBs(){
        guideCollction.register(UINib(nibName: "GuideCell", bundle: Bundle.main), forCellWithReuseIdentifier: "GuideCell")
    }
    
    
    //MARK: - protocol functions
    func reloadCollection() {
        guideCollction.reloadData()
    }
    
    //MARK: - error delegate fucntions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
    //MARK: - actions
    @IBAction func skipAction(_ sender: UIButton){
        self.navigationController?.popViewController(animated: true)
    }

}

//MARK: - collection delegate functions
extension UserGuideVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout{
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: collectionView.bounds.width,
                      height: collectionView.bounds.height)
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return presenter?.numberOfElements ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GuideCell", for: indexPath) as! GuideCell
        presenter?.configure(cell: cell, index: indexPath.item)
        return cell
    }
    
}
