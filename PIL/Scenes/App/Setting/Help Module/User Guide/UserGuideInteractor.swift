//
//  UserGuideInteractor.swift
//  PIL
//
//  Created by mac on 15/01/2022.
//

import Foundation


class UserGuideInteractor: UserGuideInputInteractorProtocol{
    
    //MARK: - variables
    weak var presenter: UserGuideOutputInteractorProtocol?
    var error: ErrorProtocol?
    var guideWorker: GuideWorkerProtocol?
    
    func getUserGuideImages(){
        guideWorker?.get(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.UserGuideImagesFeatchedSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
}
