//
//  UserGuidePresenter.swift
//  PIL
//
//  Created by mac on 15/01/2022.
//

import Foundation


class UserGuidePresenter: UserGuidePresenterProtocol, ErrorProtocol, UserGuideOutputInteractorProtocol{
    
    //MARK: - variables
    weak var view: UserGuideViewProtocol?
    var interactor: UserGuideInputInteractorProtocol?
    var router: UserGuideRouterProtocol?
    var error: ErrorProtocol?
    
    var numberOfElements: Int?
    var imagesList = [GuideDataModel]()
    
    //MARK: - didload
    init(view: UserGuideViewProtocol,
         interactor: UserGuideInputInteractorProtocol,
         router: UserGuideRouterProtocol,
         error: ErrorProtocol){
        
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    
    //MARK: - functions
    func viewDidLoad() {
        interactor?.getUserGuideImages()
    }
    
    //MARK: - cell configuration
    func configure(cell: GuideCellCiewDelegate, index: Int){
        cell.setCell(img: imagesList[index].image ?? "")
    }
    
    //MARK: - intertactor delegate functuiions
    func UserGuideImagesFeatchedSuccessfully(model: GuideModel) {
        imagesList = model.data ?? []
        numberOfElements = model.data?.count ?? 0
        self.view?.reloadCollection()
    }
    
    //MARK: - error delegate function
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    func logout(){
        UserModel.shared.logOut()
    }
    
}
