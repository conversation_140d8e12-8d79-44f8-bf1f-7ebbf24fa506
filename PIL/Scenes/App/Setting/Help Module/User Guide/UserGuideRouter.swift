//
//  UserGuideRouter.swift
//  PIL
//
//  Created by mac on 15/01/2022.
//

import Foundation

class UserGuideRouter: UserGuideRouterProtocol{
    
    var view: UserGuideViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .Help, VC: .UserGuideVC)) as! UserGuideVC
        let interactor = UserGuideInteractor()
        let router = UserGuideRouter()
        let presenter = UserGuidePresenter(view: view, interactor: interactor, router: router, error: view)
        let guideWorker = GuideWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.guideWorker = guideWorker
        router.view = view
        return view
    }
    
}
