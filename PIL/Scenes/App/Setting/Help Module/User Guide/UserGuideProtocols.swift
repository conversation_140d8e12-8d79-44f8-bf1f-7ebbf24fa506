//
//  UserGuideProtocols.swift
//  PIL
//
//  Created by mac on 15/01/2022.
//

import Foundation


protocol UserGuideViewProtocol: AnyObject{
    func reloadCollection()
}

protocol UserGuidePresenterProtocol{
    var numberOfElements: Int? { get set }
    func viewDidLoad()
    func configure(cell: GuideCellCiewDelegate, index: Int)
    func logout()
}

protocol UserGuideInputInteractorProtocol{
    func getUserGuideImages()
}

protocol UserGuideOutputInteractorProtocol: AnyObject{
    func UserGuideImagesFeatchedSuccessfully(model: GuideModel)
}

protocol UserGuideRouterProtocol{
    
}



