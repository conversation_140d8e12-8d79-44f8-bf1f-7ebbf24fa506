//
//  FAQDetailsVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import UIKit

class FAQDetailsVC: UIViewController {

    //MARK: - outelet
    @IBOutlet var titleQuestionLable: UILabel!
    @IBOutlet var descriptionQuestionLable: UITextView!
    
    
    var backItemButton = Components().backNavButton

    //MARK: - view did laod
    override func viewDidLoad() {
        super.viewDidLoad()

        Style(navigation: true)
     }
    
    //MARK: - view will apper
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        createNavigation()
    }
    

    //MARK: - functions
    func createNavigation(){
        navigationController?.createClearNaviagtion()
        navigationItem.leftBarButtonItems = [UIBarButtonItem(customView: backItemButton)]
        navigationItem.title = "FAQ Details".localized
        backItemButton.removeTarget(nil, action: nil, for: .allEvents)
        backItemButton.addTarget(self, action: #selector(FAQDetailsVC.backAction(_:)), for: .touchUpInside)
    }
    
    @objc func backAction(_ sender: UIButton){
        navigationController?.popViewController(animated: true)
    }
    
    
}
