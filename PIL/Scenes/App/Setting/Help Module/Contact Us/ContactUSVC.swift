//
//  ContactUSVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/4/22.
//

import UIKit
import KMPlaceholderTextView

/*
    choose reson from list
    and write message
 */
extension ContactUSVC: ErrorProtocol{
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
    
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.getOptions()
        }
    }
}


class ContactUSVC: UIViewController,ContactUSViewProtocol {

    //MARK:- variables
    var presenter:ContactUsPresenterProtocol?
    var backItemButton = Components().backNavButton
    var rowHeight: CGFloat = 37.5
    
    //MARK: - outlets
    @IBOutlet weak var contactUsOptionsTableList: UITableView!
    @IBOutlet weak var tableListHeight: NSLayoutConstraint!
    @IBOutlet weak var summaryView: UIView!
    @IBOutlet weak var summaryTF: UITextField!
    @IBOutlet weak var descriptionView: UIView!
    @IBOutlet weak var descriptionTV: KMPlaceholderTextView!
    @IBOutlet weak var navigationView: NavigationView!

    
    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        setUpUI()
        setXIBs()
        setUpNavigation()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        presenter?.getOptions()
    }
    
    //MARK: - functions
    
    func setUpUI(){
        summaryView.shadow(color: shadowColor)
        descriptionView.shadow(color: shadowColor)
        summaryTF.setDirection()
        descriptionTV.setDirection()
    }

    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Contact Us".localized)
    }
    
    
    func setXIBs(){
        contactUsOptionsTableList.register(UINib(nibName: "ContactUsCell", bundle: Bundle.main), forCellReuseIdentifier: "ContactUsCell")
    }
    
    func reloadOptionsTable() {
        guard let optionsCount = presenter?.numberOfOptions else {
            tableListHeight.constant = 1
            return
        }
        tableListHeight.constant = rowHeight * CGFloat(optionsCount)
        contactUsOptionsTableList.reloadData()
    }
    
    func goBack(){
        self.showAlert(withTitle: false, msg: "Added Successfully".localized) {
            self.navigationController?.popViewController(animated: true)
        }
    }
    
    //MARK: - actions

    @IBAction func submitAction(_ sender: UIButton) {
        presenter?.submit(summary: summaryTF.text!, description: descriptionTV.text!)
    }
}
