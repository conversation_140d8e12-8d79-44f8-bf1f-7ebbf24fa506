//
//  ContactUSVC + table delagate.swift
//  PIL
//
//  Created by mac on 09/01/2022.
//

import UIKit

extension ContactUSVC: UITableViewDelegate, UITableViewDataSource{
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return presenter?.numberOfOptions ?? 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ContactUsCell", for: indexPath) as! ContactUsCell
        presenter?.configure(cell: cell, at: indexPath.row)
            
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        presenter?.selectOption(at: indexPath.row)
    }
}
