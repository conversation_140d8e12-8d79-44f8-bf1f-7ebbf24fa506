//
//  ContactUSRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/4/22.
//

import Foundation


class ContactUSRouter:ContactUSRouterProtocol{
    
    var VC : ContactUSViewProtocol?
    
    static func createModule()->UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .ContactUs, VC: .ContactUSVC)) as! ContactUSVC
        let router = ContactUSRouter()
        let interactor = ContactUSInteractor()
        let presenter = ContactUSPresenter(view: view, roter: router, interactor: interactor, error: view)
        let guideWorker = ContactUsWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.guideWorker = guideWorker
        router.VC = view
        return view
    }
}
