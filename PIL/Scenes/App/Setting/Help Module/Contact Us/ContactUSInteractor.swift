//
//  ContactUSInteractor.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/4/22.
//

import Foundation

class ContactUSInteractor: ContactUsInteractorInputProtocol{
    var presenter: ContactUSInteractorOutputProtocol?
    var error: ErrorProtocol?
    var guideWorker: ContactUsWorkerProtocol?
    
    func getAll(){
        guideWorker?.get(compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()

                }else if statusCode == 200 {
                    if let _ = model.data{
                        self.presenter?.getOptionsSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                break

            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
    func createNew(with model: ContactUsRequestModel){
        guideWorker?.create(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
            case .success(let model):
                if statusCode == 403{
                    self.error?.sessionExpired?()

                }else if statusCode == 200 || statusCode == 201{
                    if model.status ?? false {
                        self.presenter?.requestCreatedSuccessfully()
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                }
                break

            case .failure(let error):
                ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                break
            }
        })
    }
    
}
