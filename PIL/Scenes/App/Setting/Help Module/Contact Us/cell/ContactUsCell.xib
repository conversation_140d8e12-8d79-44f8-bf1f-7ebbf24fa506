<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="ContactUsCell" id="KGk-i7-Jjw" customClass="ContactUsCell" customModule="PIL" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="37.5"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="37.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="6" translatesAutoresizingMaskIntoConstraints="NO" id="wan-aw-mu7">
                        <rect key="frame" x="16" y="10" width="288" height="27.5"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="17" translatesAutoresizingMaskIntoConstraints="NO" id="Eii-Be-xcz">
                                <rect key="frame" x="0.0" y="0.0" width="288" height="20.5"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="uncheck" translatesAutoresizingMaskIntoConstraints="NO" id="gJM-pZ-thr">
                                        <rect key="frame" x="0.0" y="2.5" width="16" height="16"/>
                                        <color key="tintColor" name="BaseColor"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="16" id="0KK-OW-deH"/>
                                            <constraint firstAttribute="height" constant="16" id="dph-kJ-zbF"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="imgRadius">
                                                <real key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidthImage">
                                                <real key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColorImage">
                                                <color key="value" name="BaseColor"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lrU-HF-p6Y">
                                        <rect key="frame" x="33" y="0.0" width="255" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vtC-E8-8Rn">
                                <rect key="frame" x="10" y="26.5" width="268" height="1"/>
                                <color key="backgroundColor" red="0.79215693470000004" green="0.79215687509999999" blue="0.79215687509999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="BRg-3g-o4o"/>
                                </constraints>
                            </view>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="vtC-E8-8Rn" secondAttribute="trailing" constant="10" id="PYd-DS-e3B"/>
                            <constraint firstAttribute="trailing" secondItem="Eii-Be-xcz" secondAttribute="trailing" id="fXB-Wc-V1Y"/>
                            <constraint firstItem="Eii-Be-xcz" firstAttribute="leading" secondItem="wan-aw-mu7" secondAttribute="leading" id="ivW-Qw-DNy"/>
                            <constraint firstItem="vtC-E8-8Rn" firstAttribute="leading" secondItem="wan-aw-mu7" secondAttribute="leading" constant="10" id="kEf-4B-fuJ"/>
                        </constraints>
                    </stackView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="wan-aw-mu7" secondAttribute="trailing" constant="16" id="2tj-Jm-QeX"/>
                    <constraint firstAttribute="bottom" secondItem="wan-aw-mu7" secondAttribute="bottom" id="Ldc-Qr-gJH"/>
                    <constraint firstItem="wan-aw-mu7" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="Ngo-8e-zia"/>
                    <constraint firstItem="wan-aw-mu7" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="16" id="mJy-Bz-mRk"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="optionRadioButton" destination="gJM-pZ-thr" id="8K0-Mc-wGl"/>
                <outlet property="optionTitle" destination="lrU-HF-p6Y" id="LMe-I7-EzJ"/>
                <outlet property="separator" destination="vtC-E8-8Rn" id="qum-sD-D0L"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="83.705357142857139"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="uncheck" width="14" height="14"/>
        <namedColor name="BaseColor">
            <color red="0.90200001001358032" green="0.60000002384185791" blue="0.10999999940395355" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
