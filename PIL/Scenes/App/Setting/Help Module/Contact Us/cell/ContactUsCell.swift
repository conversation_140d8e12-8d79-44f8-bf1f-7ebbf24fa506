//
//  ContactUsCell.swift
//  PIL
//
//  Created by mac on 09/01/2022.
//

import UIKit

protocol ContactUsCellViewDelegate{
    func isCell(selected: Bool)
    func setCell(title: String)
    func hideSeparator(value: Bool)
}

class ContactUsCell: UITableViewCell, ContactUsCellViewDelegate {

    //MARK: - outlets
    @IBOutlet weak var optionTitle: UILabel!
    @IBOutlet weak var optionRadioButton: UIImageView!
    @IBOutlet weak var separator: UIView!
    
    
    //MARK: - didLoad
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    
    //MARK: - funcions
    func setCell(title: String) {
        optionTitle.text = title
    }
    
    func isCell(selected: Bool){
        optionRadioButton.tintColor = #colorLiteral(red: 1, green: 0.6588235294, blue: 0.07058823529, alpha: 1)
        optionRadioButton.image = selected ? #imageLiteral(resourceName: "check") : nil
    }
    
    func hideSeparator(value: Bool){
        separator.isHidden = value
    }
}
