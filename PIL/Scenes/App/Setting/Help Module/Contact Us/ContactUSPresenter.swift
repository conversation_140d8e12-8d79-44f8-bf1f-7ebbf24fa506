//
//  ContactUSPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/4/22.
//

import Foundation

extension ContactUSPresenter: ErrorProtocol{
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
}


class ContactUSPresenter: ContactUsPresenterProtocol, ContactUSInteractorOutputProtocol{
    
    //MARK: - variables
    var view:ContactUSViewProtocol?
    var roter:ContactUSRouterProtocol?
    var interactor:ContactUsInteractorInputProtocol?
    var error: ErrorProtocol?
    var numberOfOptions: Int?
    var optionsList = [ContactUsDataModel]()
    var description: String = ""
    
    //MARK: - didLoad
    init(view:ContactUSViewProtocol,
         roter:ContactUSRouterProtocol,
         interactor:ContactUsInteractorInputProtocol,
         error: ErrorProtocol) {
        
        self.view = view
        self.roter = roter
        self.interactor = interactor
        self.error = error
    }
    
    //MARK: - funcions
    func getOptions(){
        interactor?.getAll()
    }
    
    func configure(cell: ContactUsCellViewDelegate, at index: Int) {
        let obj = optionsList[index]
        cell.isCell(selected: obj.selected ?? false)
        cell.setCell(title: obj.name ?? "")
        cell.hideSeparator(value: (index == ((numberOfOptions ?? 0) - 1)))
    }
    
    func selectOption(at index: Int) {
        for item in optionsList.enumerated(){
            item.element.selected = (item.offset == index)
        }
        view?.reloadOptionsTable()
    }
    
    
    //MARK: - delegate functions
    func getOptionsSuccessfully(model: ContactusModel){
        numberOfOptions = model.data?.count
        optionsList = model.data ?? []
        selectOption(at: 0)
    }

    func requestCreatedSuccessfully(){
        let selectedType = optionsList.filter { obj in
            return obj.selected ?? false
        }
        
        if !(selectedType.isEmpty){
            
            GoogleAnalyticsHelper.shared.help_desk_ticket(ticket_type_id: selectedType.first!.id ?? "",
                                                          report_message: description)
        }
        
        view?.goBack()
    }
    
    //MARK: - actions
    func logout(){
        UserModel.shared.logOut()
    }
    
    func submit(summary: String, description: String) {
        if summary.isEmpty{
            error?.featching(error: errors.empty.rawValue.localized)
            return
        }
        
        if description.isEmpty{
            error?.featching(error: errors.empty.rawValue.localized)
            return
        }
        
        self.description = description
        
        guard let selectedType = optionsList.filter ({ item in
            return item.selected ?? false
        }).first else {
            error?.featching(error: errors.empty.rawValue.localized)
            return
        }
        let contactUsRequest = ContactUsRequestModel(summary: summary,
                                                     description: description,
                                                     user_helpdeskid: UserModel.shared.get_HelpdeskId(),
                                                     requestTypeId: Int(selectedType.id ?? "0") ?? 0,
                                                     language: UserModel.shared.getLanguage(),
                                                     user_id: UserModel.shared.get_id())
        
        interactor?.createNew(with: contactUsRequest)
    }
}
