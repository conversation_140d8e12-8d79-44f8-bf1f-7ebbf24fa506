//
//  ContactUSProtocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 1/4/22.
//

import Foundation

protocol ContactUSViewProtocol {
    var presenter:ContactUsPresenterProtocol? {get set}
    func reloadOptionsTable()
    func goBack()
}


protocol ContactUsPresenterProtocol {
    var view:ContactUSViewProtocol? { get set}
    var numberOfOptions: Int? { get set }
    func getOptions()
    func configure(cell: ContactUsCellViewDelegate, at index: Int)
    func selectOption(at index: Int)
    func submit(summary: String, description: String)
    func logout()
}

protocol ContactUsInteractorInputProtocol{
    var presenter:ContactUSInteractorOutputProtocol? {get set}
    func getAll()
    func createNew(with model: ContactUsRequestModel)
}

protocol ContactUSInteractorOutputProtocol{
    func getOptionsSuccessfully(model: ContactusModel)
    func requestCreatedSuccessfully()
}




protocol ContactUSRouterProtocol {
    
}
