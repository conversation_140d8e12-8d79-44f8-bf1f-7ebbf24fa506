//
//  Issue.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 01/04/2023.
//

import Foundation

struct IssusesHistoryResponse: Codable{
    let status: Bool
    let data: IssueResponse?
}

struct IssueResponse: Codable{
    let responsecode: [Issue]
}

struct Issue: Codable{
    let summary: String?
    let description: String?
    let update_at: String?
    let issueId: String?
    let status: String?
}
