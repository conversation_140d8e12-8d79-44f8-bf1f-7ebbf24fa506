//
//  MyHistoryPresnter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class MyHistoryPresnter : myHistoryPresenterProtocol , myHistoryInteractorOutputProtocol {
    
    var presnter: myHistoryInteractorInputProtocol?
    var view: myHistoryViewProtocol?
    var router:myHistoryRouterProtocol?
    var interactor :myHistoryInteractorInputProtocol?
    var history: [Issue]?
    
    init(view:myHistoryViewProtocol ,
         router:myHistoryRouterProtocol ,
         interactor :myHistoryInteractorInputProtocol) {
        self.view = view
        self.interactor = interactor
        self.router = router
    }
    
    
    func viewdidload() {
        interactor?.getIssuesHistory()
    }
    
    func didFetchIssuesHistory(_ data: [Issue]) {
        history = data
        
        view?.onFetchHistory()
    }
    
    
}
