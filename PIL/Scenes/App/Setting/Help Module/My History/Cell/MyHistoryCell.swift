//
//  MyHistoryCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import UIKit

protocol HistoryHelpProtocol{
    
    
}

class MyHistoryCell: UICollectionViewCell {

    
    @IBOutlet weak var setionNameLable:UILabel!
    @IBOutlet weak var BlockedLable:UILabel!
    @IBOutlet weak var lastUpdatedLable:UILabel!
    @IBOutlet weak var numberTicketLable:UILabel!
    @IBOutlet weak var proccessTicketLable:UIButton!

    //proccessTicketLable
    override func awakeFromNib() {
        super.awakeFromNib()
        
    }
    
    func loadFrom(_ data: Issue){
        proccessTicketLable.setTitle(data.status, for: .normal)
        numberTicketLable.text = data.issueId
        setionNameLable.text = data.summary
        BlockedLable.text = data.description
        lastUpdatedLable.text = data.update_at
        
    }

}
