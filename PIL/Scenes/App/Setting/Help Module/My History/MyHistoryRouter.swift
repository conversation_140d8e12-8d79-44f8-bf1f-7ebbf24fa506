//
//  MyHistoryRouter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class MyHistoryRouter:myHistoryRouterProtocol{
    
    weak var VC: UIViewController?

    static func createModule() -> UIViewController{

        let view = SetStoryBoard.controller(controller: Helper.init(Story: .Help, VC: .MyHistoryVC)) as! MyHistoryVC
        let interactor = MyHistoryInteractor()
        let router = MyHistoryRouter()
        let worker = HelpWorker()
        let presenter = MyHistoryPresnter(view: view, router: router, interactor: interactor)
        view.presnter = presenter
        interactor.presenter = presenter
        interactor.helpWorker = worker
        router.VC = view
        return view
    }
    
}
