//
//  MyHistoryPortocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

protocol myHistoryViewProtocol: AnyObject {
    var presnter:myHistoryPresenterProtocol? {get set}
    func onFetchHistory()
}


protocol myHistoryPresenterProtocol {
    var view:myHistoryViewProtocol? {get set}
    var history: [Issue]? {get set}
    func viewdidload()
}


protocol myHistoryInteractorInputProtocol : AnyObject{
    var helpWorker: HelpWorker? {get set}
    func getIssuesHistory()
}


protocol myHistoryInteractorOutputProtocol: AnyObject {
    var presnter:myHistoryInteractorInputProtocol? {get set}
    func didFetchIssuesHistory(_ data: [Issue])
}


protocol myHistoryRouterProtocol {
    
}


