//
//  MyHistoryVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import UIKit

class MyHistoryVC: UIViewController ,myHistoryViewProtocol {
    
  
    //MARK: - varible
    var backItemButton = Components().backNavButton
    var presnter: myHistoryPresenterProtocol?

    //MARK: - outlet
    @IBOutlet var myHistoryCollectionView: UICollectionView!
    
    @IBOutlet weak var emptyyLable: UILabel!
    @IBOutlet weak var navigationView: NavigationView!
    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()
        
        Style(navigation: false)
        setXIBs()
        presnter?.viewdidload()
        emptyyLable.isHidden = true
        setUpNavigation()
    }    
 
    //MARK: - view will apper
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
    }

    //MARK: - functions
 
    
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "My History".localized)
    }
    
     

    func setXIBs(){
        myHistoryCollectionView.delegate = self
        myHistoryCollectionView.dataSource = self
        myHistoryCollectionView.register(UINib(nibName: "MyHistoryCell", bundle: Bundle.main), forCellWithReuseIdentifier: "MyHistoryCell")
    }
    
    func onFetchHistory() {
        myHistoryCollectionView.reloadData()
        if self.presnter?.history?.count ?? 0 == 0{
            self.emptyyLable.isHidden = false
        }
    }

}


extension MyHistoryVC:UICollectionViewDataSource , UICollectionViewDelegate , UICollectionViewDelegateFlowLayout{
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return presnter?.history?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = self.myHistoryCollectionView.dequeueReusableCell(withReuseIdentifier: "MyHistoryCell", for: indexPath) as! MyHistoryCell
        cell.loadFrom((presnter?.history![indexPath.row])!)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if (UIScreen.main.bounds.width) > 500 {
            return CGSize(width:  (UIScreen.main.bounds.width-20)/2, height: 100)
        }else{
            return CGSize(width:  (UIScreen.main.bounds.width-20), height: 100)

        }
    }
    
    
}
