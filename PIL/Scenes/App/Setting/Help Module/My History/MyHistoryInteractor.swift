//
//  MyHistoryInteractor.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class MyHistoryInteractor : myHistoryInteractorInputProtocol{
    
    
    //MARK: - variables
    var presenter:myHistoryInteractorOutputProtocol?
    var helpWorker: HelpWorker?
    
    func getIssuesHistory() {
        helpWorker?.getIssuesList(completion: { [self] result, statusCode in
            guard let statusCode = statusCode else { return }
            switch result {
            case .success(let response):
                if 200...299 ~= statusCode, let data = response.data?.responsecode{
                    presenter?.didFetchIssuesHistory(data)
                }else{
                    presenter?.didFetchIssuesHistory([])
                }
            case .failure(let failure):
                break
            }
        })
    }
    
}
