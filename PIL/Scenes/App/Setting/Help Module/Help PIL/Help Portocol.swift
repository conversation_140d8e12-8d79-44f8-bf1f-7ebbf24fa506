//
//  Help Portocol.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation
protocol HelpViewProtocol: AnyObject {
    var presenter: HelpPresenterProtocol? {get set}
    func reloadQuedstionsList()
    func accountDeletedSuccessfully()
}


protocol HelpPresenterProtocol: AnyObject {
    var view:HelpViewProtocol? {get set}
    func viewDidLoad()
    func getCountQuestions()->Int
    func getCountMore()->Int
    func ConfiguerMore(cell:HelpCellProtocol,index:Int)
    func selectQuestion(at index: Int)
    func configuerQuestions(cell:HelpCellProtocol,index:Int)
    func openHistory()
    func openUserGuide()
    func openContactUs()
    func selectRow(type: PILHelpTopics)
    func logout()
    func sendRequestDeleteAccount()
}

protocol HelpInteractorInputProtocol : AnyObject {
    func getHelpList()
    func getLink(model: PILHelpReuestModel)
    func deleteAccount(model:DeleteAccountReuestModel)
}

protocol HelpInteractorOutputProtocol : AnyObject {
    func getHelpListSuccessfully(model: HelpModel)
    func getLinkSuccessfully(model: HelpModel)
    func getStatusDeleteAccount(status:Bool)

}

protocol HelpRouterProtocol  : AnyObject{
    func openHistory()
    func openWebView(title: String, link: String)
    func openUserGuide()
    func openContactUs()
}
