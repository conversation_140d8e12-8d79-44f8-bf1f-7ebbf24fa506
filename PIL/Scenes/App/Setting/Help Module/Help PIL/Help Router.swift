//
//  Help Router.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class HelpRouter: HelpRouterProtocol {
    
    var VC:HelpViewProtocol?
    
    static func createModule() -> UIViewController{
        let view = SetStoryBoard.controller(controller: Helper(Story: .Help, VC: .helpVC)) as! HelpPILVC
        let router = HelpRouter()
        let interactor = HelpInteractor()
        let presenter = HelpPresenter(view: view, interactor: interactor, router: router, error: view)
        let helpWorker = HelpWorker()
        view.presenter = presenter
        interactor.presenter = presenter
        interactor.error = presenter
        interactor.helpWorker = helpWorker
        router.VC = view
        presenter.view = view

         return view
    }
     
    func openHistory(){ //MyHistoryRouter

        let deposit = MyHistoryRouter.createModule() as! MyHistoryVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(deposit, animated: true)
        }
    }
    
    func openWebView(title: String, link: String) {
        SetStoryBoard.controller(controller: Helper(Story: .Help, VC: .WebViewControllerVC)) { (controller) in
            let viewController = controller as! WebViewController
            viewController.type = .custom(title, link)
            viewController.hidesBottomBarWhenPushed = true
            if let vc = VC as? UIViewController{
                vc.navigationController?.pushViewController(viewController, animated: true)
            }
        }
    }
    
    func openUserGuide(){
        let userGuide = UserGuideRouter.createModule() as! UserGuideVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(userGuide, animated: true)
        }
    }
    
    func openContactUs(){
        let contactus = ContactUSRouter.createModule() as! ContactUSVC
        if let vc = VC as? UIViewController{
            vc.navigationController?.pushViewController(contactus, animated: true)
        }
    }
}
