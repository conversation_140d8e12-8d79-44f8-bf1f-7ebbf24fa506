//
//  HelpPresenter.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class HelpPresenter:HelpPresenterProtocol , HelpInteractorOutputProtocol, ErrorProtocol{

    
    //MARK: - variables
    var view: HelpViewProtocol?
    var interactor: HelpInteractorInputProtocol?
    var error: ErrorProtocol?
    var router: HelpRouterProtocol?
    var askedQuestions = [HelpWebLinkData]()
    var More = ["Privacy Policy".localized,"Terms & Conditions".localized /*, "User Guide".localized */, "Contact Us".localized]
    
    //MARK: - didLoad
    init(view:HelpViewProtocol ,
         interactor: HelpInteractorInputProtocol ,
         router: HelpRouterProtocol,
         error: ErrorProtocol) {
        
        self.view = view
        self.interactor = interactor
        self.router = router
        self.error = error
    }
    
    //MARK: - view did load
    /// get list  of questions
    func viewDidLoad() {
        interactor?.getHelpList()
    }

    
    
    //MARK: - questions section
    func getCountQuestions()->Int{
        return self.askedQuestions.count
    }

    func configuerQuestions(cell:HelpCellProtocol,index:Int){
        let obj = self.askedQuestions[index]
        cell.title(txt: obj.title ?? "")
    }
    
    /// Navigate to webview
    func selectQuestion(at index: Int){
        let obj = self.askedQuestions[index]
        router?.openWebView(title: obj.title ?? "",
                            link: obj.content?.iframeSrc ?? "")
        print("URL WebView",obj.content?.iframeSrc ?? "")
    }
    
    //MARK: - more section
    func getCountMore()->Int{
        return self.More.count
    }
    
    func ConfiguerMore(cell:HelpCellProtocol,index:Int){
        cell.title(txt: self.More[index])
    }
    
    func openHistory(){
        self.router?.openHistory()
    }
    
    func openUserGuide(){
        self.router?.openUserGuide()
    }
    
    func openContactUs(){
        self.router?.openContactUs()
    }
    
    func selectRow(type: PILHelpTopics) {
        interactor?.getLink(model: type.requestModel)
    }
    
    //MARK: - interactor deleagte functions
    ///response
    func getHelpListSuccessfully(model: HelpModel) {
        askedQuestions = model.data?.body ?? []
        view?.reloadQuedstionsList()
    }
    
    /// response link werb view
    func getLinkSuccessfully(model: HelpModel) {
        if let obj = model.data?.body?.first{
            router?.openWebView(title: obj.title ?? "", link: obj.content?.iframeSrc ?? "")
        }
    }
    
    /// request delete account
    func sendRequestDeleteAccount(){ // send requst delete account
        let request = DeleteAccountReuestModel(user_id: UserModel.shared.get_id())
        print("userid",UserModel.shared.get_ID())
        self.interactor?.deleteAccount(model: request)
    }
    
    /// response
    func getStatusDeleteAccount(status: Bool) {
        if status == true{
            UserModel.shared.logOut()
            self.view?.accountDeletedSuccessfully()
        }
    }
    
    //MARK: - error deleagte function
    func featching(error: String) {
        self.error?.featching(error: error)
    }
    
    func sessionExpired() {
        self.error?.sessionExpired?()
    }
    
    func noInternet() {
        self.error?.noInternet?()
    }
    
    func logout(){
        UserModel.shared.logOut()
    }
}
