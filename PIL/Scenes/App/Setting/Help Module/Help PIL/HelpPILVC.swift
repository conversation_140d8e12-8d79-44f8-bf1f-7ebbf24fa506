//
//  HelpPILVC.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/27/21.
//

import UIKit

class HelpPILVC: UIViewController ,HelpViewProtocol, ErrorProtocol{

    //MARK: - varabiles
    var backItemButton = Components().backNavButton
    var presenter: HelpPresenterProtocol?

    //MARK: - outlet
    @IBOutlet var searchTextField: UITextField!
    @IBOutlet var viewSearch: UIView!
    @IBOutlet var viewHistory: UIView!
    @IBOutlet var viewAskedQues: UIView!
    
    @IBOutlet var questionsTableView: UITableView!
    @IBOutlet var hegithQuestions: NSLayoutConstraint!
    @IBOutlet var moreTableView: UITableView!
    @IBOutlet var heightMoreTableView: NSLayoutConstraint!
    @IBOutlet weak var navigationView: NavigationView!
    
    //MARK: - view did load
    override func viewDidLoad() {
        super.viewDidLoad()
        Style(navigation: false)
        setUpUI() /// search View
        setXIBs() /// Cell UI
        setUpNavigation() /// Navigation UI
    }
 
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        countQuestions()/// get question height and update it
        self.presenter?.viewDidLoad() /// Call API
    }
    
    //MARK: - functions
 
    /// Navigation UI
    func setUpNavigation(){
        self.navigationView = navigationView.loadNib() as? NavigationView
        self.navigationView.vc = self
        self.navigationView.selectedAction(actions: [.back , .live , .token])
        self.navigationView.setNavigationTitle(title: "Help".localized)
    }
    
    /// search
    func setUpUI(){
        viewSearch.shadow(color: shadowColor)
    }
    
    /// Cell UI
    func setXIBs(){
        questionsTableView.delegate = self
        questionsTableView.dataSource = self
        questionsTableView.register(UINib(nibName: "HelpCell", bundle: nil), forCellReuseIdentifier: "HelpCell")
        
        moreTableView.delegate = self
        moreTableView.dataSource = self
        moreTableView.register(UINib(nibName: "HelpCell", bundle: nil), forCellReuseIdentifier: "HelpCell")
    }
    
        /// Calculate height Questions
    func countQuestions() {
        let count = self.presenter?.getCountQuestions() ?? 0
        self.hegithQuestions.constant = CGFloat(70*count)+CGFloat(40)
    }
    
    
    /// Calculate height More
    func countMore() {
        let count = self.presenter?.getCountMore() ?? 0
        self.heightMoreTableView.constant = CGFloat(70*count)+CGFloat(40)
    }
    
    //MARK: - view delegate functions
    func reloadQuedstionsList() {
        countQuestions()
        countMore()
        questionsTableView.reloadData()
        if self.presenter?.getCountQuestions() ?? 0 == 0{
            viewAskedQues.isHidden = true
        }
    }
    
    ///  response delete account
    func accountDeletedSuccessfully(){ // success deleted account
        let login = LoginRouter.createModule() as! LoginVC
        let authNav = UINavigationController(rootViewController: login)
        authNav.setNavigationBarHidden(true, animated: false)
        let Delegate = UIApplication.shared.delegate as! AppDelegate
        Delegate.window?.rootViewController = authNav
    }
    
    
    //MARK: - error deleagte functions
    func featching(error: String) {
        showAlert(withTitle: true, msg: error, compilition: nil)
    }
    
    func sessionExpired() {
        loginAlert(compilition: { [weak self] in
            guard let self = self else { return }
            self.presenter?.logout()
        })
    }
     
    func noInternet() {
        noInternet { [weak self] in
            guard let self = self else { return }
            self.presenter?.viewDidLoad()
        }
    }
    
    //MARK: - actions
    /// navigate to issues screen
    @IBAction func myIssuesHistoryButton(_ sender: Any) {
        self.presenter?.openHistory()
    }
    
    @IBAction func viewAllQuestionButton(_ sender: Any) {
        
    }
    
    @objc func backAction(_ sender: UIButton){
        navigationController?.popViewController(animated: true)
    }
    
    /// alert delete account
    @IBAction func deleteAccountBTN(_ sender: Any) {
        self.showConfirmActionsAlert(msg: "Are you sure to Delete your account") {
            self.presenter?.sendRequestDeleteAccount()
        }
    }
    
    
}

