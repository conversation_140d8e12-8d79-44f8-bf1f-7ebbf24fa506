//
//  HelpPIL+TableView.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation
import UIKit

extension HelpPILVC:UITableViewDataSource,UITableViewDelegate {
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
    
    /// ge count two table view
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == questionsTableView {
            return self.presenter?.getCountQuestions() ?? 0
        }else {
            return self.presenter?.getCountMore() ?? 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == questionsTableView {
            let cell = self.questionsTableView.dequeueReusableCell(withIdentifier: "HelpCell", for: indexPath) as! HelpCell
            
            self.presenter?.configuerQuestions(cell: cell, index: indexPath.row)
            return cell
        }else{
            let cell = self.moreTableView.dequeueReusableCell(withIdentifier: "HelpCell", for: indexPath) as! HelpCell
            
            self.presenter?.ConfiguerMore(cell: cell, index: indexPath.row)
            return cell
        }
    }
    
    /// navigate to screen
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        //Questions
        if tableView == questionsTableView {
//            let vc =  self.storyboard?.instantiateViewController(withIdentifier: "FAQDetailsVC") as! FAQDetailsVC
//            self.navigationController?.pushViewController(vc, animated: true)

            presenter?.selectQuestion(at: indexPath.row)
            
            //Policy and terms and condications
         }else if tableView.tag == 1{
//            let vc = self.storyboard?.instantiateViewController(withIdentifier: "WebViewController") as! WebViewController
            if indexPath.row == 0 {
                presenter?.selectRow(type: .policy)
//                self.navigationController?.pushViewController(vc, animated: true)

            }else if indexPath.row == 1{
                presenter?.selectRow(type: .terms)
//                self.navigationController?.pushViewController(vc, animated: true)

//            }else if indexPath.row == 2{
//                self.presenter?.openUserGuide()
//
            }else if indexPath.row == 2{
                self.presenter?.openContactUs()
            }
        }
    }
    
}
