//
//  HelpCell.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/27/21.
//

import UIKit

protocol HelpCellProtocol {
    func title(txt:String)
}
class HelpCell: UITableViewCell ,HelpCellProtocol{

    @IBOutlet weak var titleQuestionLable:UILabel!
    @IBOutlet weak var arrowImgView:UIImageView!
    
    override func awakeFromNib() {
        super.awakeFromNib()
        titleQuestionLable.setDirection()
        arrowImgView.setSideArrow()
        self.selectionStyle = .none
        self.backgroundColor = .clear
    }

    func title(txt:String){
        self.titleQuestionLable.text = txt
    }
    
}
