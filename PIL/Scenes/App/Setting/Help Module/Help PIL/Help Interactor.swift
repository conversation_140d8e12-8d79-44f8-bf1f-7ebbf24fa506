//
//  Help Interactor.swift
//  PIL
//
//  Created by <PERSON><PERSON> saeed on 10/28/21.
//

import Foundation

class HelpInteractor:HelpInteractorInputProtocol{
    
    weak var presenter: HelpInteractorOutputProtocol?
    var error: ErrorProtocol?
    var helpWorker: HelpWorkerProtocol?
    
    
    func getHelpList() {
        helpWorker?.getList(type: .FAQPAGESALL, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getHelpListSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    
    /// get link webview
    func getLink(model: PILHelpReuestModel) {
        helpWorker?.getLink(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getLinkSuccessfully(model: model)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
    
    
    func deleteAccount(model:DeleteAccountReuestModel){
        helpWorker?.deleteAccount(model: model, compilition: { [weak self] (result, statusCode) in
            guard let self = self else { return }
            switch result{
                case .success(let model):
                    if model.status ?? false{
                        self.presenter?.getStatusDeleteAccount(status: model.status ?? false)
                    }else{
                        self.error?.featching(error: model.message ?? "")
                    }
                    break
                
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: statusCode) { (localizedError) in
                    self.error?.featching(error: localizedError)
                } sessionExpired: {
                    self.error?.sessionExpired?()
                } noInternet: {
                    self.error?.noInternet?()
                }
                    break
            }
        })
    }
}
