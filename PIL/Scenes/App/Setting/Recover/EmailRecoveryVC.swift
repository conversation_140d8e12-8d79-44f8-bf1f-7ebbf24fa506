//
//  EmailRecoveryVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 17/03/2024.
//

import UIKit

class EmailRecoveryVC: UIViewController {
    
    @IBOutlet weak var email: UITextField!
    
    var onGetOtp: (String)->Void = { (_) in}

    override func viewDidLoad() {
        super.viewDidLoad()

    }

    @IBAction func closeAction(_ sender: Any) {
        dismiss(animated: false)
    }
    
    @IBAction func otpAction(_ sender: Any) {
        guard email.text != ""  else {return}
        if email.text?.isValidEmail() == true {
            print("Valid email")
            AuthWorker().recoverAccount("email", email.text!) { result, code in
                if code == 401 {
                    let error = try? result.get().message
                    self.showAlert(withTitle: true , msg: error ?? "Please Sign up First" , compilition: nil)
                }else{
                    self.dismiss(animated: false)
                    self.onGetOtp(self.email.text!)
                }
            }
        }else{
            showAlert(withTitle: true , msg: "Email Not Valid" , compilition: nil)
        }
    }

}

