//
//  MobileRecoveryVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 14/03/2024.
//

import UIKit

class MobileRecoveryVC: UIViewController, PhoneCodeKeySelectionDelegate {

    @IBOutlet weak var phoneNumber: UITextField!
    @IBOutlet weak var code: UILabel!
    
    var onGetOtp: (String)->Void = { (_) in}
    
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }
    
    @IBAction func closeAction(_ sender: Any) {
        dismiss(animated: false)
    }
    
    @IBAction func otpAction(_ sender: Any) {
        AuthWorker().recoverAccount("phone", phoneNumber.text!) { result, code in
            self.dismiss(animated: false)
            self.onGetOtp(self.phoneNumber.text!)
//            switch result {
//            case .success(let response):
//                if response.status == true{
//                    self.dismiss(animated: false)
//                    self.onGetOtp(self.phoneNumber.text!)
//                }
//            case .failure(let failure):
//                break
//            }
        }
    }
    
    @IBAction func dialCodeAction(_ sender: Any) {
        let phoneCodeKeys = PhoneCodeKeyRouter.createModule(selectedCodeKey: DialNumbersDataModel(_id: "623d0d277d34e60012e40a2b",
                                                                                                  name: "Egypt",
                                                                                                  alph_code: "EG",
                                                                                                  num_code: "+20")) as! PhoneCodeKeyVC
        phoneCodeKeys.actionDelegate = self
        self.present(phoneCodeKeys, animated: false, completion: nil)
    }
    
    func select(item: DialNumbersDataModel) {
        code.text = item.num_code
    }
    
    
    
}
