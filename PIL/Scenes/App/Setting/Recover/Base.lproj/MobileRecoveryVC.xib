<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="MobileRecoveryVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="code" destination="Pwh-GI-JBJ" id="uVr-Vs-giM"/>
                <outlet property="phoneNumber" destination="APN-si-bHH" id="6rg-vo-3wm"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qLR-eA-tp4">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="IO0-18-FdW">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                    <blurEffect style="regular"/>
                </visualEffectView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bvn-kD-TJ9">
                    <rect key="frame" x="20" y="321" width="353" height="210"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iDU-ui-9S8">
                            <rect key="frame" x="323" y="10" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="AnH-h1-m4j"/>
                                <constraint firstAttribute="width" constant="20" id="GVU-Hp-u1b"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="A_Close Square"/>
                            <connections>
                                <action selector="closeAction:" destination="-1" eventType="touchUpInside" id="s5P-AH-Voe"/>
                            </connections>
                        </button>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="anV-ah-pI0">
                            <rect key="frame" x="10" y="50.333333333333321" width="333" height="69.666666666666686"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mobile Recovery" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RQs-YP-SSN">
                                    <rect key="frame" x="0.0" y="0.0" width="333" height="19.666666666666668"/>
                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="THV-vS-VjU">
                                    <rect key="frame" x="0.0" y="29.666666666666686" width="333" height="40"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="iV9-cH-kKp">
                                            <rect key="frame" x="0.0" y="0.0" width="317" height="40"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="E6y-zr-uC2">
                                                    <rect key="frame" x="0.0" y="0.0" width="60" height="40"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="JSE-B4-9xU">
                                                            <rect key="frame" x="14.666666666666664" y="12" width="31" height="16"/>
                                                            <subviews>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pwh-GI-JBJ">
                                                                    <rect key="frame" x="0.0" y="0.66666666666668561" width="15" height="14.333333333333334"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                                    <color key="textColor" name="Black-White"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.down" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="IPw-i4-BbJ">
                                                                    <rect key="frame" x="15" y="5.3333333333333321" width="16" height="6"/>
                                                                    <color key="tintColor" name="Black-White"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="16" id="TwJ-DM-VbY"/>
                                                                        <constraint firstAttribute="width" constant="16" id="g2F-8R-tIJ"/>
                                                                    </constraints>
                                                                </imageView>
                                                            </subviews>
                                                        </stackView>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="irj-ra-Kcb">
                                                            <rect key="frame" x="0.0" y="0.0" width="60" height="40"/>
                                                            <state key="normal" title="Button">
                                                                <color key="titleColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="dialCodeAction:" destination="-1" eventType="touchUpInside" id="itQ-AB-yps"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="irj-ra-Kcb" firstAttribute="leading" secondItem="E6y-zr-uC2" secondAttribute="leading" id="2Ic-hk-eRk"/>
                                                        <constraint firstAttribute="width" constant="60" id="3gW-LD-izm"/>
                                                        <constraint firstAttribute="bottom" secondItem="irj-ra-Kcb" secondAttribute="bottom" id="Npb-CL-dZP"/>
                                                        <constraint firstItem="JSE-B4-9xU" firstAttribute="centerX" secondItem="E6y-zr-uC2" secondAttribute="centerX" id="R7b-HN-rGr"/>
                                                        <constraint firstItem="JSE-B4-9xU" firstAttribute="centerY" secondItem="E6y-zr-uC2" secondAttribute="centerY" id="V12-h5-o91"/>
                                                        <constraint firstItem="irj-ra-Kcb" firstAttribute="top" secondItem="E6y-zr-uC2" secondAttribute="top" id="Ys8-gX-2wh"/>
                                                        <constraint firstAttribute="trailing" secondItem="irj-ra-Kcb" secondAttribute="trailing" id="t45-Vn-pIw"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                            <real key="value" value="19"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="x5Z-v2-bCH">
                                                    <rect key="frame" x="68" y="5" width="249" height="30"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XcC-5t-Qnl">
                                                            <rect key="frame" x="0.0" y="0.0" width="1" height="30"/>
                                                            <color key="backgroundColor" name="placeHolder"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="1" id="x9o-8K-4yP"/>
                                                            </constraints>
                                                        </view>
                                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter Mobile Number" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="APN-si-bHH">
                                                            <rect key="frame" x="17" y="0.0" width="232" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="30" id="mlc-fT-1he"/>
                                                            </constraints>
                                                            <color key="textColor" name="Black-White"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits" keyboardType="phonePad"/>
                                                        </textField>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="E6y-zr-uC2" firstAttribute="top" secondItem="iV9-cH-kKp" secondAttribute="top" id="L2H-lL-uZ5"/>
                                                <constraint firstAttribute="bottom" secondItem="E6y-zr-uC2" secondAttribute="bottom" id="raD-Al-ZCc"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" name="TF-Placholder"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="iV9-cH-kKp" secondAttribute="trailing" constant="16" id="4ml-dn-J5M"/>
                                        <constraint firstItem="iV9-cH-kKp" firstAttribute="top" secondItem="THV-vS-VjU" secondAttribute="top" id="NR8-bo-mRY"/>
                                        <constraint firstItem="iV9-cH-kKp" firstAttribute="centerY" secondItem="THV-vS-VjU" secondAttribute="centerY" id="YMh-NV-l3M"/>
                                        <constraint firstAttribute="height" constant="40" id="ark-8Y-u5y"/>
                                        <constraint firstItem="iV9-cH-kKp" firstAttribute="leading" secondItem="THV-vS-VjU" secondAttribute="leading" id="lIv-u4-tbm"/>
                                        <constraint firstAttribute="bottom" secondItem="iV9-cH-kKp" secondAttribute="bottom" id="nCz-IB-juO"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="20"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="shadowColorView">
                                            <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                            <color key="value" name="border"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="RQs-YP-SSN" secondAttribute="trailing" id="Jdf-x1-Xwg"/>
                                <constraint firstItem="THV-vS-VjU" firstAttribute="leading" secondItem="anV-ah-pI0" secondAttribute="leading" id="NqL-r0-XrB"/>
                                <constraint firstItem="RQs-YP-SSN" firstAttribute="leading" secondItem="anV-ah-pI0" secondAttribute="leading" id="qwg-2I-BuE"/>
                                <constraint firstAttribute="trailing" secondItem="THV-vS-VjU" secondAttribute="trailing" id="vT4-8b-wjd"/>
                            </constraints>
                        </stackView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G1B-4K-MjD">
                            <rect key="frame" x="198" y="151" width="140" height="44"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="sFC-8B-wmD">
                                    <rect key="frame" x="25.33333333333335" y="14.666666666666686" width="89.666666666666686" height="15"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get OTP" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QAp-5e-KAC">
                                            <rect key="frame" x="0.0" y="0.0" width="64.666666666666671" height="15"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="gRF-TK-Gby" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                            <rect key="frame" x="74.666666666666657" y="2" width="15" height="11.333333333333332"/>
                                            <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="15" id="4bf-JQ-aBI"/>
                                                <constraint firstAttribute="height" constant="15" id="fz1-ST-WXt"/>
                                            </constraints>
                                        </imageView>
                                    </subviews>
                                </stackView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6hD-h4-y6v">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="44"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <connections>
                                        <action selector="otpAction:" destination="-1" eventType="touchUpInside" id="VwV-ay-Okn"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" name="Orange Primary Color"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="44" id="1EV-BW-JNK"/>
                                <constraint firstItem="sFC-8B-wmD" firstAttribute="centerY" secondItem="G1B-4K-MjD" secondAttribute="centerY" id="NUA-We-jWh"/>
                                <constraint firstItem="sFC-8B-wmD" firstAttribute="centerX" secondItem="G1B-4K-MjD" secondAttribute="centerX" id="QZm-sN-3gu"/>
                                <constraint firstItem="6hD-h4-y6v" firstAttribute="leading" secondItem="G1B-4K-MjD" secondAttribute="leading" id="TGi-SW-nDK"/>
                                <constraint firstAttribute="trailing" secondItem="6hD-h4-y6v" secondAttribute="trailing" id="WrP-js-6Li"/>
                                <constraint firstAttribute="width" constant="140" id="ahi-cb-awe"/>
                                <constraint firstItem="6hD-h4-y6v" firstAttribute="top" secondItem="G1B-4K-MjD" secondAttribute="top" id="lAK-pV-2UX"/>
                                <constraint firstAttribute="bottom" secondItem="6hD-h4-y6v" secondAttribute="bottom" id="ssn-zb-Xpm"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="10"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" name="Dialog"/>
                    <constraints>
                        <constraint firstItem="iDU-ui-9S8" firstAttribute="top" secondItem="bvn-kD-TJ9" secondAttribute="top" constant="10" id="2Cg-wz-gLX"/>
                        <constraint firstAttribute="bottom" secondItem="G1B-4K-MjD" secondAttribute="bottom" constant="15" id="LgW-BI-mVi"/>
                        <constraint firstAttribute="trailing" secondItem="anV-ah-pI0" secondAttribute="trailing" constant="10" id="NCC-He-gxo"/>
                        <constraint firstAttribute="height" constant="210" id="PCa-1K-bjb"/>
                        <constraint firstItem="anV-ah-pI0" firstAttribute="centerY" secondItem="bvn-kD-TJ9" secondAttribute="centerY" constant="-20" id="Z7H-Tv-Czr"/>
                        <constraint firstAttribute="trailing" secondItem="G1B-4K-MjD" secondAttribute="trailing" constant="15" id="dHx-VM-ffB"/>
                        <constraint firstAttribute="trailing" secondItem="iDU-ui-9S8" secondAttribute="trailing" constant="10" id="dUG-yu-Dzg"/>
                        <constraint firstItem="anV-ah-pI0" firstAttribute="centerX" secondItem="bvn-kD-TJ9" secondAttribute="centerX" id="k9T-2F-lDe"/>
                        <constraint firstItem="anV-ah-pI0" firstAttribute="leading" secondItem="bvn-kD-TJ9" secondAttribute="leading" constant="10" id="slt-If-3Q6"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                            <color key="value" name="border"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                            <real key="value" value="0.5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="qLR-eA-tp4" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="4ZU-A8-Hus"/>
                <constraint firstItem="qLR-eA-tp4" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="CDi-D5-8Sv"/>
                <constraint firstAttribute="bottom" secondItem="qLR-eA-tp4" secondAttribute="bottom" id="I9z-hG-RSw"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="qLR-eA-tp4" secondAttribute="trailing" id="QFg-aL-cFj"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="bvn-kD-TJ9" secondAttribute="trailing" constant="20" id="i46-Tl-uPo"/>
                <constraint firstItem="bvn-kD-TJ9" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="ioA-A6-GIx"/>
                <constraint firstItem="bvn-kD-TJ9" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="riP-QP-XbJ"/>
            </constraints>
            <point key="canvasLocation" x="13" y="20"/>
        </view>
    </objects>
    <designables>
        <designable name="gRF-TK-Gby">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
    </designables>
    <resources>
        <image name="A_Close Square" width="24" height="24"/>
        <image name="chevron.down" catalog="system" width="128" height="70"/>
        <image name="chevron.right" catalog="system" width="97" height="128"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TF-Placholder">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="placeHolder">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
