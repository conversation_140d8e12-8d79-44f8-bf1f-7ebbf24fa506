<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="EmailRecoveryVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="email" destination="naa-fd-6FL" id="Cgo-33-JDE"/>
                <outlet property="view" destination="hhh-dw-HD4" id="Efd-yE-com"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="hhh-dw-HD4">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EJc-QO-1CB">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="CMx-Yy-atr">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                    <blurEffect style="regular"/>
                </visualEffectView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VAy-lP-t7w">
                    <rect key="frame" x="20" y="321" width="353" height="210"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kWJ-pz-Qvm">
                            <rect key="frame" x="323" y="10" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="20" id="5cD-VH-cfg"/>
                                <constraint firstAttribute="height" constant="20" id="w6O-7g-rKy"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="A_Close Square"/>
                            <connections>
                                <action selector="closeAction:" destination="-1" eventType="touchUpInside" id="zws-sr-9ma"/>
                            </connections>
                        </button>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="3C6-bP-Udi">
                            <rect key="frame" x="10" y="50.333333333333321" width="333" height="69.666666666666686"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Email Recovery" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JfP-6C-Q6O">
                                    <rect key="frame" x="0.0" y="0.0" width="333" height="19.666666666666668"/>
                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ldD-A6-qKy">
                                    <rect key="frame" x="0.0" y="29.666666666666686" width="333" height="40"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" alignment="center" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="IDI-Fa-1pi">
                                            <rect key="frame" x="16" y="0.0" width="301" height="40"/>
                                            <subviews>
                                                <stackView opaque="NO" contentMode="scaleToFill" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Flq-1h-hqQ">
                                                    <rect key="frame" x="0.0" y="5" width="301" height="30"/>
                                                    <subviews>
                                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Enter Email" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="naa-fd-6FL">
                                                            <rect key="frame" x="0.0" y="0.0" width="301" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="30" id="3TX-uu-B2L"/>
                                                            </constraints>
                                                            <color key="textColor" name="Black-White"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits" keyboardType="emailAddress"/>
                                                        </textField>
                                                    </subviews>
                                                </stackView>
                                            </subviews>
                                        </stackView>
                                    </subviews>
                                    <color key="backgroundColor" name="TF-Placholder"/>
                                    <constraints>
                                        <constraint firstAttribute="trailing" secondItem="IDI-Fa-1pi" secondAttribute="trailing" constant="16" id="V28-vs-w0u"/>
                                        <constraint firstItem="IDI-Fa-1pi" firstAttribute="top" secondItem="ldD-A6-qKy" secondAttribute="top" id="VNG-Hv-BFj"/>
                                        <constraint firstAttribute="height" constant="40" id="aMk-h8-doW"/>
                                        <constraint firstItem="IDI-Fa-1pi" firstAttribute="leading" secondItem="ldD-A6-qKy" secondAttribute="leading" constant="16" id="eSZ-gw-9b4"/>
                                        <constraint firstItem="IDI-Fa-1pi" firstAttribute="centerY" secondItem="ldD-A6-qKy" secondAttribute="centerY" id="nHZ-87-Hes"/>
                                        <constraint firstAttribute="bottom" secondItem="IDI-Fa-1pi" secondAttribute="bottom" id="stt-9u-eIr"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="20"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="shadowColorView">
                                            <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                            <real key="value" value="1"/>
                                        </userDefinedRuntimeAttribute>
                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                            <color key="value" name="border"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="ldD-A6-qKy" secondAttribute="trailing" id="EaR-2j-yDH"/>
                                <constraint firstItem="ldD-A6-qKy" firstAttribute="leading" secondItem="3C6-bP-Udi" secondAttribute="leading" id="YyJ-Kd-eE3"/>
                                <constraint firstAttribute="trailing" secondItem="JfP-6C-Q6O" secondAttribute="trailing" id="d2o-Sm-n5i"/>
                                <constraint firstItem="JfP-6C-Q6O" firstAttribute="leading" secondItem="3C6-bP-Udi" secondAttribute="leading" id="e9J-bL-6e9"/>
                            </constraints>
                        </stackView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UJ8-fc-hiM">
                            <rect key="frame" x="198" y="151" width="140" height="44"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="F1H-w4-KAx">
                                    <rect key="frame" x="25.33333333333335" y="14.666666666666686" width="89.666666666666686" height="15"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Get OTP" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="G2Z-am-PWB">
                                            <rect key="frame" x="0.0" y="0.0" width="64.666666666666671" height="15"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="G6P-aW-MXa" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                            <rect key="frame" x="74.666666666666657" y="2" width="15" height="11.333333333333332"/>
                                            <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="width" constant="15" id="T5d-LV-Ypc"/>
                                                <constraint firstAttribute="height" constant="15" id="YEn-16-9Cx"/>
                                            </constraints>
                                        </imageView>
                                    </subviews>
                                </stackView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FRd-DY-xeB">
                                    <rect key="frame" x="0.0" y="0.0" width="140" height="44"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <connections>
                                        <action selector="otpAction:" destination="-1" eventType="touchUpInside" id="9oJ-Ha-7Jf"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" name="Orange Primary Color"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="FRd-DY-xeB" secondAttribute="trailing" id="5X2-52-uju"/>
                                <constraint firstItem="F1H-w4-KAx" firstAttribute="centerY" secondItem="UJ8-fc-hiM" secondAttribute="centerY" id="E9r-lH-HZY"/>
                                <constraint firstAttribute="bottom" secondItem="FRd-DY-xeB" secondAttribute="bottom" id="Lzj-GP-HPp"/>
                                <constraint firstItem="FRd-DY-xeB" firstAttribute="top" secondItem="UJ8-fc-hiM" secondAttribute="top" id="aZK-oS-1Fx"/>
                                <constraint firstAttribute="width" constant="140" id="fb3-08-N1t"/>
                                <constraint firstItem="FRd-DY-xeB" firstAttribute="leading" secondItem="UJ8-fc-hiM" secondAttribute="leading" id="gHS-Km-Ymy"/>
                                <constraint firstItem="F1H-w4-KAx" firstAttribute="centerX" secondItem="UJ8-fc-hiM" secondAttribute="centerX" id="qo0-pT-1fq"/>
                                <constraint firstAttribute="height" constant="44" id="sRV-UF-hMe"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                    <real key="value" value="10"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" name="Dialog"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="210" id="3wu-UA-Qn9"/>
                        <constraint firstItem="3C6-bP-Udi" firstAttribute="centerY" secondItem="VAy-lP-t7w" secondAttribute="centerY" constant="-20" id="FrZ-gT-OI3"/>
                        <constraint firstAttribute="trailing" secondItem="UJ8-fc-hiM" secondAttribute="trailing" constant="15" id="Nei-dR-mBM"/>
                        <constraint firstAttribute="bottom" secondItem="UJ8-fc-hiM" secondAttribute="bottom" constant="15" id="V7P-Cr-mTz"/>
                        <constraint firstAttribute="trailing" secondItem="kWJ-pz-Qvm" secondAttribute="trailing" constant="10" id="YHR-uJ-dnp"/>
                        <constraint firstItem="3C6-bP-Udi" firstAttribute="centerX" secondItem="VAy-lP-t7w" secondAttribute="centerX" id="imp-Ut-ZCQ"/>
                        <constraint firstItem="3C6-bP-Udi" firstAttribute="leading" secondItem="VAy-lP-t7w" secondAttribute="leading" constant="10" id="sSc-RC-gpc"/>
                        <constraint firstItem="kWJ-pz-Qvm" firstAttribute="top" secondItem="VAy-lP-t7w" secondAttribute="top" constant="10" id="vL6-CT-l07"/>
                        <constraint firstAttribute="trailing" secondItem="3C6-bP-Udi" secondAttribute="trailing" constant="10" id="xfx-6D-CH5"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                            <color key="value" name="border"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                            <real key="value" value="0.5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="wm3-K4-QMc"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="VAy-lP-t7w" firstAttribute="centerY" secondItem="hhh-dw-HD4" secondAttribute="centerY" id="K6B-0k-MI8"/>
                <constraint firstItem="wm3-K4-QMc" firstAttribute="trailing" secondItem="VAy-lP-t7w" secondAttribute="trailing" constant="20" id="KGA-fY-eI2"/>
                <constraint firstAttribute="bottom" secondItem="EJc-QO-1CB" secondAttribute="bottom" id="Ntu-w7-xDw"/>
                <constraint firstItem="VAy-lP-t7w" firstAttribute="leading" secondItem="wm3-K4-QMc" secondAttribute="leading" constant="20" id="VGc-Aw-jGB"/>
                <constraint firstItem="EJc-QO-1CB" firstAttribute="top" secondItem="hhh-dw-HD4" secondAttribute="top" id="hsF-XN-a3y"/>
                <constraint firstItem="wm3-K4-QMc" firstAttribute="trailing" secondItem="EJc-QO-1CB" secondAttribute="trailing" id="iNW-jn-hOz"/>
                <constraint firstItem="EJc-QO-1CB" firstAttribute="leading" secondItem="wm3-K4-QMc" secondAttribute="leading" id="vgR-mj-DfN"/>
            </constraints>
            <point key="canvasLocation" x="13" y="20"/>
        </view>
    </objects>
    <designables>
        <designable name="G6P-aW-MXa">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
    </designables>
    <resources>
        <image name="A_Close Square" width="24" height="24"/>
        <image name="chevron.right" catalog="system" width="97" height="128"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TF-Placholder">
            <color red="0.98000001907348633" green="0.98000001907348633" blue="0.98000001907348633" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
