<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="dark"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="RecoverAccountVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="navigation" destination="wfW-gR-5Be" id="Zy3-c1-ix2"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rzi-hi-PKU" customClass="MainBackgroundGradientView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wfW-gR-5Be" customClass="NavigationView" customModule="PIL" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="120"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="120" id="saA-H8-TZz"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Choose a recovery method" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0UC-oT-tmU">
                    <rect key="frame" x="20" y="145" width="195.33333333333334" height="19.333333333333343"/>
                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                    <color key="textColor" name="Black-White"/>
                    <nil key="highlightedColor"/>
                </label>
                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="B3f-2S-ZiG">
                    <rect key="frame" x="15" y="179.33333333333334" width="363" height="116.00000000000003"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y2u-d6-bpe">
                            <rect key="frame" x="0.0" y="0.0" width="363" height="55"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mobile Number" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SQu-wv-6AF">
                                    <rect key="frame" x="0.0" y="18" width="111.33333333333333" height="19.333333333333329"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="sc6-8E-Wau" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="343" y="19.666666666666657" width="20" height="16.333333333333329"/>
                                    <color key="tintColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="XB7-hv-9d1"/>
                                        <constraint firstAttribute="width" constant="20" id="YUZ-xF-E7b"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xf3-fP-u9j">
                                    <rect key="frame" x="0.0" y="0.0" width="363" height="55"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <connections>
                                        <action selector="mobileAction:" destination="-1" eventType="touchUpInside" id="TUz-xQ-XN9"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="Xf3-fP-u9j" firstAttribute="leading" secondItem="Y2u-d6-bpe" secondAttribute="leading" id="15x-DW-nql"/>
                                <constraint firstAttribute="bottom" secondItem="Xf3-fP-u9j" secondAttribute="bottom" id="7Zv-re-fYm"/>
                                <constraint firstItem="sc6-8E-Wau" firstAttribute="centerY" secondItem="Y2u-d6-bpe" secondAttribute="centerY" id="950-6g-WCn"/>
                                <constraint firstAttribute="trailing" secondItem="sc6-8E-Wau" secondAttribute="trailing" id="B4D-dX-RhM"/>
                                <constraint firstAttribute="trailing" secondItem="Xf3-fP-u9j" secondAttribute="trailing" id="ROQ-Ss-Lzz"/>
                                <constraint firstItem="Xf3-fP-u9j" firstAttribute="top" secondItem="Y2u-d6-bpe" secondAttribute="top" id="V7B-d7-nOb"/>
                                <constraint firstItem="SQu-wv-6AF" firstAttribute="centerY" secondItem="Y2u-d6-bpe" secondAttribute="centerY" id="aul-gh-D15"/>
                                <constraint firstAttribute="height" constant="55" id="sWQ-4U-feq"/>
                                <constraint firstItem="SQu-wv-6AF" firstAttribute="leading" secondItem="Y2u-d6-bpe" secondAttribute="leading" id="uJv-tB-a3F"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="C6v-U6-ttJ">
                            <rect key="frame" x="0.0" y="55" width="363" height="3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="3" id="FE2-qH-mfy"/>
                            </constraints>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VZi-xQ-5Gf">
                            <rect key="frame" x="0.0" y="58" width="363" height="55"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Email" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="K1f-FF-zUk">
                                    <rect key="frame" x="0.0" y="18" width="39" height="19.333333333333329"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="tA6-MD-1u5" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="343" y="19.666666666666657" width="20" height="16.333333333333329"/>
                                    <color key="tintColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="20" id="03i-3i-5xs"/>
                                        <constraint firstAttribute="height" constant="20" id="ggH-sw-rdO"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hP9-H7-zvc">
                                    <rect key="frame" x="0.0" y="0.0" width="363" height="55"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <connections>
                                        <action selector="emailAction:" destination="-1" eventType="touchUpInside" id="c3g-Iv-Avb"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="hP9-H7-zvc" secondAttribute="trailing" id="9kW-IA-mv0"/>
                                <constraint firstItem="hP9-H7-zvc" firstAttribute="top" secondItem="VZi-xQ-5Gf" secondAttribute="top" id="Cvq-0d-ZwI"/>
                                <constraint firstItem="K1f-FF-zUk" firstAttribute="centerY" secondItem="VZi-xQ-5Gf" secondAttribute="centerY" id="K05-CZ-gtI"/>
                                <constraint firstItem="tA6-MD-1u5" firstAttribute="centerY" secondItem="VZi-xQ-5Gf" secondAttribute="centerY" id="Qod-39-PBH"/>
                                <constraint firstAttribute="bottom" secondItem="hP9-H7-zvc" secondAttribute="bottom" id="ZuU-86-3ei"/>
                                <constraint firstItem="hP9-H7-zvc" firstAttribute="leading" secondItem="VZi-xQ-5Gf" secondAttribute="leading" id="dtK-5H-EJy"/>
                                <constraint firstAttribute="height" constant="55" id="gmn-Wg-pvQ"/>
                                <constraint firstAttribute="trailing" secondItem="tA6-MD-1u5" secondAttribute="trailing" id="nJW-oX-bpK"/>
                                <constraint firstItem="K1f-FF-zUk" firstAttribute="leading" secondItem="VZi-xQ-5Gf" secondAttribute="leading" id="rJc-5p-zQc"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="D81-Wi-mHk">
                            <rect key="frame" x="0.0" y="112.99999999999997" width="363" height="3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="3" id="dOP-IR-8id"/>
                            </constraints>
                        </imageView>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KSK-e8-T6t">
                            <rect key="frame" x="0.0" y="115.99999999999997" width="363" height="55"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Apple ID" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="U04-Lb-AmM">
                                    <rect key="frame" x="0.0" y="18" width="61.666666666666664" height="19.333333333333329"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="LX5-P4-Aec" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="343" y="19.666666666666686" width="20" height="16.333333333333329"/>
                                    <color key="tintColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="20" id="bBM-Io-heE"/>
                                        <constraint firstAttribute="height" constant="20" id="pbE-ac-dsS"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zlc-Y7-xWr">
                                    <rect key="frame" x="0.0" y="0.0" width="363" height="55"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="U04-Lb-AmM" firstAttribute="leading" secondItem="KSK-e8-T6t" secondAttribute="leading" id="5SP-5d-4f0"/>
                                <constraint firstItem="zlc-Y7-xWr" firstAttribute="leading" secondItem="KSK-e8-T6t" secondAttribute="leading" id="5oS-Nm-umq"/>
                                <constraint firstItem="U04-Lb-AmM" firstAttribute="centerY" secondItem="KSK-e8-T6t" secondAttribute="centerY" id="Dcp-3p-diK"/>
                                <constraint firstAttribute="trailing" secondItem="zlc-Y7-xWr" secondAttribute="trailing" id="MI9-pU-eiX"/>
                                <constraint firstAttribute="trailing" secondItem="LX5-P4-Aec" secondAttribute="trailing" id="PyT-JJ-eJX"/>
                                <constraint firstItem="LX5-P4-Aec" firstAttribute="centerY" secondItem="KSK-e8-T6t" secondAttribute="centerY" id="WXc-7x-9qB"/>
                                <constraint firstAttribute="bottom" secondItem="zlc-Y7-xWr" secondAttribute="bottom" id="q8X-Z1-RFE"/>
                                <constraint firstAttribute="height" constant="55" id="s3V-Of-8Ur"/>
                                <constraint firstItem="zlc-Y7-xWr" firstAttribute="top" secondItem="KSK-e8-T6t" secondAttribute="top" id="u8j-iS-9Lh"/>
                            </constraints>
                        </view>
                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="NRp-PV-9fh">
                            <rect key="frame" x="0.0" y="115.99999999999997" width="363" height="3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="3" id="RF4-4y-q2j"/>
                            </constraints>
                        </imageView>
                        <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Bkh-xw-GVI">
                            <rect key="frame" x="0.0" y="115.99999999999997" width="363" height="55"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Google" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Yu-EG-sU8">
                                    <rect key="frame" x="0.0" y="18" width="52" height="19.333333333333329"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                    <color key="textColor" name="Black-White"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="chevron.right" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="nyZ-sE-Jb4" customClass="LocalizedImage" customModule="PIL" customModuleProvider="target">
                                    <rect key="frame" x="343" y="19.666666666666686" width="20" height="16.333333333333329"/>
                                    <color key="tintColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="33L-DY-MUS"/>
                                        <constraint firstAttribute="width" constant="20" id="f2W-pv-aDO"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="MAp-5O-m2N">
                                    <rect key="frame" x="0.0" y="0.0" width="363" height="55"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="MAp-5O-m2N" secondAttribute="trailing" id="Ag2-Xe-yZt"/>
                                <constraint firstItem="4Yu-EG-sU8" firstAttribute="leading" secondItem="Bkh-xw-GVI" secondAttribute="leading" id="Go4-7O-Y60"/>
                                <constraint firstItem="MAp-5O-m2N" firstAttribute="leading" secondItem="Bkh-xw-GVI" secondAttribute="leading" id="PJ3-pK-puv"/>
                                <constraint firstAttribute="bottom" secondItem="MAp-5O-m2N" secondAttribute="bottom" id="Vkq-4k-cvd"/>
                                <constraint firstItem="4Yu-EG-sU8" firstAttribute="centerY" secondItem="Bkh-xw-GVI" secondAttribute="centerY" id="bjY-sv-0cU"/>
                                <constraint firstAttribute="height" constant="55" id="dLa-8Z-iab"/>
                                <constraint firstAttribute="trailing" secondItem="nyZ-sE-Jb4" secondAttribute="trailing" id="fiN-fg-q04"/>
                                <constraint firstItem="nyZ-sE-Jb4" firstAttribute="centerY" secondItem="Bkh-xw-GVI" secondAttribute="centerY" id="wCA-ZL-vfX"/>
                                <constraint firstItem="MAp-5O-m2N" firstAttribute="top" secondItem="Bkh-xw-GVI" secondAttribute="top" id="zMj-XB-n9u"/>
                            </constraints>
                        </view>
                        <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="A_Divider" translatesAutoresizingMaskIntoConstraints="NO" id="gvo-sV-C2Z">
                            <rect key="frame" x="0.0" y="115.99999999999997" width="363" height="3"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="3" id="tnQ-PO-IYi"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <constraints>
                        <constraint firstItem="C6v-U6-ttJ" firstAttribute="leading" secondItem="B3f-2S-ZiG" secondAttribute="leading" id="OHh-0X-gSa"/>
                        <constraint firstAttribute="trailing" secondItem="C6v-U6-ttJ" secondAttribute="trailing" id="k96-lt-BPz"/>
                    </constraints>
                </stackView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="0UC-oT-tmU" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="20" id="GyP-rQ-4xv"/>
                <constraint firstItem="0UC-oT-tmU" firstAttribute="top" secondItem="wfW-gR-5Be" secondAttribute="bottom" constant="25" id="IqR-bm-2Rk"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="rzi-hi-PKU" secondAttribute="trailing" id="JKI-ty-8Ps"/>
                <constraint firstItem="wfW-gR-5Be" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="Mhz-HA-jmc"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="B3f-2S-ZiG" secondAttribute="trailing" constant="15" id="Q3f-2T-si5"/>
                <constraint firstItem="B3f-2S-ZiG" firstAttribute="top" secondItem="0UC-oT-tmU" secondAttribute="bottom" constant="15" id="Sj0-2M-yNG"/>
                <constraint firstItem="rzi-hi-PKU" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="W4t-cf-hZo"/>
                <constraint firstItem="B3f-2S-ZiG" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" constant="15" id="aNp-pz-Bo7"/>
                <constraint firstAttribute="bottom" secondItem="rzi-hi-PKU" secondAttribute="bottom" id="c3B-MU-aP2"/>
                <constraint firstItem="rzi-hi-PKU" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="cZn-dU-ntr"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="wfW-gR-5Be" secondAttribute="trailing" id="kRP-bX-Dsd"/>
                <constraint firstItem="wfW-gR-5Be" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="pdc-LK-evR"/>
            </constraints>
            <point key="canvasLocation" x="111" y="20"/>
        </view>
    </objects>
    <designables>
        <designable name="LX5-P4-Aec">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
        <designable name="nyZ-sE-Jb4">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
        <designable name="sc6-8E-Wau">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
        <designable name="tA6-MD-1u5">
            <size key="intrinsicContentSize" width="12.666666666666666" height="20.333333333333332"/>
        </designable>
    </designables>
    <resources>
        <image name="A_Divider" width="324" height="1"/>
        <image name="chevron.right" catalog="system" width="97" height="128"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
