<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Poppins-Regular.ttf">
            <string>Poppins-Regular</string>
        </array>
        <array key="cairo_medium.ttf">
            <string>Cairo-Bold</string>
        </array>
        <array key="cairo_regular.ttf">
            <string>Cairo-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="VerifyRecoveryVC" customModule="PIL" customModuleProvider="target">
            <connections>
                <outlet property="resendBtn" destination="xmu-AU-CCL" id="b52-nS-PLk"/>
                <outlet property="timer" destination="Mlq-hn-zoH" id="qU1-e4-EwO"/>
                <outlet property="timerStack" destination="woP-Bh-B9Y" id="tdE-sa-K5S"/>
                <outlet property="typingDigitsTF" destination="VrN-GY-hjq" id="d2E-d9-Zqw"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
                <outlet property="warning" destination="XM9-xu-dLT" id="TJr-Q9-RHW"/>
                <outletCollection property="codeDigits" destination="4Jz-qa-0gk" collectionClass="NSMutableArray" id="o4C-IW-KAf"/>
                <outletCollection property="codeDigits" destination="xy7-6x-Vun" collectionClass="NSMutableArray" id="Dfb-8j-Syl"/>
                <outletCollection property="codeDigits" destination="zHI-jT-T9r" collectionClass="NSMutableArray" id="cPw-n4-LfN"/>
                <outletCollection property="codeDigits" destination="xQk-zC-g7R" collectionClass="NSMutableArray" id="Qj6-fT-dvF"/>
                <outletCollection property="codeDigits" destination="BMu-8l-0UP" collectionClass="NSMutableArray" id="htG-Cu-LPz"/>
                <outletCollection property="codeDigits" destination="y11-Kh-DC6" collectionClass="NSMutableArray" id="Ggq-DS-K7t"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vGS-Nv-Dxr">
                    <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Rz0-Zm-IKA">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                    <blurEffect style="regular"/>
                </visualEffectView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nHP-uq-xDn">
                    <rect key="frame" x="11.666666666666657" y="241" width="370" height="370"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xoG-c8-hBk">
                            <rect key="frame" x="340" y="10" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="irj-j8-Bg8"/>
                                <constraint firstAttribute="width" constant="20" id="pa7-ra-6Nq"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="A_Close Square"/>
                            <connections>
                                <action selector="close:" destination="-1" eventType="touchUpInside" id="LMP-Rk-tFr"/>
                            </connections>
                        </button>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="x5p-0s-e54">
                            <rect key="frame" x="10" y="17.666666666666686" width="350" height="334.66666666666669"/>
                            <subviews>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="50" translatesAutoresizingMaskIntoConstraints="NO" id="g9v-ua-lI4">
                                    <rect key="frame" x="13" y="0.0" width="324" height="208"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="2" translatesAutoresizingMaskIntoConstraints="NO" id="HVO-LV-bic">
                                            <rect key="frame" x="2.3333333333333428" y="0.0" width="319.33333333333326" height="22.666666666666668"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="We Sent A 6-Digital Code To Your Phone" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aP7-nF-SNS">
                                                    <rect key="frame" x="0.0" y="0.0" width="319.33333333333331" height="22.666666666666668"/>
                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="16"/>
                                                    <color key="textColor" name="Black-White"/>
                                                    <color key="highlightedColor" name="black-white"/>
                                                </label>
                                                <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="phone number" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gjb-Um-34J">
                                                    <rect key="frame" x="0.0" y="0.0" width="0.0" height="22.666666666666668"/>
                                                    <fontDescription key="fontDescription" name="Cairo-Regular" family="Cairo" pointSize="12"/>
                                                    <color key="textColor" name="black-white"/>
                                                    <color key="highlightedColor" name="black-white"/>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="947-lr-8CL">
                                            <rect key="frame" x="0.0" y="72.666666666666643" width="324" height="135.33333333333337"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Enter your verification code" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SZV-G9-v1Z">
                                                    <rect key="frame" x="0.0" y="0.0" width="324" height="19.666666666666668"/>
                                                    <fontDescription key="fontDescription" name="Poppins-Regular" family="Poppins" pointSize="14"/>
                                                    <color key="textColor" name="Black-White"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JjY-Jh-Wf1">
                                                    <rect key="frame" x="0.0" y="29.666666666666686" width="324" height="62"/>
                                                    <subviews>
                                                        <stackView opaque="NO" contentMode="scaleToFill" semanticContentAttribute="forceLeftToRight" distribution="fillEqually" spacing="3" translatesAutoresizingMaskIntoConstraints="NO" id="ADq-Qw-Mwf">
                                                            <rect key="frame" x="13" y="9" width="298" height="44"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7kl-gp-FtN">
                                                                    <rect key="frame" x="0.0" y="0.0" width="40" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4Jz-qa-0gk">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="TlX-Nr-cPd"/>
                                                                                <constraint firstAttribute="width" constant="10" id="xaX-08-Ipm"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="4Jz-qa-0gk" firstAttribute="centerY" secondItem="7kl-gp-FtN" secondAttribute="centerY" id="091-a0-g5w"/>
                                                                        <constraint firstItem="4Jz-qa-0gk" firstAttribute="centerX" secondItem="7kl-gp-FtN" secondAttribute="centerX" id="Ovu-8N-tor"/>
                                                                        <constraint firstAttribute="height" constant="44" id="gyQ-M1-U0Z"/>
                                                                        <constraint firstAttribute="width" constant="40" id="wse-n9-hEy"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aIb-gu-hxT">
                                                                    <rect key="frame" x="43.000000000000007" y="0.0" width="39.999999999999993" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xy7-6x-Vun">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="10" id="C8o-Ln-Md7"/>
                                                                                <constraint firstAttribute="height" constant="20" id="StA-sa-dy7"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="xy7-6x-Vun" firstAttribute="centerX" secondItem="aIb-gu-hxT" secondAttribute="centerX" id="Oqb-B2-PO5"/>
                                                                        <constraint firstItem="xy7-6x-Vun" firstAttribute="centerY" secondItem="aIb-gu-hxT" secondAttribute="centerY" id="qgt-4E-xF5"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BHb-fd-Cqd">
                                                                    <rect key="frame" x="86" y="0.0" width="40" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zHI-jT-T9r">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="10" id="IjK-bb-5XZ"/>
                                                                                <constraint firstAttribute="height" constant="20" id="dve-SI-sJX"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="zHI-jT-T9r" firstAttribute="centerX" secondItem="BHb-fd-Cqd" secondAttribute="centerX" id="DtS-fu-Ldj"/>
                                                                        <constraint firstItem="zHI-jT-T9r" firstAttribute="centerY" secondItem="BHb-fd-Cqd" secondAttribute="centerY" id="gPh-AD-aCU"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="-" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bwh-5F-NiP">
                                                                    <rect key="frame" x="129" y="0.0" width="40" height="44"/>
                                                                    <fontDescription key="fontDescription" type="system" pointSize="30"/>
                                                                    <color key="textColor" name="black-white"/>
                                                                    <nil key="highlightedColor"/>
                                                                </label>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mE7-s1-Gmh">
                                                                    <rect key="frame" x="172" y="0.0" width="40" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xQk-zC-g7R">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="10" id="Sc6-xM-IzF"/>
                                                                                <constraint firstAttribute="height" constant="20" id="XFv-jY-nCd"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="xQk-zC-g7R" firstAttribute="centerX" secondItem="mE7-s1-Gmh" secondAttribute="centerX" id="F3H-cC-4qu"/>
                                                                        <constraint firstItem="xQk-zC-g7R" firstAttribute="centerY" secondItem="mE7-s1-Gmh" secondAttribute="centerY" id="gUD-92-Myy"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="g3n-Ct-duy">
                                                                    <rect key="frame" x="215.00000000000003" y="0.0" width="40" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BMu-8l-0UP">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="j2E-LK-DUp"/>
                                                                                <constraint firstAttribute="width" constant="10" id="rsb-wt-Q5U"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="BMu-8l-0UP" firstAttribute="centerX" secondItem="g3n-Ct-duy" secondAttribute="centerX" id="9gg-yB-Fos"/>
                                                                        <constraint firstItem="BMu-8l-0UP" firstAttribute="centerY" secondItem="g3n-Ct-duy" secondAttribute="centerY" id="UHu-yJ-LQn"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eog-gq-tWc">
                                                                    <rect key="frame" x="258" y="0.0" width="40" height="44"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y11-Kh-DC6">
                                                                            <rect key="frame" x="15" y="12" width="10" height="20"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="20" id="7jT-gS-JtZ"/>
                                                                                <constraint firstAttribute="width" constant="10" id="YMJ-uK-aer"/>
                                                                            </constraints>
                                                                            <fontDescription key="fontDescription" type="boldSystem" pointSize="16"/>
                                                                            <color key="textColor" name="Black-White"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" name="TF-Placholder"/>
                                                                    <constraints>
                                                                        <constraint firstItem="y11-Kh-DC6" firstAttribute="centerX" secondItem="eog-gq-tWc" secondAttribute="centerX" id="6Qm-NN-6fX"/>
                                                                        <constraint firstItem="y11-Kh-DC6" firstAttribute="centerY" secondItem="eog-gq-tWc" secondAttribute="centerY" id="K6p-sy-MIr"/>
                                                                    </constraints>
                                                                    <userDefinedRuntimeAttributes>
                                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                                            <color key="value" name="border"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                                                                            <real key="value" value="1"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                                                            <real key="value" value="8"/>
                                                                        </userDefinedRuntimeAttribute>
                                                                    </userDefinedRuntimeAttributes>
                                                                </view>
                                                            </subviews>
                                                        </stackView>
                                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="VrN-GY-hjq">
                                                            <rect key="frame" x="0.0" y="0.0" width="324" height="62"/>
                                                            <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="324" id="cNA-f9-kmp"/>
                                                            </constraints>
                                                            <color key="textColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                            <textInputTraits key="textInputTraits"/>
                                                            <connections>
                                                                <action selector="typeDigits:" destination="-1" eventType="editingChanged" id="e79-bh-Dfr"/>
                                                            </connections>
                                                        </textField>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="62" id="2Xx-ig-YFR"/>
                                                        <constraint firstAttribute="width" constant="324" id="8kT-fN-XIc"/>
                                                        <constraint firstItem="VrN-GY-hjq" firstAttribute="leading" secondItem="JjY-Jh-Wf1" secondAttribute="leading" id="Bb7-qY-QiE"/>
                                                        <constraint firstAttribute="bottom" secondItem="VrN-GY-hjq" secondAttribute="bottom" id="JAv-og-mfK"/>
                                                        <constraint firstItem="ADq-Qw-Mwf" firstAttribute="centerY" secondItem="JjY-Jh-Wf1" secondAttribute="centerY" id="OBA-eO-ks7"/>
                                                        <constraint firstItem="ADq-Qw-Mwf" firstAttribute="centerX" secondItem="JjY-Jh-Wf1" secondAttribute="centerX" id="OLB-Mv-qui"/>
                                                        <constraint firstAttribute="trailing" secondItem="VrN-GY-hjq" secondAttribute="trailing" id="mfb-AL-cwT"/>
                                                        <constraint firstItem="VrN-GY-hjq" firstAttribute="top" secondItem="JjY-Jh-Wf1" secondAttribute="top" id="rtd-mV-dfg"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                                                            <color key="value" red="0.86274509799999999" green="0.86666666670000003" blue="0.88235294119999996" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="If you have not received it, please check  your spam folder" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XM9-xu-dLT">
                                                    <rect key="frame" x="0.0" y="101.66666666666669" width="324" height="33.666666666666657"/>
                                                    <fontDescription key="fontDescription" type="italicSystem" pointSize="14"/>
                                                    <color key="textColor" name="Black-White"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="XM9-xu-dLT" secondAttribute="trailing" id="cY1-at-03e"/>
                                                <constraint firstAttribute="trailing" secondItem="SZV-G9-v1Z" secondAttribute="trailing" id="enJ-qg-t67"/>
                                                <constraint firstItem="XM9-xu-dLT" firstAttribute="leading" secondItem="947-lr-8CL" secondAttribute="leading" id="heb-Lc-zjI"/>
                                                <constraint firstItem="SZV-G9-v1Z" firstAttribute="leading" secondItem="947-lr-8CL" secondAttribute="leading" id="yYG-jm-lkU"/>
                                            </constraints>
                                        </stackView>
                                    </subviews>
                                </stackView>
                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8di-bC-gIH">
                                    <rect key="frame" x="0.0" y="218" width="350" height="44"/>
                                    <subviews>
                                        <stackView opaque="NO" contentMode="scaleToFill" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="SX1-hw-OOc">
                                            <rect key="frame" x="153" y="11.999999999999998" width="44" height="20.333333333333329"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Verify" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Nhg-z2-79r">
                                                    <rect key="frame" x="0.0" y="0.0" width="44" height="20.333333333333332"/>
                                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                        </stackView>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GVJ-Ut-NaN">
                                            <rect key="frame" x="0.0" y="0.0" width="350" height="44"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <connections>
                                                <action selector="verifyAction:" destination="-1" eventType="touchUpInside" id="Jdh-SL-okd"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" name="Orange Primary Color"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="GVJ-Ut-NaN" secondAttribute="bottom" id="A1M-Me-NX7"/>
                                        <constraint firstAttribute="trailing" secondItem="GVJ-Ut-NaN" secondAttribute="trailing" id="DhX-Nv-vT0"/>
                                        <constraint firstAttribute="height" constant="44" id="HuY-8m-ZiR"/>
                                        <constraint firstItem="GVJ-Ut-NaN" firstAttribute="leading" secondItem="8di-bC-gIH" secondAttribute="leading" id="J37-Re-3Uf"/>
                                        <constraint firstItem="GVJ-Ut-NaN" firstAttribute="top" secondItem="8di-bC-gIH" secondAttribute="top" id="Vjv-4i-9dW"/>
                                        <constraint firstItem="SX1-hw-OOc" firstAttribute="centerX" secondItem="8di-bC-gIH" secondAttribute="centerX" id="aHe-xG-JTz"/>
                                        <constraint firstItem="SX1-hw-OOc" firstAttribute="centerY" secondItem="8di-bC-gIH" secondAttribute="centerY" id="pbl-cZ-psL"/>
                                    </constraints>
                                    <userDefinedRuntimeAttributes>
                                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                                            <real key="value" value="10"/>
                                        </userDefinedRuntimeAttribute>
                                    </userDefinedRuntimeAttributes>
                                </view>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xmu-AU-CCL">
                                    <rect key="frame" x="129" y="271.99999999999994" width="92" height="30"/>
                                    <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                    <state key="normal" title="Resend Code">
                                        <color key="titleColor" name="Black-White"/>
                                    </state>
                                    <connections>
                                        <action selector="resendCode" destination="-1" eventType="touchUpInside" id="0BY-ex-RK3"/>
                                    </connections>
                                </button>
                                <stackView opaque="NO" contentMode="scaleToFill" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="woP-Bh-B9Y">
                                    <rect key="frame" x="120.66666666666666" y="311.99999999999994" width="108.66666666666666" height="22.666666666666686"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Re-send After" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="laD-VJ-FPY">
                                            <rect key="frame" x="0.0" y="0.0" width="72.333333333333329" height="22.666666666666668"/>
                                            <fontDescription key="fontDescription" name="Cairo-Bold" family="Cairo" pointSize="12"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="02:00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mlq-hn-zoH">
                                            <rect key="frame" x="77.333333333333314" y="0.0" width="31.333333333333329" height="22.666666666666668"/>
                                            <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                            <color key="textColor" name="Black-White"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="8di-bC-gIH" secondAttribute="trailing" id="A3P-mN-KZP"/>
                                <constraint firstItem="8di-bC-gIH" firstAttribute="leading" secondItem="x5p-0s-e54" secondAttribute="leading" id="h0d-zR-KvX"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" name="Dialog"/>
                    <constraints>
                        <constraint firstItem="x5p-0s-e54" firstAttribute="leading" secondItem="nHP-uq-xDn" secondAttribute="leading" constant="10" id="BtX-eE-8R5"/>
                        <constraint firstAttribute="width" constant="370" id="SLG-GZ-o0P"/>
                        <constraint firstAttribute="trailing" secondItem="x5p-0s-e54" secondAttribute="trailing" constant="10" id="UGM-5j-3QG"/>
                        <constraint firstItem="x5p-0s-e54" firstAttribute="centerX" secondItem="nHP-uq-xDn" secondAttribute="centerX" id="WZz-bT-SRw"/>
                        <constraint firstItem="x5p-0s-e54" firstAttribute="centerY" secondItem="nHP-uq-xDn" secondAttribute="centerY" id="Yn6-lF-19q"/>
                        <constraint firstAttribute="height" constant="370" id="ZaD-tN-kXm"/>
                        <constraint firstItem="xoG-c8-hBk" firstAttribute="top" secondItem="nHP-uq-xDn" secondAttribute="top" constant="10" id="a2Y-l3-lfO"/>
                        <constraint firstAttribute="trailing" secondItem="xoG-c8-hBk" secondAttribute="trailing" constant="10" id="mMw-2U-cOD"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="color" keyPath="borderColorView">
                            <color key="value" name="border"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidthView">
                            <real key="value" value="0.5"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="viewRadius">
                            <real key="value" value="10"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="nHP-uq-xDn" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="2iD-ub-Owr"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="vGS-Nv-Dxr" secondAttribute="trailing" id="5Xg-aG-Ptj"/>
                <constraint firstItem="vGS-Nv-Dxr" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="Cx5-Cw-qnT"/>
                <constraint firstAttribute="bottom" secondItem="vGS-Nv-Dxr" secondAttribute="bottom" id="Ply-mA-Bsp"/>
                <constraint firstItem="vGS-Nv-Dxr" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="ZB6-tP-iey"/>
                <constraint firstItem="nHP-uq-xDn" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="jEy-ZZ-YPr"/>
            </constraints>
            <point key="canvasLocation" x="13" y="20"/>
        </view>
    </objects>
    <resources>
        <image name="A_Close Square" width="24" height="24"/>
        <namedColor name="Black-White">
            <color red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Dialog">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="Orange Primary Color">
            <color red="1" green="0.76899999380111694" blue="0.090000003576278687" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="TF-Placholder">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="black-white">
            <color red="0.32899999618530273" green="0.32400000095367432" blue="0.4779999852180481" alpha="0.33000001311302185" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <namedColor name="border">
            <color red="0.67799997329711914" green="0.67799997329711914" blue="0.67799997329711914" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
    </resources>
</document>
