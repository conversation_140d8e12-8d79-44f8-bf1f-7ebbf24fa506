//
//  RecoverAccountVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 14/03/2024.
//

import UIKit

class RecoverAccountVC: UIViewController {
    
    @IBOutlet weak var navigation: NavigationView!

    override func viewDidLoad() {
        super.viewDidLoad()

        navigation = navigation.loadNib() as? NavigationView
        navigation.vc = self
        navigation.setNavigationTitle(title: "Recovery".localized)
    }

    @IBAction func mobileAction(_ sender: Any) {
        let vc = MobileRecoveryVC.loadFromNib()
        vc.modalPresentationStyle = .overCurrentContext
        vc.onGetOtp = { (phone) in
            let vc = VerifyRecoveryVC.loadFromNib()
            vc.modalPresentationStyle = .overCurrentContext
            vc.device = phone
            vc.type = "phone"
            self.present(vc, animated: false)
        }
        self.present(vc, animated: false)
    }
    
    @IBAction func emailAction(_ sender: Any) {
        let vc = EmailRecoveryVC.loadFromNib()
        vc.modalPresentationStyle = .overCurrentContext
        vc.onGetOtp = { (email) in
            let vc = VerifyRecoveryVC.loadFromNib()
            vc.modalPresentationStyle = .overCurrentContext
            vc.device = email
            vc.type = "email"
            self.present(vc, animated: false)
        }
        self.present(vc, animated: false)
    }
}
