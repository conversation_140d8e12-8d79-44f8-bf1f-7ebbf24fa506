//
//  VerifyRecoveryVC.swift
//  PIL
//
//  Created by <PERSON><PERSON><PERSON> on 14/03/2024.
//

import UIKit
import AdSupport
//import AppTrackingTransparency
import KeychainAccess

class VerifyRecoveryVC: UIViewController {
    
    @IBOutlet weak var phoneTxt: UILabel!
    @IBOutlet weak var verifyBtn: UIButton!
    @IBOutlet weak var resendBtn: UIButton!
    @IBOutlet weak var typingDigitsTFView: UIView!
    @IBOutlet weak var typingDigitsTF: UITextField!
    @IBOutlet var codeDigits: [UILabel]!
    @IBOutlet weak var warning: UILabel!
    @IBOutlet weak var timer: UILabel!
    @IBOutlet weak var timerStack: UIStackView!

    let UUIDValue = UIDevice.current.identifierForVendor!.uuidString
    var IDFA = ""
    var inActiveColor = UIColor(named: "Black-White") ?? .clear
    var errorColor = UIColor(named: "Red") ?? .clear
    var successColor = UIColor(named: "Green") ?? .clear
//    var otp: String?
    var type: String?
    var device: String?
    var timerObj : Timer!
    var min: Int = 1
    var sec: Int = 60
    var verify_secret = ""
    let keychain = Keychain(service: "com.gt.playitleague")
    
    override func viewDidLoad() {
        super.viewDidLoad()
//        requestTrackingAuthorization()
        self.verify_secret = "\(keychain["uniqueId"]?.encryptAES() ?? "")"
//        self.verify_secret = "\(self.UUIDValue)".encryptAES() ?? ""
        print("-----IDFA------>  \(self.UUIDValue)")
        print("------------Crypto------>    \(self.verify_secret)")
        print("------------De---Crypto------>    \(String(describing: self.verify_secret.decryptAES()))")
        typingDigitsTF.keyboardType = .numberPad
        typingDigitsTF.becomeFirstResponder()
        warning.isHidden = type == "phone"
        startTimer()
    }
    
    func openAppSettings() {
        guard let appSettingsURL = URL(string: UIApplication.openSettingsURLString) else { return }
        UIApplication.shared.open(appSettingsURL, options: [:], completionHandler: nil)
    }
    
//    func requestTrackingAuthorization() {
//        if #available(iOS 14.5, *) {
//            ATTrackingManager.requestTrackingAuthorization { status in
//                switch status {
//                case .authorized:
//                    print("Tracking authorization granted.")
//                    // Handle tracking authorization granted
//                    self.IDFA = UserModel.shared.getIDFA()
//                    self.verify_secret = "\(self.IDFA)".encryptAES() ?? ""
//                    print("-----IDFA------>  \(self.IDFA)")
//                    print("------------Crypto------>    \(self.verify_secret)")
//                    print("------------De---Crypto------>    \(String(describing: self.verify_secret.decryptAES()))")
//                case .denied:
//                    print("Tracking authorization denied.")
//                    // Handle tracking authorization denied
//                    let alert = UIAlertController(title: nil, message: "Playit would like to access IDFA for functionality purpose".localized , preferredStyle: .alert)
//                    let action = UIAlertAction(title: "Settings".localized , style: .default) { _ in
//                        self.openAppSettings()
//                    }
//                    alert.addAction(action)
//                    self.present(alert, animated: true)
//                case .notDetermined:
//                    print("Tracking authorization not yet determined.")
//                    // Handle tracking authorization not yet determined
//                    let alert = UIAlertController(title: nil, message: "Playit would like to access IDFA for functionality purpose".localized , preferredStyle: .alert)
//                    let action = UIAlertAction(title: "Settings".localized , style: .default) { _ in
//                        self.openAppSettings()
//                    }
//                    alert.addAction(action)
//                    self.present(alert, animated: true)
//                case .restricted:
//                    print("Tracking authorization restricted.")
//                    // Handle tracking authorization restricted
//                    let alert = UIAlertController(title: nil, message: "Playit would like to access IDFA for functionality purpose".localized , preferredStyle: .alert)
//                    let action = UIAlertAction(title: "Settings".localized , style: .default) { _ in
//                        self.openAppSettings()
//                    }
//                    alert.addAction(action)
//                    self.present(alert, animated: true)
//                @unknown default:
//                    print("Unknown tracking authorization status.")
//                    let alert = UIAlertController(title: nil, message: "Playit would like to access IDFA for functionality purpose".localized , preferredStyle: .alert)
//                    let action = UIAlertAction(title: "Settings".localized , style: .default) { _ in
//                        self.openAppSettings()
//                    }
//                    alert.addAction(action)
//                    self.present(alert, animated: true)
//                }
//            }
//        } else {
//            // Fallback on earlier versions
//            print("Tracking authorization not available.")
//        }
//    }
    
    func featching(error: String) {
        showAlert(withTitle: false, msg: error, compilition: nil)
    }
    
    @IBAction func verifyAction(_ sender: Any) {
        if verify_secret != "" {
            AuthWorker.shared.verifyRecoverAccount(type ?? "", device ?? "", verify_secret , typingDigitsTF.text ?? "") { result, status in
                switch result {
                case .success(let response):
                    if let user = response.user{
                        UserModel.shared.setReferralCode(code: user.referralCode ?? "")
                        UserModel.shared.loginAsGuest(status: false)
                        UserModel.shared.setData(model: user)
                        UserModel.shared.setLogin(value: true)
                        UserModel.shared.setFirstTimeV(value: true)
                        let TBC = SetStoryBoard.controller(controller: Helper(Story: .AppST, VC: .TBC))
                        let Delegate = UIApplication.shared.delegate as! AppDelegate
                        Delegate.window?.rootViewController = TBC
                    }
                case .failure(let error):
                    ErrorHandler.handler(error: error, statusCode: status) { (localizedError) in
                        self.featching(error: localizedError)
                    } sessionExpired: {
    //                    self.sessionExpired()
                    } noInternet: {
    //                    self.noInternet()
                    }
                    break
                }
            }
        }else{
            let alert = UIAlertController(title: nil, message: "Playit would like to access IDFA for functionality purpose".localized , preferredStyle: .alert)
            let action = UIAlertAction(title: "Settings".localized , style: .default) { _ in
                self.openAppSettings()
            }
            alert.addAction(action)
            self.present(alert, animated: true)
        }
    }
    
    @IBAction func resendCode(){
        AuthWorker().recoverAccount(type ?? "", device ?? "") { result, code in
            self.typingDigitsTF.text = ""
            self.setCode(code: "")
            for item in self.codeDigits{
                item.textColor = self.inActiveColor
            }
            self.typingDigitsTF.becomeFirstResponder()
            self.startTimer()
        }
    }
    
    
    @IBAction func close(_ sender: Any) {
        dismiss(animated: false)
    }
    
    @IBAction func typeDigits(_ sender: UITextField) {
        setCode(code: sender.text!)
    }
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        if textField == typingDigitsTF{
            if textField.text!.count > 6{
                endEditing()
            }
        }
    }
    
    func startTimer(){
        timer.text = "02:00"
        min = 1
        sec = 60
        timerStack.isHidden = false
        resendBtn.isHidden = true
        timerObj = Timer()
        timerObj = Timer.scheduledTimer(withTimeInterval: 1, repeats: true, block: { [weak self] (timer) in
            guard let self = self else { return }
            if self.sec == 0 && self.min != 0{
                self.sec = 60
                self.min-=1
            }else if self.sec == 0 && self.min == 0{
                timer.invalidate()
                self.timerStack.isHidden = true
                self.resendBtn.isHidden = false
            }
            self.sec-=1
            
            var secTxt = ""
            
            self.sec<10 ? (secTxt = "0\(self.sec)") : (secTxt = "\(self.sec)")
            
            self.timer.text = "0\(self.min):\(secTxt)"
        })
    }
    
    func setCode(code: String){
        let digits = code.map{String($0)}
        for index in 0..<6{
            if index <= (digits.count-1){
                codeDigits[index].text = digits[index]
            }else{
                codeDigits[index].textColor = inActiveColor
                codeDigits[index].text = ""
            }
        }
        
        if digits.count == 6{
            endEditing()
            let characters = Array(code)
            var code = ""
            for item in 0..<characters.count{
                self.codeDigits[item].text = "\(characters[item])"
                code+="\(characters[item])"
                self.typingDigitsTF.text = code
                
            }
        }
    }
    
}
