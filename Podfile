# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'

#source 'https://github.com/CocoaPods/Specs.git'

post_install do |installer|
  installer.pods_project.build_configurations.each do |config|
    config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    config.build_settings['ENABLE_BITCODE'] = 'NO'
  end
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      config.build_settings['ENABLE_BITCODE'] = 'NO'

      # Force disable bitcode for all frameworks
      config.build_settings['BITCODE_GENERATION_MODE'] = ''
      config.build_settings['ENABLE_BITCODE'] = 'NO'
    end
  end

  # Additional step to strip bitcode from frameworks
  installer.pods_project.targets.each do |target|
    if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.framework"
      target.build_configurations.each do |config|
        config.build_settings['BITCODE_GENERATION_MODE'] = ''
        config.build_settings['ENABLE_BITCODE'] = 'NO'
      end
    end
  end
end

target 'PIL' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!
  
  
  pod 'SDWebImage'
  pod 'SDWebImageSVGKitPlugin'
  pod 'Alamofire', '~> 5.0.0-rc.3'
  pod 'lottie-ios' , '~> 3.4'
  pod 'IQKeyboardManager'
  pod 'MOLH'
  pod 'KMPlaceholderTextView', '~> 1.4.0'
  pod 'Cosmos', '~> 23.0'
  
  pod 'Zip' , '~> 2.1'
  pod 'TYProgressBar'
  pod 'Kingfisher', '~> 7.0'
  pod 'TransitionRouter'
  
#  pod 'FacebookSDK'#, '~> 11.2.1'
#  pod 'FBSDKLoginKit'
  pod 'FBSDKLoginKit'#, '~> 11.2.1'
 # pod 'FacebookSDK/ShareKit', '~> 11.2.1'
 # pod 'FacebookSDK/PlacesKit', '~> 11.2.1'
  pod 'FBSDKCoreKit'
  pod 'FBSDKMessengerShareKit'
#  pod 'FBAudienceNetwork'
  
  pod 'Firebase'
  pod 'FirebaseCore'
  pod 'FirebaseMessaging'
  pod 'FirebaseDatabase'
  pod 'CodableFirebase'
  pod 'FirebaseCrashlytics'
  pod 'FirebaseAnalytics'
  pod 'FirebaseDynamicLinks'
  pod 'FirebaseAuth'
  pod 'GoogleSignIn'
  pod 'DropDown'
  pod 'KeychainAccess'
  
  pod 'Google-Mobile-Ads-SDK'
  
  #agora
  pod 'Floaty', '~> 4.2.0'
  pod 'AGEVideoLayout', '~> 1.0.2'
  pod 'AgoraRtcEngine_iOS', '3.4.2'
  
  pod 'ContextLabel'
  pod 'netfox'
  
  pod 'GoogleMaps'
  pod 'GooglePlaces'
  pod 'AlertTransition/Easy', "~> 2.1.0"
  pod 'AnimatableReload'
  pod 'SwiftPopup'
  
  pod 'ActiveLabel'
  pod 'HMSegmentedControl'
#  pod 'SwiftSignalRClient'
  pod 'CropViewController'
  pod 'KDCircularProgress'
#  pod 'SSZipArchive'
pod 'NotificationBannerSwift', '~> 3.0.0'
pod 'PINRemoteImage'

pod 'AppLovinSDK'

#pod 'SnapSDK', :subspecs => ['SCSDKLoginKit','SCSDKBitmojiKit']
pod 'SnapSDK', :subspecs => ['SCSDKLoginKit', 'SCSDKCreativeKit', 'SCSDKBitmojiKit']


#pod 'AppLovinSDK'
#pod 'AppLovinMediationGoogleAdManagerAdapter'
#pod 'AppLovinMediationInMobiAdapter'
#pod 'AppLovinMediationUnityAdsAdapter'
#pod 'AppLovinMediationAdColonyAdapter'
#pod 'AppLovinMediationTapjoyAdapter'
#pod 'AppLovinMediationTencentGDTAdapter'
#pod 'AppLovinMediationVungleAdapter'
#pod 'AppLovinMediationIronSourceAdapter'
#pod 'AppLovinMediationChartboostAdapter'
#pod 'AppLovinMediationMyTargetAdapter'
#pod 'AppLovinMediationSmaatoAdapter'
#pod 'AppLovinMediationGoogleAdapter'
#pod 'AppLovinMediationByteDanceAdapter'


end
