PODS:
  - ActiveLabel (1.1.0)
  - AGEVideoLayout (1.0.4)
  - AgoraRtcEngine_iOS (3.4.2)
  - Alamofire (5.0.5)
  - AlertTransition/Core (2.1.0)
  - AlertTransition/Easy (2.1.0):
    - AlertTransition/Core
  - AnimatableReload (0.2.0)
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - AppLovinSDK (12.6.1)
  - CocoaLumberjack (3.8.5):
    - CocoaLumberjack/Core (= 3.8.5)
  - CocoaLumberjack/Core (3.8.5)
  - CodableFirebase (0.2.1)
  - ContextLabel (1.6.0)
  - Cosmos (23.0.0)
  - CropViewController (2.7.4)
  - DropDown (2.3.13)
  - FBAEMKit (17.0.2):
    - FBSDKCoreKit_Basics (= 17.0.2)
  - FBSDKCoreKit (17.0.2):
    - FBAEMKit (= 17.0.2)
    - FBSDKCoreKit_Basics (= 17.0.2)
  - FBSDKCoreKit_Basics (17.0.2)
  - FBSDKLoginKit (17.0.2):
    - FBSDKCoreKit (= 17.0.2)
  - FBSDKMessengerShareKit (1.3.2)
  - Firebase (10.27.0):
    - Firebase/Core (= 10.27.0)
  - Firebase/Core (10.27.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.27.0)
  - Firebase/CoreOnly (10.27.0):
    - FirebaseCore (= 10.27.0)
  - FirebaseAnalytics (10.27.0):
    - FirebaseAnalytics/AdIdSupport (= 10.27.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.27.0)
  - FirebaseAuth (10.27.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.27.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.27.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.27.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.27.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseDatabase (10.27.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - FirebaseSharedSwift (~> 10.0)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - leveldb-library (~> 1.22)
  - FirebaseDynamicLinks (10.27.0):
    - FirebaseCore (~> 10.0)
  - FirebaseInstallations (10.27.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.27.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfigInterop (10.27.0)
  - FirebaseSessions (10.27.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (10.27.0)
  - Floaty (4.2.0)
  - Google-Mobile-Ads-SDK (11.5.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (10.27.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.27.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.27.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.27.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GooglePlaces (8.5.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUserMessagingPlatform (2.4.0)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.4.1)
  - HMSegmentedControl (1.5.6)
  - IQKeyboardManager (6.5.19)
  - KDCircularProgress (1.5.4)
  - KeychainAccess (4.2.2)
  - Kingfisher (7.12.0)
  - KMPlaceholderTextView (1.4.0)
  - leveldb-library (1.22.5)
  - lottie-ios (3.5.0)
  - MarqueeLabel (4.0.5)
  - MOLH (1.4.3)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - netfox (1.21.0)
  - NotificationBannerSwift (3.0.6):
    - MarqueeLabel (~> 4.0.5)
    - SnapKit (~> 5.0.1)
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - PINRemoteImage (3.0.4):
    - PINRemoteImage/PINCache (= 3.0.4)
  - PINRemoteImage/Core (3.0.4):
    - PINOperation
  - PINRemoteImage/PINCache (3.0.4):
    - PINCache (~> 3.0.4)
    - PINRemoteImage/Core
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RecaptchaInterop (100.0.0)
  - SDWebImage (5.19.2):
    - SDWebImage/Core (= 5.19.2)
  - SDWebImage/Core (5.19.2)
  - SDWebImageSVGKitPlugin (1.4.0):
    - SDWebImage/Core (~> 5.10)
    - SVGKit (~> 3.0)
  - SnapKit (5.0.1)
  - SnapSDK/SCSDKBitmojiKit (1.15.0):
    - PINCache (< 3.1, >= 2.3)
    - PINOperation (~> 1.2.1)
    - SnapSDK/SCSDKCoreKit
  - SnapSDK/SCSDKCoreKit (1.15.0)
  - SnapSDK/SCSDKCreativeKit (1.15.0):
    - SnapSDK/SCSDKCoreKit
  - SnapSDK/SCSDKLoginKit (1.15.0):
    - SnapSDK/SCSDKCoreKit
  - SVGKit (3.0.0):
    - CocoaLumberjack (~> 3.0)
  - SwiftPopup (5.0.0):
    - SnapKit (~> 5.0.0)
  - TransitionRouter (0.2.0)
  - TYProgressBar (0.8.1)
  - Zip (2.1.2)

DEPENDENCIES:
  - ActiveLabel
  - AGEVideoLayout (~> 1.0.2)
  - AgoraRtcEngine_iOS (= 3.4.2)
  - Alamofire (~> 5.0.0-rc.3)
  - AlertTransition/Easy (~> 2.1.0)
  - AnimatableReload
  - AppLovinSDK
  - CodableFirebase
  - ContextLabel
  - Cosmos (~> 23.0)
  - CropViewController
  - DropDown
  - FBSDKCoreKit
  - FBSDKLoginKit
  - FBSDKMessengerShareKit
  - Firebase
  - FirebaseAnalytics
  - FirebaseAuth
  - FirebaseCore
  - FirebaseCrashlytics
  - FirebaseDatabase
  - FirebaseDynamicLinks
  - FirebaseMessaging
  - Floaty (~> 4.2.0)
  - Google-Mobile-Ads-SDK
  - GoogleMaps
  - GooglePlaces
  - GoogleSignIn
  - HMSegmentedControl
  - IQKeyboardManager
  - KDCircularProgress
  - KeychainAccess
  - Kingfisher (~> 7.0)
  - KMPlaceholderTextView (~> 1.4.0)
  - lottie-ios (~> 3.4)
  - MOLH
  - netfox
  - NotificationBannerSwift (~> 3.0.0)
  - PINRemoteImage
  - SDWebImage
  - SDWebImageSVGKitPlugin
  - SnapSDK/SCSDKBitmojiKit
  - SnapSDK/SCSDKCreativeKit
  - SnapSDK/SCSDKLoginKit
  - SwiftPopup
  - TransitionRouter
  - TYProgressBar
  - Zip (~> 2.1)

SPEC REPOS:
  trunk:
    - ActiveLabel
    - AGEVideoLayout
    - AgoraRtcEngine_iOS
    - Alamofire
    - AlertTransition
    - AnimatableReload
    - AppAuth
    - AppLovinSDK
    - CocoaLumberjack
    - CodableFirebase
    - ContextLabel
    - Cosmos
    - CropViewController
    - DropDown
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FBSDKMessengerShareKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseDatabase
    - FirebaseDynamicLinks
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - Floaty
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GooglePlaces
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HMSegmentedControl
    - IQKeyboardManager
    - KDCircularProgress
    - KeychainAccess
    - Kingfisher
    - KMPlaceholderTextView
    - leveldb-library
    - lottie-ios
    - MarqueeLabel
    - MOLH
    - nanopb
    - netfox
    - NotificationBannerSwift
    - PINCache
    - PINOperation
    - PINRemoteImage
    - PromisesObjC
    - PromisesSwift
    - RecaptchaInterop
    - SDWebImage
    - SDWebImageSVGKitPlugin
    - SnapKit
    - SnapSDK
    - SVGKit
    - SwiftPopup
    - TransitionRouter
    - TYProgressBar
    - Zip

SPEC CHECKSUMS:
  ActiveLabel: 5e3f4de79a1952d4604b845a0610d4776e4b82b3
  AGEVideoLayout: a0122fa3fde4fac227b08da8bee0c3fe8de1682c
  AgoraRtcEngine_iOS: 3a4d64f2866ffc122e090a693153bb673021f6fe
  Alamofire: df2f8f826963b08b9a870791ad48e07a10090b2e
  AlertTransition: a5ebd60b8858b2530be539686bd301ec61e5634f
  AnimatableReload: 9576d3019bfb103f76b5f37fa6f8915bd8307527
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  AppLovinSDK: a892bbeff744749a8121bd863aa1399f7eef6ef1
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  CodableFirebase: 0bf201991eeec35eb9fcd987d271ea463cf34a80
  ContextLabel: 4cd5c6415f9484c6d86e139fee3b4e4a50fef571
  Cosmos: b5c6a9a637e28a061b54c8f8b1d8529d7d4db73f
  CropViewController: 3489bbf95a3e11c654382b0bae08ac645cdf1b93
  DropDown: 8a2116376c1981888557f72ec2ffc9a5e0e456ec
  FBAEMKit: 619f96ea65427e8afca240d5b0f4703738dfdf5c
  FBSDKCoreKit: a5f384db2e9ee84e98494fed8f983d2bd79accff
  FBSDKCoreKit_Basics: d35c775aaf243a2d731dfae7be3a74b1987285ab
  FBSDKLoginKit: f8ca5f7ab7c4e5b93e729d94975b0db7fcc511ed
  FBSDKMessengerShareKit: bdd14265f5845e6d1c95a128ab33705a68ca1c21
  Firebase: 26b040b20866a55f55eb3611b9fcf3ae64816b86
  FirebaseAnalytics: f9211b719db260cc91aebee8bb539cb367d0dfd1
  FirebaseAppCheckInterop: 0dd062c9926a76332ca5711dbed6f1a9ac540b54
  FirebaseAuth: 77a012b7e08042bf44d0db835ca2e86e6ca7bbd3
  FirebaseCore: a2b95ae4ce7c83ceecfbbbe3b6f1cddc7415a808
  FirebaseCoreExtension: 4ec89dd0c6de93d6becde32122d68b7c35f6bf5d
  FirebaseCoreInternal: 4b297a2d56063dbea2c1d0d04222d44a8d058862
  FirebaseCrashlytics: 81ea6ec96519388687f6061beb838a8eec482293
  FirebaseDatabase: 0e7f9f98f425a92b16a06cf9ca09810456e2dc80
  FirebaseDynamicLinks: 087bf18ffabe8b6abe0c80301fd16ba2225ce373
  FirebaseInstallations: 766dabca09fd94aef922538aaf144cc4a6fb6869
  FirebaseMessaging: 585984d0a1df120617eb10b44cad8968b859815e
  FirebaseRemoteConfigInterop: c55a739f5ab121792776e191d9fd437dc624a541
  FirebaseSessions: 2fdf949f9e58295a57703ae8f2efc44f9fa3aa16
  FirebaseSharedSwift: a03fe7a59ee646fef71099a887f774fe25d745b8
  Floaty: e2bd5a0f6f7f70899e26ff2da31081c8bee8d1b0
  Google-Mobile-Ads-SDK: 7db2098033ad3bfcd72a11e7503b49700a93029e
  GoogleAppMeasurement: f65fc137531af9ad647f1c0a42f3b6a4d3a98049
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GooglePlaces: 426efb69051e7b460e16300ba63598687d10fa1a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUserMessagingPlatform: f131fa7978d2ba88d7426702b057c2cc318e6595
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 8000756fc1c19d2e5697b90311f7832d2e33f6cd
  HMSegmentedControl: 34c1f54d822d8308e7b24f5d901ec674dfa31352
  IQKeyboardManager: c8665b3396bd0b79402b4c573eac345a31c7d485
  KDCircularProgress: 98200c6b8e85d22bc04eb644721d3b1e12b0686b
  KeychainAccess: c0c4f7f38f6fc7bbe58f5702e25f7bd2f65abf51
  Kingfisher: 53a10ea35051a436b5fb626ca2dd8f3144d755e9
  KMPlaceholderTextView: 2206c3df39979e9396e2d96fa9f427a8951121b0
  leveldb-library: e8eadf9008a61f9e1dde3978c086d2b6d9b9dc28
  lottie-ios: c55158d67d0629a260625cc2ded2052b829e3c3e
  MarqueeLabel: 00cc0bcd087111dca575878b3531af980559707d
  MOLH: e45827d200aeb1310641090fd8cd03ad2b42ae72
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  netfox: 9d5cc727fe7576c4c7688a2504618a156b7d44b7
  NotificationBannerSwift: 7021be2338f8f29cf424b0aca43da462bf9e2a1a
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  PINRemoteImage: 6256a644f4996991f124811763c7286b2a63ff94
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  SDWebImage: dfe95b2466a9823cf9f0c6d01217c06550d7b29a
  SDWebImageSVGKitPlugin: 7542dd07c344ec3415ded0461a1161a6f087e0c9
  SnapKit: 97b92857e3df3a0c71833cce143274bf6ef8e5eb
  SnapSDK: 1e68aad748e080f49e3802559b3b76026666ef62
  SVGKit: 1ad7513f8c74d9652f94ed64ddecda1a23864dea
  SwiftPopup: 31874508c0d91788edf71419a5bbeb3ebd498ab9
  TransitionRouter: 3a86b6b48bca3a96a4e201109968196ad0b7b4e2
  TYProgressBar: 1ec8ab0da29a6ccf80e20821388a8d2f484404c8
  Zip: b3fef584b147b6e582b2256a9815c897d60ddc67

PODFILE CHECKSUM: b48406c25b7d08bc374ecb208a243754fb09ab28

COCOAPODS: 1.16.2
