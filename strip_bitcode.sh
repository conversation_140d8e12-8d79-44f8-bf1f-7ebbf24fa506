#!/bin/bash

# Advanced script to strip bitcode from all frameworks
echo "🔧 Advanced Bitcode Stripping Script"
echo "===================================="

# Function to check if binary contains bitcode
has_bitcode() {
    local binary="$1"
    if otool -l "$binary" 2>/dev/null | grep -q "__LLVM\|__bitcode"; then
        return 0  # Has bitcode
    else
        return 1  # No bitcode
    fi
}

# Function to strip bitcode from a binary
strip_bitcode_from_binary() {
    local binary_path="$1"
    local framework_name="$2"

    if [ ! -f "$binary_path" ]; then
        echo "⚠️  Binary not found: $binary_path"
        return 1
    fi

    echo "🔍 Checking: $framework_name"

    # Check if binary has bitcode
    if has_bitcode "$binary_path"; then
        echo "📦 Found bitcode in $framework_name, stripping..."

        # Try multiple methods to strip bitcode
        local temp_file="${binary_path}.tmp"
        local success=false

        # Method 1: xcrun bitcode_strip
        if xcrun bitcode_strip -r "$binary_path" -o "$temp_file" 2>/dev/null; then
            mv "$temp_file" "$binary_path"
            success=true
            echo "✅ Method 1: xcrun bitcode_strip succeeded for $framework_name"
        else
            rm -f "$temp_file"

            # Method 2: llvm-strip
            if command -v llvm-strip >/dev/null 2>&1; then
                cp "$binary_path" "$temp_file"
                if llvm-strip --remove-section=__LLVM --remove-section=__bitcode "$temp_file" 2>/dev/null; then
                    mv "$temp_file" "$binary_path"
                    success=true
                    echo "✅ Method 2: llvm-strip succeeded for $framework_name"
                else
                    rm -f "$temp_file"
                fi
            fi
        fi

        if [ "$success" = true ]; then
            echo "🎉 Successfully stripped bitcode from $framework_name"
        else
            echo "❌ Failed to strip bitcode from $framework_name"
        fi
    else
        echo "✓ No bitcode found in $framework_name"
    fi
}

# Function to process a framework
process_framework() {
    local framework_path="$1"
    local framework_name=$(basename "$framework_path" .framework)

    # Get the main executable name from Info.plist
    local info_plist="$framework_path/Info.plist"
    local executable_name=""

    if [ -f "$info_plist" ]; then
        executable_name=$(/usr/libexec/PlistBuddy -c "Print :CFBundleExecutable" "$info_plist" 2>/dev/null || echo "")
    fi

    # If no executable name found, use framework name
    if [ -z "$executable_name" ]; then
        executable_name="$framework_name"
    fi

    local binary_path="$framework_path/$executable_name"
    strip_bitcode_from_binary "$binary_path" "$framework_name"
}

# Main execution
echo "🚀 Starting bitcode removal process..."

# Process frameworks in Pods directory
if [ -d "Pods" ]; then
    echo ""
    echo "📁 Processing Pods frameworks..."
    find Pods -name "*.framework" -type d | while read framework; do
        process_framework "$framework"
    done
fi

# Process frameworks in build directory
if [ -d "build" ]; then
    echo ""
    echo "📁 Processing build frameworks..."
    find build -name "*.framework" -type d | while read framework; do
        process_framework "$framework"
    done
fi

# Process frameworks in DerivedData (if accessible)
DERIVED_DATA_PATH="$HOME/Library/Developer/Xcode/DerivedData"
if [ -d "$DERIVED_DATA_PATH" ]; then
    echo ""
    echo "📁 Processing DerivedData frameworks..."
    find "$DERIVED_DATA_PATH" -name "PIL-*" -type d 2>/dev/null | head -1 | while read project_dir; do
        if [ -d "$project_dir" ]; then
            find "$project_dir" -name "*.framework" -type d | while read framework; do
                process_framework "$framework"
            done
        fi
    done
fi

echo ""
echo "🎯 Bitcode stripping completed!"
echo "=================================="
